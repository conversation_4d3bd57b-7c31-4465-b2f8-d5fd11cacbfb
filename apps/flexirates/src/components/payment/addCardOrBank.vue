<script setup lang="ts">
import type { PropType } from 'vue'
import { toTypedSchema } from '@vee-validate/yup'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, nextTick, ref, watch } from 'vue'
import * as yup from 'yup'
import CustomDialog from '@/components/customDialog/index.vue'
import { paymentMethod as paymentMethodApi } from '@/services/flexirates'
import { useCountryStore } from '@/store/modules/country'

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  showRadio: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: '',
  },
  otherData: {
    type: Object as PropType<{
      property_id?: number
    }>,
    default: () => ({}),
    required: false,
  },
})

const emit = defineEmits<Emits>()

const toast = useToast()

const addType = ref('bank')

const bankLoading = ref(false)
const cardLoading = ref(false)

const addBankRef = ref()

// 计算当前应该显示哪个表单
const currentForm = computed(() => {
  if (props.showRadio) {
    return addType.value
  }
  if (props.type) {
    return props.type
  }
  return 'bank'
})

// 判断是否显示卡片表单
const showCardForm = computed(() => {
  return currentForm.value === 'card'
})

const modelCard = ref(
  {
    cardNumber: '',
    expiryDate: '',
    nameOnCard: '',
    securityCode: '',
    nickname: '',
    firstName: '',
    lastName: '',
    line_1: '',
    line_2: '',
    country: '',
    city: '',
    state: '',
    phone: '',
    postcode: '',
    country_iso2: '',
    email: '',
    isDefaultPrimary: true,
  },
)
const modelBank = ref({
  bsbNumber: '',
  accountName: '',
  accountNumber: '',
  nickname: '',
  isDefaultPrimary: true,
})

const schemaCard = toTypedSchema(yup.object({
  cardNumber: yup.string().required('Card number is required'),
  expiryDate: yup.string().required('Expiry Date is required'),
  nameOnCard: yup.string().required('Name on card is required'),
  securityCode: yup.string().test('security-code', 'Please enter a valid security code (3-4 digits)', (value) => {
    if (!value || value.trim() === '') {
      return false
    }
    return /^\d{3,4}$/.test(value)
  }).required('Security code is required'),
  nickname: yup.string(),
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  line_1: yup.string().required('Address line 1 is required'),
  line_2: yup.string().optional(),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  phone: yup.string().required('Phone is required'),
  postcode: yup.string().required('Postcode is required'),
  country: yup.string().optional().test('country', 'Country is required', (value) => {
    if (!value || value.trim() === '') {
      return false
    }
    return true
  }),
  email: yup.string().required('Email is required').email('Invalid email format'),
  isDefaultPrimary: yup.boolean(),
}))

const schemaBank = toTypedSchema(yup.object({
  bsbNumber: yup.string().required('Card number is required').max(6, 'Maximum input of 6 digits.'),
  accountName: yup.string().required('Account name is required'),
  accountNumber: yup.string().required('Account Number on card is required'),
  nickname: yup.string().optional(),
  isDefaultPrimary: yup.boolean(),
}))

const onCardSubmit = async (values: any) => {
  const sendData = {
    ...props.otherData,
    return_url: window.location.href,
    type: 2,
    weight: values.isDefaultPrimary ? 1 : 2,
    card: {
      first_name: values.firstName,
      last_name: values.lastName,
      card_number: values.cardNumber,
      security_code: values.securityCode,
      name_on_card: values.nameOnCard,
      expiration_year: values.expiryDate.split('/')[1],
      expiration_month: values.expiryDate.split('/')[0],
      email: values.email,
      country_iso2: values.country,
      postcode: values.postcode,
      state: values.state,
      city: values.city,
      line_1: values.line_1,
      line_2: values.line_2,
      phone: values.phone,
      nickname: values.nickname,
    },
  }
  try {
    cardLoading.value = true
    const res = await paymentMethodApi.addCardOrBank(sendData)
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully Add Card.', life: 3000 })
      if (res.data.html) {
        const div = document.createElement('div')
        div.innerHTML = res.data.html
        document.body.appendChild(div)
        nextTick(() => {
          const stepUpForm = document.querySelector('#step-up-form') as HTMLFormElement
          if (stepUpForm) {
            stepUpForm?.submit()
          }
        })
      }
      emit('update:visible', false)
      emit('close')
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    cardLoading.value = false
  }
}
const onBankSubmit = async (values: any) => {
  const sendData = {
    ...props.otherData,
    return_url: window.location.href,
    type: 1,
    weight: values.isDefaultPrimary ? 1 : 2,
    bank: {
      bsb: values.bsbNumber.replace(/-/g, ''),
      account_no: values.accountNumber,
      account_name: values.accountName,
      nickname: values.nickname,
    },
  }
  try {
    bankLoading.value = true
    const res = await paymentMethodApi.addCardOrBank(sendData)
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully Add Bank Account.', life: 3000 })

      emit('close')
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    bankLoading.value = false
  }
}

if (props?.otherData?.property_id) {
  modelCard.value.isDefaultPrimary = false
  modelBank.value.isDefaultPrimary = false
}

const countryStore = useCountryStore()

const { countries, isLoading: isGetCountryLoading, isLoaded: isGetCountryLoaded } = storeToRefs(countryStore)

const stateOptions = ref<Array<{ name: string, code: string }>>([])

const setCountry = (value: string, isInit = false) => {
  if (!isInit) {
    modelCard.value.state = ''
  }
  const country = countries.value.find((item: any) => item.iso2 === value)
  if (country && country.state && country.state.length > 0) {
    stateOptions.value = country?.state
  }
  else {
    stateOptions.value = []
  }
}

watch(() => isGetCountryLoaded, (newVal) => {
  if (newVal && modelCard.value.country) {
    setCountry(modelCard.value.country, true)
  }
})
</script>

<template>
  <div>
    <CustomDialog
      title="Add New Payment Method" :visible="props.visible"
      @update:visible="(val) => emit('update:visible', val)"
    >
      <template #content>
        <div v-if="props.showRadio" class="flex items-center pt-10">
          <div class="type-item flex gap-4">
            <RadioButton v-model="addType" value="bank" input-id="bankAccount" />
            <label for="bankAccount" class="form-label">Bank Account</label>
          </div>
          <div class="type-item flex gap-4">
            <RadioButton v-model="addType" value="card" input-id="cardNumber" />
            <label for="cardNumber" class="form-label">Card Number</label>
          </div>
        </div>
        <div v-if="showCardForm" class="card">
          <ScrollPanel style="width: 600px; height: 700px">
            <VeeForm
              class="add-card-form" :initial-values="modelCard" :validation-schema="schemaCard"
              @submit="onCardSubmit"
            >
              <div class="relative mb-12 mt-6">
                <Field
                  v-slot="{ field, handleChange, errorMessage }" v-model="modelCard.cardNumber" as="div"
                  class="form-group " name="cardNumber"
                >
                  <label>Card Number<span class="is-require">*</span></label>
                  <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                  <Message
                    v-if="errorMessage" class="top-[48px] left-[260px] absolute" severity="error"
                    variant="simple"
                  >
                    {{ errorMessage }}
                  </Message>
                </Field>
                <div class="card-types">
                  <img src="@/assets/flexirates/register-payment.png" alt="card">
                </div>
              </div>

              <!-- <div class="flex items-center gap-2">
                            <label class="form-label">Expiry Date<span class="is-require">*</span></label>
                            <Field v-slot="{ field }" as="div" class="form-group expiry-date" name="expiryStart">
                                <div class="expiry-inputs">
                                    <DatePicker v-model="field.value" show-icon fluid icon-display="input"
                                        input-id="expiryStart" />
                                </div>
                            </Field>
                            <Field v-slot="{ field }" as="div" class="form-group expiry-date" name="expiryEnd">
                                <div class="expiry-inputs">
                                    <DatePicker v-model="field.value" show-icon fluid icon-display="input"
                                        input-id="expiryEnd" />
                                </div>
                            </Field>
                        </div> -->

              <Field
                v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.expiryDate" as="div"
                class="form-group expiry-date" name="expiryDate"
              >
                <label>Expiry Date<span class="is-require">*</span></label>
                <div class="expiry-inputs">
                  <InputMask
                    :model-value="field.value" placeholder="MM/YY" mask="99/99" class="w-full"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <Field
                v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.nameOnCard" as="div"
                class="form-group" name="nameOnCard"
              >
                <label>Name on Card<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>

              <div class="flex items-center pb-[1.5rem] relative">
                <label class="form-label">Name<span class="is-require">*</span></label>
                <div class="flex items-center gap-4">
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.firstName" class="relative"
                    as="div" name="firstName"
                  >
                    <InputText
                      v-model="field.value" class="w-full" placeholder="First name"
                      @value-change="handleChange"
                    />
                    <Message v-if="errorMessage" class="absolute" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                  <Field
                    v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.lastName" as="div"
                    name="lastName" class="relative"
                  >
                    <InputText
                      v-model="field.value" class="w-full" placeholder="Last name"
                      @value-change="handleChange"
                    />
                    <Message class="absolute" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                </div>
              </div>

              <Field
                v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.securityCode" as="div"
                class="form-group" name="securityCode"
              >
                <label>CVV<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                <Message class="absolute top-[35px] left-[180px] " severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>

              <div class="flex items-center">
                <label class="form-label">Address<span class="is-require">*</span></label>
                <div class="w-full flex flex-col gap-1">
                  <Field
                    v-slot="{ field, errorMessage }" v-model="modelCard.line_1" as="div"
                    class="form-group relative" name="line_1"
                  >
                    <InputText v-model="field.value" class="w-full" placeholder="Address Line 1" />
                    <Message v-if="errorMessage" class="absolute top-[37px]" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </Field>
                  <Field v-slot="{ field }" v-model="modelCard.line_2" as="div" class="form-group" name="line_2">
                    <InputText v-model="field.value" class="w-full" placeholder="Address Line 2" />
                  </Field>
                </div>
              </div>

              <Field
                v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.country" as="div"
                class="form-group" name="country"
              >
                <label>Country<span class="is-require">*</span></label>
                <Select
                  id="country" v-model="field.value" class="w-full" :options="countries" option-label="name"
                  option-value="iso2" placeholder="Select country" :loading="isGetCountryLoading" filter
                  show-clear @value-change="(e: any) => {
                    handleChange(e)
                    setCountry(e)
                  }"
                />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="modelCard.state" as="div"
                class="form-group" name="state"
              >
                <label>State<span class="is-require">*</span></label>
                <Select
                  id="state" class="w-full" :model-value="field.value" :options="stateOptions" option-label="name"
                  option-value="name" placeholder="Select state" :loading="isGetCountryLoading"
                  :disabled="!modelCard.country" filter @value-change="handleChange"
                />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>
              <Field
                v-slot="{ field, errorMessage, handleChange }" v-model="modelCard.city" as="div" class="form-group"
                name="city"
              >
                <label>City<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="modelCard.phone" as="div"
                class="form-group" name="phone"
              >
                <label>Phone<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="modelCard.postcode" as="div"
                class="form-group" name="postcode"
              >
                <label>Postcode<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>
              <!-- <Field
                v-slot="{ field }" v-model="modelCard.country_iso2" as="div" class="form-group"
                name="country_iso2"
              >
                <label>Country ISO2<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" />
              </Field> -->
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="modelCard.email" as="div"
                class="form-group" name="email"
              >
                <label>Email<span class="is-require">*</span></label>
                <InputText v-model="field.value" class="w-full" @value-change="handleChange" />
                <Message
                  v-if="errorMessage" class="absolute top-[35px] left-[180px] " severity="error"
                  variant="simple"
                >
                  {{ errorMessage }}
                </Message>
              </Field>
              <Field v-slot="{ field }" as="div" class="form-group" name="nickname">
                <label>Nickname</label>
                <InputText v-model="field.value" class="w-full" />
              </Field>

              <Field v-slot="{ field }" as="div" class="form-group payment-method mt-10" name="isDefaultPrimary">
                <div class="payment-options">
                  <div class="option">
                    <RadioButton v-model="field.value" :value="true" input-id="primary" />
                    <label for="primary">Make Default Primary Payment Method?</label>
                  </div>
                  <div class="option">
                    <RadioButton v-model="field.value" :value="false" input-id="secondary" />
                    <label for="secondary">Make Default Secondary Payment Method?</label>
                  </div>
                </div>
              </Field>
              <small class="hint-text">This will not affect any current active schedules. Your selected
                preference will
                be used to prefill future registrations.</small>

              <div class="form-actions">
                <Button
                  type="submit" label="SAVE" severity="warn" class="btn" :loading="cardLoading"
                  @click="() => { console.log(modelCard) }"
                />
              </div>
            </VeeForm>
          </ScrollPanel>
        </div>
        <div v-else class="bank">
          <VeeForm
            ref="addBankRef" class="add-account-form mt-6" :validation-schema="schemaBank"
            :initial-values="modelBank" @submit="onBankSubmit"
          >
            <Field
              v-slot="{ field, errorMessage }" v-model="modelBank.bsbNumber" as="div" class="form-group"
              name="bsbNumber"
            >
              <label>BSB Number<span class="is-require">*</span></label>
              <InputText v-model="field.value" class="w-full" />
              <Message v-if="errorMessage" class="mt-2 form-message" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
            <Field
              v-slot="{ field, errorMessage }" v-model="modelBank.accountName" as="div" class="form-group"
              name="accountName"
            >
              <label>Account Name<span class="is-require">*</span></label>
              <InputText v-model="field.value" class="w-full" />
              <Message v-if="errorMessage" class="mt-2 form-message" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
            <Field
              v-slot="{ field, errorMessage }" v-model="modelBank.accountNumber" as="div" class="form-group"
              name="accountNumber"
            >
              <label>Account Number<span class="is-require">*</span></label>
              <InputText v-model="field.value" class="w-full" />
              <Message v-if="errorMessage" class="mt-2 form-message" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
            <Field v-slot="{ field }" v-model="modelBank.nickname" as="div" class="form-group" name="nickname">
              <label>Nickname</label>
              <InputText v-model="field.value" class="w-full" />
            </Field>

            <Field
              v-slot="{ field }" v-model="modelBank.isDefaultPrimary" as="div"
              class="form-group payment-method mt-10" name="isDefaultPrimary"
            >
              <div v-if="!props?.otherData?.property_id" class="payment-options">
                <div class="option">
                  <RadioButton v-model="field.value" :value="true" input-id="primary" />
                  <label for="primary">Make Default Primary Payment Method?</label>
                </div>
                <div class="option">
                  <RadioButton v-model="field.value" :value="false" input-id="secondary" />
                  <label for="secondary">Make Default Secondary Payment Method?</label>
                </div>
              </div>
            </Field>
            <small class="hint-text">This will not affect any current active schedules. Your selected
              preference will
              be used to prefill future registrations.</small>

            <div class="form-actions">
              <Button type="submit" label="SAVE" severity="warn" class="btn" :loading="bankLoading" />
            </div>
          </VeeForm>
        </div>
      </template>
    </CustomDialog>
  </div>
</template>

<style lang="scss" scoped>
.add-account-form,
.add-account-form {
  .is-require {
    color: #0073cf;
  }

  .form-group {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    position: relative;

    label {
      display: block;
      width: 178px;
      min-width: 178px;
      font-size: 16px;
      font-weight: 600;
    }

    .form-message {
      position: absolute;
      left: 180px;
      top: 30px;
    }
  }

  .payment-options {
    display: flex;
    gap: 4rem;
    margin-bottom: 1rem;

    .option {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }

  .hint-text {
    display: block;
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }

  .form-actions {
    margin-top: 1.5rem;
    display: flex;
    justify-content: flex-end;
  }
}

.btn {
  padding: 8px 20px;
  font-size: 16px;
  --p-button-label-font-weight: 600;
}

.form-label {
  display: block;
  width: 178px;
  min-width: 178px;
  font-size: 16px;
  font-weight: 600;
}

.add-card-form {
  .is-require {
    color: #0073cf;
  }

  .form-group {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    position: relative;

    label {
      display: block;
      width: 178px;
      min-width: 178px;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .card-types {
    display: flex;
    gap: 0.5rem;
    position: absolute;
    top: 45px;
    left: 180px;

    img {
      height: 20px;
      margin-right: 30px;
    }
  }

  .expiry-date {
    .expiry-inputs {
      display: flex;
      gap: 0.5rem;

      .month-input,
      .year-input {
        width: 80px;
      }
    }
  }

  .payment-options {
    display: flex;

    gap: 4rem;
    margin-bottom: 1rem;

    .option {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }

  .hint-text {
    display: block;
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }

  .form-actions {
    margin-top: 1.5rem;
    display: flex;
    justify-content: flex-end;

  }
}
</style>
