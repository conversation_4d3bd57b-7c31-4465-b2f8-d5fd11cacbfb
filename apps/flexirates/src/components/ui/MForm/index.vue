<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Form as VeeForm } from 'vee-validate'
import { computed, provide, reactive, ref } from 'vue'
import { z } from 'zod'
import { formContextKey, formRegisterKey } from '.'

const props = withDefaults(defineProps<{
  // 表单模型数据
  model?: Record<string, any>
  // 是否禁用表单
  disabled?: boolean
  // 表单项标签宽度
  labelWidth?: string | number
  // 表单项标签位置
  labelPosition?: 'top' | 'left' | 'right'
  // 表单项标签对齐方式
  labelAlign?: 'left' | 'right' | 'center'
  // 表单项间距
  itemGap?: string | number
  // 验证触发方式
  trigger?: 'blur' | 'change' | 'input' | ('blur' | 'change' | 'input')[]
  // 是否在组件挂载时验证
  validateOnMount?: boolean
}>(), {
  model: () => ({}),
  disabled: false,
  labelWidth: 'auto',
  labelPosition: 'left',
  labelAlign: 'left',
  itemGap: '20px',
  trigger: 'blur',
  validateOnMount: false,
})

const emit = defineEmits<{
  (e: 'submit', values: Record<string, any>): void
  (e: 'invalid', errors: Record<string, string>): void
}>()

const formRef = ref<InstanceType<typeof VeeForm> | null>(null)
// 使用 $attrs 直接绑定到组件上

// 提供给子组件的上下文
provide(formContextKey, {
  model: computed(() => props.model),
  disabled: computed(() => props.disabled),
  labelWidth: computed(() => props.labelWidth),
  labelPosition: computed(() => props.labelPosition),
  labelAlign: computed(() => props.labelAlign),
  itemGap: computed(() => props.itemGap),
  trigger: computed(() => props.trigger),
  validateOnMount: computed(() => props.validateOnMount),
})

// 存储所有表单项的规则
const formRules = reactive<Record<string, any>>({})

// 注册表单项规则
const registerField = (name: string, rules: any) => {
  formRules[name] = rules
}

// 注销表单项规则
const unregisterField = (name: string) => {
  delete formRules[name]
}

// 提供注册和注销方法给子组件
provide(formRegisterKey, {
  registerField,
  unregisterField,
})

// 构建 schema
const schema = computed(() => {
  const schemaObj: Record<string, any> = {}

  for (const [field, rules] of Object.entries(formRules)) {
    if (rules) {
      schemaObj[field] = rules
    }
  }

  // 如果没有规则，返回一个空的 schema
  if (Object.keys(schemaObj).length === 0) {
    return {}
  }

  // 处理规则
  try {
    // 检查是否所有规则都是 zod 类型
    const allZod = Object.values(schemaObj).every(rule => rule instanceof z.ZodType)

    if (allZod) {
      // 如果都是 zod 规则，使用 toTypedSchema 转换
      return toTypedSchema(z.object(schemaObj))
    }
    else {
      // 如果有非 zod 规则，直接返回规则对象
      return schemaObj
    }
  }
  catch (error) {
    console.error('Invalid schema:', error)
    return {}
  }
})

// 处理表单提交
const handleSubmit = (values: Record<string, any>, ctx: any) => {
  if (!ctx.errors || Object.keys(ctx.errors).length === 0) {
    emit('submit', values)
  }
  else {
    emit('invalid', ctx.errors || {})
  }
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetForm: () => formRef.value?.resetForm(),
  submit: () => {
    if (formRef.value) {
      // 手动触发表单提交
      const submitEvent = new Event('submit', { cancelable: true })
      formRef.value.$el.dispatchEvent(submitEvent)
    }
  },
  setFieldValue: (field: string, value: any) => formRef.value?.setFieldValue(field, value),
  setErrors: (errors: Record<string, string>) => formRef.value?.setErrors(errors),
})
</script>

<template>
  <VeeForm
    ref="formRef"
    :validation-schema="schema"
    v-bind="$attrs"
    @submit="handleSubmit"
  >
    <slot />
  </VeeForm>
</template>

<style scoped>
/* 可以根据需要添加样式 */
</style>
