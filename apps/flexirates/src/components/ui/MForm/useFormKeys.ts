import { inject } from 'vue'
import { formContextKey, formRegisterKey } from '.'

/**
 * 获取表单上下文
 * @returns 表单上下文
 */
export function useFormContext() {
  const formContext = inject(formContextKey, null)
  if (!formContext) {
    console.warn('useFormContext must be used inside MForm')
  }
  return formContext
}

/**
 * 获取表单注册方法
 * @returns 表单注册方法
 */
export function useFormRegister() {
  const formRegister = inject(formRegisterKey, null)
  if (!formRegister) {
    console.warn('useFormRegister must be used inside MForm')
  }
  return formRegister
}
