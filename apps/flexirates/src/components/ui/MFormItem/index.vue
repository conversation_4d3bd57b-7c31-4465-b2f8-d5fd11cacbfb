<script setup lang="ts">
import { Field, useField } from 'vee-validate'
import { computed, onBeforeUnmount, onMounted, provide, ref, useAttrs } from 'vue'
import { z } from 'zod'
import { useFormContext, useFormRegister } from '../MForm/useFormKeys'
import { formItemContextKey } from './useFormItem'

// 定义规则类型
type RuleTypes = z.ZodTypeAny | ((value: any, form?: Record<string, any>) => boolean | Promise<boolean>)

const props = withDefaults(defineProps<{
  // 表单项名称，必须与表单数据的字段名一致
  prop?: string
  // 表单项标签
  label?: string
  // 表单项验证规则，支持 zod 或函数形式
  rules?: RuleTypes
  // 验证触发方式
  trigger?: 'blur' | 'change' | 'input' | ('blur' | 'change' | 'input')[]
  // 标签宽度，优先级高于表单组件的 labelWidth
  labelWidth?: string | number
  // 标签位置，优先级高于表单组件的 labelPosition
  labelPosition?: 'top' | 'left' | 'right'
  // 标签对齐方式，优先级高于表单组件的 labelAlign
  labelAlign?: 'left' | 'right' | 'center'
  // 是否在组件挂载时验证
  validateOnMount?: boolean
}>(), {
  prop: '',
  label: '',
  required: false,
  trigger: 'blur',
  labelWidth: undefined,
  labelPosition: undefined,
  labelAlign: undefined,
  validateOnMount: false,
})

// 获取表单上下文
const formContext = useFormContext() || {
  model: computed(() => ({})),
  disabled: computed(() => false),
  labelWidth: computed(() => 'auto'),
  labelPosition: computed(() => 'left'),
  labelAlign: computed(() => 'left'),
  itemGap: computed(() => '20px'),
  trigger: computed(() => 'blur'),
  validateOnMount: computed(() => false),
}

// 从表单模型中获取字段值
const fieldValue = computed(() => {
  const model = formContext.model.value as Record<string, any>
  if (!model || !props.prop) {
    return undefined
  }

  // 支持嵌套属性路径，如 'type.name'
  if (props.prop.includes('.')) {
    return props.prop.split('.').reduce((obj, key) => {
      return obj && typeof obj === 'object' ? obj[key] : undefined
    }, model)
  }

  return model[props.prop]
})

// 获取表单注册方法
const formRegister = useFormRegister() || {
  registerField: (_name: string, _rules: any) => {},
  unregisterField: (_name: string) => {},
}

// 计算标签样式
const labelStyle = computed(() => {
  const position = props.labelPosition || formContext.labelPosition.value
  const align = props.labelAlign || formContext.labelAlign.value
  const width = props.labelWidth || formContext.labelWidth.value

  return {
    width: position === 'top' ? '100%' : (typeof width === 'number' ? `${width}px` : width),
    textAlign: align as 'left' | 'right' | 'center',
    display: position === 'top' ? 'block' : 'inline-block',
    verticalAlign: 'middle',
  }
})

// 计算表单项样式
const formItemStyle = computed(() => {
  const position = props.labelPosition || formContext.labelPosition.value
  const gap = formContext.itemGap.value

  return {
    marginBottom: typeof gap === 'number' ? `${gap}px` : gap,
    display: position === 'top' ? 'block' : 'flex',
    alignItems: 'flex-start',
  }
})

// 获取实际的触发方式
const actualTrigger = computed(() => {
  return props.trigger || formContext.trigger.value
})

// 获取实际的是否在挂载时验证
const actualValidateOnMount = computed(() => {
  return props.validateOnMount
})

// 根据 rules 推断是否必填
const isRequired = computed(() => {
  // 如果是 zod 规则，检查是否有 .min(1) 或其他必填限制
  if (props.rules instanceof z.ZodType) {
    // 这里我们只能做一个粗略的判断，因为 zod 规则的内部结构不是公开的
    // 如果是 string().min(1) 或 array().min(1) 等等，可以认为是必填的
    const zodStr = props.rules.toString()
    return zodStr.includes('.min(1)') || zodStr.includes('nonempty') || !zodStr.includes('optional')
  }

  return false
})

// 检查是否应该在特定触发方式下验证
const shouldValidateOn = (trigger: 'blur' | 'change' | 'input') => {
  const currentTrigger = actualTrigger.value
  if (Array.isArray(currentTrigger)) {
    return currentTrigger.includes(trigger)
  }
  return currentTrigger === trigger
}

// 过滤出需要传递给 Field 组件的属性
const attrs: Record<string, unknown> = useAttrs()
const fieldAttrs = computed(() => {
  const { class: _, style: __, ...rest } = attrs
  return rest
})

// 处理规则转换
const convertRules = (rules: RuleTypes) => {
  // 如果是 zod 规则，直接返回
  if (rules instanceof z.ZodType) {
    return rules
  }

  // 如果是函数，转换为验证器函数
  if (typeof rules === 'function') {
    return rules
  }

  // 如果没有规则，返回一个始终验证通过的函数
  return () => ({ valid: true })
}

// 获取字段实例
const { validate: validateField } = useField(props.prop)

// 表单项验证状态
const validateState = ref('')

// 验证方法
const validate = async (trigger?: 'blur' | 'change' | 'input') => {
  // 如果指定了触发方式，但与当前表单项的触发方式不匹配，则不验证
  if (trigger && !shouldValidateOn(trigger)) {
    return { valid: true }
  }

  try {
    // 使用字段值进行验证
    const result = await validateField()

    // 根据验证结果设置状态
    if (result.valid) {
      validateState.value = 'success'
      return true
    }
    else {
      validateState.value = 'error'
      return false
    }
  }
  catch {
    validateState.value = 'error'
    return false
  }
}

// 提供表单项上下文给子组件
provide(formItemContextKey, {
  validate,
  validateState: computed(() => validateState.value),
  trigger: actualTrigger,
})

// 在组件挂载时注册表单项规则
onMounted(() => {
  if (props.rules && props.prop) {
    const convertedRules = convertRules(props.rules)
    formRegister.registerField(props.prop, convertedRules)
  }
})

// 在组件卸载前注销表单项规则
onBeforeUnmount(() => {
  formRegister.unregisterField(props.prop)
})
</script>

<template>
  <div class="m-form-item" :style="formItemStyle">
    <label
      v-if="label"
      class="m-form-item__label"
      :style="labelStyle"
      :for="prop"
    >
      {{ label }}
      <span v-if="isRequired" class="m-form-item__required">*</span>
    </label>
    <div class="m-form-item__content">
      <Field
        v-slot="{ errorMessage }"
        :model-value="fieldValue"
        v-bind="fieldAttrs"
        :name="prop"
        :validate-on-input="shouldValidateOn('input')"
        :validate-on-change="shouldValidateOn('change')"
        :validate-on-blur="shouldValidateOn('blur')"
        :validate-on-mount="actualValidateOnMount"
      >
        <div class="m-form-item__field">
          <slot />
        </div>
        <Message v-if="errorMessage" class="m-form-item__error" severity="error" variant="simple">
          {{ errorMessage }}
        </Message>
      </Field>
    </div>
  </div>
</template>

<style scoped>
.m-form-item {
  margin-bottom: 18px;
}

.m-form-item__label {
  font-weight: 500;
  line-height: 1.5;
  padding-right: 12px;
  box-sizing: border-box;
}

.m-form-item__required {
  color: #f56c6c;
  margin-left: 4px;
}

.m-form-item__content {
  flex: 1;
  min-width: 0;
}

.m-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>
