import { inject } from 'vue'

// 表单项上下文注入键
export const formItemContextKey = Symbol('form-item-context')

export interface FormItemContext {
  validate: (trigger?: 'blur' | 'change' | 'input') => Promise<boolean>
  validateState: { value: string }
  trigger: { value: 'blur' | 'change' | 'input' | ('blur' | 'change' | 'input')[] }
}

/**
 * 获取表单项上下文
 * @returns 表单项上下文
 */
export function useFormItem() {
  const formItemContext = inject(formItemContextKey, null) as FormItemContext | null

  if (!formItemContext) {
    console.warn('useFormItem must be used inside MFormItem')
  }

  return {
    formItem: formItemContext,
  }
}
