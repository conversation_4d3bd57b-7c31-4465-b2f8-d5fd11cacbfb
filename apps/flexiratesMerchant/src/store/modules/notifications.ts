import { defineStore } from 'pinia'
import { ref } from 'vue'
import { notifications } from '@/services/api/index'

export const useNotificationStore = defineStore('notifications', () => {
  const unreadCount = ref(0)
  let timer: number | null = null
  const getUnreadCount = async () => {
    try {
      const res = await notifications.getUnreadCount()
      unreadCount.value = res?.data?.count || 0
    }
    catch {
      unreadCount.value = 0
    }
  }

  const startPolling = () => {
    getUnreadCount()
    if (timer) { clearInterval(timer) }
    timer = window.setInterval(getUnreadCount, 5000)
  }

  const stopPolling = () => {
    if (timer) { clearInterval(timer) }
    timer = null
  }

  return { unreadCount, getUnreadCount, startPolling, stopPolling }
}, {
  persist: true,
})
