import{_ as ee,B as te,a as ae,s as oe,b as le,c as se}from"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import{s as re}from"./index-Dvu_aI8r.js";import{s as ne}from"./index-BH-zSNAS.js";import{d as ie,a9 as V,a8 as j,i as B,j as E,a as i,o as l,b1 as T,g as u,b as y,h as s,e as g,c as d,F as c,r as k,H as w,s as ue,u as N,t as m,l as $,m as b,f as x,U as z}from"./index-Cr_XsrqN.js";import{s as de}from"./index-BwvvB-v3.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ce={class:"table-search"},fe={class:"flex gap-4 justify-end mb-2"},me={class:"flex flex-col gap-2"},ve={class:"empty-state-container"},ye={class:"empty-state-title"},be={class:"empty-state-message"},ge={class:"paginator-container"},he={class:"flex items-center gap-2"},ke={class:"paginator-button-container"},we=ie({__name:"BaseDataTable",props:V({value:{},columns:{},minWidth:{default:"49rem"},rows:{default:20},dataKey:{default:"id"},rowsPerPageOptions:{default(){return[20,50,100]}},stripedRows:{type:Boolean,default:!1},showMultipleColumn:{type:Boolean,default:!0},showEditColumn:{type:Boolean,default:!0},editColumnLabel:{default(){return z().t("actions")}},exportLabel:{default(){return z().t("export")}},paginator:{type:Boolean,default:!0},editColumnHeader:{},size:{default:void 0},scrollable:{type:Boolean,default:!1},failed:{type:Boolean},failureMessage:{},showSearchBar:{type:Boolean,default:!0},rowHover:{type:Boolean,default:!1}},{searchFields:{type:Object,default(){return{}}},searchFieldsModifiers:{},"v-model:selection":{type:Array,default(){return[]}},"v-model:selectionModifiers":{}}),emits:V(["sort","page","changeSearch","export","edit","editRows","rowClick"],["update:searchFields","update:v-model:selection"]),setup(C,{expose:H,emit:L}){const o=C,h=L,r=j(C,"searchFields"),S=B(),P=j(C,"v-model:selection"),U=()=>{const t={};for(const a in r.value)if(Object.prototype.hasOwnProperty.call(r.value,a)){const f=r.value[a].value;t[a]=f}return t},F=(t,a)=>{a.value=t,h("changeSearch",U())},v=B({}),D=B();o.columns.forEach(t=>{v.value[t.field]===void 0&&(v.value[t.field]=!0)});const I=E(()=>o.columns.filter(t=>v.value[t.field])),q=t=>{v.value[t]=!v.value[t]},A=E(()=>{let t=!1;for(const a of Object.keys(r.value))Object.prototype.hasOwnProperty.call(r.value,a)&&r.value[a].isSuperSearch&&(t=!0);return t}),O=()=>{for(const t of Object.keys(r.value))if(Object.prototype.hasOwnProperty.call(r.value,t)&&r.value[t].isSuperSearch)return t};return H({exportCSV:()=>{S.value.exportCSV()},getSelectedRowsData:()=>S.value.getSelectedRowsData()}),(t,a)=>{const f=ue,M=le,K=ne,W=re,X=se;return l(),i(X,{ref_key:"dataTableRef",ref:S,selection:P.value,"onUpdate:selection":a[3]||(a[3]=e=>P.value=e),value:t.value,"striped-rows":o.stripedRows,"table-style":{"min-width":o.minWidth},paginator:o.paginator&&t.value.length>0,rows:o.rows,"data-key":o.dataKey,size:o.size,"rows-per-page-options":o.rowsPerPageOptions,scrollable:o.scrollable,class:"base-data-table","row-hover":o.rowHover,onSort:a[4]||(a[4]=e=>h("sort",e)),onPage:a[5]||(a[5]=e=>h("page",e)),onRowClick:a[6]||(a[6]=e=>h("rowClick",e))},T({empty:u(()=>[s("div",ve,[s("div",{class:x(["empty-state-card",{"error-state":o.failed}])},[s("div",{class:x(["empty-state-icon-container",{"error-icon":o.failed}])},[s("i",{class:x(o.failed?"pi pi-exclamation-triangle":"pi pi-inbox")},null,2)],2),s("h3",ye,[o.failed?(l(),d(c,{key:0},[b(m(t.$t("common.requestFailed")||"Request Failed"),1)],64)):(l(),d(c,{key:1},[b(m(t.$t("common.noDataFound")||"No data found"),1)],64))]),s("p",be,[o.failed&&o.failureMessage?(l(),d(c,{key:0},[b(m(o.failureMessage),1)],64)):o.failed?(l(),d(c,{key:1},[b(m(t.$t("common.requestFailedDescription")||"An error occurred while fetching data. Please try again later."),1)],64)):(l(),d(c,{key:2},[b(m(t.$t("common.noDataFoundDescription")||"Try adjusting your search or filter to find what you're looking for."),1)],64))]),$(t.$slots,"empty-action",{},void 0,!0)],2)])]),paginatorcontainer:u(({rows:e,page:n,pageCount:_=0,pageLinks:G=[],prevPageCallback:J,nextPageCallback:Q,rowChangeCallback:Y,changePageCallback:Z})=>{var R;return[s("div",ge,[s("div",he,[a[8]||(a[8]=s("span",{class:"hidden sm:block"},"Show",-1)),g(K,{"model-value":e,options:(R=o.rowsPerPageOptions)==null?void 0:R.map(p=>({label:p,value:p})),"option-label":"label","option-value":"value",onChange:p=>Y(p.value)},null,8,["model-value","options","onChange"])]),s("div",ke,[g(W,{align:"center",layout:"vertical"}),n!==0?(l(),i(f,{key:0,icon:"pi pi-chevron-left",class:"paginator-button",label:"PREV",rounded:"",text:"",onClick:J},null,8,["onClick"])):y("",!0),(l(!0),d(c,null,k(G,p=>(l(),i(f,{key:p,label:String(p),disabled:p===n+1,class:"paginator-button-page",onClick:Ce=>Z(p-1)},null,8,["label","disabled","onClick"]))),128)),n!==_-1?(l(),i(f,{key:1,label:"NEXT",class:"paginator-button",icon:"pi pi-chevron-right","icon-pos":"right",rounded:"",text:"",onClick:Q},null,8,["onClick"])):y("",!0)])])]}),default:u(()=>[t.showMultipleColumn?(l(),i(M,{key:0,"selection-mode":"multiple",exportable:!1})):y("",!0),(l(!0),d(c,null,k(I.value,e=>(l(),i(M,w({ref_for:!0},e,{key:e.field}),T({_:2},[e.field==="action"?{name:"header",fn:u(()=>[a[7]||(a[7]=s("i",{class:"pi pi-cog m-auto !text-xl"},null,-1))]),key:"0"}:void 0,e.field==="action"?{name:"body",fn:u(n=>[$(t.$slots,e.template,w({ref_for:!0},n),void 0,!0)]),key:"1"}:e.template?{name:"body",fn:u(n=>[$(t.$slots,e.template,w({ref_for:!0},n),void 0,!0)]),key:"2"}:e!=null&&e.format?{name:"body",fn:u(n=>[b(m((e==null?void 0:e.format)&&e.format(n.data)),1)]),key:"3"}:void 0]),1040))),128))]),_:2},[o.showSearchBar?{name:"header",fn:u(()=>[A.value?(l(),i(ee,{key:0,"search-field":r.value[O()],"onUpdate:value":a[0]||(a[0]=e=>F(e,r.value[O()]))},null,8,["search-field"])):y("",!0),s("div",ce,[g(te,null,{default:u(()=>[(l(!0),d(c,null,k(r.value,(e,n)=>(l(),i(ae,w({key:n},{ref_for:!0},e,{"is-hide":!!(e!=null&&e.isHide),"onUpdate:value":_=>F(_,e)}),null,16,["is-hide","onUpdate:value"]))),128))]),_:1}),s("div",fe,[o.showEditColumn?(l(),i(f,{key:0,icon:"pi pi-cog",label:o.editColumnLabel,severity:"secondary",onClick:a[1]||(a[1]=e=>D.value.toggle(e))},null,8,["label"])):y("",!0),g(f,{icon:"pi pi-external-link",label:o.exportLabel,severity:"secondary",onClick:a[2]||(a[2]=e=>t.$emit("export"))},null,8,["label"]),o.showEditColumn?(l(),i(N(oe),{key:1,ref_key:"editEditColumnPopover",ref:D,class:"p-4"},{default:u(()=>[s("div",me,[(l(!0),d(c,null,k(o.columns,e=>(l(),d("div",{key:e.field,class:"flex items-center gap-2"},[g(N(de),{"model-value":v.value[e.field],binary:!0,severity:"secondary",onChange:n=>q(e.field)},null,8,["model-value","onChange"]),s("span",null,m(e.header),1)]))),128))])]),_:1},512)):y("",!0)])])]),key:"0"}:void 0]),1032,["selection","value","striped-rows","table-style","paginator","rows","data-key","size","rows-per-page-options","scrollable","row-hover"])}}}),Fe=pe(we,[["__scopeId","data-v-68dc56d7"]]);export{Fe as _};
