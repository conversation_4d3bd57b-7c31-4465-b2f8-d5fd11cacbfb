import{d as Ne,i as A,j as P,q as Ve,p as Me,w as Le,c as f,f as S,b,h as p,F as le,r as oe,a as N,l as T,e as $,u as w,s as C,n as ye,t as V,g as Oe,m as be,ba as ke,o as u}from"./index-Cr_XsrqN.js";import{s as Re}from"./index--SCaD3QG.js";import{s as G}from"./index-BquGHVpa.js";import{_ as Be}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Pe={key:0,class:"p-fileupload-errors"},Te={key:1,class:"p-fileupload-avatar-content"},We={key:0,class:"p-fileupload-avatar-placeholder"},He={key:1,class:"p-fileupload-avatar-preview"},je=["src"],qe={key:0,class:"p-fileupload-avatar-actions"},Ye={key:2,class:"p-fileupload-gallery-content"},Ge={key:0,class:"p-fileupload-gallery-placeholder"},Je={class:"p-fileupload-gallery-grid"},Xe={class:"p-fileupload-gallery-item-preview"},Ke=["src","alt"],Qe={class:"p-fileupload-gallery-item-overlay"},Ze={class:"p-fileupload-gallery-item-info"},Ie={class:"p-fileupload-gallery-item-name"},er={class:"p-fileupload-gallery-item-size"},rr={class:"p-fileupload-gallery-item-actions"},ar={key:1,class:"p-fileupload-gallery-actions"},tr={key:3,class:"p-fileupload-content"},sr={class:"p-fileupload-upload"},lr={key:0},or={key:1},nr={class:"p-fileupload-row-content"},ir={class:"p-fileupload-file"},ur={class:"p-fileupload-file-thumbnail"},pr=["src","alt"],dr={class:"p-fileupload-file-details"},cr={class:"p-fileupload-file-name"},fr={class:"p-fileupload-file-size"},vr={key:0,class:"p-fileupload-file-error"},gr={key:1,class:"p-fileupload-file-warning"},mr={class:"p-fileupload-file-actions"},hr={key:0,class:"p-fileupload-file-progress"},wr={key:1,class:"p-fileupload-actions"},yr={key:2,class:"p-fileupload-progress"},br={class:"p-fileupload-progress-label"},kr=["accept","multiple","disabled"],xr=Ne({__name:"BaseFileUpload",props:{mode:{type:String,default:"normal",validator:_=>["normal","avatar","gallery"].includes(_)},multiple:{type:Boolean,default:!0},accept:{type:String,default:""},maxSize:{type:Number,default:5*1024*1024},maxFiles:{type:Number,default:0},autoUpload:{type:Boolean,default:!0},uploadFunction:{type:[Function,null],default:null},uploadApiFunction:{type:Function,default:null},uploadUrl:{type:String,default:""},headers:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},showFileList:{type:Boolean,default:!0},fileListMaxHeight:{type:String,default:"300px"},imageCompressionOptions:{type:Object,default:()=>({enabled:!1,maxWidth:1920,maxHeight:1080,quality:.8,mimeType:"image/jpeg"})},chunkedUpload:{type:Boolean,default:!1},chunkSize:{type:Number,default:2*1024*1024},previewBeforeUpload:{type:Boolean,default:!1},modelValue:{type:Array,default:()=>[]},checkUploadUrl:{type:Boolean,default:!0},formDataName:{type:String,default:"upload_files[]"},fetchRemoteFileInfo:{type:Function,default:null}},emits:["update:modelValue","select","beforeUpload","progress","success","error","remove","exceed","exceedSize"],setup(_,{expose:xe,emit:Fe}){const s=_,d=Fe,x=new Map,ne=()=>{const e=Date.now(),r=60*60*1e3;for(const[a,t]of x.entries())e-t.timestamp>r&&x.delete(a)};ne();const M=(e,r)=>{const a=new FormData;a.append(s.formDataName,e),r!=null&&r.onProgress&&(setTimeout(()=>r.onProgress(50),500),setTimeout(()=>r.onProgress(100),1e3));const t=r!=null&&r.signal?new AbortController:void 0;r!=null&&r.signal&&t&&r.signal.addEventListener("abort",()=>{t.abort()});const g=s.uploadApiFunction;return g(a).then(n=>n)};M.supportsChunks=!1;const Z=A(null),L=A(!1),O=A([]),o=A([]),z=A(!1),E=A({}),R=A(0),B=A([]),J=P(()=>s.mode==="avatar"),ie=P(()=>s.mode==="gallery"),Ue=P(()=>s.accept.split(",").map(e=>e.trim())),I=P(()=>o.value.length>0),W=P(()=>I.value&&!z.value&&!s.disabled),ue=P(()=>({maxHeight:s.fileListMaxHeight,overflowY:"auto"})),X=["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","image/bmp"],_e=P(()=>({images:o.value.filter(r=>X.includes(r.file.type)),documents:o.value.filter(r=>!X.includes(r.file.type))})),pe=()=>Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15),ee=()=>{s.disabled||Z.value&&Z.value.click()},$e=e=>{if(s.disabled)return;let r=null;e instanceof Event&&e.target?r=e.target.files:e instanceof FileList&&(r=e),!(!r||r.length===0)&&de(r)},re=e=>{s.disabled||(e.preventDefault(),L.value=!1,e.dataTransfer&&de(e.dataTransfer.files))},ae=e=>{s.disabled||(e.preventDefault(),L.value=!0)},te=e=>{s.disabled||(e.preventDefault(),L.value=!1)},de=async e=>{const r=[];if(ne(),s.maxFiles>0&&e.length+o.value.length>s.maxFiles){d("exceed",{files:e,maxFiles:s.maxFiles,currentCount:o.value.length}),window.$toast.add({severity:"error",summary:"Error",detail:`You have exceeded the maximum number of files (${s.maxFiles})`,life:3e3});return}J.value&&H();for(let a=0;a<e.length;a++){const t=e[a];if(s.maxSize>0&&t.size>s.maxSize){d("exceedSize",{file:t,maxSize:s.maxSize}),window.$toast.add({severity:"error",summary:"Error",detail:"You have exceeded the maximum file size",life:3e3});continue}s.multiple||O.value.length===0?r.push(t):(r.push(t),H())}r.length!==0&&(await Ce(r),s.autoUpload&&(s.uploadUrl||s.uploadFunction||typeof M=="function")&&j())},ce=e=>{var g;if(!s.accept)return!0;const r=Ue.value,a=e.type,t=(g=e.name.split(".").pop())==null?void 0:g.toLowerCase();return!!r.some(n=>!!(n===a||n.endsWith("/*")&&a.startsWith(n.replace("/*","/"))||n.startsWith(".")&&`.${t}`===n))},Ce=async e=>{const r=[];for(const t of e){const g=pe();let n="",i="pending",h;(s.accept?ce(t):!0)||(i="error",h=`Unsupported file format: must be a file of type: ${s.accept}`),X.includes(t.type)&&(n=URL.createObjectURL(t));const k=se(t),v=x.get(k),m={file:t,id:g,progress:0,status:i,preview:n,error:h,warning:v?`Note: This file has failed ${v.attempts} upload attempt(s)`:void 0};o.value.push(m),i==="pending"&&r.push(t),v&&v.attempts>1&&i==="pending"&&window.$toast.add({severity:"warn",summary:"Previous Upload Failed",detail:`File "${t.name}" failed to upload previously, retrying`,life:3e3})}const a=e.filter(t=>s.accept?ce(t):!0);O.value=[...O.value,...a],r.length>0&&d("select",r),D()},D=()=>{if(s.multiple){const e=o.value.map(r=>{if(r.status==="success"){let a={url:"",name:r.file.name,size:r.file.size,type:r.file.type,status:"success"};if(r.response){if(typeof r.response=="string")a.url=r.response;else if(typeof r.response=="object")if("url"in r.response)a.url=r.response.url;else if(Array.isArray(r.response.file_path)&&r.response.file_path.length>0)a.url=r.response.file_path[0];else{const t=["url","path","filePath","file_url","fileUrl","src","source"];for(const g of t)if(g in r.response&&typeof r.response[g]=="string"){a.url=r.response[g];break}a.url||(a={...a,...r.response})}}return!a.url&&r.preview&&(a.url=r.preview),a}else{if(r.status==="error")return{name:r.file.name,size:r.file.size,type:r.file.type,status:"error",error:r.error,preview:r.preview||null,url:""};if(r.status==="pending"||r.status==="uploading")return{name:r.file.name,size:r.file.size,type:r.file.type,status:r.status,preview:r.preview||null,url:""}}return null}).filter(r=>r!==null);d("update:modelValue",e)}else if(o.value.length>0){const e=o.value[o.value.length-1];if(e.status==="success"){let r={url:"",name:e.file.name,size:e.file.size,type:e.file.type,status:"success"};if(e.response){if(typeof e.response=="string")r.url=e.response;else if(typeof e.response=="object")if("url"in e.response)r.url=e.response.url;else if(Array.isArray(e.response.file_path)&&e.response.file_path.length>0)r.url=e.response.file_path[0];else{const a=["url","path","filePath","file_url","fileUrl","src","source"];for(const t of a)if(t in e.response&&typeof e.response[t]=="string"){r.url=e.response[t];break}r.url||(r={...r,...e.response})}}!r.url&&e.preview&&(r.url=e.preview),d("update:modelValue",[r])}else d("update:modelValue",[{name:e.file.name,size:e.file.size,type:e.file.type,status:e.status,error:e.error,preview:e.preview||null,url:""}])}else d("update:modelValue",[])},H=()=>{o.value.forEach(e=>{e.preview&&URL.revokeObjectURL(e.preview)}),O.value=[],o.value=[],D()},fe=e=>{const r=o.value.findIndex(t=>t.id===e);if(r===-1)return;const a=o.value[r];a.status==="uploading"&&e in E.value&&(E.value[e].abort(),delete E.value[e]),a.preview&&URL.revokeObjectURL(a.preview),o.value.splice(r,1),O.value=O.value.filter(t=>t!==a.file),d("remove",a.file),D()},ze=e=>new Promise((r,a)=>{if(!s.imageCompressionOptions.enabled||!e.type.startsWith("image/")){r(e);return}const t=new FileReader;t.readAsDataURL(e),t.onload=g=>{var i;const n=new Image;n.src=(i=g.target)==null?void 0:i.result,n.onload=()=>{const{maxWidth:h,maxHeight:c,quality:k,mimeType:v}=s.imageCompressionOptions,m=document.createElement("canvas");let y=n.width,l=n.height;y>h&&(l=Math.round(l*h/y),y=h),l>c&&(y=Math.round(y*c/l),l=c),m.width=y,m.height=l;const F=m.getContext("2d");if(!F){a(new Error("Could not get canvas context"));return}F.drawImage(n,0,0,y,l),m.toBlob(U=>{if(!U){a(new Error("Image compression failed"));return}const Y=new File([U],e.name,{type:v||e.type,lastModified:Date.now()});r(Y)},v||e.type,k)},n.onerror=()=>{a(new Error("Failed to load image"))}},t.onerror=()=>{a(new Error("Failed to read file"))}}),ve=e=>e.startsWith("image/")?"pi pi-image":e.startsWith("video/")?"pi pi-video":e.startsWith("audio/")?"pi pi-volume-up":e.includes("pdf")?"pi pi-file-pdf":e.includes("word")||e.includes("document")?"pi pi-file-word":e.includes("excel")||e.includes("sheet")?"pi pi-file-excel":e.includes("powerpoint")||e.includes("presentation")?"pi pi-file-powerpoint":e.includes("zip")||e.includes("compressed")?"pi pi-folder":"pi pi-file",ge=e=>e===0?"0 MB":`${(e/(1024*1024)).toFixed(2)} MB`,K=e=>(Se(e.id),new Promise((r,a)=>{const{file:t,id:g}=e,n=se(t);if(s.uploadFunction||M){const c=s.uploadFunction||M;e.status="uploading",e.progress=0;const k=new AbortController;E.value[g]={xhr:null,abort:()=>k.abort()};const v=m=>{e.progress=m,Q(),d("progress",{file:t,progress:m})};c(t,{headers:s.headers,onProgress:v,signal:k.signal}).then(m=>{const{code:y,data:l,message:F}=m;if(e.status="success",y===0)Array.isArray(l==null?void 0:l.file_path)&&(l==null?void 0:l.file_path.length)>0?e.response=l.file_path[0]:e.response=m,x.delete(n),D(),d("success",{file:t,response:m}),r();else{e.status="error",e.error=F||"Invalid server response format",d("error",{file:t,error:e.error});const U=x.get(n)||{attempts:0};x.set(n,{timestamp:Date.now(),attempts:U.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:F||"Invalid server response format",life:3e3}),e.status="error",e.error=F||"Invalid server response format",d("error",{file:t,error:e.error}),D(),a(new Error(F||"Invalid server response format"))}}).catch(m=>{if(m.name==="AbortError")return;e.status="error",e.error=m.message||"Upload failed",d("error",{file:t,error:e.error});const y=x.get(n)||{attempts:0};x.set(n,{timestamp:Date.now(),attempts:y.attempts+1}),a(m)});return}const i=new XMLHttpRequest,h=new FormData;h.append("file",t),i.open("POST",s.uploadUrl),Object.entries(s.headers).forEach(([c,k])=>{i.setRequestHeader(c,k.toString())}),i.upload.addEventListener("progress",c=>{if(c.lengthComputable){const k=Math.round(c.loaded*100/c.total);e.progress=k,Q(),d("progress",{file:t,progress:k})}}),i.onload=()=>{if(i.status>=200&&i.status<300)try{const c=JSON.parse(i.responseText);e.status="success",e.response=c,x.delete(n),D(),d("success",{file:t,response:c}),r()}catch{e.status="error",e.error="Invalid server response format",d("error",{file:t,error:e.error});const c=x.get(n)||{attempts:0};x.set(n,{timestamp:Date.now(),attempts:c.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Invalid server response format",life:3e3}),a(new Error("Invalid server response format"))}else{e.status="error",e.error=`${i.status}: ${i.statusText}`,d("error",{file:t,error:e.error});const c=x.get(n)||{attempts:0};x.set(n,{timestamp:Date.now(),attempts:c.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:`${i.status}: ${i.statusText}`,life:3e3}),a(new Error(`Upload failed: ${i.status} ${i.statusText}`))}},i.onerror=()=>{e.status="error",e.error="Network error",console.log("Network error",e),d("error",{file:t,error:"Network error"});const c=x.get(n)||{attempts:0};x.set(n,{timestamp:Date.now(),attempts:c.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Network error",life:3e3}),a(new Error("Network error"))},i.onabort=()=>{e.status="error",e.error="Upload cancelled",e.progress=0,d("error",{file:t,error:"Upload cancelled"}),window.$toast&&window.$toast.add({severity:"info",summary:"Info",detail:"Upload cancelled",life:3e3}),a(new Error("Upload cancelled"))},E.value[g]={xhr:i,abort:()=>i.abort()},i.send(h)})),se=e=>`${e.name}_${e.size}_${e.lastModified}`,Ee=e=>new Promise((r,a)=>{const{file:t,id:g}=e,n=s.chunkSize,i=Math.ceil(t.size/n);let h=0,c=0;if((s.uploadFunction||M)&&t.size>n){const v=s.uploadFunction||M;if(!v.supportsChunks){K(e).then(r).catch(a);return}e.status="uploading",e.progress=0;const m=new AbortController;E.value[g]={xhr:null,abort:()=>m.abort()};const y=l=>{e.progress=l,Q(),d("progress",{file:t,progress:l})};v(t,{headers:s.headers,onProgress:y,signal:m.signal,chunked:!0,chunkSize:n}).then(l=>{e.status="success",e.response=l,D(),d("success",{file:t,response:l}),r()}).catch(l=>{l.name!=="AbortError"&&(e.status="error",e.error=l.message||"Upload failed",d("error",{file:t,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Upload Error",detail:l.message||"Chunked upload failed",life:3e3}),a(l))});return}const k=async()=>{if(h>=i){e.status="success",D(),r();return}const v=h*n,m=Math.min(t.size,v+n),y=t.slice(v,m),l=new XMLHttpRequest,F=new FormData;F.append("file",y,t.name),F.append("chunk",h.toString()),F.append("chunks",i.toString()),F.append("fileName",t.name),l.open("POST",s.uploadUrl),Object.entries(s.headers).forEach(([U,Y])=>{l.setRequestHeader(U,Y.toString())}),l.upload.addEventListener("progress",U=>{if(U.lengthComputable){const Y=Math.round(U.loaded*100/U.total);c=Math.round((h+Y/100)/i*100),e.progress=c,Q(),d("progress",{file:t,progress:c})}}),l.onload=()=>{if(l.status>=200&&l.status<300)try{if(h===i-1){const U=JSON.parse(l.responseText);e.response=U,d("success",{file:t,response:U})}h++,k()}catch{e.status="error",e.error="Invalid server response format",d("error",{file:t,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Invalid server response format",life:3e3}),a(new Error("Invalid server response format"))}else e.status="error",e.error=`${l.status}: ${l.statusText}`,d("error",{file:t,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:`Chunk upload failed: ${l.status} ${l.statusText}`,life:3e3}),a(new Error(`Upload failed: ${l.status} ${l.statusText}`))},l.onerror=()=>{e.status="error",e.error="Network error",d("error",{file:t,error:"Network error"}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Network error during chunk upload",life:3e3}),a(new Error("Network error"))},l.onabort=()=>{e.status="error",e.error="Upload cancelled",e.progress=0,d("error",{file:t,error:"Upload cancelled"}),window.$toast&&window.$toast.add({severity:"info",summary:"Info",detail:"Chunked upload cancelled",life:3e3}),a(new Error("Upload cancelled"))},E.value[g]={xhr:l,abort:()=>l.abort()},l.send(F)};k().catch(a)}),Q=()=>{if(o.value.length===0){R.value=0;return}const e=o.value.reduce((r,a)=>r+a.progress,0);R.value=Math.round(e/o.value.length)},De=()=>{if(s.checkUploadUrl&&!s.uploadFunction&&!s.uploadUrl&&!M)return B.value.push("No upload function or URL provided"),!1;if(o.value.length===0)return B.value.push("No files selected"),!1;const e={files:O.value,cancel:!1};return d("beforeUpload",e),!e.cancel},j=async()=>{if(B.value=[],!De()){B.value.length>0&&window.$toast&&window.$toast.add({severity:"error",summary:"Validation Error",detail:B.value[0],life:3e3});return}z.value=!0;try{const e=o.value.filter(r=>r.status==="pending"&&r.error===void 0);for(const r of e){r.status="uploading",r.progress=0;try{let a=r.file;s.imageCompressionOptions.enabled&&r.file.type.startsWith("image/")&&(a=await ze(r.file)),s.chunkedUpload&&a.size>s.chunkSize?await Ee(r):await K(r)}catch(a){console.error("File upload failed:",r.file.name,a)}}}catch(e){window.$toast&&window.$toast.add({severity:"error",summary:"Upload Error",detail:e instanceof Error?e.message:"An error occurred during upload",life:3e3})}finally{z.value=!1}},me=()=>{Object.values(E.value).forEach(e=>e.abort()),E.value={},o.value.forEach(e=>{e.status==="uploading"&&(e.status="error",e.error="Upload cancelled",e.progress=0)}),z.value=!1,R.value=0};xe({upload:j,abort:me,clearFiles:H,submit:()=>{W.value&&j()}}),Ve(()=>{Object.values(E.value).forEach(e=>e.abort()),o.value.forEach(e=>{e.preview&&URL.revokeObjectURL(e.preview)})});const he=()=>{if(s.modelValue){if(Array.isArray(s.modelValue)&&s.modelValue.length>0)s.modelValue.forEach(e=>{if(typeof e=="string")q(e);else if(typeof e=="object"&&e!==null){const r=e;"status"in r&&r.status==="error"?q(r.url||"",r.name,r.size,"error",r.error):"url"in r&&q(r.url,r.name,r.size)}});else if(typeof s.modelValue=="string"&&s.modelValue)q(s.modelValue);else if(typeof s.modelValue=="object"&&s.modelValue!==null&&"url"in s.modelValue){const e=s.modelValue;q(e.url,e.name,e.size)}}},q=async(e,r,a,t,g)=>{let n=r||we(e),i=Ae(e);const h=pe();if(s.fetchRemoteFileInfo)try{const v=await s.fetchRemoteFileInfo(e);v.type&&(i=v.type),v.name&&(n=v.name)}catch(v){console.warn("Failed to fetch remote file info:",v)}let c="";if(X.includes(i)&&(c=e),i.startsWith("image/")&&!a){const v=new Image;v.onload=()=>{const m=Math.round(v.width*v.height*.25),y=o.value.findIndex(l=>l.id===h);if(y!==-1){const l=new File([new ArrayBuffer(m)],n,{type:i});o.value[y].file=l,o.value=[...o.value]}},v.src=e}const k={file:new File([],n,{type:i}),id:h,progress:100,status:t||"success",response:{url:e},preview:c||""};t==="error"&&g&&(k.error=g),o.value.push(k)},we=e=>{const r=e.split("/");return r[r.length-1].split("?")[0]},Ae=e=>{var g;const a=((g=we(e).split(".").pop())==null?void 0:g.toLowerCase())||"";return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",svg:"image/svg+xml",webp:"image/webp",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",zip:"application/zip",mp3:"audio/mpeg",mp4:"video/mp4"}[a]||"application/octet-stream"};Me(()=>{he()}),Le(()=>s.modelValue,()=>{o.value.length===0&&he()},{deep:!0});const Se=e=>{const r=o.value.find(a=>a.id===e);if(r){const a=se(r.file);x.delete(a),r.warning&&(delete r.warning,o.value=[...o.value])}};return(e,r)=>(u(),f("div",{class:S(["p-fileupload p-component",{"p-fileupload-advanced":!J.value,"p-fileupload-avatar":J.value,"p-fileupload-gallery":ie.value,"p-disabled":_.disabled}])},[B.value.length>0?(u(),f("div",Pe,[(u(!0),f(le,null,oe(B.value,(a,t)=>(u(),N(w(Re),{key:t,severity:"error",closable:!0},{default:Oe(()=>[be(V(a),1)]),_:2},1024))),128))])):b("",!0),J.value?(u(),f("div",Te,[p("div",{class:S(["p-fileupload-avatar-dropzone",{"p-fileupload-dragover":L.value}]),onClick:ee,onDragover:ae,onDragleave:te,onDrop:re},[o.value.length===0||!o.value[0].preview?(u(),f("div",We,[r[1]||(r[1]=p("i",{class:"pi pi-user p-fileupload-avatar-icon"},null,-1)),T(e.$slots,"upload-text",{},()=>[r[0]||(r[0]=p("div",{class:"p-fileupload-text"}," Drop or click to upload avatar ",-1))],!0)])):(u(),f("div",He,[p("img",{src:o.value[0].preview,alt:"Avatar Preview"},null,8,je),r[2]||(r[2]=p("div",{class:"p-fileupload-avatar-overlay"},[p("div",{class:"p-fileupload-avatar-overlay-content"},[p("i",{class:"pi pi-camera"}),p("span",null,"Change avatar")])],-1))]))],34),o.value.length>0&&!_.autoUpload&&!z.value&&o.value[0].status==="pending"?(u(),f("div",qe,[$(w(C),{icon:"pi pi-upload",class:"p-button-rounded p-button-success",disabled:!W.value,onClick:j},null,8,["disabled"]),$(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger",onClick:H})])):b("",!0),z.value&&o.value.filter(a=>a.status==="uploading").length>1?(u(),N(w(G),{key:1,value:R.value},null,8,["value"])):b("",!0)])):ie.value?(u(),f("div",Ye,[p("div",{class:S(["p-fileupload-gallery-dropzone",{"p-fileupload-dragover":L.value}]),onClick:ee,onDragover:ae,onDragleave:te,onDrop:re},[o.value.length===0?(u(),f("div",Ge,[T(e.$slots,"upload-icon",{},()=>[r[3]||(r[3]=p("i",{class:"pi pi-images p-fileupload-gallery-icon"},null,-1))],!0),T(e.$slots,"upload-text",{},()=>[r[4]||(r[4]=p("span",null,"Drop or click to upload images",-1))],!0)])):b("",!0)],34),_.showFileList&&o.value.length>0?(u(),f("div",{key:0,class:"p-fileupload-gallery-files",style:ye(ue.value)},[p("div",Je,[(u(!0),f(le,null,oe(_e.value.images,a=>(u(),f("div",{key:a.id,class:S(["p-fileupload-gallery-item",{"p-fileupload-gallery-item-uploading":a.status==="uploading","p-fileupload-gallery-item-success":a.status==="success","p-fileupload-gallery-item-error":a.status==="error"}])},[p("div",Xe,[a.preview?(u(),f("img",{key:0,src:a.preview,alt:a.file.name},null,8,Ke)):(u(),f("i",{key:1,class:S(ve(a.file.type))},null,2))]),p("div",Qe,[p("div",Ze,[p("span",Ie,V(a.file.name),1),p("span",er,V(ge(a.file.size)),1)]),p("div",rr,[a.status==="pending"?(u(),N(w(C),{key:0,icon:"pi pi-upload",class:"p-button-rounded p-button-outlined p-button-sm p-button-success",onClick:()=>{W.value&&K(a)}},null,8,["onClick"])):b("",!0),$(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-outlined p-button-sm p-button-danger",onClick:()=>fe(a.id)},null,8,["onClick"])]),a.status==="uploading"?(u(),N(w(G),{key:0,value:a.progress,class:"p-fileupload-gallery-item-progress"},null,8,["value"])):b("",!0),a.status==="success"?(u(),N(w(ke),{key:1,value:"Success",severity:"success"})):b("",!0),a.status==="error"?(u(),N(w(ke),{key:2,value:"Error",severity:"danger"})):b("",!0)])],2))),128))])],4)):b("",!0),!_.autoUpload&&!z.value&&I.value?(u(),f("div",ar,[$(w(C),{icon:"pi pi-upload",label:"Upload All",disabled:!W.value,onClick:j},null,8,["disabled"]),$(w(C),{icon:"pi pi-times",label:"Clear",class:"p-button-outlined p-button-danger",onClick:H})])):b("",!0),z.value&&o.value.filter(a=>a.status==="uploading").length>1?(u(),N(w(G),{key:2,value:R.value},null,8,["value"])):b("",!0)])):(u(),f("div",tr,[p("div",{class:S(["p-fileupload-dropzone",{"p-fileupload-dragover":L.value}]),onClick:ee,onDragover:ae,onDragleave:te,onDrop:re},[T(e.$slots,"upload-area",{},()=>[p("div",sr,[T(e.$slots,"upload-icon",{},()=>[r[5]||(r[5]=p("i",{class:"pi pi-upload p-fileupload-icon"},null,-1))],!0),T(e.$slots,"upload-text",{},()=>[L.value?(u(),f("span",or,"Release to upload")):(u(),f("span",lr,"Drop files here or click to upload"))],!0)])],!0)],34),_.showFileList&&o.value.length>0?(u(),f("div",{key:0,class:"p-fileupload-files",style:ye(ue.value)},[(u(!0),f(le,null,oe(o.value,a=>(u(),f("div",{key:a.id,class:S(["p-fileupload-row",{"p-fileupload-row-uploading":a.status==="uploading","p-fileupload-row-success":a.status==="success","p-fileupload-row-error":a.status==="error","p-fileupload-row-warning":a.warning}])},[p("div",nr,[p("div",ir,[T(e.$slots,"file-item",{file:a.file,preview:a.preview,status:a.status,warning:a.warning},()=>[p("div",ur,[a.preview?(u(),f("img",{key:0,src:a.preview,alt:a.file.name,class:"p-fileupload-file-preview"},null,8,pr)):(u(),f("i",{key:1,class:S(["p-fileupload-file-icon",[ve(a.file.type)]])},null,2))]),p("div",dr,[p("div",cr,V(a.file.name),1),p("div",fr,V(ge(a.file.size)),1),a.status==="error"?(u(),f("div",vr,V(a.error),1)):b("",!0),a.warning?(u(),f("div",gr,[r[6]||(r[6]=p("i",{class:"pi pi-exclamation-triangle",style:{"margin-right":"4px"}},null,-1)),be(" "+V(a.warning),1)])):b("",!0)])],!0)]),p("div",mr,[a.status==="pending"?(u(),N(w(C),{key:0,icon:"pi pi-upload",class:"p-button-outlined p-button-rounded p-button-success",onClick:()=>{W.value&&K(a)}},null,8,["onClick"])):b("",!0),$(w(C),{icon:"pi pi-times",class:"p-button-outlined p-button-rounded p-button-danger",onClick:()=>fe(a.id)},null,8,["onClick"])])]),a.status==="uploading"?(u(),f("div",hr,[$(w(G),{value:a.progress},null,8,["value"])])):b("",!0)],2))),128))],4)):b("",!0),!_.autoUpload&&!z.value&&I.value?(u(),f("div",wr,[$(w(C),{icon:"pi pi-upload",label:"Upload All",disabled:!W.value,onClick:j},null,8,["disabled"]),$(w(C),{icon:"pi pi-ban",label:"Clear",class:"p-button-outlined p-button-danger",onClick:H})])):b("",!0),z.value&&o.value.filter(a=>a.status==="uploading").length>1?(u(),f("div",yr,[p("div",br,V(R.value)+"% ",1),$(w(G),{value:R.value},null,8,["value"]),$(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger p-button-outlined p-fileupload-cancel",onClick:me})])):b("",!0)])),p("input",{ref_key:"fileInput",ref:Z,type:"file",class:"p-hidden",accept:_.accept,multiple:_.multiple,disabled:_.disabled,onChange:$e},null,40,kr)],2))}}),Er=Be(xr,[["__scopeId","data-v-c8ea65a6"]]);export{Er as _};
