import{j as k,i as F,bB as ae,w as X,bC as fe,bD as ue,bE as Z,u as de,d as pe,p as me,J as dt,q as ge,c as pt,o as rt,h as Wt,a as he,l as zt,e as ve,b0 as we,g as ye,z as xe,b as At,n as Ht,f as be,t as It,m as Ae,A as Re,aU as Oe}from"./index-Cr_XsrqN.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";const nt=Math.min,J=Math.max,gt=Math.round,mt=Math.floor,W=t=>({x:t,y:t}),Se={left:"right",right:"left",bottom:"top",top:"bottom"},Ee={start:"end",end:"start"};function Ot(t,e,o){return J(t,nt(e,o))}function lt(t,e){return typeof t=="function"?t(e):t}function K(t){return t.split("-")[0]}function ct(t){return t.split("-")[1]}function Jt(t){return t==="x"?"y":"x"}function Tt(t){return t==="y"?"height":"width"}function q(t){return["top","bottom"].includes(K(t))?"y":"x"}function Lt(t){return Jt(q(t))}function Te(t,e,o){o===void 0&&(o=!1);const n=ct(t),i=Lt(t),s=Tt(i);let r=i==="x"?n===(o?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(r=ht(r)),[r,ht(r)]}function Le(t){const e=ht(t);return[Ct(t),e,Ct(e)]}function Ct(t){return t.replace(/start|end/g,e=>Ee[e])}function Pe(t,e,o){const n=["left","right"],i=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return o?e?i:n:e?n:i;case"left":case"right":return e?s:r;default:return[]}}function ke(t,e,o,n){const i=ct(t);let s=Pe(K(t),o==="start",n);return i&&(s=s.map(r=>r+"-"+i),e&&(s=s.concat(s.map(Ct)))),s}function ht(t){return t.replace(/left|right|bottom|top/g,e=>Se[e])}function De(t){return{top:0,right:0,bottom:0,left:0,...t}}function Kt(t){return typeof t!="number"?De(t):{top:t,right:t,bottom:t,left:t}}function vt(t){const{x:e,y:o,width:n,height:i}=t;return{width:n,height:i,top:o,left:e,right:e+n,bottom:o+i,x:e,y:o}}function jt(t,e,o){let{reference:n,floating:i}=t;const s=q(e),r=Lt(e),l=Tt(r),c=K(e),a=s==="y",d=n.x+n.width/2-i.width/2,u=n.y+n.height/2-i.height/2,p=n[l]/2-i[l]/2;let f;switch(c){case"top":f={x:d,y:n.y-i.height};break;case"bottom":f={x:d,y:n.y+n.height};break;case"right":f={x:n.x+n.width,y:u};break;case"left":f={x:n.x-i.width,y:u};break;default:f={x:n.x,y:n.y}}switch(ct(e)){case"start":f[r]-=p*(o&&a?-1:1);break;case"end":f[r]+=p*(o&&a?-1:1);break}return f}const Be=async(t,e,o)=>{const{placement:n="bottom",strategy:i="absolute",middleware:s=[],platform:r}=o,l=s.filter(Boolean),c=await(r.isRTL==null?void 0:r.isRTL(e));let a=await r.getElementRects({reference:t,floating:e,strategy:i}),{x:d,y:u}=jt(a,n,c),p=n,f={},m=0;for(let g=0;g<l.length;g++){const{name:h,fn:v}=l[g],{x:w,y:x,data:A,reset:b}=await v({x:d,y:u,initialPlacement:n,placement:p,strategy:i,middlewareData:f,rects:a,platform:r,elements:{reference:t,floating:e}});d=w??d,u=x??u,f={...f,[h]:{...f[h],...A}},b&&m<=50&&(m++,typeof b=="object"&&(b.placement&&(p=b.placement),b.rects&&(a=b.rects===!0?await r.getElementRects({reference:t,floating:e,strategy:i}):b.rects),{x:d,y:u}=jt(a,p,c)),g=-1)}return{x:d,y:u,placement:p,strategy:i,middlewareData:f}};async function Gt(t,e){var o;e===void 0&&(e={});const{x:n,y:i,platform:s,rects:r,elements:l,strategy:c}=t,{boundary:a="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:p=!1,padding:f=0}=lt(e,t),m=Kt(f),h=l[p?u==="floating"?"reference":"floating":u],v=vt(await s.getClippingRect({element:(o=await(s.isElement==null?void 0:s.isElement(h)))==null||o?h:h.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:a,rootBoundary:d,strategy:c})),w=u==="floating"?{x:n,y:i,width:r.floating.width,height:r.floating.height}:r.reference,x=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),A=await(s.isElement==null?void 0:s.isElement(x))?await(s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},b=vt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:x,strategy:c}):w);return{top:(v.top-b.top+m.top)/A.y,bottom:(b.bottom-v.bottom+m.bottom)/A.y,left:(v.left-b.left+m.left)/A.x,right:(b.right-v.right+m.right)/A.x}}const Me=t=>({name:"arrow",options:t,async fn(e){const{x:o,y:n,placement:i,rects:s,platform:r,elements:l,middlewareData:c}=e,{element:a,padding:d=0}=lt(t,e)||{};if(a==null)return{};const u=Kt(d),p={x:o,y:n},f=Lt(i),m=Tt(f),g=await r.getDimensions(a),h=f==="y",v=h?"top":"left",w=h?"bottom":"right",x=h?"clientHeight":"clientWidth",A=s.reference[m]+s.reference[f]-p[f]-s.floating[m],b=p[f]-s.reference[f],E=await(r.getOffsetParent==null?void 0:r.getOffsetParent(a));let y=E?E[x]:0;(!y||!await(r.isElement==null?void 0:r.isElement(E)))&&(y=l.floating[x]||s.floating[m]);const R=A/2-b/2,C=y/2-g[m]/2-1,B=nt(u[v],C),Y=nt(u[w],C),M=B,I=y-g[m]-Y,S=y/2-g[m]/2+R,j=Ot(M,S,I),N=!c.arrow&&ct(i)!=null&&S!==j&&s.reference[m]/2-(S<M?B:Y)-g[m]/2<0,L=N?S<M?S-M:S-I:0;return{[f]:p[f]+L,data:{[f]:j,centerOffset:S-j-L,...N&&{alignmentOffset:L}},reset:N}}}),Fe=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var o,n;const{placement:i,middlewareData:s,rects:r,initialPlacement:l,platform:c,elements:a}=e,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:p,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:g=!0,...h}=lt(t,e);if((o=s.arrow)!=null&&o.alignmentOffset)return{};const v=K(i),w=q(l),x=K(l)===l,A=await(c.isRTL==null?void 0:c.isRTL(a.floating)),b=p||(x||!g?[ht(l)]:Le(l)),E=m!=="none";!p&&E&&b.push(...ke(l,g,m,A));const y=[l,...b],R=await Gt(e,h),C=[];let B=((n=s.flip)==null?void 0:n.overflows)||[];if(d&&C.push(R[v]),u){const S=Te(i,r,A);C.push(R[S[0]],R[S[1]])}if(B=[...B,{placement:i,overflows:C}],!C.every(S=>S<=0)){var Y,M;const S=(((Y=s.flip)==null?void 0:Y.index)||0)+1,j=y[S];if(j&&(!(u==="alignment"?w!==q(j):!1)||B.every(P=>P.overflows[0]>0&&q(P.placement)===w)))return{data:{index:S,overflows:B},reset:{placement:j}};let N=(M=B.filter(L=>L.overflows[0]<=0).sort((L,P)=>L.overflows[1]-P.overflows[1])[0])==null?void 0:M.placement;if(!N)switch(f){case"bestFit":{var I;const L=(I=B.filter(P=>{if(E){const $=q(P.placement);return $===w||$==="y"}return!0}).map(P=>[P.placement,P.overflows.filter($=>$>0).reduce(($,bt)=>$+bt,0)]).sort((P,$)=>P[1]-$[1])[0])==null?void 0:I[0];L&&(N=L);break}case"initialPlacement":N=l;break}if(i!==N)return{reset:{placement:N}}}return{}}}};async function Ne(t,e){const{placement:o,platform:n,elements:i}=t,s=await(n.isRTL==null?void 0:n.isRTL(i.floating)),r=K(o),l=ct(o),c=q(o)==="y",a=["left","top"].includes(r)?-1:1,d=s&&c?-1:1,u=lt(e,t);let{mainAxis:p,crossAxis:f,alignmentAxis:m}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return l&&typeof m=="number"&&(f=l==="end"?m*-1:m),c?{x:f*d,y:p*a}:{x:p*a,y:f*d}}const Ve=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var o,n;const{x:i,y:s,placement:r,middlewareData:l}=e,c=await Ne(e,t);return r===((o=l.offset)==null?void 0:o.placement)&&(n=l.arrow)!=null&&n.alignmentOffset?{}:{x:i+c.x,y:s+c.y,data:{...c,placement:r}}}}},_e=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:o,y:n,placement:i}=e,{mainAxis:s=!0,crossAxis:r=!1,limiter:l={fn:h=>{let{x:v,y:w}=h;return{x:v,y:w}}},...c}=lt(t,e),a={x:o,y:n},d=await Gt(e,c),u=q(K(i)),p=Jt(u);let f=a[p],m=a[u];if(s){const h=p==="y"?"top":"left",v=p==="y"?"bottom":"right",w=f+d[h],x=f-d[v];f=Ot(w,f,x)}if(r){const h=u==="y"?"top":"left",v=u==="y"?"bottom":"right",w=m+d[h],x=m-d[v];m=Ot(w,m,x)}const g=l.fn({...e,[p]:f,[u]:m});return{...g,data:{x:g.x-o,y:g.y-n,enabled:{[p]:s,[u]:r}}}}}};function wt(){return typeof window<"u"}function Q(t){return Pt(t)?(t.nodeName||"").toLowerCase():"#document"}function D(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function H(t){var e;return(e=(Pt(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Pt(t){return wt()?t instanceof Node||t instanceof D(t).Node:!1}function V(t){return wt()?t instanceof Element||t instanceof D(t).Element:!1}function z(t){return wt()?t instanceof HTMLElement||t instanceof D(t).HTMLElement:!1}function Yt(t){return!wt()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof D(t).ShadowRoot}function at(t){const{overflow:e,overflowX:o,overflowY:n,display:i}=_(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+o)&&!["inline","contents"].includes(i)}function $e(t){return["table","td","th"].includes(Q(t))}function yt(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function kt(t){const e=Dt(),o=V(t)?_(t):t;return["transform","translate","scale","rotate","perspective"].some(n=>o[n]?o[n]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!e&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!e&&(o.filter?o.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(o.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(o.contain||"").includes(n))}function We(t){let e=U(t);for(;z(e)&&!ot(e);){if(kt(e))return e;if(yt(e))return null;e=U(e)}return null}function Dt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ot(t){return["html","body","#document"].includes(Q(t))}function _(t){return D(t).getComputedStyle(t)}function xt(t){return V(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function U(t){if(Q(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Yt(t)&&t.host||H(t);return Yt(e)?e.host:e}function Qt(t){const e=U(t);return ot(e)?t.ownerDocument?t.ownerDocument.body:t.body:z(e)&&at(e)?e:Qt(e)}function st(t,e,o){var n;e===void 0&&(e=[]),o===void 0&&(o=!0);const i=Qt(t),s=i===((n=t.ownerDocument)==null?void 0:n.body),r=D(i);if(s){const l=St(r);return e.concat(r,r.visualViewport||[],at(i)?i:[],l&&o?st(l):[])}return e.concat(i,st(i,[],o))}function St(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Zt(t){const e=_(t);let o=parseFloat(e.width)||0,n=parseFloat(e.height)||0;const i=z(t),s=i?t.offsetWidth:o,r=i?t.offsetHeight:n,l=gt(o)!==s||gt(n)!==r;return l&&(o=s,n=r),{width:o,height:n,$:l}}function Bt(t){return V(t)?t:t.contextElement}function et(t){const e=Bt(t);if(!z(e))return W(1);const o=e.getBoundingClientRect(),{width:n,height:i,$:s}=Zt(e);let r=(s?gt(o.width):o.width)/n,l=(s?gt(o.height):o.height)/i;return(!r||!Number.isFinite(r))&&(r=1),(!l||!Number.isFinite(l))&&(l=1),{x:r,y:l}}const ze=W(0);function te(t){const e=D(t);return!Dt()||!e.visualViewport?ze:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function He(t,e,o){return e===void 0&&(e=!1),!o||e&&o!==D(t)?!1:e}function G(t,e,o,n){e===void 0&&(e=!1),o===void 0&&(o=!1);const i=t.getBoundingClientRect(),s=Bt(t);let r=W(1);e&&(n?V(n)&&(r=et(n)):r=et(t));const l=He(s,o,n)?te(s):W(0);let c=(i.left+l.x)/r.x,a=(i.top+l.y)/r.y,d=i.width/r.x,u=i.height/r.y;if(s){const p=D(s),f=n&&V(n)?D(n):n;let m=p,g=St(m);for(;g&&n&&f!==m;){const h=et(g),v=g.getBoundingClientRect(),w=_(g),x=v.left+(g.clientLeft+parseFloat(w.paddingLeft))*h.x,A=v.top+(g.clientTop+parseFloat(w.paddingTop))*h.y;c*=h.x,a*=h.y,d*=h.x,u*=h.y,c+=x,a+=A,m=D(g),g=St(m)}}return vt({width:d,height:u,x:c,y:a})}function Mt(t,e){const o=xt(t).scrollLeft;return e?e.left+o:G(H(t)).left+o}function ee(t,e,o){o===void 0&&(o=!1);const n=t.getBoundingClientRect(),i=n.left+e.scrollLeft-(o?0:Mt(t,n)),s=n.top+e.scrollTop;return{x:i,y:s}}function Ie(t){let{elements:e,rect:o,offsetParent:n,strategy:i}=t;const s=i==="fixed",r=H(n),l=e?yt(e.floating):!1;if(n===r||l&&s)return o;let c={scrollLeft:0,scrollTop:0},a=W(1);const d=W(0),u=z(n);if((u||!u&&!s)&&((Q(n)!=="body"||at(r))&&(c=xt(n)),z(n))){const f=G(n);a=et(n),d.x=f.x+n.clientLeft,d.y=f.y+n.clientTop}const p=r&&!u&&!s?ee(r,c,!0):W(0);return{width:o.width*a.x,height:o.height*a.y,x:o.x*a.x-c.scrollLeft*a.x+d.x+p.x,y:o.y*a.y-c.scrollTop*a.y+d.y+p.y}}function je(t){return Array.from(t.getClientRects())}function Ye(t){const e=H(t),o=xt(t),n=t.ownerDocument.body,i=J(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),s=J(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight);let r=-o.scrollLeft+Mt(t);const l=-o.scrollTop;return _(n).direction==="rtl"&&(r+=J(e.clientWidth,n.clientWidth)-i),{width:i,height:s,x:r,y:l}}function qe(t,e){const o=D(t),n=H(t),i=o.visualViewport;let s=n.clientWidth,r=n.clientHeight,l=0,c=0;if(i){s=i.width,r=i.height;const a=Dt();(!a||a&&e==="fixed")&&(l=i.offsetLeft,c=i.offsetTop)}return{width:s,height:r,x:l,y:c}}function Ue(t,e){const o=G(t,!0,e==="fixed"),n=o.top+t.clientTop,i=o.left+t.clientLeft,s=z(t)?et(t):W(1),r=t.clientWidth*s.x,l=t.clientHeight*s.y,c=i*s.x,a=n*s.y;return{width:r,height:l,x:c,y:a}}function qt(t,e,o){let n;if(e==="viewport")n=qe(t,o);else if(e==="document")n=Ye(H(t));else if(V(e))n=Ue(e,o);else{const i=te(t);n={x:e.x-i.x,y:e.y-i.y,width:e.width,height:e.height}}return vt(n)}function ne(t,e){const o=U(t);return o===e||!V(o)||ot(o)?!1:_(o).position==="fixed"||ne(o,e)}function Xe(t,e){const o=e.get(t);if(o)return o;let n=st(t,[],!1).filter(l=>V(l)&&Q(l)!=="body"),i=null;const s=_(t).position==="fixed";let r=s?U(t):t;for(;V(r)&&!ot(r);){const l=_(r),c=kt(r);!c&&l.position==="fixed"&&(i=null),(s?!c&&!i:!c&&l.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||at(r)&&!c&&ne(t,r))?n=n.filter(d=>d!==r):i=l,r=U(r)}return e.set(t,n),n}function Je(t){let{element:e,boundary:o,rootBoundary:n,strategy:i}=t;const r=[...o==="clippingAncestors"?yt(e)?[]:Xe(e,this._c):[].concat(o),n],l=r[0],c=r.reduce((a,d)=>{const u=qt(e,d,i);return a.top=J(u.top,a.top),a.right=nt(u.right,a.right),a.bottom=nt(u.bottom,a.bottom),a.left=J(u.left,a.left),a},qt(e,l,i));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Ke(t){const{width:e,height:o}=Zt(t);return{width:e,height:o}}function Ge(t,e,o){const n=z(e),i=H(e),s=o==="fixed",r=G(t,!0,s,e);let l={scrollLeft:0,scrollTop:0};const c=W(0);function a(){c.x=Mt(i)}if(n||!n&&!s)if((Q(e)!=="body"||at(i))&&(l=xt(e)),n){const f=G(e,!0,s,e);c.x=f.x+e.clientLeft,c.y=f.y+e.clientTop}else i&&a();s&&!n&&i&&a();const d=i&&!n&&!s?ee(i,l):W(0),u=r.left+l.scrollLeft-c.x-d.x,p=r.top+l.scrollTop-c.y-d.y;return{x:u,y:p,width:r.width,height:r.height}}function Rt(t){return _(t).position==="static"}function Ut(t,e){if(!z(t)||_(t).position==="fixed")return null;if(e)return e(t);let o=t.offsetParent;return H(t)===o&&(o=o.ownerDocument.body),o}function oe(t,e){const o=D(t);if(yt(t))return o;if(!z(t)){let i=U(t);for(;i&&!ot(i);){if(V(i)&&!Rt(i))return i;i=U(i)}return o}let n=Ut(t,e);for(;n&&$e(n)&&Rt(n);)n=Ut(n,e);return n&&ot(n)&&Rt(n)&&!kt(n)?o:n||We(t)||o}const Qe=async function(t){const e=this.getOffsetParent||oe,o=this.getDimensions,n=await o(t.floating);return{reference:Ge(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function Ze(t){return _(t).direction==="rtl"}const tn={convertOffsetParentRelativeRectToViewportRelativeRect:Ie,getDocumentElement:H,getClippingRect:Je,getOffsetParent:oe,getElementRects:Qe,getClientRects:je,getDimensions:Ke,getScale:et,isElement:V,isRTL:Ze};function ie(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function en(t,e){let o=null,n;const i=H(t);function s(){var l;clearTimeout(n),(l=o)==null||l.disconnect(),o=null}function r(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),s();const a=t.getBoundingClientRect(),{left:d,top:u,width:p,height:f}=a;if(l||e(),!p||!f)return;const m=mt(u),g=mt(i.clientWidth-(d+p)),h=mt(i.clientHeight-(u+f)),v=mt(d),x={rootMargin:-m+"px "+-g+"px "+-h+"px "+-v+"px",threshold:J(0,nt(1,c))||1};let A=!0;function b(E){const y=E[0].intersectionRatio;if(y!==c){if(!A)return r();y?r(!1,y):n=setTimeout(()=>{r(!1,1e-7)},1e3)}y===1&&!ie(a,t.getBoundingClientRect())&&r(),A=!1}try{o=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch{o=new IntersectionObserver(b,x)}o.observe(t)}return r(!0),s}function nn(t,e,o,n){n===void 0&&(n={});const{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:r=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=n,a=Bt(t),d=i||s?[...a?st(a):[],...st(e)]:[];d.forEach(v=>{i&&v.addEventListener("scroll",o,{passive:!0}),s&&v.addEventListener("resize",o)});const u=a&&l?en(a,o):null;let p=-1,f=null;r&&(f=new ResizeObserver(v=>{let[w]=v;w&&w.target===a&&f&&(f.unobserve(e),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var x;(x=f)==null||x.observe(e)})),o()}),a&&!c&&f.observe(a),f.observe(e));let m,g=c?G(t):null;c&&h();function h(){const v=G(t);g&&!ie(g,v)&&o(),g=v,m=requestAnimationFrame(h)}return o(),()=>{var v;d.forEach(w=>{i&&w.removeEventListener("scroll",o),s&&w.removeEventListener("resize",o)}),u==null||u(),(v=f)==null||v.disconnect(),f=null,c&&cancelAnimationFrame(m)}}const on=Ve,rn=_e,sn=Fe,ln=Me,cn=(t,e,o)=>{const n=new Map,i={platform:tn,...o},s={...i.platform,_c:n};return Be(t,e,{...i,platform:s})};function an(t){return t!=null&&typeof t=="object"&&"$el"in t}function Et(t){if(an(t)){const e=t.$el;return Pt(e)&&Q(e)==="#comment"?null:e}return t}function tt(t){return typeof t=="function"?t():de(t)}function fn(t){return{name:"arrow",options:t,fn(e){const o=Et(tt(t.element));return o==null?{}:ln({element:o,padding:t.padding}).fn(e)}}}function re(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function Xt(t,e){const o=re(t);return Math.round(e*o)/o}function un(t,e,o){o===void 0&&(o={});const n=o.whileElementsMounted,i=k(()=>{var y;return(y=tt(o.open))!=null?y:!0}),s=k(()=>tt(o.middleware)),r=k(()=>{var y;return(y=tt(o.placement))!=null?y:"bottom"}),l=k(()=>{var y;return(y=tt(o.strategy))!=null?y:"absolute"}),c=k(()=>{var y;return(y=tt(o.transform))!=null?y:!0}),a=k(()=>Et(t.value)),d=k(()=>Et(e.value)),u=F(0),p=F(0),f=F(l.value),m=F(r.value),g=ae({}),h=F(!1),v=k(()=>{const y={position:f.value,left:"0",top:"0"};if(!d.value)return y;const R=Xt(d.value,u.value),C=Xt(d.value,p.value);return c.value?{...y,transform:"translate("+R+"px, "+C+"px)",...re(d.value)>=1.5&&{willChange:"transform"}}:{position:f.value,left:R+"px",top:C+"px"}});let w;function x(){if(a.value==null||d.value==null)return;const y=i.value;cn(a.value,d.value,{middleware:s.value,placement:r.value,strategy:l.value}).then(R=>{u.value=R.x,p.value=R.y,f.value=R.strategy,m.value=R.placement,g.value=R.middlewareData,h.value=y!==!1})}function A(){typeof w=="function"&&(w(),w=void 0)}function b(){if(A(),n===void 0){x();return}if(a.value!=null&&d.value!=null){w=n(a.value,d.value,x);return}}function E(){i.value||(h.value=!1)}return X([s,r,l,i],x,{flush:"sync"}),X([a,d],b,{flush:"sync"}),X(i,E,{flush:"sync"}),fe()&&ue(A),{x:Z(u),y:Z(p),strategy:Z(f),placement:Z(m),middlewareData:Z(g),isPositioned:Z(h),floatingStyles:v,update:x}}const dn={class:"base-popover-container"},pn=["tabindex"],mn={key:0,class:"base-popover-title"},gn={class:"base-popover-content"},hn=pe({__name:"BasePopover",props:{trigger:{type:String,default:"hover",validator:t=>["click","focus","hover","contextmenu"].includes(t)},title:{type:String,default:""},effect:{type:String,default:"light"},content:{type:String,default:""},width:{type:[String,Number],default:"auto"},placement:{type:String,default:"bottom",validator:t=>["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"].includes(t)},disabled:{type:Boolean,default:!1},visible:{type:Boolean,default:void 0},offset:{type:Number,default:12},transition:{type:String,default:"el-fade-in-linear"},showArrow:{type:Boolean,default:!0},popperOptions:{type:Object,default:()=>({modifiers:[{name:"computeStyles",options:{gpuAcceleration:!1}}]})},popperClass:{type:String,default:""},popperStyle:{type:[String,Object],default:""},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},tabindex:{type:[Number,String],default:0},teleported:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},virtualTriggering:{type:Boolean,default:!1}},emits:["update:visible","show","beforeEnter","afterEnter","hide","beforeLeave","afterLeave"],setup(t,{expose:e,emit:o}){const n=t,i=o,s=F(null),r=F(null),l=F(null),c=F(!1),a=k(()=>typeof n.width=="number"?`${n.width}px`:n.width);X(()=>n.visible,O=>{O!==void 0&&(c.value=O)}),X(c,O=>{i("update:visible",O),i(O?"show":"hide")});const d=F(n.placement),{floatingStyles:u,middlewareData:p,placement:f,isPositioned:m,update:g}=un(s,r,{placement:d,strategy:"absolute",middleware:[on(n.offset),sn(),rn({padding:5}),...n.showArrow?[fn({element:l})]:[]],whileElementsMounted:nn});X(()=>n.placement,O=>{d.value=O,dt(()=>{g&&g()})});const h=k(()=>{if(typeof n.popperStyle=="string")try{return JSON.parse(n.popperStyle)}catch{return{}}return n.popperStyle}),v=k(()=>({...u.value,width:a.value,...h.value})),w=k(()=>{var $t;if(!n.showArrow)return{};if(!(($t=p.value)!=null&&$t.arrow))return{};const{x:O,y:T}=p.value.arrow,le={position:"absolute",width:"8px",height:"8px",backgroundColor:n.effect==="dark"?"var(--tw-gray-800)":"white",zIndex:1},ut=f.value,ce="rotate(45deg)";let Ft,Nt,Vt,_t,it={};return ut.includes("top")?(Vt="-4px",it={borderTop:"none",borderLeft:"none",borderRight:"1px solid var(--tw-gray-200)",borderBottom:"1px solid var(--tw-gray-200)"}):ut.includes("bottom")?(Ft="-4px",it={borderBottom:"none",borderRight:"none",borderLeft:"1px solid var(--tw-gray-200)",borderTop:"1px solid var(--tw-gray-200)"}):ut.includes("left")?(Nt="-4px",it={borderLeft:"none",borderBottom:"none",borderRight:"1px solid var(--tw-gray-200)",borderTop:"1px solid var(--tw-gray-200)"}):ut.includes("right")&&(_t="-4px",it={borderRight:"none",borderTop:"none",borderLeft:"1px solid var(--tw-gray-200)",borderBottom:"1px solid var(--tw-gray-200)"}),{...le,...it,transform:ce,top:T!=null?`${T}px`:Ft,left:O!=null?`${O}px`:_t,right:Nt,bottom:Vt}}),x=k(()=>n.effect==="dark"?"is-dark":"is-light");let A,b,E;const y=()=>{A&&(window.clearTimeout(A),A=void 0),b&&(window.clearTimeout(b),b=void 0),E&&(window.clearTimeout(E),E=void 0)},R=()=>{n.disabled||(y(),n.showAfter===0?(c.value=!0,B()):A=window.setTimeout(()=>{c.value=!0,B()},n.showAfter))},C=()=>{n.disabled||(y(),n.hideAfter===0?c.value=!1:b=window.setTimeout(()=>{c.value=!1},n.hideAfter))},B=()=>{n.autoClose>0&&(E=window.setTimeout(()=>{c.value=!1},n.autoClose))},Y=O=>{const T=O.target;c.value&&!n.virtualTriggering&&s.value&&r.value&&!s.value.contains(T)&&!r.value.contains(T)&&C()};me(()=>{document.addEventListener("click",Y),dt(()=>{n.visible!==void 0&&(c.value=n.visible)})}),ge(()=>{document.removeEventListener("click",Y),y()}),X(m,O=>{O?(i("beforeEnter"),dt(()=>{i("afterEnter")})):(i("beforeLeave"),dt(()=>{i("afterLeave")}))}),e({hide:C});const M=F(!1),I=F(!1),S=()=>{n.trigger==="hover"&&(M.value=!0,R())},j=()=>{n.trigger==="hover"&&(M.value=!1,setTimeout(()=>{!M.value&&!I.value&&C()},50))},N=()=>{n.trigger==="hover"&&(I.value=!0)},L=()=>{n.trigger==="hover"&&(I.value=!1,M.value||C())},P=()=>{n.trigger==="click"&&(c.value?C():R())},$=()=>{n.trigger==="focus"&&R()},bt=()=>{n.trigger==="focus"&&C()},se=O=>{n.trigger==="contextmenu"&&(O.preventDefault(),c.value?C():R())};return(O,T)=>(rt(),pt("div",dn,[Wt("div",{ref_key:"referenceRef",ref:s,class:"base-popover-reference",tabindex:n.disabled?void 0:n.tabindex,onMouseenter:S,onMouseleave:j,onClick:P,onFocus:$,onBlur:bt,onContextmenu:se},[zt(O.$slots,"reference",{isOpen:c.value},void 0,!0)],40,pn),(rt(),he(Oe,{to:"body",disabled:!n.teleported},[ve(we,{name:n.transition,onBeforeEnter:T[0]||(T[0]=ft=>i("beforeEnter")),onAfterEnter:T[1]||(T[1]=ft=>i("afterEnter")),onBeforeLeave:T[2]||(T[2]=ft=>i("beforeLeave")),onAfterLeave:T[3]||(T[3]=ft=>i("afterLeave"))},{default:ye(()=>[c.value||n.persistent?xe((rt(),pt("div",{key:0,ref_key:"floatingRef",ref:r,class:be(["base-popover",[x.value,n.popperClass]]),style:Ht(v.value),role:"tooltip",onMouseenter:N,onMouseleave:L},[n.title?(rt(),pt("div",mn,It(n.title),1)):At("",!0),Wt("div",gn,[zt(O.$slots,"default",{},()=>[Ae(It(n.content),1)],!0)]),n.showArrow?(rt(),pt("div",{key:1,ref_key:"arrowRef",ref:l,class:"base-popover-arrow",style:Ht(w.value)},null,4)):At("",!0)],38)),[[Re,c.value]]):At("",!0)]),_:3},8,["name"])],8,["disabled"]))]))}}),yn=Ce(hn,[["__scopeId","data-v-d09b863a"]]);export{yn as B};
