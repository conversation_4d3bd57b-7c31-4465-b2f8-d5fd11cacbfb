import{a0 as f,a1 as v,a3 as m,c as y,o as h,h as r,H as s,l as u,d as E,bb as C,i as g,p as B,a as P,b1 as T,r as S,g as R,b2 as A,b3 as O}from"./index-Cr_XsrqN.js";import{a as L}from"./index-BiHyKo52.js";import{s as V}from"./index-DhbL6JTo.js";var I=(t=>(t.TEXT="TEXT",t.SELECT="SELECT",t.MULTISELECT="MULTISELECT",t.DATE="date",t.DATE_RANGE="DATE_RANGE",t.NUMBER="NUMBER",t.NUMBER_RANGE="NUMBER_RANGE",t.CHECKBOX="CHECKBOX",t.RADIO="RADIO",t.SWITCH="SWITCH",t))(I||{}),N=f`
    .p-toggleswitch {
        display: inline-block;
        width: dt('toggleswitch.width');
        height: dt('toggleswitch.height');
    }

    .p-toggleswitch-input {
        cursor: pointer;
        appearance: none;
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        opacity: 0;
        z-index: 1;
        outline: 0 none;
        border-radius: dt('toggleswitch.border.radius');
    }

    .p-toggleswitch-slider {
        cursor: pointer;
        width: 100%;
        height: 100%;
        border-width: dt('toggleswitch.border.width');
        border-style: solid;
        border-color: dt('toggleswitch.border.color');
        background: dt('toggleswitch.background');
        transition:
            background dt('toggleswitch.transition.duration'),
            color dt('toggleswitch.transition.duration'),
            border-color dt('toggleswitch.transition.duration'),
            outline-color dt('toggleswitch.transition.duration'),
            box-shadow dt('toggleswitch.transition.duration');
        border-radius: dt('toggleswitch.border.radius');
        outline-color: transparent;
        box-shadow: dt('toggleswitch.shadow');
    }

    .p-toggleswitch-handle {
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: dt('toggleswitch.handle.background');
        color: dt('toggleswitch.handle.color');
        width: dt('toggleswitch.handle.size');
        height: dt('toggleswitch.handle.size');
        inset-inline-start: dt('toggleswitch.gap');
        margin-block-start: calc(-1 * calc(dt('toggleswitch.handle.size') / 2));
        border-radius: dt('toggleswitch.handle.border.radius');
        transition:
            background dt('toggleswitch.transition.duration'),
            color dt('toggleswitch.transition.duration'),
            inset-inline-start dt('toggleswitch.slide.duration'),
            box-shadow dt('toggleswitch.slide.duration');
    }

    .p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-slider {
        background: dt('toggleswitch.checked.background');
        border-color: dt('toggleswitch.checked.border.color');
    }

    .p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.checked.background');
        color: dt('toggleswitch.handle.checked.color');
        inset-inline-start: calc(dt('toggleswitch.width') - calc(dt('toggleswitch.handle.size') + dt('toggleswitch.gap')));
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-slider {
        background: dt('toggleswitch.hover.background');
        border-color: dt('toggleswitch.hover.border.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.hover.background');
        color: dt('toggleswitch.handle.hover.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-slider {
        background: dt('toggleswitch.checked.hover.background');
        border-color: dt('toggleswitch.checked.hover.border.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.checked.hover.background');
        color: dt('toggleswitch.handle.checked.hover.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible) .p-toggleswitch-slider {
        box-shadow: dt('toggleswitch.focus.ring.shadow');
        outline: dt('toggleswitch.focus.ring.width') dt('toggleswitch.focus.ring.style') dt('toggleswitch.focus.ring.color');
        outline-offset: dt('toggleswitch.focus.ring.offset');
    }

    .p-toggleswitch.p-invalid > .p-toggleswitch-slider {
        border-color: dt('toggleswitch.invalid.border.color');
    }

    .p-toggleswitch.p-disabled {
        opacity: 1;
    }

    .p-toggleswitch.p-disabled .p-toggleswitch-slider {
        background: dt('toggleswitch.disabled.background');
    }

    .p-toggleswitch.p-disabled .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.disabled.background');
    }
`,D={root:{position:"relative"}},M={root:function(i){var o=i.instance,l=i.props;return["p-toggleswitch p-component",{"p-toggleswitch-checked":o.checked,"p-disabled":l.disabled,"p-invalid":o.$invalid}]},input:"p-toggleswitch-input",slider:"p-toggleswitch-slider",handle:"p-toggleswitch-handle"},U=v.extend({name:"toggleswitch",style:N,classes:M,inlineStyles:D}),x={name:"BaseToggleSwitch",extends:L,props:{trueValue:{type:null,default:!0},falseValue:{type:null,default:!1},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},inputId:{type:String,default:null},inputClass:{type:[String,Object],default:null},inputStyle:{type:Object,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:U,provide:function(){return{$pcToggleSwitch:this,$parentInstance:this}}},p={name:"ToggleSwitch",extends:x,inheritAttrs:!1,emits:["change","focus","blur"],methods:{getPTOptions:function(i){var o=i==="root"?this.ptmi:this.ptm;return o(i,{context:{checked:this.checked,disabled:this.disabled}})},onChange:function(i){if(!this.disabled&&!this.readonly){var o=this.checked?this.falseValue:this.trueValue;this.writeValue(o,i),this.$emit("change",i)}},onFocus:function(i){this.$emit("focus",i)},onBlur:function(i){var o,l;this.$emit("blur",i),(o=(l=this.formField).onBlur)===null||o===void 0||o.call(l,i)}},computed:{checked:function(){return this.d_value===this.trueValue},dataP:function(){return m({checked:this.checked,disabled:this.disabled,invalid:this.$invalid})}}},z=["data-p-checked","data-p-disabled","data-p"],_=["id","checked","tabindex","disabled","readonly","aria-checked","aria-labelledby","aria-label","aria-invalid"],H=["data-p"],G=["data-p"];function X(t,i,o,l,d,e){return h(),y("div",s({class:t.cx("root"),style:t.sx("root")},e.getPTOptions("root"),{"data-p-checked":e.checked,"data-p-disabled":t.disabled,"data-p":e.dataP}),[r("input",s({id:t.inputId,type:"checkbox",role:"switch",class:[t.cx("input"),t.inputClass],style:t.inputStyle,checked:e.checked,tabindex:t.tabindex,disabled:t.disabled,readonly:t.readonly,"aria-checked":e.checked,"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel,"aria-invalid":t.invalid||void 0,onFocus:i[0]||(i[0]=function(){return e.onFocus&&e.onFocus.apply(e,arguments)}),onBlur:i[1]||(i[1]=function(){return e.onBlur&&e.onBlur.apply(e,arguments)}),onChange:i[2]||(i[2]=function(){return e.onChange&&e.onChange.apply(e,arguments)})},e.getPTOptions("input")),null,16,_),r("div",s({class:t.cx("slider")},e.getPTOptions("slider"),{"data-p":e.dataP}),[r("div",s({class:t.cx("handle")},e.getPTOptions("handle"),{"data-p":e.dataP}),[u(t.$slots,"handle",{checked:e.checked})],16,G)],16,H)],16,z)}p.render=X;var q={name:"InputSwitch",extends:p,mounted:function(){console.warn("Deprecated since v4. Use ToggleSwitch component instead.")}};const J=E({__name:"BaseRangeDatePicker",emits:["valueChange"],setup(t,{emit:i}){const o=i,l=C(),d=g(),e=g(1),w=a=>{var n;l["selection-mode"]==="range"&&((n=d.value)!=null&&n.overlayVisible)&&Array.isArray(a)&&a.length===2&&a[0]&&a[1]&&(d.value.overlayVisible=!1),o("valueChange",a)};return B(()=>{window.innerWidth<768?e.value=1:e.value=2}),(a,n)=>{const b=V;return h(),P(b,s(a.$attrs,{ref_key:"datePickerRef",ref:d,"number-of-months":e.value,class:"base-range-date-picker","panel-class":"base-range-date-picker-panel",onValueChange:w}),T({_:2},[S(a.$slots,(j,c)=>({name:c,fn:R(k=>[u(a.$slots,c,A(O(k)))])}))]),1040,["number-of-months"])}}});export{I as S,J as _,q as s};
