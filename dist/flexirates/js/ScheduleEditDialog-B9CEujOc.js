import{T as Ce}from"./index-BMUxa2Nh.js";import{s as ke}from"./index-BwvvB-v3.js";import{d as Fe,i as D,w as we,c as E,o as v,e as y,g as x,h as a,b as U,F as X,a as I,u as t,t as c,E as ye,G as q,m as u,f as Oe,s as Ie,I as Re}from"./index-Cr_XsrqN.js";import{a as He,s as $e}from"./index-fEC-lipk.js";import{s as je}from"./index-DsAxJM5H.js";import{s as We}from"./index--SCaD3QG.js";import{s as Be}from"./index-DhbL6JTo.js";import{s as Qe}from"./index-BH-zSNAS.js";import{C as Y,D as T}from"./decimal-D7roSeZH.js";import{F as z}from"./index-B1qA3Nva.js";import{t as qe,c as ze,a as ie,b as Ke}from"./index.esm-Qe0UyvbF.js";import{d as o}from"./dayjs.min-BN044Q0-.js";import{_ as _e}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{a as Ge,u as Xe}from"./property-4kw8xdA_.js";import{E as de,P as F,a as _}from"./index-D2QnTskR.js";import{_ as Je}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ze={class:"dialog"},ea={class:"w-full md:w-[650px]"},aa={class:"text-[16px] font-semibold mb-6"},ta={class:"mb-8"},la={class:"flex flex-col md:flex-row md:justify-between gap-2 md:gap-0"},na={class:"flex items-center gap-4"},sa={class:"flex items-center gap-4"},oa={key:0},ia={class:"flex flex-col md:flex-row md:justify-between gap-2 md:gap-0"},da={class:"flex flex-col gap-2"},ua={class:"flex flex-col gap-2"},ma={key:0,class:"font-bold"},ra={class:"datePick"},pa={class:"flex items-center"},ca={class:"flex items-center"},fa={class:"curren-details grid grid-cols-1 md:grid-cols-2 mt-4 gap-y-6"},va={class:"flex gap-4 font-semibold"},ya={class:"flex gap-4 font-semibold"},_a={class:""},ba={class:"flex gap-4 font-semibold"},Da={class:"flex gap-4 font-semibold"},Pa={class:"flex-1"},xa={class:"update-form"},Ta={class:"flex items-center"},ha={class:"flex items-center"},La={class:"flex items-center"},Ua={class:"flex items-center"},Aa={key:0,class:"text-sm text-gray-600 ml-[208px] mt-2"},ga={key:0},Sa={key:1},Na={class:"form-item flex"},Va={class:"form-item__content"},Ea={class:"ml-2"},Ya={key:0,class:"form-item flex mt-4"},Ma={class:"form-item__content"},Ca={class:"ml-2"},ka={class:"form-item flex mt-4"},Fa={class:"schedule-form-label"},wa={class:"ml-2"},Oa={class:"flex justify-end mt-4"},Ia={class:"flex items-center mt-4 gap-4"},Ra={for:"terms",class:"text-sm"},Ha={class:"font-bold text-2xl tips-wrap flex justify-center items-center"},$a={class:"ml-2"},ja={class:"flex justify-end mt-4"},Wa=Fe({__name:"ScheduleEditDialog",props:{visible:{type:Boolean},propertyList:{}},emits:["update:visible","confirm"],setup(be,{expose:De,emit:Pe}){const ue=be,ee=Pe,w=D(de.SKIP_NEXT_PAYMENT),ae=D(),me=D(!1),J=D(!1),te=D(!1),le=D(!1),ne=D(),h=D(),M=D({startDate:null,endDate:null,plan:null,firstDate:null,amount:null}),se=D(!1),xe=D([{label:"Calculated Installments",value:F.CALCULATED_INSTALLMENTS},{label:"Custom Installment Amount",value:F.CUSTOM_INSTALLMENT_AMOUNT},{label:"Pay Full Amount",value:F.PAY_FULL_AMOUNT_NOW}]),Te=D([{label:"Weekly",value:_.WEEKLY},{label:"Fortnightly",value:_.FORTNIGHTLY},{label:"Monthly",value:_.MONTHLY},{label:"Quarterly",value:_.QUARTERLY}]),p=D({paymentPlan:F.CALCULATED_INSTALLMENTS,paymentPlanSchedule:null,firstDate:null,amount:null}),Z=D(""),R=D(0),B=D({start:null,end:null}),oe=D(!1),he=()=>{J.value=!0},n=D(null),re=async l=>{try{n.value=null,oe.value=!0;const{data:e,code:i}=await Ge(Number(l));i===0&&(n.value=e)}catch(e){console.log(e)}finally{oe.value=!1}},Le=async()=>{var l,e,i,s,d,b;if(!se.value)return window.$toast.add({severity:"warn",summary:"warn",detail:"You must agree to the terms and conditions"});try{te.value=!0;const f={id:(l=n.value)==null?void 0:l.id,edit_type:w.value};if(w.value===de.PAUSE_SCHEDULE)f.pause_start_date=o(M.value.startDate).format("YYYY-MM-DD"),f.pause_end_date=o(M.value.endDate).format("YYYY-MM-DD"),f.first_payment_date=o(M.value.startDate).format("YYYY-MM-DD"),f.payment_plan=((i=(e=n.value)==null?void 0:e.schedule_details)==null?void 0:i.payment_plan)||_.QUARTERLY,f.amount=Number(((s=n.value)==null?void 0:s.next_payment_amount)||0);else if(w.value===de.EDIT_SCHEDULE_DETAILS){const L=(d=h.value)==null?void 0:d.values;f.first_payment_date=o(L.firstDate).format("YYYY-MM-DD"),L.paymentPlan===F.PAY_FULL_AMOUNT_NOW?(f.payment_plan=_.FULL_AMOUNT,f.amount=Number(((b=n.value)==null?void 0:b.next_payment_amount)||0)):L.paymentPlan===F.CALCULATED_INSTALLMENTS?(f.payment_plan=L.paymentPlanSchedule||_.QUARTERLY,f.amount=Number(Z.value||0)):L.paymentPlan===F.CUSTOM_INSTALLMENT_AMOUNT&&(f.payment_plan=L.paymentPlanSchedule||_.QUARTERLY,f.amount=Number(L.amount||0))}const{code:C}=await Xe(f);C===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Update schedule success"}),ee("confirm"),J.value=!1,ee("update:visible",!1))}catch(f){console.log(f)}finally{te.value=!1}},Ue=l=>{ee("update:visible",l)},Ae=l=>{if(l&&M.value.endDate){const e=o(l),i=o(M.value.endDate);(i.isBefore(e)||i.isSame(e,"day"))&&(M.value.endDate=null,ne.value&&ne.value.setFieldValue("endDate",null))}},K=D(""),pe=D(""),ge=(l,e,i)=>{if(!l||!e||i<=0)return"";const s=o(l);let d=s;switch(e){case _.WEEKLY:d=s.add((i-1)*7,"day");break;case _.FORTNIGHTLY:d=s.add((i-1)*14,"day");break;case _.MONTHLY:d=s.add(i-1,"month");break;case _.QUARTERLY:d=s.add((i-1)*3,"month");break;default:d=s;break}return d.format("YYYY-MM-DD")},Se=l=>{var j,W,A,k;if(!p.value.firstDate||!p.value.paymentPlanSchedule||!l||T(l).lte(0)){R.value=0,K.value="0",pe.value="";return}const e=T(((j=n.value)==null?void 0:j.total_remaining)||0),i=T(l),s=o(p.value.firstDate),d=o((k=(A=(W=n.value)==null?void 0:W.date_range)==null?void 0:A.pause)==null?void 0:k.end);let b=0;switch(p.value.paymentPlanSchedule){case _.QUARTERLY:b=Math.abs(d.diff(s,"quarter"))+1;break;case _.MONTHLY:b=Math.abs(d.diff(s,"month"))+1;break;case _.FORTNIGHTLY:{const S=d.diff(s,"day")+1;b=S<=14?1:Math.ceil(S/14);break}case _.WEEKLY:{const S=d.diff(s,"day")+1;b=S<=7?1:Math.ceil(S/7);break}default:b=1;break}const f=e.div(i).floor().toNumber(),C=Math.min(f,b),L=e.sub(i.mul(C)).toFixed(2,T.ROUND_HALF_UP);R.value=C,K.value=L,T(L).gt(0)&&C<b&&(R.value=C+1),pe.value=ge(new Date(p.value.firstDate),p.value.paymentPlanSchedule,R.value)},Ne=()=>{var b,f,C,L,j,W,A,k,S,G;if(!p.value.firstDate||!p.value.paymentPlanSchedule||!((b=n.value)!=null&&b.final_payment_date)){R.value=0,p.value.amount=null;return}if(h!=null&&h.value&&((C=(f=h==null?void 0:h.value)==null?void 0:f.values)==null?void 0:C.paymentPlan)===F.CUSTOM_INSTALLMENT_AMOUNT){(j=(L=h==null?void 0:h.value)==null?void 0:L.values)!=null&&j.amount&&Se((A=(W=h==null?void 0:h.value)==null?void 0:W.values)==null?void 0:A.amount);return}const l=((k=n.value)==null?void 0:k.next_payment_amount)||1,e=o(p.value.firstDate),i=o((S=n.value)==null?void 0:S.final_payment_date);let s=0,d="0";switch(p.value.paymentPlanSchedule){case _.FULL_AMOUNT:s=1,d=T(((G=n.value)==null?void 0:G.next_payment_amount)||1).toFixed(2,T.ROUND_HALF_UP);break;case _.QUARTERLY:s=Math.abs(i.diff(e,"quarter")||1),d=T(l).div(s).toFixed(2,T.ROUND_HALF_UP);break;case _.MONTHLY:s=Math.abs(i.diff(e,"month")||1),d=T(l).div(s).toFixed(2,T.ROUND_HALF_UP);break;case _.FORTNIGHTLY:{const H=i.diff(e,"day")+1;s=H<=14?1:Math.ceil(H/14),d=T(l).div(s).toFixed(2,T.ROUND_HALF_UP);break}case _.WEEKLY:{const H=i.diff(e,"day")+1;s=H<=7?1:Math.ceil(H/7),d=T(l).div(s).toFixed(2,T.ROUND_HALF_UP);break}}Z.value=d,R.value=Math.floor(s)},Ve=qe(ze({paymentPlan:ie().min(1,"Payment plan is required"),paymentPlanSchedule:ie().when("paymentPlan",{is:l=>[F.CALCULATED_INSTALLMENTS,F.CUSTOM_INSTALLMENT_AMOUNT].includes(l),then:l=>l.min(1,"Payment schedule is required"),otherwise:l=>l.optional()}),firstDate:Ke().min(o().startOf("day").toDate(),"First payment date must be today or in the future"),amount:ie().when("paymentPlan",{is:F.CUSTOM_INSTALLMENT_AMOUNT,then:l=>l.min(.01,"Amount is required"),otherwise:l=>l.optional()})})),Ee=l=>{var i,s,d;const e=(d=(s=(i=n.value)==null?void 0:i.date_range)==null?void 0:s.first_payment_dates)==null?void 0:d[l];e&&(B.value.start=e.start,B.value.end=e.end,h.value&&(p.value.firstDate=o(e.start).toDate(),h.value.setFieldValue("firstDate",o(e.start).toDate())))},Ye=l=>{me.value=!0,ae.value=l,re(l)};return we([()=>p.value.amount,()=>p.value.firstDate,()=>p.value.paymentPlanSchedule,()=>p.value.paymentPlan,()=>{var l;return(l=n.value)==null?void 0:l.final_payment_date}],()=>{Ne()},{immediate:!0}),De({setPropertyDetailAndLoad:Ye}),(l,e)=>{const i=Qe,s=He,d=Be,b=We,f=je,C=$e,L=Ie,j=ke,W=Ce;return v(),E("div",Ze,[y(_e,{visible:ue.visible,title:"Edit Schedule","onUpdate:visible":Ue},{content:x(()=>{var A,k,S,G,H,ce,fe,ve;return[a("div",ea,[a("div",aa,[e[13]||(e[13]=a("div",{class:"py-4"}," Select Property: ",-1)),a("div",null,[y(i,{modelValue:ae.value,"onUpdate:modelValue":e[0]||(e[0]=m=>ae.value=m),options:ue.propertyList,"option-label":"label","option-value":"value",placeholder:"— Please Select —",class:"w-full",loading:oe.value,disabled:me.value,onValueChange:re},null,8,["modelValue","options","loading","disabled"])])]),n.value?(v(),E(X,{key:0},[a("div",ta,[a("div",la,[a("div",na,[y(s,{modelValue:w.value,"onUpdate:modelValue":e[1]||(e[1]=m=>w.value=m),"input-id":"skip",name:"skip",value:t(Y).Flexirates.EditPlanType.SKIP_NEXT_PAYMENT},null,8,["modelValue","value"]),e[14]||(e[14]=a("label",{for:"skip",class:"edit-label"},"Skip Next Payment",-1))]),a("div",sa,[y(s,{modelValue:w.value,"onUpdate:modelValue":e[2]||(e[2]=m=>w.value=m),"input-id":"edit",name:"edit",value:t(Y).Flexirates.EditPlanType.EDIT_SCHEDULE_DETAILS},null,8,["modelValue","value"]),e[15]||(e[15]=a("label",{for:"edit",class:"edit-label"},"Edit Schedule details",-1))])])]),w.value===t(Y).Flexirates.EditPlanType.SKIP_NEXT_PAYMENT?(v(),E("div",oa,[e[19]||(e[19]=a("div",{class:"flex justify-center text-[18px] pb-8 font-bold"}," You have chosen to skip your next scheduled payment. ",-1)),a("div",ia,[a("div",da,[a("div",null,[e[16]||(e[16]=a("span",{class:"font-bold"},"Skipped Scheduled Payment Date : ",-1)),a("span",null,c(t(o)((A=n.value)==null?void 0:A.next_payment_date).format("DD/MM/YYYY")),1)]),a("div",null,[e[17]||(e[17]=a("span",{class:"font-bold"}," Regular Payment Amount : ",-1)),a("span",null,c(t(z).formatAmount((k=n.value)==null?void 0:k.next_payment_amount)),1)])]),a("div",null,[a("div",ua,[a("div",null,[n.value?(v(),E("span",ma," Next Scheduled Payment Date : ")):U("",!0),a("span",null,c(t(o)((S=n.value)==null?void 0:S.adjust_next_payment_date).format("DD/MM/YYYY")),1)]),a("div",null,[e[18]||(e[18]=a("span",{class:"font-bold"}," Adjusted Regular Payment Amount : ",-1)),a("span",null,c(t(z).formatAmount((G=n.value)==null?void 0:G.adjust_next_payment_amount)),1)])])])]),e[20]||(e[20]=a("div",{class:"skip-tips"},' Your regular payment amount has been adjusted to align with your selected plan.To update your payment frequency or regular payment amount, click "Edit Schedule Details" in the options above. ',-1))])):w.value===t(Y).Flexirates.EditPlanType.PAUSE_SCHEDULE?(v(),I(f,{key:1,style:{width:"100%",height:"180px"}},{default:x(()=>[y(t(ye),{ref_key:"scheduleFormRef",ref:ne},{default:x(({values:m})=>[e[23]||(e[23]=a("div",{class:"text-[18px] font-semibold pb-2"}," Select the pause period ",-1)),a("div",ra,[y(t(q),{modelValue:M.value.startDate,"onUpdate:modelValue":e[3]||(e[3]=N=>M.value.startDate=N),as:"div",name:"startDate",class:"schedule-form-item"},{default:x(({field:N,errorMessage:$,handleChange:Q})=>{var r,P,V,g;return[a("div",pa,[e[21]||(e[21]=a("label",{class:"schedule-form-label"},[u("Pause Start Date"),a("span",null,"*"),u(":")],-1)),y(d,{modelValue:N.value,"onUpdate:modelValue":O=>N.value=O,"show-icon":"",fluid:"","icon-display":"input","input-id":"startDate",class:"w-full","min-date":t(o)((P=(r=n.value)==null?void 0:r.date_range.pause)==null?void 0:P.start).toDate(),"max-date":t(o)((g=(V=n.value)==null?void 0:V.date_range.pause)==null?void 0:g.end).add(1,"day").toDate(),"date-format":"dd/mm/yy",onValueChange:O=>{Q(O),Ae(O)}},null,8,["modelValue","onUpdate:modelValue","min-date","max-date","onValueChange"])]),$?(v(),I(b,{key:0,class:"ml-46 mt-2",severity:"error",size:"small",variant:"simple"},{default:x(()=>[u(c($),1)]),_:2},1024)):U("",!0)]}),_:1},8,["modelValue"]),y(t(q),{modelValue:M.value.endDate,"onUpdate:modelValue":e[4]||(e[4]=N=>M.value.endDate=N),as:"div",name:"endDate",class:"schedule-form-item"},{default:x(({field:N,errorMessage:$,handleChange:Q})=>{var r,P;return[a("div",ca,[e[22]||(e[22]=a("label",{class:"schedule-form-label"},[u("Pause End Date"),a("span",null,"*"),u(":")],-1)),y(d,{modelValue:N.value,"onUpdate:modelValue":V=>N.value=V,disabled:M.value.startDate===null,"min-date":t(o)(m.startDate).toDate(),"max-date":t(o)((P=(r=n.value)==null?void 0:r.date_range.pause)==null?void 0:P.end).toDate(),"date-format":"dd/mm/yy","show-icon":"",fluid:"","icon-display":"input","input-id":"startDate",class:"w-full",onValueChange:Q},null,8,["modelValue","onUpdate:modelValue","disabled","min-date","max-date","onValueChange"])]),$?(v(),I(b,{key:0,class:"ml-46 mt-2",severity:"error",size:"small",variant:"simple"},{default:x(()=>[u(c($),1)]),_:2},1024)):U("",!0)]}),_:2},1032,["modelValue"])])]),_:1,__:[23]},512)]),_:1})):U("",!0),w.value===t(Y).Flexirates.EditPlanType.EDIT_SCHEDULE_DETAILS?(v(),E(X,{key:2},[e[35]||(e[35]=a("div",{class:"text-[18px] font-semibold pb-2"}," Current Schedule Details ",-1)),a("div",fa,[a("div",va,[e[24]||(e[24]=a("div",null," Payment Plan: ",-1)),a("div",null,c((H=n.value)==null?void 0:H.schedule_details.payment_plan_desc),1)]),a("div",ya,[e[25]||(e[25]=a("div",null," first Payment Date: ",-1)),a("div",_a,c(t(o)((ce=n.value)==null?void 0:ce.first_payment_date).format("DD/MM/YYYY")),1)]),a("div",ba,[e[26]||(e[26]=a("div",null," Regular Payment Amount: ",-1)),a("div",null,c(t(z).formatAmount((fe=n.value)==null?void 0:fe.next_payment_amount)),1)]),a("div",Da,[e[27]||(e[27]=a("div",null," Last Payment Date: ",-1)),a("div",Pa,c(t(o)((ve=n.value)==null?void 0:ve.final_payment_date).format("DD/MM/YYYY")),1)])]),e[36]||(e[36]=a("div",{class:"text-[18px] font-semibold pb-2"}," Update Schedule ",-1)),y(t(ye),{ref_key:"editFormRef",ref:h,"validation-schema":t(Ve)},{default:x(({values:m})=>{var N,$,Q;return[a("div",xa,[y(t(q),{modelValue:p.value.paymentPlan,"onUpdate:modelValue":e[5]||(e[5]=r=>p.value.paymentPlan=r),as:"div",name:"paymentPlan",class:"schedule-form-item"},{default:x(({field:r,errorMessage:P,handleChange:V})=>[a("div",Ta,[e[28]||(e[28]=a("label",{class:"schedule-form-label"},[u("Payment Plan"),a("span",null,"*"),u(":")],-1)),y(i,{modelValue:r.value,"onUpdate:modelValue":g=>r.value=g,options:xe.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Plan",class:"w-full",onValueChange:V},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"])]),P?(v(),I(b,{key:0,class:"ml-46 mt-2",severity:"error",size:"small",variant:"simple"},{default:x(()=>[u(c(P),1)]),_:2},1024)):U("",!0)]),_:1},8,["modelValue"]),[t(Y).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,t(Y).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(m==null?void 0:m.paymentPlan)?(v(),I(t(q),{key:0,modelValue:p.value.paymentPlanSchedule,"onUpdate:modelValue":e[6]||(e[6]=r=>p.value.paymentPlanSchedule=r),as:"div",name:"paymentPlanSchedule",class:"schedule-form-item"},{default:x(({field:r,errorMessage:P,handleChange:V})=>[a("div",ha,[e[29]||(e[29]=a("label",{class:"schedule-form-label"},[u("Payment Schedule"),a("span",null,"*"),u(":")],-1)),y(i,{modelValue:r.value,"onUpdate:modelValue":g=>r.value=g,options:Te.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Schedule",class:"w-full",onValueChange:g=>{V(g),Ee(g)}},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"])]),P?(v(),I(b,{key:0,class:"ml-46 mt-2",severity:"error",size:"small",variant:"simple"},{default:x(()=>[u(c(P),1)]),_:2},1024)):U("",!0)]),_:1},8,["modelValue"])):U("",!0),y(t(q),{modelValue:p.value.firstDate,"onUpdate:modelValue":e[7]||(e[7]=r=>p.value.firstDate=r),as:"div",name:"firstDate",class:"schedule-form-item"},{default:x(({field:r,errorMessage:P,handleChange:V})=>{var g,O;return[a("div",La,[e[30]||(e[30]=a("label",{class:"schedule-form-label"},[u("First Payment Date"),a("span",null,"*"),u(":")],-1)),y(d,{modelValue:r.value,"onUpdate:modelValue":Me=>r.value=Me,disabled:m.paymentPlanSchedule===null,"min-date":m.paymentPlan===t(Y).Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW?t(o)().add(3,"day").toDate():B.value.start?t(o)(B.value.start).toDate():new Date,"max-date":m.paymentPlan===t(Y).Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW?t(o)((g=n.value)==null?void 0:g.final_payment_date).subtract(1,"day").toDate():B.value.end?t(o)(B.value.end).toDate():t(o)((O=n.value)==null?void 0:O.final_payment_date).toDate(),"date-format":"dd/mm/yy","show-icon":"",fluid:"","icon-display":"input","input-id":"firstDate",class:Oe(["w-full",{"p-invalid":P}]),onValueChange:V},null,8,["modelValue","onUpdate:modelValue","disabled","min-date","max-date","class","onValueChange"])]),P?(v(),I(b,{key:0,class:"ml-46 mt-2",severity:"error",size:"small",variant:"simple"},{default:x(()=>[u(c(P),1)]),_:2},1024)):U("",!0)]}),_:2},1032,["modelValue"]),(m==null?void 0:m.paymentPlan)===t(Y).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT?(v(),I(t(q),{key:1,modelValue:p.value.amount,"onUpdate:modelValue":e[8]||(e[8]=r=>p.value.amount=r),as:"div",name:"amount",class:"schedule-form-item"},{default:x(({field:r,errorMessage:P,handleChange:V})=>{var g;return[a("div",Ua,[e[31]||(e[31]=a("label",{class:"schedule-form-label"},[u("Amount"),a("span",null,"*"),u(":")],-1)),y(C,{modelValue:r.value,"onUpdate:modelValue":O=>r.value=O,"min-fraction-digits":2,"max-fraction-digits":2,max:Number(((g=n.value)==null?void 0:g.total_remaining)||0),"input-id":"amount",fluid:"",onValueChange:V},null,8,["modelValue","onUpdate:modelValue","max","onValueChange"])]),m.amount>0?(v(),E("div",Aa,[Number(K.value)>0?(v(),E("p",ga," The last payment amount will be: "+c(t(z).formatAmount(K.value)),1)):Number(K.value)===0?(v(),E("p",Sa," All payments will be exactly "+c(t(z).formatAmount(new(t(T))(m.amount).toFixed(2))),1)):U("",!0)])):U("",!0),P?(v(),I(b,{key:1,class:"ml-46 mt-2",severity:"error",size:"small",variant:"simple"},{default:x(()=>[u(c(P),1)]),_:2},1024)):U("",!0)]}),_:2},1032,["modelValue"])):U("",!0)]),a("div",Na,[e[32]||(e[32]=a("label",{class:"schedule-form-label"},"Last payment date :",-1)),a("div",Va,[a("span",Ea,c((N=n.value)!=null&&N.final_payment_date?t(o)(($=n.value)==null?void 0:$.final_payment_date).format("DD/MM/YYYY"):""),1)])]),(m==null?void 0:m.paymentPlan)===t(Y).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS?(v(),E("div",Ya,[e[33]||(e[33]=a("label",{class:"schedule-form-label"},"Regular payment amount :",-1)),a("div",Ma,[a("span",Ca,c(Z.value?t(z).formatAmount(new(t(T))(Z.value).toFixed(2)):""),1)])])):U("",!0),a("div",ka,[a("label",Fa,[u("Number of payments during "+c(t(o)().year())+" ",1),(Q=n.value)!=null&&Q.final_payment_date?(v(),E(X,{key:0},[u(" - "+c(t(o)(n.value.final_payment_date).year()),1)],64)):U("",!0),e[34]||(e[34]=u(" :"))]),a("span",wa,[(m==null?void 0:m.paymentPlan)===t(Y).Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW?(v(),E(X,{key:0},[u(" 1 ")],64)):(v(),E(X,{key:1},[u(c(R.value?R.value:""),1)],64))])])]}),_:1},8,["validation-schema"])],64)):U("",!0),a("div",Oa,[y(L,{label:"NEXT",severity:"warn",class:"btn",onClick:he})])],64)):U("",!0)])]}),_:1},8,["visible"]),y(_e,{visible:J.value,title:"Confirmation","onUpdate:visible":e[11]||(e[11]=A=>J.value=A)},{content:x(()=>{var A,k;return[e[40]||(e[40]=a("div",{class:"text-[20px] font-semibold pb-2 flex mt-4 mb-4"}," Important Information before confirming your changes. ",-1)),e[41]||(e[41]=a("ul",{class:"paragraph"},[a("li",{class:"mb-4"}," If your FlexiRates account has 3 or more declined payments within a financial year, your registration may be canceled. "),a("li",{class:"mb-4"}," If a payment is skipped or fails, your regular payment amount may adjust from the original setup. "),a("li",{class:"mb-4"},[u(" You may edit a schedule up to 5 times per property, including: "),a("ul",{class:"paragraph"},[a("li",{class:"ml-4"}," Skipping a payment schedule "),a("li",{class:"ml-4"}," Pausing a schedule "),a("li",{class:"ml-4"}," Editing schedule details ")])])],-1)),e[42]||(e[42]=a("div",null,[u(" If you require additional edits, please "),a("span",{class:"font-bold text-[#1b1548] cursor-pointer"},"contact our support team"),u(" for assistance. ")],-1)),a("div",Ia,[y(j,{modelValue:se.value,"onUpdate:modelValue":e[9]||(e[9]=S=>se.value=S),binary:"","input-id":"terms"},null,8,["modelValue"]),a("label",Ra,[e[37]||(e[37]=u(" By ticking, you are confirming that you have read, understood andd agree to the ")),a("span",{class:"font-bold cursor-pointer",onClick:e[10]||(e[10]=Re(S=>le.value=!0,["stop"]))}," Terms and Conditions "),e[38]||(e[38]=u(". "))])]),a("div",Ha,[e[39]||(e[39]=a("span",{class:"text-[#545454]"},"Edit Schedule Count : ",-1)),a("span",$a,c((A=n.value)==null?void 0:A.schedule_details.edited_count)+" of "+c((k=n.value)==null?void 0:k.schedule_details.editable_count),1)]),a("div",ja,[y(L,{label:"CONFIRM",severity:"warn",class:"btn",loading:te.value,onClick:Le},null,8,["loading"])])]}),_:1},8,["visible"]),y(W,{modelValue:le.value,"onUpdate:modelValue":e[12]||(e[12]=A=>le.value=A)},null,8,["modelValue"])])}}}),it=Je(Wa,[["__scopeId","data-v-a018db2a"]]);export{it as S};
