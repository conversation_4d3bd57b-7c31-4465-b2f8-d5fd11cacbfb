import{_ as O}from"./BaseDataTable-BjLz7Fg0.js";import{a6 as g,a7 as N,d as R,i as o,j as Y,c,o as u,h as C,e as p,b as q,F,r as H,f as D,t as d,u as i,g as _,s as G,m as W}from"./index-Cr_XsrqN.js";import{d as $}from"./dayjs.min-BN044Q0-.js";import{u as J}from"./useDict-B_iRj5rJ.js";import{u as K}from"./useRequestList-B2228N3W.js";import{a as Q}from"./dict-CFM3a_vV.js";import{_ as U}from"./_plugin-vue_export-helper-DlAUqK2U.js";const me=()=>g("user/getAuthDevices"),ve=l=>N("user/removeAuthDevice",l),T=l=>g("/activityLog/list",{params:l}),pe=l=>g("/activityLog/export",{params:l}),X={class:"search-tags"},Z=["onClick"],ee={key:0,class:"flex justify-center mt-4"},te=R({__name:"activityLog",emits:["update:params"],setup(l,{emit:k}){const w=k,m=K({requestFn:T}),M=o([{field:"",header:"Date & Time",style:{width:"200px",padding:"0 8px"},template:"date"},{field:"activity_type_desc",header:"Activity Type",style:{width:"300px"}},{field:"activity_detail",header:"Details"},{field:"status",header:"Status",style:{width:"100px"},template:"status"}]),{list:P,loading:S,total:f,onPageChange:z,failed:I,failureMessage:V}=m,v=o(null),y=o([]),n=o(1),h=o(10),x=()=>{n.value=1,m.refresh()},r=o(!1),L=o([]),j=async()=>{var t;if(!r.value){r.value=!0;try{const s=n.value+1,a=await T({page:s,page_size:h.value,activity_types:v.value});Array.isArray((t=a==null?void 0:a.data)==null?void 0:t.data)&&a.data.data.length>0&&(L.value=a.data.data,n.value=s)}finally{r.value=!1}}},B=t=>{v.value=t,n.value=1;const s={page:1,page_size:h.value,activity_types:t};m.updateParams(s),w("update:params",t)};J("customer_activity_type_filter",t=>{y.value=Q(t,{label:"ALL ACTIVITIES",value:null})}),x();const b=Y(()=>[...P.value,...L.value]);return(t,s)=>{var A;const a=G,E=O;return u(),c("div",null,[C("div",X,[(u(!0),c(F,null,H(y.value,e=>(u(),c("div",{key:e.label,class:D(["tag-item cursor-pointer",{active:v.value===e.value}]),onClick:se=>B(e.value)},d(e.label),11,Z))),128))]),p(E,{value:b.value,columns:M.value,loading:i(S),"show-multiple-column":!1,paginator:!1,"show-search-bar":!1,rows:10,lazy:!0,"data-key":"id",failed:i(I),"failure-message":i(V),"row-hover":!1,"total-records":i(f),class:"mt-4",onRefresh:x,onPage:s[0]||(s[0]=e=>i(z)(e))},{date:_(({data:e})=>[W(d(i($)(e==null?void 0:e.activity_at).format("DD MMM YYYY, HH:mm A")),1)]),status:_(({data:e})=>[C("span",{class:D(["font-semibold",{"text-green-500":(e==null?void 0:e.activity_status)===1,"text-red-500":(e==null?void 0:e.activity_status)===0}])},d(e==null?void 0:e.activity_status_desc),3)]),"empty-action":_(()=>[p(a,{label:"Refresh"})]),_:1},8,["value","columns","loading","failed","failure-message","total-records"]),((A=b.value)==null?void 0:A.length)<i(f)?(u(),c("div",ee,[p(a,{label:"VIEW MORE",class:"btn",loading:r.value,onClick:j},null,8,["loading"])])):q("",!0)])}}}),ae=U(te,[["__scopeId","data-v-98312313"]]),de=Object.freeze(Object.defineProperty({__proto__:null,default:ae},Symbol.toStringTag,{value:"Module"}));export{ae as A,de as a,pe as e,me as g,ve as r};
