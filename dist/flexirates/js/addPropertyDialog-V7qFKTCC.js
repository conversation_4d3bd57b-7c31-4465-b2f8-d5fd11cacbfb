import{s as _e}from"./index-fEC-lipk.js";import{s as Pe}from"./index-DhbL6JTo.js";import{s as be}from"./index-BH-zSNAS.js";import{d as Te,i as V,w as J,j as Ae,p as Le,c as x,o as r,e as T,g as y,h as l,a as N,b as A,u as s,E as X,G as k,m as p,t as v,s as Ne,F as B}from"./index-Cr_XsrqN.js";import{s as De}from"./index--SCaD3QG.js";import{s as Ue}from"./index-DdGtf4Je.js";import{C as S,D as f}from"./decimal-D7roSeZH.js";import{F as Y}from"./index-B1qA3Nva.js";import{t as Z,c as ee,d as I,b as Ve,a as ae}from"./index.esm-Qe0UyvbF.js";import{d as D}from"./dayjs.min-BN044Q0-.js";import{_ as xe}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{A as ge}from"./addCardOrBank-CpEoqTrU.js";import{d as he}from"./home-Bcs-QcZ3.js";import{g as Me}from"./property-4kw8xdA_.js";import{g as Ce}from"./transactions-DCpZnSzG.js";import{P as g,a as c}from"./index-D2QnTskR.js";import{_ as ke}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-CQnajQNe.js";import"./index-DsAxJM5H.js";import"./index-5-OuhCK9.js";import"./register-payment-CcrZD2od.js";import"./paymentMethod-CihfujB6.js";import"./country-ZCPlDRUV.js";const Fe={class:"new-schedule-container"},Oe={class:"flex flex-col md:flex-row md:items-center gap-2"},we={class:"flex flex-col md:flex-row md:items-center gap-2"},Ee={class:"form-actions"},Se={class:"flex items-center"},Ye={class:"flex items-center"},Ie={key:0},Re={key:1},qe={class:"flex items-center"},He={class:"flex items-center"},$e={key:0},We={class:"flex items-center"},Be={class:"flex items-center"},Ge={class:"flex items-center"},Ke={class:"flex-1 flex flex-col"},Qe={class:"flex items-center"},ze={class:"flex flex-col flex-1"},je={key:1,class:"text-sm text-gray-600 mt-2"},Je={key:0},Xe={key:1},Ze={class:"form-group font-semibold"},ea={class:"flex items-center"},aa={class:"h-4"},ta={class:"flex items-center mt-4"},la={class:"h-4"},na={key:0,class:"flex items-center mt-4"},oa={class:"h-4"},sa={class:"flex gap-2 font-semibold mt-4"},ra={class:"h-4"},ia={class:"form-actions"},ua=Te({__name:"addPropertyDialog",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","close","refresh"],setup(te,{emit:le}){const ne=te,R=le,C=V(!1),H=V(!1),_=V(),E=V(!1),$=V(!1),oe=V([{label:"Calculated Installments",value:g.CALCULATED_INSTALLMENTS},{label:"Custom Installment Amount",value:g.CUSTOM_INSTALLMENT_AMOUNT},{label:"Pay Full Amount",value:g.PAY_FULL_AMOUNT_NOW}]),se=V([{label:"Weekly",value:c.WEEKLY},{label:"Fortnightly",value:c.FORTNIGHTLY},{label:"Monthly",value:c.MONTHLY},{label:"Quarterly",value:c.QUARTERLY}]),F=V(""),G=V(""),re=()=>{W(),E.value=!1},ie=Z(ee({propertyNumber:I().required("Please enter property number."),verificationCode:I().required("Please enter verification code.")})),ue=Z(ee({primaryPayment:I().required("Primary Payment Method is required"),secondaryPayment:I().optional(),paymentPlanType:ae().min(0,"Payment Plan Type is required"),paymentPlan:ae().when("paymentPlanType",{is:t=>[g.CALCULATED_INSTALLMENTS,g.CUSTOM_INSTALLMENT_AMOUNT].includes(t),then:t=>t.min(0,"Payment Plan is required"),otherwise:t=>t.optional()}),firstPaymentDate:Ve().min(D().startOf("day").toDate(),"First payment date must be today or in the future").typeError("First Payment Date is required").required("First Payment Date is required"),amount:I().when(["paymentPlanType"],{is:t=>t===g.CUSTOM_INSTALLMENT_AMOUNT,then:t=>t.required("Amount is required").min(0,"Amount is required"),otherwise:t=>t.optional()})})),O=V({propertyNumber:"",verificationCode:""}),a=V({primaryPayment:"",secondaryPayment:"",paymentPlanType:g.CALCULATED_INSTALLMENTS,paymentPlan:c.WEEKLY,firstPaymentDate:"",amount:"",numberOfPaymentDuring:0,regularPaymentAmount:"",lastPaymentDate:""}),q=V(),K=()=>{R("update:visible",!1),R("close"),C.value=!1},me=async t=>{var e;try{H.value=!0;const u=await Me({property_number:t.propertyNumber,verification_code:t.verificationCode});u.code===0&&(_.value=u.data,C.value=!0,a.value.paymentPlanType!==g.PAY_FULL_AMOUNT_NOW&&(a.value.paymentPlan=((e=_.value)==null?void 0:e.default_payment_plan)||null))}catch(u){console.error("Error verifying:",u)}finally{H.value=!1}},de=async()=>{var n;let t=null,e=a.value.paymentPlan;a.value.paymentPlanType===g.PAY_FULL_AMOUNT_NOW?(t=((n=_.value)==null?void 0:n.total_amount_due)||0,e=c.FULL_AMOUNT):a.value.paymentPlanType===g.CALCULATED_INSTALLMENTS?t=a.value.regularPaymentAmount:(e=c.FULL_AMOUNT,t=a.value.amount);const u={property_number:O.value.propertyNumber,verification_code:O.value.verificationCode,primary_payment_method_id:a.value.primaryPayment,secondary_payment_method_id:a.value.secondaryPayment,payment_plan:e,first_payment_date:D(a.value.firstPaymentDate).format("YYYY-MM-DD"),amount:Number(t),return_url:window.location.href};try{$.value=!0,(await he(u)).code===0&&(K(),window.$toast.add({severity:"success",summary:"Success",detail:"Add property successful"}),W(),R("refresh"))}catch(i){console.error("Error submitting payment:",i)}finally{$.value=!1}},Q=(t,e)=>{if(typeof e=="string"&&e==="")E.value=!0;else switch(t){case"first":a.value.primaryPayment=e;break;case"second":a.value.secondaryPayment=e;break}},W=async()=>{const t=await Ce();q.value=t.data.map(e=>({label:e.payment_method,value:e.id})),q.value.push({label:"Add New Payment Method",value:""})},ye=(t,e,u)=>{if(!t||!e||u<=0)return"";const n=D(t);let i=n;switch(e){case c.WEEKLY:i=n.add((u-1)*7,"day");break;case c.FORTNIGHTLY:i=n.add((u-1)*14,"day");break;case c.MONTHLY:i=n.add(u-1,"month");break;case c.QUARTERLY:i=n.add((u-1)*3,"month");break;default:i=n;break}return i.format("YYYY-MM-DD")},pe=t=>{var P;if(!a.value.firstPaymentDate||!a.value.paymentPlan||!t||f(t).lte(0)){a.value.numberOfPaymentDuring=0,F.value="0",G.value="";return}const e=f(((P=_.value)==null?void 0:P.total_amount_due)||0),u=f(t),n=D(a.value.firstPaymentDate),i=D("2026-06-30");let L=0;switch(a.value.paymentPlan){case c.QUARTERLY:L=Math.abs(i.diff(n,"quarter"))+1;break;case c.MONTHLY:L=Math.abs(i.diff(n,"month"))+1;break;case c.FORTNIGHTLY:{const U=i.diff(n,"day")+1;L=U<=14?1:Math.ceil(U/14);break}case c.WEEKLY:{const U=i.diff(n,"day")+1;L=U<=7?1:Math.ceil(U/7);break}default:L=1;break}const w=e.div(u).floor().toNumber(),h=Math.min(w,L),d=e.sub(u.mul(h)).toFixed(2,f.ROUND_HALF_UP);a.value.numberOfPaymentDuring=h,F.value=d,f(d).gt(0)&&h<L&&(a.value.numberOfPaymentDuring=h+1),G.value=ye(new Date(a.value.firstPaymentDate),a.value.paymentPlan,a.value.numberOfPaymentDuring)},ve=()=>{var L,w,h,d;if(!a.value.firstPaymentDate||!a.value.paymentPlan||!((L=_.value)!=null&&L.last_payment_date)){a.value.numberOfPaymentDuring=0,a.value.regularPaymentAmount="0";return}if(a.value.paymentPlanType===g.CUSTOM_INSTALLMENT_AMOUNT){a.value.amount&&pe(a.value.amount);return}const t=((w=_.value)==null?void 0:w.total_amount_due)||1,e=D(a.value.firstPaymentDate),u=D((h=_.value)==null?void 0:h.last_payment_date);let n=0,i="0";switch(a.value.paymentPlan){case c.FULL_AMOUNT:n=1,i=new f(((d=_.value)==null?void 0:d.total_amount_due)||1).toFixed(2,f.ROUND_HALF_UP);break;case c.QUARTERLY:n=Math.abs(u.diff(e,"quarter")||1),i=new f(t).div(n).toFixed(2,f.ROUND_HALF_UP);break;case c.MONTHLY:n=Math.abs(u.diff(e,"month")||1),i=new f(t).div(n).toFixed(2,f.ROUND_HALF_UP);break;case c.FORTNIGHTLY:{const P=u.diff(e,"day")+1;n=P<=14?1:Math.ceil(P/14),i=new f(t).div(n).toFixed(2,f.ROUND_HALF_UP);break}case c.WEEKLY:{const P=u.diff(e,"day")+1;n=P<=7?1:Math.ceil(P/7),i=new f(t).div(n).toFixed(2,f.ROUND_HALF_UP);break}}a.value.regularPaymentAmount=i,a.value.numberOfPaymentDuring=Math.floor(n)};J([()=>a.value.amount,()=>a.value.firstPaymentDate,()=>a.value.paymentPlan,()=>a.value.paymentPlanType,()=>{var t;return(t=_.value)==null?void 0:t.last_payment_date}],()=>{ve()},{immediate:!0}),J(()=>a.value.paymentPlanType,t=>{var e;t===g.PAY_FULL_AMOUNT_NOW?a.value.paymentPlan=c.FULL_AMOUNT:a.value.paymentPlan=((e=_.value)==null?void 0:e.default_payment_plan)||null,a.value.amount="",F.value=""});const ce=Ae(()=>{const t=D(),e=D("2025-09-27");return t.isAfter(e)?t.add(30,"day").toDate():D("2025-09-30").toDate()});return Le(()=>{W()}),(t,e)=>{const u=Ue,n=De,i=Ne,L=be,w=Pe,h=_e;return r(),x("div",null,[T(xe,{visible:ne.visible,title:C.value?"New Schedule":"Add New Property","onUpdate:visible":e[11]||(e[11]=d=>R("update:visible",d)),onClose:K},{content:y(()=>[l("div",Fe,[C.value?A("",!0):(r(),N(s(X),{key:0,class:"verify-form","validation-schema":s(ie),"initial-values":O.value,onSubmit:me},{default:y(()=>[T(s(k),{modelValue:O.value.propertyNumber,"onUpdate:modelValue":e[0]||(e[0]=d=>O.value.propertyNumber=d),as:"div",name:"propertyNumber",class:"form-group"},{default:y(({field:d,errorMessage:P})=>[l("div",Oe,[e[13]||(e[13]=l("label",{for:"propertyNumber",class:"form-label"},"Property Number:",-1)),T(u,{modelValue:d.value,"onUpdate:modelValue":U=>d.value=U,class:"w-full",disabled:C.value},null,8,["modelValue","onUpdate:modelValue","disabled"])]),P?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text verify-error"},{default:y(()=>[p(v(P),1)]),_:2},1024)):A("",!0)]),_:1},8,["modelValue"]),T(s(k),{modelValue:O.value.verificationCode,"onUpdate:modelValue":e[1]||(e[1]=d=>O.value.verificationCode=d),as:"div",name:"verificationCode",class:"form-group"},{default:y(({field:d,errorMessage:P})=>[l("div",we,[e[14]||(e[14]=l("label",{for:"verificationCode",class:"form-label"},"Verification Code:",-1)),T(u,{modelValue:d.value,"onUpdate:modelValue":U=>d.value=U,class:"w-full",disabled:C.value},null,8,["modelValue","onUpdate:modelValue","disabled"])]),P?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text verify-error"},{default:y(()=>[p(v(P),1)]),_:2},1024)):A("",!0)]),_:1},8,["modelValue"]),l("div",Ee,[C.value?(r(),N(i,{key:1,label:"VERIFIED",severity:"success",class:"verify-btn",disabled:""})):(r(),N(i,{key:0,type:"submit",label:"VERIFY",severity:"warn",class:"verify-btn",loading:H.value},null,8,["loading"]))])]),_:1},8,["validation-schema","initial-values"])),C.value?(r(),N(s(X),{key:1,class:"payment-form","validation-schema":s(ue),"initial-values":a.value,onSubmit:de},{default:y(()=>{var d,P,U,z,j;return[T(s(k),{modelValue:a.value.primaryPayment,"onUpdate:modelValue":e[3]||(e[3]=o=>a.value.primaryPayment=o),as:"div",name:"primaryPayment",class:"form-group"},{default:y(({field:o,errorMessage:b})=>[l("div",Se,[e[15]||(e[15]=l("label",{for:"primaryPayment",class:"form-label payment-label"},"Primary Payment Method",-1)),T(L,{modelValue:o.value,"onUpdate:modelValue":m=>o.value=m,options:q.value,"option-label":"label","option-value":"value",class:"w-full",placeholder:"Select or add payment method",fluid:"",size:"large",onValueChange:e[2]||(e[2]=m=>Q("first",m))},{option:y(m=>[l("div",Ye,[m.option.value?(r(),x("div",Ie,v(m.option.label),1)):(r(),x("div",Re,v(m.option.label),1))])]),_:2},1032,["modelValue","onUpdate:modelValue","options"])]),b?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text ml-[220px]"},{default:y(()=>[p(v(b),1)]),_:2},1024)):A("",!0)]),_:1},8,["modelValue"]),T(s(k),{modelValue:a.value.secondaryPayment,"onUpdate:modelValue":e[6]||(e[6]=o=>a.value.secondaryPayment=o),as:"div",name:"secondaryPayment",class:"form-group"},{default:y(({field:o,errorMessage:b})=>[l("div",qe,[e[16]||(e[16]=l("label",{for:"secondaryPayment",class:"form-label payment-label"},"Secondary Payment Method",-1)),T(L,{modelValue:o.value,"onUpdate:modelValue":m=>o.value=m,options:q.value,"option-label":"label","option-value":"value",class:"w-full",placeholder:"Select or add payment method",fluid:"",size:"large",onValueChange:e[5]||(e[5]=m=>Q("second",m))},{option:y(m=>[l("div",He,[m.option.value?(r(),x("div",$e,v(m.option.label),1)):(r(),x("div",{key:1,onClick:e[4]||(e[4]=M=>E.value=!0)},v(m.option.label),1))])]),_:2},1032,["modelValue","onUpdate:modelValue","options"])]),b?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text ml-[220px]"},{default:y(()=>[p(v(b),1)]),_:2},1024)):A("",!0),e[17]||(e[17]=l("div",{class:"tips-text"}," *Fallback option if the primary payment fails. Strongly recommended to avoid payment interruptions. ",-1))]),_:1,__:[17]},8,["modelValue"]),T(s(k),{modelValue:a.value.paymentPlanType,"onUpdate:modelValue":e[7]||(e[7]=o=>a.value.paymentPlanType=o),as:"div",name:"paymentPlanType",class:"form-group"},{default:y(({field:o,errorMessage:b,handleChange:m})=>[l("div",We,[e[18]||(e[18]=l("label",{for:"paymentPlanType",class:"form-label schedule-label"},[p("Payment Plan"),l("span",{class:"required"},"*"),p(":")],-1)),T(L,{modelValue:o.value,"onUpdate:modelValue":M=>o.value=M,size:"large","option-label":"label","option-value":"value",options:oe.value,class:"w-full",onValueChange:m},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"])]),b?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text ml-[160px]"},{default:y(()=>[p(v(b),1)]),_:2},1024)):A("",!0)]),_:1},8,["modelValue"]),[s(S).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,s(S).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(a.value.paymentPlanType)?(r(),N(s(k),{key:0,modelValue:a.value.paymentPlan,"onUpdate:modelValue":e[8]||(e[8]=o=>a.value.paymentPlan=o),as:"div",name:"paymentPlan",class:"form-group"},{default:y(({field:o,errorMessage:b,handleChange:m})=>[l("div",Be,[e[19]||(e[19]=l("label",{for:"paymentPlan",class:"form-label schedule-label"},[p("Payment Schedule"),l("span",{class:"required"},"*"),p(":")],-1)),T(L,{modelValue:o.value,"onUpdate:modelValue":M=>o.value=M,size:"large","option-label":"label","option-value":"value",options:se.value,class:"w-full",onValueChange:m},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"])]),b?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text ml-[160px]"},{default:y(()=>[p(v(b),1)]),_:2},1024)):A("",!0)]),_:1},8,["modelValue"])):A("",!0),T(s(k),{modelValue:a.value.firstPaymentDate,"onUpdate:modelValue":e[9]||(e[9]=o=>a.value.firstPaymentDate=o),as:"div",name:"firstPaymentDate",class:"form-group"},{default:y(({field:o,errorMessage:b,handleChange:m})=>[l("div",Ge,[e[20]||(e[20]=l("label",{for:"firstPaymentDate",class:"form-label schedule-label"},[p("First Payment Date"),l("span",{class:"required"},"*"),p(":")],-1)),l("div",Ke,[T(w,{modelValue:o.value,"onUpdate:modelValue":M=>o.value=M,"show-icon":"",fluid:"","icon-display":"input",class:"w-full","min-date":s(D)().add(3,"day").toDate(),"max-date":ce.value,"date-format":"dd/mm/yy",onValueChange:m},null,8,["modelValue","onUpdate:modelValue","min-date","max-date","onValueChange"]),b?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text"},{default:y(()=>[p(v(b),1)]),_:2},1024)):A("",!0)])])]),_:1},8,["modelValue"]),a.value.paymentPlanType===s(S).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT?(r(),N(s(k),{key:1,modelValue:a.value.amount,"onUpdate:modelValue":e[10]||(e[10]=o=>a.value.amount=o),as:"div",name:"amount",class:"form-group"},{default:y(({field:o,errorMessage:b,handleChange:m})=>{var M;return[l("div",Qe,[e[21]||(e[21]=l("label",{for:"amount",class:"form-label schedule-label"},[p("Amount"),l("span",{class:"required"},"*"),p(":")],-1)),l("div",ze,[T(h,{modelValue:o.value,"onUpdate:modelValue":fe=>o.value=fe,class:"w-full",fluid:"","min-fraction-digits":2,"max-fraction-digits":2,max:Number(((M=_.value)==null?void 0:M.total_amount_due)||0),onValueChange:m},null,8,["modelValue","onUpdate:modelValue","max","onValueChange"]),b?(r(),N(n,{key:0,severity:"error",variant:"simple",class:"error-text"},{default:y(()=>[p(v(b),1)]),_:2},1024)):A("",!0),a.value.amount?(r(),x("div",je,[Number(F.value)>0?(r(),x("p",Je," The last payment amount will be: "+v(s(Y).formatAmount(F.value))+" "+v(s(Y).formatAmount(F.value)),1)):Number(F.value)===0?(r(),x("p",Xe," All payments will be exactly "+v(s(Y).formatAmount(new(s(f))(a.value.amount).toFixed(2))),1)):A("",!0)])):A("",!0)])])]}),_:1},8,["modelValue"])):A("",!0),l("div",Ze,[l("div",ea,[e[22]||(e[22]=l("div",{class:"form-label schedule-label"}," Total Amount Due: ",-1)),l("div",aa,v((d=_.value)!=null&&d.total_amount_due?s(Y).formatAmount(new(s(f))(((P=_.value)==null?void 0:P.total_amount_due)||0).toFixed(2)):""),1)]),l("div",ta,[e[23]||(e[23]=l("div",{class:"form-label schedule-label"}," Last Payment Date: ",-1)),l("div",la,v((U=_.value)!=null&&U.last_payment_date?s(D)((z=_.value)==null?void 0:z.last_payment_date).format("DD/MM/YYYY"):""),1)]),a.value.paymentPlanType===s(S).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS?(r(),x("div",na,[e[24]||(e[24]=l("div",{class:"form-label schedule-label"}," Regular Payment Amount: ",-1)),l("div",oa,v(a.value.regularPaymentAmount?s(Y).formatAmount(new(s(f))(a.value.regularPaymentAmount||0).toFixed(2)):""),1)])):A("",!0),l("div",sa,[l("div",null,[p(" Number of payments during "+v(s(D)().year())+" ",1),(j=_.value)!=null&&j.last_payment_date?(r(),x(B,{key:0},[p(" - "+v(s(D)(_.value.last_payment_date).year()),1)],64)):A("",!0),e[25]||(e[25]=p(": "))]),l("div",ra,[a.value.paymentPlanType===s(S).Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW?(r(),x(B,{key:0},[p(" 1 ")],64)):(r(),x(B,{key:1},[p(v(a.value.numberOfPaymentDuring?a.value.numberOfPaymentDuring:""),1)],64))])])]),e[26]||(e[26]=l("div",{class:"important-notice"},[l("p",{class:"text-red-600 text-sm"}," Please Note that if your FlexiRates account accrues 3 dishonoured (declined) payments during the financial year, your FlexiRates registration will be cancelled. "),l("p",{class:"text-red-600 text-sm"}," The regular payment amount may differ from the original setup amount if a payment is skipped or fails. ")],-1)),l("div",ia,[T(i,{type:"submit",label:"CONFIRM",severity:"warn",class:"confirm-btn",loading:$.value},null,8,["loading"])])]}),_:1,__:[26]},8,["validation-schema","initial-values"])):A("",!0)])]),_:1},8,["visible","title"]),T(ge,{visible:E.value,"onUpdate:visible":e[12]||(e[12]=d=>E.value=d),"show-radio":!0,onClose:re},null,8,["visible"])])}}}),Oa=ke(ua,[["__scopeId","data-v-6c0accc1"]]);export{Oa as default};
