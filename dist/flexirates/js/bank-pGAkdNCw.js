import{g as e}from"./getAssetsFile-BMGmg7rM.js";const t=r=>{switch(r){case 1:return"Visa";case 2:return"Mastercard";case 3:return"American Express";case 4:return"Discover";case 5:return"Diners Club";case 0:case 6:return"Carte Blanche"}return""},c=r=>{switch(r){case 1:return e("merchant/card-type/visa.png");case 2:return e("merchant/card-type/mastercard.png");case 3:return e("merchant/card-type/amex.png");case 4:return e("merchant/card-type/discover.png");case 5:return e("merchant/card-type/diners.png");case 0:case 6:return e("merchant/card-type/carte-blanche.png")}return""},n=r=>{switch(r){case 1:return e("merchant/card-type/visa.png");case 2:return e("merchant/card-type/mastercard.png");case 0:case 6:return e("merchant/card-type/carte-blanche.png")}return""};export{t as a,c as b,n as g};
