import{s as J}from"./index--SCaD3QG.js";import{s as Q}from"./index-DdGtf4Je.js";import{d as X,K as Z,y as ee,i as u,p as te,c as b,o as c,b as k,h as t,m as d,t as a,f as F,u as f,e as n,s as se,F as I,r as z,z as ae,_ as le,g as m,E as ie,G as E,a as A,H as S,$ as oe}from"./index-Cr_XsrqN.js";import{t as ne,c as de,d as P}from"./index.esm-Qe0UyvbF.js";import{d as re}from"./dayjs.min-BN044Q0-.js";import{_ as T}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{u as G}from"./useDict-B_iRj5rJ.js";import{g as ue,d as ce,e as me,c as ve}from"./paymentMethod-CihfujB6.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";const fe={key:0,class:"card-header"},be={class:"card-title"},ye={class:"flex items-center gap-6"},_e={key:1,class:"details"},ge={class:"details-grid pb-8"},ke={class:"detail-item"},Ne={class:"detail-value"},xe={class:"detail-item"},we={class:"detail-value"},he={class:"detail-item"},De={class:"detail-value"},Ve={class:"detail-item"},Be={class:"detail-value"},Ce={class:"detail-item"},Ee={class:"detail-value"},Ae={class:"detail-item"},Se={class:"detail-value"},Pe={class:"payment-methods"},Te={class:"serial"},Le={class:"address"},Me={class:"nickname-section"},$e={class:"nickname-form"},qe={class:"dialog"},Ue={class:"w-[180px]"},Ye={class:"flex flex-col gap-y-4 mt-8"},Fe={class:"w-[660px] line-height-20"},Ie={class:"py-[10px] pl-[20px]"},ze={class:"w-[550px]"},Ge={class:"relative flex items-center"},Re={class:"relative flex items-center"},je={class:"relative flex items-center"},He={class:"tips-text"},Oe={class:"flex justify-end pt-6"},Ke=X({__name:"bankDetail",setup(We){const x=Z(),v=ee(),o=u(),_=u(""),w=u(!1),h=u(!1),{getLabel:R}=G("payment_method_status"),{getLabel:L}=G("weight"),M=async()=>{try{const l=await ue({id:Number(v==null?void 0:v.params.id)});o.value=l.data,_.value=l.data.nickname}catch(l){console.log(l)}},$=l=>re(l).format("DD MMM YYYY"),j=async()=>{if(_.value)try{w.value=!0,(await ve({id:Number(v==null?void 0:v.params.id),nickname:_.value})).code===0&&x.add({severity:"success",summary:"Tips",detail:"Successfully added Nickname .",life:3e3})}catch(l){console.log(l)}finally{w.value=!1}},g=u(!1),D=u(!1),N=u(!1),H=async()=>{try{h.value=!0,(await ce({id:Number(v==null?void 0:v.params.id)})).code===0?(x.add({severity:"success",summary:"Tips",detail:"Successfully delete card .",life:3e3}),oe.push({name:"paymentMethods"})):(g.value=!1,D.value=!0)}catch(l){console.log(l)}finally{h.value=!1}},O=u(),V=u(!1),y=u({bsb:"",accountName:"",accountNumber:""}),K=ne(de({bsb:P().required("BSB is required").max(6,"Maximum input of 6 digits."),accountName:P().required("Account Name is required"),accountNumber:P().required("Account Number is required")})),W=async l=>{const e={id:o.value.id,bsb:l.bsb,account_name:l.accountName,account_no:l.accountNumber};try{V.value=!0,(await me(e)).code===0&&(x.add({severity:"success",summary:"Tips",detail:"Successfully Update Bank Info.",life:3e3}),N.value=!1,M())}catch(p){console.log(p)}finally{V.value=!1}};return te(()=>{M()}),(l,e)=>{var q,U,Y;const p=se,B=Q,C=J;return c(),b("div",null,[o.value?(c(),b("div",fe,[t("div",be,[d(a((q=o.value)==null?void 0:q.account_name)+" ",1),t("span",{class:F(["status-indicator",{"status-inactive-indicator":o.value.status!==1}])},a(f(R)(o.value.status)),3)]),t("div",ye,[n(p,{label:"EDIT DETAILS",class:"btn",severity:"warn",onClick:e[0]||(e[0]=s=>N.value=!0)}),n(p,{label:"DELETE ACCOUNT",class:"btn",severity:"warn",onClick:e[1]||(e[1]=s=>g.value=!0)})])])):k("",!0),o.value?(c(),b("div",_e,[e[22]||(e[22]=t("h2",{class:"section-title"}," Details ",-1)),e[23]||(e[23]=t("div",{class:"divider"},null,-1)),t("div",ge,[t("div",ke,[e[10]||(e[10]=t("div",{class:"detail-label"}," Bank Account Name: ",-1)),t("div",Ne,a(o.value.account_name),1)]),t("div",xe,[e[11]||(e[11]=t("div",{class:"detail-label"}," Payment Method Added Date: ",-1)),t("div",we,a($(o.value.created_at)),1)]),t("div",he,[e[12]||(e[12]=t("div",{class:"detail-label"}," BSB: ",-1)),t("div",De,a(o.value.bsb),1)]),t("div",Ve,[e[13]||(e[13]=t("div",{class:"detail-label"}," Last Payment Date: ",-1)),t("div",Be,a($(o.value.last_payment)),1)]),t("div",Ce,[e[14]||(e[14]=t("div",{class:"detail-label"}," Account Number: ",-1)),t("div",Ee,a((U=o.value)==null?void 0:U.account_no),1)]),t("div",Ae,[e[15]||(e[15]=t("div",{class:"detail-label"}," Edit count: ",-1)),t("div",Se,a((Y=o.value)==null?void 0:Y.edit_times),1)])]),e[24]||(e[24]=t("h2",{class:"section-title-description"}," Linked properties ",-1)),t("div",Pe,[e[16]||(e[16]=t("div",{class:"section-description"}," Payment Preferences ",-1)),e[17]||(e[17]=t("div",{class:"divider"},null,-1)),(c(!0),b(I,null,z(o.value.customer_plan,(s,i)=>(c(),b("div",{key:s.id,class:"payment-method-row"},[t("div",Te,a(i+1),1),t("div",Le,a(s.street_address),1),t("div",{class:F(["payment-type",s.weight===1?"primary":"secondary"])},a(f(L)(s.weight))+" Payment Method ",3)]))),128))]),t("div",Me,[e[19]||(e[19]=t("h2",{class:"section-title"}," Add a Nickname ",-1)),e[20]||(e[20]=t("div",{class:"divider"},null,-1)),t("div",$e,[e[18]||(e[18]=t("div",{class:"w-60"}," NickName ",-1)),ae(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>_.value=s),type:"text",class:"nickname-input",placeholder:"Nickname"},null,512),[[le,_.value]]),n(p,{class:"save-button",severity:"warn",loading:w.value,label:"SAVE",onClick:j},null,8,["loading"])]),e[21]||(e[21]=t("div",{class:"help-text"}," Give this payment method a name to help you recognize it. ",-1))])])):k("",!0),t("div",qe,[n(T,{visible:g.value,"onUpdate:visible":e[4]||(e[4]=s=>g.value=s)},{content:m(()=>[t("div",Ue,[e[25]||(e[25]=t("div",{class:"font-bold text-2xl text-center text-[#031f73]"}," Are you sure you want to delete this Payment Method? ",-1)),t("div",Ye,[n(p,{class:"btn",label:"CANCEL",onClick:e[3]||(e[3]=s=>g.value=!1)}),n(p,{class:"btn",label:"YES",severity:"warn",loading:h.value,onClick:H},null,8,["loading"])])])]),_:1},8,["visible"]),n(T,{visible:D.value,title:"Notice","onUpdate:visible":e[5]||(e[5]=s=>D.value=s)},{content:m(()=>{var s;return[t("div",Fe,[e[26]||(e[26]=t("div",{class:"font-semibold text-xl pd-10"}," This Payment Method can not be deleted. ",-1)),e[27]||(e[27]=t("div",{class:"pd-10"}," It is currently linked to the following properties : ",-1)),t("div",Ie,[(c(!0),b(I,null,z((s=o.value)==null?void 0:s.customer_plan,(i,r)=>(c(),b("p",{key:i.id},a(r+1)+". "+a(i.street_address)+" ( "+a(f(L)(i.weight))+" ) ",1))),128))]),e[28]||(e[28]=t("div",{class:"pd-10"}," To remove this payment method, please update the payment method assigned to each property listed above. ",-1)),e[29]||(e[29]=t("div",{class:"font-semibold pd-10"}," How to update: ",-1)),e[30]||(e[30]=t("div",{class:"pd-10"},[t("p",null,[d(" Go to the "),t("span",{class:"font-semibold"},"Property Details"),d(' of each linked property and click "Change Payment Method." ')]),t("p",null,[d(" You can only choose from "),t("span",{class:"font-semibold"},"existing payment methods"),d(", so if you don’t have an alternative saved, please add a new one first via the Payment Methods section. ")])],-1))])]}),_:1},8,["visible"]),n(T,{visible:N.value,title:"Edit Bank Account Details","onUpdate:visible":e[9]||(e[9]=s=>N.value=s)},{content:m(()=>[t("div",ze,[n(f(ie),{ref_key:"formRef",ref:O,"validation-schema":f(K),onSubmit:W},{default:m(()=>{var s;return[n(f(E),{modelValue:y.value.bsb,"onUpdate:modelValue":e[6]||(e[6]=i=>y.value.bsb=i),class:"edit-form-item",as:"div",name:"bsb"},{default:m(({field:i,errorMessage:r})=>[t("div",Ge,[e[31]||(e[31]=t("label",{class:"edit-label"},[d("BSB Number"),t("span",{class:"required"},"*"),d(":")],-1)),n(B,S(i,{type:"text",class:"w-full"}),null,16),r?(c(),A(C,{key:0,class:"mt-2 edit-message",severity:"error",variant:"simple"},{default:m(()=>[d(a(r),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),n(f(E),{modelValue:y.value.accountName,"onUpdate:modelValue":e[7]||(e[7]=i=>y.value.accountName=i),class:"edit-form-item",as:"div",name:"accountName"},{default:m(({field:i,errorMessage:r})=>[t("div",Re,[e[32]||(e[32]=t("label",{class:"edit-label"},[d("Account Name"),t("span",{class:"required"},"*"),d(":")],-1)),n(B,S(i,{type:"text",class:"w-full"}),null,16),r?(c(),A(C,{key:0,class:"mt-2 edit-message",severity:"error",variant:"simple"},{default:m(()=>[d(a(r),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),n(f(E),{modelValue:y.value.accountNumber,"onUpdate:modelValue":e[8]||(e[8]=i=>y.value.accountNumber=i),class:"edit-form-item",as:"div",name:"accountNumber"},{default:m(({field:i,errorMessage:r})=>[t("div",je,[e[33]||(e[33]=t("label",{class:"edit-label"},[d("Account Number"),t("span",{class:"required"},"*"),d(":")],-1)),n(B,S(i,{type:"text",class:"w-full"}),null,16),r?(c(),A(C,{key:0,class:"mt-2 edit-message",severity:"error",variant:"simple"},{default:m(()=>[d(a(r),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),t("div",He," Please note: You can only update your bank account details up to 3 times for security reasons. Current Edit Count : "+a((s=o.value)==null?void 0:s.edit_times)+"/3 ",1),t("div",Oe,[n(p,{label:"SAVE",severity:"warn",type:"submit",class:"btn save-btn",loading:V.value},null,8,["loading"])])]}),_:1},8,["validation-schema"])])]),_:1},8,["visible"])])])}}}),ot=pe(Ke,[["__scopeId","data-v-1267a84c"]]);export{ot as default};
