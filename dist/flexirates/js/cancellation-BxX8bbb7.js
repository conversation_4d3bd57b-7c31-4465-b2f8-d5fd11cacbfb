import{_ as te}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{s as le}from"./index--SCaD3QG.js";import{d as se,i as s,x as ae,K as oe,p as ie,c as r,o as i,h as t,b as v,e as o,t as d,n as ne,s as re,u as m,g as C,E as de,m as k,G as ue,a as ce}from"./index-Cr_XsrqN.js";import{s as ve}from"./index-DdGtf4Je.js";import{s as me}from"./index-BH-zSNAS.js";import{F as P}from"./index-B1qA3Nva.js";import{t as fe,c as pe,d as ye}from"./index.esm-Qe0UyvbF.js";import{G as be}from"./index-Cm8e2Mjp.js";import{g as _e}from"./home-Bcs-QcZ3.js";import{c as ge,g as we}from"./support-BHa7CsES.js";import{a as Ce}from"./transactions-DCpZnSzG.js";import{_ as xe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-CQnajQNe.js";const Ve={class:"flexirates-wrap"},he={class:"mt-6 color-grey"},ke={class:"text-xl font-semibold mb-4"},Pe={key:0},Re={key:1},Te={key:0,class:"grid grid-cols-1 md:grid-cols-2 mt-4"},Se={class:"flex flex-col gap-4"},Ae={class:"flex"},$e={class:"flex"},Ee={class:"flex"},Ne={key:1,class:"reason mt-12"},De={key:0,class:"mt-4"},qe={key:2,class:"mt-6 flex justify-end"},Oe={key:0,class:"overview color-grey"},Fe={class:"grid grid-cols-1 gap-x-26 mt-5"},Ue={class:"overview-left"},Be={class:"mb-4"},Ie={class:"mb-4"},Le={class:"mb-4"},Ge={class:"mt-12"},He={class:"flex justify-between mt-12"},je={class:"dialog"},ze={class:"flex flex-col gap-8 mt-8 text-lg"},Ke={class:"font-semibold text-(--color-btn-warn)"},Me={key:0},Ye={class:"flex items-center mt-8"},Xe={class:"flex justify-end gap-4 mt-6"},Je=se({__name:"cancellation",setup(Qe){const f=s(),n=s(),p=s(""),c=s(!1),R=s(),u=s(),B=ae(),I=oe(),T=s(null),y=s(!1),L=l=>{l&&(b.value.google_token=l,y.value=!0)},G=()=>{y.value=!1},H=()=>{y.value=!1},x=s(!1),w=s(!1),j=fe(pe({verificationCode:ye().required("Verification Code is required")})),b=s({verificationCode:"",google_token:""}),z=s([{label:"Property Sold",value:"Property Sold"},{label:"Financial Hardship",value:"Financial Hardship"},{label:"Don't want to use",value:"Don't want to use"},{label:"Others",value:"Others"}]),K=async l=>{const e={remark:n.value==="Others"?p.value:n.value,property_id:f.value.value,verification_code:l.verificationCode};h.value=!0;try{(await ge(e)).code===0&&(B.push({name:"support"}),window.$toast.add({severity:"success",summary:"Success",detail:"Cancellation Request successfully!"}))}catch(_){console.log(_)}finally{h.value=!1}},M=async()=>{if(!f.value)return I.add({severity:"warn",summary:"Tips",detail:"Please select  a property!",life:3e3});x.value=!0;try{const l=await _e({id:f.value.value});l.code===0&&(u.value=l.data,c.value=!0)}catch(l){console.log(l)}finally{x.value=!1}},Y=l=>l===1?"#39b54a":l===5?"#eb001b":"#1b1548",V=s(!1),h=s(!1),X=async()=>{var l;y.value||window.$toast.add({severity:"warn",summary:"Tips",detail:"Please verify reCAPTCHA first!"}),n.value?n.value==="Others"&&!((l=p.value)!=null&&l.trim())?window.$toast.add({severity:"warn",summary:"Tips",detail:"Please enter a reason!"}):(V.value=!0,(await we()).code===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"The cancellation request verification code was sent successfully!"}),V.value=!1,w.value=!0)):window.$toast.add({severity:"warn",summary:"Tips",detail:"Please select a reason!"})},J=()=>{var l;b.value.google_token="",y.value=!1,(l=T.value)==null||l.reset(),w.value=!1};return ie(()=>{Ce({is_all:1,is_include_cancel:0}).then(l=>{R.value=l.data.map(e=>({label:e.street_address,value:e.id}))})}),(l,e)=>{var A,$,E,N,D,q,O,F;const _=me,S=ve,g=re,Q=le,W=te;return i(),r("div",null,[t("div",Ve,[e[10]||(e[10]=t("div",{class:"flexirates-title"},[t("div",{class:"flexirates-title-text"}," Cancellation Request ")],-1)),t("div",he,[t("div",ke,[c.value?(i(),r("div",Re," Property Details ")):(i(),r("div",Pe," Select Property: "))]),o(_,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=a=>f.value=a),"show-clear":!0,options:R.value,size:"large","option-label":"label",disabled:c.value,placeholder:"— Please Select —",class:"w-full md:w-180"},null,8,["modelValue","options","disabled"]),c.value?(i(),r("div",Te,[t("div",Se,[t("div",Ae,[e[6]||(e[6]=t("div",{class:"font-bold w-50"}," Property Number: ",-1)),t("div",null,d((A=u.value)==null?void 0:A.property_number),1)]),t("div",$e,[e[7]||(e[7]=t("div",{class:"font-bold w-50"}," Schedule Status: ",-1)),t("div",null,d((E=($=u.value)==null?void 0:$.schedule_details)==null?void 0:E.payment_plan_desc),1)]),t("div",Ee,[e[8]||(e[8]=t("div",{class:"font-bold w-50"}," Status: ",-1)),t("div",{style:ne({color:Y((N=u.value)==null?void 0:N.status)}),class:"font-bold"},d((D=u.value)==null?void 0:D.status_desc),5)])])])):v("",!0),c.value?(i(),r("div",Ne,[e[9]||(e[9]=t("div",{class:"text-xl font-semibold mb-4"},[t("div",null," Reason for Cancellation Request ")],-1)),o(_,{modelValue:n.value,"onUpdate:modelValue":e[1]||(e[1]=a=>n.value=a),"show-clear":!0,options:z.value,size:"large","option-label":"label","option-value":"value",placeholder:"— Please Select —",class:"w-full md:w-180"},null,8,["modelValue","options"]),n.value==="Others"?(i(),r("div",De,[o(S,{modelValue:p.value,"onUpdate:modelValue":e[2]||(e[2]=a=>p.value=a),name:"othersReason",class:"w-full md:w-180",type:"text",placeholder:"Please enter your reason"},null,8,["modelValue"])])):v("",!0)])):v("",!0),c.value?v("",!0):(i(),r("div",qe,[o(g,{label:"NEXT",severity:"warn",class:"btn",loading:x.value,onClick:M},null,8,["loading"])]))])]),c.value?(i(),r("div",Oe,[e[15]||(e[15]=t("div",{class:"overview-title text-xl font-semibold"}," Payment Overview ",-1)),t("div",Fe,[t("div",Ue,[t("div",Be,[e[11]||(e[11]=t("span",{class:"font-semibold mr-2"},"Total Rate Amount:",-1)),t("span",null,d(m(P).formatAmount((q=u.value)==null?void 0:q.total_amount)),1)]),t("div",Ie,[e[12]||(e[12]=t("span",{class:"font-semibold mr-2"},"Total Amount Paid:",-1)),t("span",null,d(m(P).formatAmount((O=u.value)==null?void 0:O.total_paid)),1)]),t("div",Le,[e[13]||(e[13]=t("span",{class:"font-semibold mr-2"},"Total Amount Pending:",-1)),t("span",null,d(m(P).formatAmount((F=u.value)==null?void 0:F.total_remaining)),1)]),t("div",Ge,[e[14]||(e[14]=t("div",{class:"font-semibold mb-3"}," *CAPTCHA ",-1)),o(be,{ref_key:"recaptchaRef",ref:T,class:"mb-2",onVerify:L,onExpired:G,onError:H},null,512)])])]),t("div",He,[o(g,{label:"BACK",class:"btn",onClick:e[3]||(e[3]=a=>l.$router.back())}),o(g,{label:"CANCEL REGISTRATION",severity:"warn",class:"btn",loading:V.value,onClick:X},null,8,["loading"])])])):v("",!0),t("div",je,[o(W,{title:"Cancellation Request Verification",visible:w.value,"onUpdate:visible":e[5]||(e[5]=a=>w.value=a)},{content:C(()=>[o(m(de),{"validation-schema":m(j),"initial-values":b.value,onSubmit:K},{default:C(()=>[t("div",ze,[t("div",null,[e[16]||(e[16]=k("You've selected ")),t("span",Ke,d(n.value),1)]),n.value==="Others"?(i(),r("div",Me,[e[17]||(e[17]=k(" Additional Note: ")),t("span",null,d(p.value),1)])):v("",!0),e[18]||(e[18]=t("div",null," To verify this request, we've sent a verification code to your email. ",-1)),e[19]||(e[19]=t("div",null," Please enter the code below to confirm your are the authorised user. ",-1))]),o(m(ue),{modelValue:b.value.verificationCode,"onUpdate:modelValue":e[4]||(e[4]=a=>b.value.verificationCode=a),as:"div",name:"verificationCode"},{default:C(({field:a,errorMessage:U,handleChange:Z})=>[t("div",Ye,[e[20]||(e[20]=t("div",{class:"font-bold w-80"}," Verification Code ",-1)),o(S,{modelValue:a.value,"onUpdate:modelValue":ee=>a.value=ee,name:"verificationCode",type:"text",class:"w-full",onValueChange:Z},null,8,["modelValue","onUpdate:modelValue","onValueChange"])]),U?(i(),ce(Q,{key:0,severity:"error",variant:"simple",class:"ml-51 mt-2"},{default:C(()=>[k(d(U),1)]),_:2},1024)):v("",!0)]),_:1},8,["modelValue"]),t("div",Xe,[o(g,{label:"CANCEL",class:"verify-btn btn",onClick:J}),o(g,{label:"VERIFY AND SUBMIT",severity:"warn",class:"verify-btn btn",type:"submit",loading:h.value},null,8,["loading"])])]),_:1},8,["validation-schema","initial-values"])]),_:1},8,["visible"])])])}}}),vt=xe(Je,[["__scopeId","data-v-ecee7d20"]]);export{vt as default};
