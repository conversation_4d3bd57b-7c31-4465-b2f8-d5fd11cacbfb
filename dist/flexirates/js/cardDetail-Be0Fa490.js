import{_ as w}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{d as S,K as B,y as $,i as r,p as z,c as n,o as d,b as N,h as s,e as u,m,t as i,f as D,u as _,s as U,F as M,r as C,z as F,_ as G,g as P,$ as I}from"./index-Cr_XsrqN.js";import{d as R}from"./dayjs.min-BN044Q0-.js";import{u as L}from"./useDict-B_iRj5rJ.js";import{g as j,d as H,c as K}from"./paymentMethod-CihfujB6.js";import{_ as O}from"./_plugin-vue_export-helper-DlAUqK2U.js";const W={key:0,class:"card-header"},q={class:"card-title"},J={key:1,class:"details"},Q={class:"details-grid pb-8"},X={class:"detail-item"},Z={class:"detail-value"},ee={class:"detail-item"},se={class:"detail-value"},te={class:"detail-item"},ae={class:"detail-value"},le={class:"detail-item"},ie={class:"detail-value"},oe={class:"payment-methods"},ne={class:"serial"},de={class:"address"},re={class:"nickname-section"},ue={class:"nickname-form"},ve={class:"dialog"},ce={class:"w-[180px]"},me={class:"flex flex-col gap-y-4 mt-8"},pe={class:"w-[660px] leading-[20px]"},ye={class:"py-[10px] pl-[20px]"},fe=S({__name:"cardDetail",setup(be){const x=B(),o=$(),a=r(),v=r(""),y=r(!1),f=r(!1),{getLabel:E}=L("payment_method_status"),{getLabel:k}=L("weight"),T=async()=>{try{const l=await j({id:Number(o==null?void 0:o.params.id)});a.value=l.data,v.value=l.data.nickname}catch(l){console.log(l)}},h=l=>R(l).format("DD MMM YYYY"),V=async()=>{if(v.value)try{y.value=!0,(await K({id:Number(o==null?void 0:o.params.id),nickname:v.value})).code===0&&x.add({severity:"success",summary:"Tips",detail:"Successfully added Nickname .",life:3e3})}catch(l){console.log(l)}finally{y.value=!1}},c=r(!1),b=r(!1),Y=async()=>{try{f.value=!0,(await H({id:Number(o==null?void 0:o.params.id)})).code===0?(x.add({severity:"success",summary:"Tips",detail:"Successfully delete card .",life:3e3}),I.push({name:"paymentMethods"})):(c.value=!1,b.value=!0)}catch(l){console.log(l)}finally{f.value=!1}};return z(()=>{T()}),(l,e)=>{const p=U,A=w;return d(),n("div",null,[a.value?(d(),n("div",W,[s("div",q,[m(i(a.value.account_name)+" ",1),s("span",{class:D(["status-indicator",{"status-inactive-indicator":a.value.status!==1}])},i(_(E)(a.value.status)),3)]),u(p,{label:"DELETE CARD",class:"delete-button",severity:"warn",onClick:e[0]||(e[0]=t=>c.value=!0)})])):N("",!0),a.value?(d(),n("div",J,[e[15]||(e[15]=s("h2",{class:"section-title"}," Details ",-1)),e[16]||(e[16]=s("div",{class:"divider"},null,-1)),s("div",Q,[s("div",X,[e[5]||(e[5]=s("div",{class:"detail-label"}," Card Number: ",-1)),s("div",Z,i(a.value.account_no),1)]),s("div",ee,[e[6]||(e[6]=s("div",{class:"detail-label"}," Payment Method Added Date: ",-1)),s("div",se,i(h(a.value.created_at)),1)]),s("div",te,[e[7]||(e[7]=s("div",{class:"detail-label"}," Expiry (MM/YY): ",-1)),s("div",ae,i(a.value.expiration_month)+"/"+i(a.value.expiration_year),1)]),s("div",le,[e[8]||(e[8]=s("div",{class:"detail-label"}," Last Payment Date: ",-1)),s("div",ie,i(h(a.value.last_payment)),1)])]),e[17]||(e[17]=s("h2",{class:"section-title-description"}," Linked properties ",-1)),s("div",oe,[e[9]||(e[9]=s("div",{class:"section-description"}," Payment Preferences ",-1)),e[10]||(e[10]=s("div",{class:"divider"},null,-1)),(d(!0),n(M,null,C(a.value.customer_plan,(t,g)=>(d(),n("div",{key:t.id,class:"payment-method-row"},[s("div",ne,i(g+1),1),s("div",de,i(t.street_address),1),s("div",{class:D(["payment-type",t.weight===1?"primary":"secondary"])},i(_(k)(t.weight))+" Payment Method ",3)]))),128))]),s("div",re,[e[12]||(e[12]=s("h2",{class:"section-title"}," Add a Nickname ",-1)),e[13]||(e[13]=s("div",{class:"divider"},null,-1)),s("div",ue,[e[11]||(e[11]=s("div",{class:"w-60"}," NickName ",-1)),F(s("input",{"onUpdate:modelValue":e[1]||(e[1]=t=>v.value=t),type:"text",class:"nickname-input",placeholder:"Nickname"},null,512),[[G,v.value]]),u(p,{class:"save-button",severity:"warn",loading:y.value,label:"SAVE",onClick:V},null,8,["loading"])]),e[14]||(e[14]=s("div",{class:"help-text"}," Give this payment method a name to help you recognize it. ",-1))])])):N("",!0),s("div",ve,[u(w,{visible:c.value,"onUpdate:visible":e[3]||(e[3]=t=>c.value=t)},{content:P(()=>[s("div",ce,[e[18]||(e[18]=s("div",{class:"font-bold text-2xl text-center text-[#031f73]"}," Are you sure you want to delete this Payment Method? ",-1)),s("div",me,[u(p,{class:"btn",label:"CANCEL",onClick:e[2]||(e[2]=t=>c.value=!1)}),u(p,{class:"btn",label:"YES",severity:"warn",loading:f.value,onClick:Y},null,8,["loading"])])])]),_:1},8,["visible"]),u(A,{visible:b.value,title:"Notice","onUpdate:visible":e[4]||(e[4]=t=>b.value=t)},{content:P(()=>[s("div",pe,[e[19]||(e[19]=s("div",{class:"font-semibold text-xl py-[10px]"}," This Payment Method can not be deleted. ",-1)),e[20]||(e[20]=s("div",{class:"py-[10px]"}," It is currently linked to the following properties : ",-1)),s("div",ye,[(d(!0),n(M,null,C(a.value.customer_plan,(t,g)=>(d(),n("p",{key:t.id},i(g+1)+". "+i(t.street_address)+" ( "+i(_(k)(t.weight))+" ) ",1))),128))]),e[21]||(e[21]=s("div",{class:"py-[10px]"}," To remove this payment method, please update the payment method assigned to each property listed above. ",-1)),e[22]||(e[22]=s("div",{class:"font-semibold py-[10px]"}," How to update: ",-1)),e[23]||(e[23]=s("div",{class:"py-[10px]"},[s("p",null,[m(" Go to the "),s("span",{class:"font-semibold"},"Property Details"),m(' of each linked property and click "Change Payment Method." ')]),s("p",null,[m(" You can only choose from "),s("span",{class:"font-semibold"},"existing payment methods"),m(", so if you don’t have an alternative saved, please add a new one first via the Payment Methods section. ")])],-1))])]),_:1},8,["visible"])])])}}}),Ne=O(fe,[["__scopeId","data-v-c0ae63a3"]]);export{Ne as default};
