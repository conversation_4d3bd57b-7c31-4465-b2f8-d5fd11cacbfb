import{a6 as c,Y as i,i as u,j as f}from"./index-Cr_XsrqN.js";const v=()=>c("/getCountry",{baseURL:"/api/merchant"}),m=i("country",()=>{const r=u([]),e=u(!1),t=u(!1),a=u(null),n=async()=>{if(!(t.value||e.value)){e.value=!0,a.value=null;try{const{code:s,data:l}=await v();s===0?(r.value=l,t.value=!0):a.value="Failed to fetch countries"}catch(s){a.value=s instanceof Error?s.message:"Unknown error occurred"}finally{e.value=!1}}},o=()=>{r.value=[],t.value=!1,a.value=null};return{countries:f(()=>(!t.value&&!e.value&&n(),r.value)),isLoading:e,isLoaded:t,error:a,countriesData:r,fetchCountries:n,resetCountries:o}},{persist:!1});export{m as u};
