import{_ as L}from"./BaseDataTable-BjLz7Fg0.js";import{b as A}from"./bank-pGAkdNCw.js";import{d as U,c as h,o as n,b as d,u as r,h as o,n as H,F as X,t as i,y as G,i as c,p as J,a as y,e as f,s as Q,g as a,m as w,f as Z}from"./index-Cr_XsrqN.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{F as O}from"./index-B1qA3Nva.js";import{d as W}from"./dayjs.min-BN044Q0-.js";import{u as ee}from"./useExport-BWofQ1KN.js";import{g as te,e as se}from"./schedule-CTKLwp03.js";import{_ as oe}from"./editScheduleDialog.vue_vue_type_script_setup_true_lang-BCUnm19q.js";import re from"./oneOffPaymentDialog-CfQfKkyv.js";import"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import"./index-CQnajQNe.js";import"./index-BH-zSNAS.js";import"./index-DdGtf4Je.js";import"./index-BiHyKo52.js";import"./index-fEC-lipk.js";import"./index-Dvu_aI8r.js";import"./index-DhbL6JTo.js";import"./index-BwvvB-v3.js";import"./date-B2oFx4oA.js";import"./getAssetsFile-BMGmg7rM.js";import"./Website-CKuRBN-m.js";import"./flex-login-logo-mini-BGXreUGR.js";import"./property-BTlta3pB.js";import"./register-payment-method-CcrZD2od.js";import"./register-payment-CcrZD2od.js";import"./logo-white-DjPdav-u.js";import"./logo-CO0W3x5n.js";import"./ScheduleEditDialog-B9CEujOc.js";import"./index-BMUxa2Nh.js";import"./index-DsAxJM5H.js";import"./index--SCaD3QG.js";import"./decimal-D7roSeZH.js";import"./index-D2QnTskR.js";import"./index.esm-Qe0UyvbF.js";import"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import"./property-4kw8xdA_.js";import"./transactions-DCpZnSzG.js";import"./index.vue_vue_type_style_index_0_lang-4VPfa-Ly.js";import"./addCardOrBank-CpEoqTrU.js";import"./index-5-OuhCK9.js";import"./paymentMethod-CihfujB6.js";import"./country-ZCPlDRUV.js";import"./home-Bcs-QcZ3.js";const ie={class:"base-card-type"},ae={key:0,class:"base-card-type__icon"},le=["src"],ne={class:"base-card-type__text"},pe=U({__name:"BaseCardType",props:{text:{default:""},cardType:{default:null},isShowCardNumber:{type:Boolean,default:!1}},setup(C){const t=C,p=l=>l==null?void 0:l.slice(-4),v=l=>{switch(l){case 1:return{width:"40px",maxWidth:"40px",height:"28px",borderRadius:"4px"};case 2:return{width:"28px",maxWidth:"28px",height:"28px",borderRadius:"4px"};case 3:return{width:"42px",maxWidth:"42px",height:"28px",borderRadius:"4px"};case 4:return{width:"34px",maxWidth:"34px",height:"32px",borderRadius:"4px"};case 5:return{width:"34px",maxWidth:"34px",height:"28px",borderRadius:"4px"};case 0:case 6:return{width:"28px",maxWidth:"28px",height:"28px"}}return{width:"24px",height:"24px",borderRadius:"4px"}};return(l,x)=>(n(),h("div",ie,[t.cardType!==null&&r(A)(t.cardType)?(n(),h("div",ae,[o("img",{style:H(v(t.cardType)),src:r(A)(t.cardType),alt:"icon"},null,12,le)])):d("",!0),t.isShowCardNumber&&t.text&&p(t.text)?(n(),h(X,{key:1},[x[0]||(x[0]=o("span",{class:"base-card-type__text"}," •••• ",-1)),o("div",ne,i(p(t.text)),1)],64)):d("",!0)]))}}),de=I(pe,[["__scopeId","data-v-cff4858a"]]),me={class:"schedule-detail"},ue={class:"bg-white dark:bg-gray-800 p-6 px-8 rounded-2xl flex items-center"},ce={class:"header-title"},fe={class:"title line-clamp-1"},xe={class:"text-(color:--color-gray-500)"},_e={class:"flex flex-col md:flex-row gap-2 md:gap-6"},ye={class:"bg-white dark:bg-gray-800 rounded-2xl p-6 px-8 mt-4"},he={class:"flex items-center justify-between mb-4"},ve={class:"flex flex-col md:flex-row gap-2 md:gap-6"},ge={class:"underline"},be=U({__name:"details",setup(C){const t=G(),p=c(!1),{handleExport:v,isExporting:l}=ee({exportFn:se,getParams:()=>({id:t.params.id})}),x=c([{field:"due_date",header:"Due Date",style:{width:"14%"},template:"dueDate"},{field:"amount",header:"Amount",template:"amount",style:{width:"14%"}},{field:"",header:"Payment Method",template:"paymentMethod",style:{width:"14%"}},{field:"frequency",header:"Frequency",style:{width:"14%"}},{field:"",header:"Status",template:"status",style:{width:"14%"}},{field:"",header:"Failed Payment",template:"failedPayment",style:{width:"14%"}},{field:"",header:"Edit Count",template:"editCount",style:{width:"14%"}}]),k=c([]),m=c(!1),_=c(!1),j=()=>{m.value=!1,g()},g=async()=>{p.value=!0;try{const{code:b,data:s}=await te(Number(t.params.id));b===0&&(k.value=s)}finally{p.value=!1}};return J(()=>{g()}),(b,s)=>{var D,T,E,M,$,B,S,F,R,q,V,Y,N,P;const u=Q,z=de,K=L;return n(),h("div",me,[o("div",ue,[o("div",ce,[o("div",fe,[s[6]||(s[6]=o("span",{class:"text-(color:--color-blue-500)"}," Office: ",-1)),o("span",xe,i((T=(D=r(t))==null?void 0:D.query)==null?void 0:T.address),1)]),o("div",_e,[((M=(E=r(t))==null?void 0:E.query)==null?void 0:M.status)==="1"?(n(),y(u,{key:0,label:"MAKE A ONE-OFF PAYMENT",severity:"warn",onClick:s[0]||(s[0]=e=>_.value=!0)})):d("",!0),((B=($=r(t))==null?void 0:$.query)==null?void 0:B.status)==="1"?(n(),y(u,{key:1,label:"EDIT SCHEDULE",severity:"warn",onClick:s[1]||(s[1]=e=>m.value=!0)})):d("",!0),f(u,{class:"w-35",label:"BACK",severity:"warn",onClick:s[2]||(s[2]=e=>b.$router.back())})])])]),o("div",ye,[o("div",he,[s[7]||(s[7]=o("div",{class:"flex items-center gap-2 flex-1"},[o("span",{class:"text-(color:--color-blue-500) font-bold text-3xl"}," Schedules ")],-1)),o("div",ve,[f(u,{label:"EXPORT",class:"w-35",severity:"warn",loading:r(l),onClick:s[3]||(s[3]=e=>r(v)("csv"))},null,8,["loading"])])]),f(K,{value:k.value,columns:x.value,loading:p.value,"show-multiple-column":!1,paginator:!1,"show-search-bar":!1,"data-key":"id",onRefresh:g},{amount:a(({data:e})=>[w(i(r(O).formatAmount(e==null?void 0:e.amount)),1)]),dueDate:a(({data:e})=>[o("span",ge,i(r(W)(e==null?void 0:e.due_date).format("DD MMM YYYY")),1)]),paymentMethod:a(({data:e})=>[f(z,{"card-type":e==null?void 0:e.credit_brand,"is-show-card-number":!0,text:e==null?void 0:e.account_no},null,8,["card-type","text"])]),failTimes:a(({data:e})=>[w(i(r(O).formatAmount(e==null?void 0:e.fail_times)),1)]),status:a(({data:e})=>[o("span",{class:Z(["font-medium",{"text-red-500":e.status_desc==="Failed","text-green-500":e.status_desc==="Success","text-orange-500":e.status_desc==="Upcoming"}])},i(e==null?void 0:e.status_desc),3)]),newDate:a(({data:e})=>[w(i(r(W)(e.next_process_date).format("DD MMM YYYY")),1)]),failedPayment:a(({data:e})=>[o("span",null,i(e.failed_times)+" of "+i(e.failable_times),1)]),editCount:a(({data:e})=>[o("span",null,i(e.edited_times)+" of "+i(e.editable_count),1)]),"empty-action":a(()=>[f(u,{label:"Refresh"})]),_:1},8,["value","columns","loading"])]),((F=(S=r(t))==null?void 0:S.query)==null?void 0:F.status)==="1"&&m.value?(n(),y(oe,{key:0,visible:m.value,"onUpdate:visible":s[4]||(s[4]=e=>m.value=e),"property-id":Number((q=(R=r(t))==null?void 0:R.query)==null?void 0:q.related_id),onConfirm:j},null,8,["visible","property-id"])):d("",!0),((Y=(V=r(t))==null?void 0:V.query)==null?void 0:Y.status)==="1"&&_.value?(n(),y(re,{key:1,modelValue:_.value,"onUpdate:modelValue":s[5]||(s[5]=e=>_.value=e),"property-id":Number((P=(N=r(t))==null?void 0:N.query)==null?void 0:P.related_id)},null,8,["modelValue","property-id"])):d("",!0)])}}}),ut=I(be,[["__scopeId","data-v-071fe714"]]);export{ut as default};
