import{S as m}from"./ScheduleEditDialog-B9CEujOc.js";import{d as u,i as l,p as y,a as v,o as b}from"./index-Cr_XsrqN.js";import{a as g}from"./transactions-DCpZnSzG.js";const D=u({__name:"editScheduleDialog",props:{visible:{type:Boolean},propertyId:{}},emits:["update:visible","confirm"],setup(p,{emit:n}){const t=p,r=n,i=l(),s=l([]),c=async()=>{try{const e=await g();s.value=e.data.map(o=>{var a;return{label:o.street_address??"",value:((a=o.id)==null?void 0:a.toString())??""}})}catch(e){console.log("Failed to fetch property list:",e)}},d=()=>{r("confirm")},f=e=>{r("update:visible",e)};return y(()=>{var e;c(),t.propertyId&&((e=i.value)==null||e.setPropertyDetailAndLoad(String(t.propertyId)))}),(e,o)=>(b(),v(m,{ref_key:"editScheduleDialogRef",ref:i,visible:t.visible,"property-list":s.value,"onUpdate:visible":f,onConfirm:d},null,8,["visible","property-list"]))}});export{D as _};
