import{s as O}from"./index-DdGtf4Je.js";import{d as G,i as c,y as Z,c as m,f as _,F as y,h as a,e as l,u as p,E as F,g as u,s as D,P as K,Q,G as S,b as w,a as V,t as h,m as C,o as r}from"./index-Cr_XsrqN.js";import{s as W}from"./index--SCaD3QG.js";import{s as Y}from"./index-CEK2t3I_.js";import{t as L,c as N,d as T,g as H}from"./index.esm-Qe0UyvbF.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-CQnajQNe.js";import"./index-6Y5dfxc3.js";const X={class:"form-item__content flex flex-col gap-y-2"},ss={class:"password-strength"},es={key:0,class:"password-strength__progress"},ts={key:1,class:"password-strength__progress"},as={class:"password-strength-tips"},os={class:"password-strength__text"},ls={class:"form-item__content flex flex-col gap-y-2"},rs={class:"form-tools"},ns={class:"form-tools-item"},is={class:"form-item__content flex flex-col gap-y-2"},ds={class:"form-tools"},cs={class:"form-tools-item"},ms={class:"flex justify-center flex-wrap forget-password-submit-wrap"},us={class:"flex justify-center mt-4"},ps={key:1,class:"is-send-success"},fs={class:"flex justify-center mt-4"},ws=G({__name:"forgetPassword",emits:["changeShowType"],setup(vs,{emit:U}){const P=U,R=c(window.location.href),I=Z(),k=c(I.query.forgetPasswordToken||""),$=c(!1),b=c(!1),x=c(!1),n=c("Low"),E=c(),B=c(),q=L(N({email:T().email().required()})),A=L(N({newPassword:T().required("Password required").min(8,"At least 8 characters").test("lowercase","Must include a lowercase letter",e=>/[a-z]/.test(e||"")).test("uppercase","Must include an uppercase letter",e=>/[A-Z]/.test(e||"")).test("number","Must include a number",e=>/\d/.test(e||"")).test("symbol","Must include a symbol",e=>/[!@#$%^&*(),.?":{}|<>]/.test(e||"")),confirmPassword:T().oneOf([H("newPassword")],"Passwords must match").required("Confirm password")})),j=e=>{if(!e){n.value="Low";return}const s=/[a-z]/.test(e),i=/[A-Z]/.test(e),d=/\d/.test(e),f=/[!@#$%^&*(),.?":{}|<>]/.test(e),g=e.length;let t=0;g>=8&&t++,s&&t++,i&&t++,d&&t++,f&&t++,g>=12&&t++,t<=2?n.value="Low":t<=4?n.value="Medium":n.value="Excellent"},z=async()=>{var s,i;const e=await((s=E.value)==null?void 0:s.validate());if(e!=null&&e.valid){b.value=!0;try{const{code:d}=await K({email:(i=e.values)==null?void 0:i.email,path:R.value});d===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Email sent successfully",life:3e3}),$.value=!0)}finally{b.value=!1}}},M=async()=>{var s,i;const e=await((s=B.value)==null?void 0:s.validate());if(e!=null&&e.valid){x.value=!0;try{const{code:d}=await Q({token:k.value,new_password:(i=e.values)==null?void 0:i.newPassword});d===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Password reset successfully"}),P("changeShowType","login"))}finally{x.value=!1}}};return(e,s)=>{const i=Y,d=W,f=D,g=O;return r(),m("div",{class:_(["forget-password",{"has-token":k.value}])},[k.value?(r(),m(y,{key:0},[s[3]||(s[3]=a("h1",{class:"forget-password__title"}," New Password ",-1)),s[4]||(s[4]=a("p",{class:"forget-password__desc"}," Enter a new password. ",-1)),l(p(F),{ref_key:"newPasswordFormRef",ref:B,"validation-schema":p(A),"initial-values":{newPassword:"",confirmPassword:""},class:"new-password-form",onSubmit:M},{default:u(()=>[l(p(S),{name:"newPassword",as:"div",class:"form-item"},{default:u(({field:t,errorMessage:o})=>[a("div",X,[l(i,{modelValue:t.value,"onUpdate:modelValue":v=>t.value=v,class:_([{"p-invalid":o,"p-valid":t.value&&!o},"form-input"]),name:"newPassword",placeholder:"New Password*","toggle-mask":!0,"input-props":{autocomplete:"new-password"},feedback:!1,onValueChange:j},null,8,["modelValue","onUpdate:modelValue","class"]),t.value?(r(),m(y,{key:0},[a("div",ss,[a("div",{class:_(["password-strength__bar",n.value==="Low"?"password-strength__bar--low":n.value==="Medium"?"password-strength__bar--medium":"password-strength__bar--strong"])},[s[2]||(s[2]=a("div",{class:"password-strength__progress"},null,-1)),n.value==="Medium"||n.value==="Excellent"?(r(),m("div",es)):w("",!0),n.value==="Excellent"?(r(),m("div",ts)):w("",!0)],2)]),a("div",as,[a("span",os,"Password strength: "+h(n.value),1),o?(r(),V(d,{key:0,severity:"error",variant:"simple"},{default:u(()=>[C(h(o),1)]),_:2},1024)):w("",!0)])],64)):w("",!0)])]),_:1}),l(p(S),{name:"confirmPassword",as:"div",class:"form-item mt-12"},{default:u(({field:t,errorMessage:o})=>[a("div",ls,[l(i,{modelValue:t.value,"onUpdate:modelValue":v=>t.value=v,class:_([{"p-invalid":o},"form-input"]),name:"confirmPassword",placeholder:"Re-enter Password*","toggle-mask":!0,"input-props":{autocomplete:"new-password"},feedback:!1},null,8,["modelValue","onUpdate:modelValue","class"]),o?(r(),V(d,{key:0,class:"!mt-2 !ml-6",severity:"error",variant:"simple"},{default:u(()=>[C(h(o),1)]),_:2},1024)):w("",!0)])]),_:1}),a("div",rs,[a("div",ns,[l(f,{loading:x.value,type:"submit",severity:"info",label:"SUBMIT",class:"new-password-submit w-full"},null,8,["loading"])])])]),_:1},8,["validation-schema"])],64)):(r(),m(y,{key:1},[$.value?(r(),m("div",ps,[s[7]||(s[7]=a("h1",{class:"title"}," Check Your Inbox ",-1)),s[8]||(s[8]=a("p",{class:"forgot-subtitle"}," We have sent password recovery instructions to your email. ",-1)),a("div",fs,[l(f,{class:"back-to-login",severity:"primary",label:"BACK TO LOGIN",onClick:s[1]||(s[1]=t=>P("changeShowType","login"))})])])):(r(),m(y,{key:0},[s[5]||(s[5]=a("h1",{class:"forget-password__title"}," Forgot Password ",-1)),s[6]||(s[6]=a("p",{class:"forget-password__desc"}," Enter your email and we’ll send you a link to reset your password. ",-1)),l(p(F),{ref_key:"formRef",ref:E,"validation-schema":p(q),class:"forget-password-form",onSubmit:z},{default:u(()=>[l(p(S),{name:"email",as:"div",class:"form-item"},{default:u(({field:t,errorMessage:o})=>[a("div",is,[l(g,{modelValue:t.value,"onUpdate:modelValue":v=>t.value=v,class:_([{"p-invalid":o},"form-input"]),name:"email",type:"text",placeholder:"Email"},null,8,["modelValue","onUpdate:modelValue","class"]),o?(r(),V(d,{key:0,class:"!mt-2 !ml-6",severity:"error",variant:"simple"},{default:u(()=>[C(h(o),1)]),_:2},1024)):w("",!0)])]),_:1}),a("div",ds,[a("div",cs,[a("div",ms,[l(f,{loading:b.value,type:"submit",severity:"info",label:"SUBMIT",class:"forget-password-submit"},null,8,["loading"])]),a("div",us,[l(f,{class:"!p-0 back-to-login",text:"",label:"Back to Login",onClick:s[0]||(s[0]=t=>P("changeShowType","login"))})])])])]),_:1},8,["validation-schema"])],64))],64))],2)}}}),Vs=J(ws,[["__scopeId","data-v-ec06cfe0"]]);export{Vs as default};
