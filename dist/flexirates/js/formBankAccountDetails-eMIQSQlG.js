import{s as x}from"./index-5-OuhCK9.js";import{s as C}from"./index--SCaD3QG.js";import{s as F}from"./index-DdGtf4Je.js";import{t as S}from"./vee-validate-zod-Bde0vWNn.js";import{d as D,D as E,i as P,a as i,o as n,g as d,e as o,h as s,u as m,G as b,b as c,H as V,m as f,t as p,E as T}from"./index-Cr_XsrqN.js";import{o as $,s as v}from"./types-C7gnwoCB.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";const _={class:"field mb-4"},O={class:"form-row mb-4"},R={class:"field mb-4"},g={class:"field mb-4"},j={class:"field mb-4"},q={class:"field mb-4"},G={class:"form-row mb-4"},H={class:"field mb-4"},z={class:"field mb-4"},J={class:"form-row mb-4"},K={class:"field mb-4"},Q={class:"field mb-4"},W={class:"field mb-4"},X={class:"field mb-4"},Y={class:"field mb-4"},Z=D({__name:"formBankAccountDetails",props:{readonly:{type:Boolean,default:!1}},setup(r,{expose:A}){const t=E({companyName:"",firstName:"",lastName:"",addressLine1:"",addressLine2:"",city:"",state:"",postcode:"",email:"",bsbNumber:"",accountNumber:"",bankAccountName:""}),N=P(),L=S($({companyName:v().optional(),firstName:v().min(2,"First name must be at least 2 characters"),lastName:v().min(2,"Last name must be at least 2 characters"),addressLine1:v().min(5,"Address must be at least 5 characters"),addressLine2:v().optional(),city:v().min(2,"City must be at least 2 characters"),state:v().min(2,"State is required"),postcode:v().regex(/^\d{4}$/,"Postcode must be 4 digits"),email:v().email("Please enter a valid email address"),bsbNumber:v().regex(/^\d{3}-\d{3}$/,"BSB number must be in the format 123-456"),accountNumber:v().regex(/^\d{6,10}$/,"Account number must be between 6-10 digits"),bankAccountName:v().min(2,"Bank account name must be at least 2 characters")})),w=async()=>{var e;return await((e=N.value)==null?void 0:e.validate())};return A({validate:w,resetForm:()=>{var k;(k=N.value)==null||k.resetForm()},submitForm:async()=>{const k=await w();return k.valid?k.values:!1},getCurrentValues:()=>{var k;return(k=N.value)==null?void 0:k.values}}),(k,e)=>{const y=F,u=C,B=x;return n(),i(m(T),{ref_key:"formRef",ref:N,"validation-schema":m(L),"initial-values":t,class:"space-y-6"},{default:d(()=>[o(m(b),{modelValue:t.companyName,"onUpdate:modelValue":e[0]||(e[0]=l=>t.companyName=l),name:"companyName"},{default:d(({field:l,errorMessage:a})=>[s("div",_,[e[12]||(e[12]=s("label",{class:"mb-2 block"},"Company Name (Optional)",-1)),o(y,V(l,{placeholder:"Company name",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),s("div",O,[o(m(b),{modelValue:t.firstName,"onUpdate:modelValue":e[1]||(e[1]=l=>t.firstName=l),as:"div",name:"firstName",class:"form-col"},{default:d(({field:l,errorMessage:a})=>[s("div",R,[e[13]||(e[13]=s("label",{class:"mb-2 block"},"First Name",-1)),o(y,V(l,{placeholder:"First name",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),o(m(b),{modelValue:t.lastName,"onUpdate:modelValue":e[2]||(e[2]=l=>t.lastName=l),as:"div",name:"lastName",class:"form-col"},{default:d(({field:l,errorMessage:a})=>[s("div",g,[e[14]||(e[14]=s("label",{class:"mb-2 block"},"Last Name",-1)),o(y,V(l,{placeholder:"Last name",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),o(m(b),{modelValue:t.addressLine1,"onUpdate:modelValue":e[3]||(e[3]=l=>t.addressLine1=l),name:"addressLine1"},{default:d(({field:l,errorMessage:a})=>[s("div",j,[e[15]||(e[15]=s("label",{class:"mb-2 block"},"Address Line 1",-1)),o(y,V(l,{placeholder:"Address line 1",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),o(m(b),{modelValue:t.addressLine2,"onUpdate:modelValue":e[4]||(e[4]=l=>t.addressLine2=l),name:"addressLine2"},{default:d(({field:l,errorMessage:a})=>[s("div",q,[e[16]||(e[16]=s("label",{class:"mb-2 block"},"Address Line 2 (Optional)",-1)),o(y,V(l,{placeholder:"Address line 2",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),s("div",G,[o(m(b),{modelValue:t.city,"onUpdate:modelValue":e[5]||(e[5]=l=>t.city=l),as:"div",name:"city",class:"form-col"},{default:d(({field:l,errorMessage:a})=>[s("div",H,[e[17]||(e[17]=s("label",{class:"mb-2 block"},"City",-1)),o(y,V(l,{placeholder:"City",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),o(m(b),{modelValue:t.state,"onUpdate:modelValue":e[6]||(e[6]=l=>t.state=l),as:"div",name:"state",class:"form-col"},{default:d(({field:l,errorMessage:a})=>[s("div",z,[e[18]||(e[18]=s("label",{class:"mb-2 block"},"State",-1)),o(y,V(l,{placeholder:"State",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),s("div",J,[o(m(b),{modelValue:t.postcode,"onUpdate:modelValue":e[7]||(e[7]=l=>t.postcode=l),as:"div",name:"postcode",class:"form-col"},{default:d(({field:l,errorMessage:a})=>[s("div",K,[e[19]||(e[19]=s("label",{class:"mb-2 block"},"Postcode",-1)),o(y,V(l,{placeholder:"Postcode",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),o(m(b),{modelValue:t.email,"onUpdate:modelValue":e[8]||(e[8]=l=>t.email=l),as:"div",name:"email",class:"form-col"},{default:d(({field:l,errorMessage:a})=>[s("div",Q,[e[20]||(e[20]=s("label",{class:"mb-2 block"},"Email Address",-1)),o(y,V(l,{placeholder:"Email address",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),o(m(b),{modelValue:t.bsbNumber,"onUpdate:modelValue":e[9]||(e[9]=l=>t.bsbNumber=l),name:"bsbNumber"},{default:d(({field:l,errorMessage:a,handleChange:U})=>[s("div",W,[e[21]||(e[21]=s("label",{class:"mb-2 block"},"BSB Number",-1)),o(B,{"model-value":l.value,placeholder:"Enter BSB number (no dashes, 6 digits)",mask:"999-999",class:"w-full",disabled:r.readonly,"onUpdate:modelValue":U},null,8,["model-value","disabled","onUpdate:modelValue"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),o(m(b),{modelValue:t.accountNumber,"onUpdate:modelValue":e[10]||(e[10]=l=>t.accountNumber=l),name:"accountNumber"},{default:d(({field:l,errorMessage:a})=>[s("div",X,[e[22]||(e[22]=s("label",{class:"mb-2 block"},"Account Number",-1)),o(y,V(l,{placeholder:"Enter bank account number",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),o(m(b),{modelValue:t.bankAccountName,"onUpdate:modelValue":e[11]||(e[11]=l=>t.bankAccountName=l),name:"bankAccountName"},{default:d(({field:l,errorMessage:a})=>[s("div",Y,[e[23]||(e[23]=s("label",{class:"mb-2 block"},"Bank Account Name",-1)),o(y,V(l,{placeholder:"Name of bank account",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),a?(n(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(a),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),_:1},8,["validation-schema","initial-values"])}}}),ne=I(Z,[["__scopeId","data-v-edb678c2"]]);export{ne as default};
