import{s as E}from"./index-BH-zSNAS.js";import{s as $}from"./index-5-OuhCK9.js";import{s as R}from"./index--SCaD3QG.js";import{s as G}from"./index-DdGtf4Je.js";import{t as H}from"./vee-validate-zod-Bde0vWNn.js";import{d as j,i as P,C as z,D as F,w as J,c as K,o as p,e as t,g as s,h as r,u as m,G as u,a as f,b as y,H as V,m as v,t as _,E as Q}from"./index-Cr_XsrqN.js";import{u as W}from"./country-ZCPlDRUV.js";import{o as X,s as c}from"./types-C7gnwoCB.js";import{_ as Z}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CQnajQNe.js";import"./index-BiHyKo52.js";const M={class:"field mb-4 mt-4"},ee={class:"form-row mb-4"},le={class:"field"},ae={class:"field"},te={class:"field mb-4"},se={class:"billing-address-form mt-6"},oe={class:"field mb-4"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},ie={class:"field"},re={class:"field"},ne={class:"field mb-4"},me={class:"field mb-4"},ue={class:"field mb-4"},ce={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"},pe={class:"field"},fe={class:"field"},ye={class:"field"},ve={class:"field mb-4"},_e={class:"field mb-4"},Ve=j({__name:"formPayment",setup(be,{expose:I}){const g=P(),L=W(),{countries:k,isLoading:U,isLoaded:O}=z(L),x=P(),o=F({country:"",first_name:"",last_name:"",company:"",address_line1:"",address_line2:"",city:"",state:"",postal_code:"",phone:""}),b=F({cardNumber:"",expiryDate:"",securityCode:"",nameOnCard:"",email:""}),Y=H(X({cardNumber:c().min(13,"Card number must be between 13 and 19 digits").max(19,"Card number must be between 13 and 19 digits").refine(n=>{const l=n.replace(/\D/g,"");let d=0,i=!1;for(let C=l.length-1;C>=0;C--){let h=Number.parseInt(l.charAt(C));i&&(h*=2,h>9&&(h-=9)),d+=h,i=!i}return d%10===0},"Invalid card number"),expiryDate:c().regex(/^\d{2}\/\d{2}$/,"Expiry date must be in MM/YY format").refine(n=>{const[l,d]=n.split("/"),i=new Date,C=i.getFullYear()%100,h=i.getMonth()+1,e=Number.parseInt(l),a=Number.parseInt(d);return e<1||e>12?!1:!(a<C||a===C&&e<h)},"Card has expired or invalid date"),securityCode:c().regex(/^\d{3,4}$/,"Security code must be 3 or 4 digits"),nameOnCard:c().min(2,"Name must be at least 2 characters").refine(n=>n.includes(" "),"Please enter your full name as it appears on the card"),email:c().email("Invalid email"),country:c().min(1,"Country/Region is required"),firstName:c().min(1,"First name is required"),lastName:c().min(1,"Last name is required"),company:c().optional(),address:c().min(1,"Address is required"),addressLine2:c().optional(),city:c().min(1,"City is required"),state:c().min(1,"State/territory is required"),postcode:c().min(1,"Postcode is required"),phone:c().min(1,"Phone is required")})),D=async()=>{var l;return await((l=g.value)==null?void 0:l.validate())},A=()=>{var n;(n=g.value)==null||n.resetForm()},S=async()=>{const n=await D();return n.valid?n.values:!1},B=()=>{var n;return(n=g.value)==null?void 0:n.values},q=(n,l=!1)=>{l||(o.state="");const d=k.value.find(i=>i.iso2===n);d&&d.state&&d.state.length>0?x.value=d==null?void 0:d.state:x.value=[]},T=J(()=>O,n=>{n&&o.country&&(q(o.country,!0),T())},{immediate:!0});return I({validate:D,resetForm:A,submitForm:S,getCurrentValues:B}),(n,l)=>{const d=G,i=R,C=$,h=E;return p(),K("div",null,[t(m(Q),{ref_key:"formRef",ref:g,"validation-schema":m(Y),class:"space-y-6",onSubmit:S},{default:s(()=>[l[17]||(l[17]=r("span",{class:"text-gray-600 mb-4 italic"}," All fields are required unless marked as optional. ",-1)),t(m(u),{modelValue:b.cardNumber,"onUpdate:modelValue":l[0]||(l[0]=e=>b.cardNumber=e),name:"cardNumber"},{default:s(({field:e,errorMessage:a})=>[r("div",M,[t(d,V(e,{placeholder:"Card Number",class:"w-full"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),r("div",ee,[t(m(u),{modelValue:b.expiryDate,"onUpdate:modelValue":l[1]||(l[1]=e=>b.expiryDate=e),as:"div",name:"expiryDate",class:"form-col"},{default:s(({field:e,errorMessage:a})=>[r("div",le,[t(C,{"model-value":e.value,placeholder:"Expiration date (MM/YY)",mask:"99/99",class:"w-full"},null,8,["model-value"]),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),t(m(u),{modelValue:b.securityCode,"onUpdate:modelValue":l[2]||(l[2]=e=>b.securityCode=e),as:"div",name:"securityCode",class:"form-col"},{default:s(({field:e,errorMessage:a})=>[r("div",ae,[t(d,V(e,{placeholder:"Please enter the CVV(Security code)",class:"w-full"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"])]),t(m(u),{modelValue:b.nameOnCard,"onUpdate:modelValue":l[3]||(l[3]=e=>b.nameOnCard=e),name:"nameOnCard"},{default:s(({field:e,errorMessage:a})=>[r("div",te,[t(d,V(e,{placeholder:"Name on card",class:"w-full"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),r("div",se,[l[15]||(l[15]=r("h3",{class:"text-xl font-semibold mb-4"}," Billing address ",-1)),l[16]||(l[16]=r("p",{class:"text-gray-600 mb-4"}," Select the address that matches your card or payment method. ",-1)),r("div",oe,[t(m(u),{modelValue:o.country,"onUpdate:modelValue":l[4]||(l[4]=e=>o.country=e),name:"country"},{default:s(({field:e,errorMessage:a,handleChange:N})=>[t(h,{id:"country",modelValue:e.value,"onUpdate:modelValue":[w=>e.value=w,w=>{N(w),q(w)}],class:"w-full",options:m(k),"option-label":"name","option-value":"iso2",placeholder:"Select country",loading:m(U),filter:"","show-clear":""},null,8,["modelValue","onUpdate:modelValue","options","loading"]),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),r("div",de,[r("div",ie,[t(m(u),{modelValue:o.first_name,"onUpdate:modelValue":l[5]||(l[5]=e=>o.first_name=e),name:"firstName"},{default:s(({field:e,errorMessage:a})=>[t(d,V(e,{id:"firstName",class:"w-full",placeholder:"First name"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),r("div",re,[t(m(u),{modelValue:o.last_name,"onUpdate:modelValue":l[6]||(l[6]=e=>o.last_name=e),name:"lastName"},{default:s(({field:e,errorMessage:a})=>[t(d,V(e,{id:"lastName",class:"w-full",placeholder:"Last name"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])])]),r("div",ne,[t(m(u),{modelValue:o.company,"onUpdate:modelValue":l[7]||(l[7]=e=>o.company=e),name:"company"},{default:s(({field:e})=>[t(d,V(e,{id:"company",class:"w-full",placeholder:"Company (optional)"}),null,16)]),_:1},8,["modelValue"])]),r("div",me,[t(m(u),{modelValue:o.address_line1,"onUpdate:modelValue":l[8]||(l[8]=e=>o.address_line1=e),name:"address"},{default:s(({field:e,errorMessage:a})=>[t(d,V(e,{id:"address",class:"w-full",placeholder:"Address"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),r("div",ue,[t(m(u),{modelValue:o.address_line2,"onUpdate:modelValue":l[9]||(l[9]=e=>o.address_line2=e),name:"addressLine2"},{default:s(({field:e})=>[t(d,V(e,{id:"addressLine2",class:"w-full",placeholder:"Address line 2 (optional)"}),null,16)]),_:1},8,["modelValue"])]),r("div",ce,[r("div",pe,[t(m(u),{modelValue:o.city,"onUpdate:modelValue":l[10]||(l[10]=e=>o.city=e),name:"city"},{default:s(({field:e,errorMessage:a})=>[t(d,V(e,{id:"city",class:"w-full",placeholder:"City"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),r("div",fe,[t(m(u),{modelValue:o.state,"onUpdate:modelValue":l[11]||(l[11]=e=>o.state=e),name:"state"},{default:s(({field:e,errorMessage:a,handleChange:N})=>[t(h,{id:"state",class:"w-full","model-value":e.value,options:x.value,"option-label":"name","option-value":"name",placeholder:"Select state",loading:m(U),disabled:!o.country,filter:"","onUpdate:modelValue":N},null,8,["model-value","options","loading","disabled","onUpdate:modelValue"]),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),r("div",ye,[t(m(u),{modelValue:o.postal_code,"onUpdate:modelValue":l[12]||(l[12]=e=>o.postal_code=e),name:"postcode"},{default:s(({field:e,errorMessage:a})=>[t(d,V(e,{id:"postcode",class:"w-full",placeholder:"Postcode"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])])]),r("div",ve,[t(m(u),{modelValue:o.phone,"onUpdate:modelValue":l[13]||(l[13]=e=>o.phone=e),name:"phone"},{default:s(({field:e,errorMessage:a})=>[t(d,V(e,{id:"phone",class:"w-full",placeholder:"Phone (required)"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),t(m(u),{modelValue:b.email,"onUpdate:modelValue":l[14]||(l[14]=e=>b.email=e),name:"email"},{default:s(({field:e,errorMessage:a})=>[r("div",_e,[t(d,V(e,{placeholder:"Billing Email",class:"w-full"}),null,16),a?(p(),f(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[v(_(a),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"])])]),_:1,__:[17]},8,["validation-schema"])])}}}),Pe=Z(Ve,[["__scopeId","data-v-49a52219"]]);export{Pe as default};
