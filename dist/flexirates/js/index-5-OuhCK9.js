import{a1 as f,H as d,aa as h,ab as c,a as p,o as m,f as g}from"./index-Cr_XsrqN.js";import{s as k}from"./index-DdGtf4Je.js";import{s as v}from"./index-BiHyKo52.js";var b={root:function(e){var s=e.instance;return["p-inputmask",{"p-filled":s.$filled}]}},y=f.extend({name:"inputmask",classes:b}),P={name:"BaseInputMask",extends:v,props:{slotChar:{type:String,default:"_"},id:{type:String,default:null},class:{type:[String,Object],default:null},mask:{type:String,default:null},placeholder:{type:String,default:null},autoClear:{type:Boolean,default:!0},unmask:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1}},style:y,provide:function(){return{$pcInputMask:this,$parentInstance:this}}},$={name:"InputMask",extends:P,inheritAttrs:!1,emits:["focus","blur","keydown","complete","keypress","paste"],inject:{$pcFluid:{default:null}},data:function(){return{currentVal:""}},watch:{mask:function(e,s){s!==e&&this.initMask()},disabled:function(e,s){e!==s&&this.updateValue()}},mounted:function(){this.initMask()},updated:function(){this.isValueUpdated()&&this.updateValue()},methods:{onInput:function(e){e.isComposing||(this.androidChrome?this.handleAndroidInput(e):this.handleInputChange(e),this.updateModelValue(e.target.value))},onFocus:function(e){var s=this;if(!this.readonly){if(this.focus=!0,this.focusText=this.$el.value,!this.$el.value||this.$el.value===this.defaultBuffer)requestAnimationFrame(function(){s.$el===document.activeElement&&s.caret(0,0)});else{var t=this.checkVal();this.caretTimeoutId=setTimeout(function(){s.$el===document.activeElement&&(s.writeBuffer(),t===s.mask.replace("?","").length?s.caret(0,t):s.caret(t))},10)}this.$emit("focus",e)}},onBlur:function(e){var s,t;if(this.focus=!1,this.checkVal(),this.updateModelValue(e.target.value),this.$el.value!==this.focusText){var i=document.createEvent("HTMLEvents");i.initEvent("change",!0,!1),this.$el.dispatchEvent(i)}this.$emit("blur",e),(s=(t=this.formField).onBlur)===null||s===void 0||s.call(t,e)},onKeyDown:function(e){if(!this.readonly){var s=e.code,t,i,a,l=/iphone/i.test(h());this.oldVal=this.$el.value,s==="Backspace"||s==="Delete"||l&&s==="Escape"?(t=this.caret(),i=t.begin,a=t.end,a-i===0&&(i=s!=="Delete"?this.seekPrev(i):a=this.seekNext(i-1),a=s==="Delete"?this.seekNext(a):a),this.clearBuffer(i,a),this.shiftL(i,a-1),this.updateModelValue(e.target.value),e.preventDefault()):s==="Enter"?(this.$el.blur(),this.updateModelValue(e.target.value)):s==="Escape"&&(this.$el.value=this.focusText,this.caret(0,this.checkVal()),this.updateModelValue(e.target.value),e.preventDefault()),this.$emit("keydown",e)}},onKeyPress:function(e){var s=this;if(!this.readonly){var t=e.code,i=this.caret(),a,l,r,u;if(!(e.ctrlKey||e.altKey||e.metaKey||e.shiftKey||e.key==="CapsLock"||e.key==="Escape"||e.key==="Tab")){if(t&&t!=="Enter"){if(i.end-i.begin!==0&&(this.clearBuffer(i.begin,i.end),this.shiftL(i.begin,i.end-1)),a=this.seekNext(i.begin-1),a<this.len&&(l=e.key,this.tests[a].test(l))){if(this.shiftR(a),this.buffer[a]=l,this.writeBuffer(),r=this.seekNext(a),/android/i.test(h())){var o=function(){s.caret(r)};setTimeout(o,0)}else this.caret(r);i.begin<=this.lastRequiredNonMaskPos&&(u=this.isCompleted())}e.preventDefault()}this.updateModelValue(e.target.value),u&&this.$emit("complete",e),this.$emit("keypress",e)}}},onPaste:function(e){this.handleInputChange(e),this.$emit("paste",e)},caret:function(e,s){var t,i,a;if(!(!this.$el.offsetParent||this.$el!==document.activeElement))if(typeof e=="number")i=e,a=typeof s=="number"?s:i,this.$el.setSelectionRange?this.$el.setSelectionRange(i,a):this.$el.createTextRange&&(t=this.$el.createTextRange(),t.collapse(!0),t.moveEnd("character",a),t.moveStart("character",i),t.select());else return this.$el.setSelectionRange?(i=this.$el.selectionStart,a=this.$el.selectionEnd):document.selection&&document.selection.createRange&&(t=document.selection.createRange(),i=0-t.duplicate().moveStart("character",-1e5),a=i+t.text.length),{begin:i,end:a}},isCompleted:function(){for(var e=this.firstNonMaskPos;e<=this.lastRequiredNonMaskPos;e++)if(this.tests[e]&&this.buffer[e]===this.getPlaceholder(e))return!1;return!0},getPlaceholder:function(e){return e<this.slotChar.length?this.slotChar.charAt(e):this.slotChar.charAt(0)},seekNext:function(e){for(;++e<this.len&&!this.tests[e];);return e},seekPrev:function(e){for(;--e>=0&&!this.tests[e];);return e},shiftL:function(e,s){var t,i;if(!(e<0)){for(t=e,i=this.seekNext(s);t<this.len;t++)if(this.tests[t]){if(i<this.len&&this.tests[t].test(this.buffer[i]))this.buffer[t]=this.buffer[i],this.buffer[i]=this.getPlaceholder(i);else break;i=this.seekNext(i)}this.writeBuffer(),this.caret(Math.max(this.firstNonMaskPos,e))}},shiftR:function(e){var s,t,i,a;for(s=e,t=this.getPlaceholder(e);s<this.len;s++)if(this.tests[s])if(i=this.seekNext(s),a=this.buffer[s],this.buffer[s]=t,i<this.len&&this.tests[i].test(a))t=a;else break},handleAndroidInput:function(e){var s=this.$el.value,t=this.caret();if(this.oldVal&&this.oldVal.length&&this.oldVal.length>s.length){for(this.checkVal(!0);t.begin>0&&!this.tests[t.begin-1];)t.begin--;if(t.begin===0)for(;t.begin<this.firstNonMaskPos&&!this.tests[t.begin];)t.begin++;this.caret(t.begin,t.begin)}else{for(this.checkVal(!0);t.begin<this.len&&!this.tests[t.begin];)t.begin++;this.caret(t.begin,t.begin)}this.isCompleted()&&this.$emit("complete",e)},clearBuffer:function(e,s){var t;for(t=e;t<s&&t<this.len;t++)this.tests[t]&&(this.buffer[t]=this.getPlaceholder(t))},writeBuffer:function(){this.$el.value=this.buffer.join("")},checkVal:function(e){this.isValueChecked=!0;var s=this.$el.value,t=-1,i,a,l;for(i=0,l=0;i<this.len;i++)if(this.tests[i]){for(this.buffer[i]=this.getPlaceholder(i);l++<s.length;)if(a=s.charAt(l-1),this.tests[i].test(a)){this.buffer[i]=a,t=i;break}if(l>s.length){this.clearBuffer(i+1,this.len);break}}else this.buffer[i]===s.charAt(l)&&l++,i<this.partialPosition&&(t=i);return e?this.writeBuffer():t+1<this.partialPosition?this.autoClear||this.buffer.join("")===this.defaultBuffer?(this.$el.value&&(this.$el.value=""),this.clearBuffer(0,this.len)):this.writeBuffer():(this.writeBuffer(),this.$el.value=this.$el.value.substring(0,t+1)),this.partialPosition?i:this.firstNonMaskPos},handleInputChange:function(e){var s=e.type==="paste";if(!(this.readonly||s)){var t=this.checkVal(!0);this.caret(t),this.updateModelValue(e.target.value),this.isCompleted()&&this.$emit("complete",e)}},getUnmaskedValue:function(){for(var e=[],s=0;s<this.buffer.length;s++){var t=this.buffer[s];this.tests[s]&&t!==this.getPlaceholder(s)&&e.push(t)}return e.join("")},unmaskValue:function(e){for(var s=[],t=e.split(""),i=0;i<t.length;i++){var a=t[i];this.tests[i]&&a!==this.getPlaceholder(i)&&s.push(a)}return s.join("")},updateModelValue:function(e){if(this.currentVal!==e){var s=this.unmask?this.unmaskValue(e):e;this.currentVal=e,this.writeValue(this.defaultBuffer!==s?s:"")}},updateValue:function(){var e=this,s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.$el&&(this.d_value==null?(this.$el.value="",s&&this.updateModelValue("")):(this.$el.value=this.d_value,this.checkVal(),setTimeout(function(){e.$el&&(e.writeBuffer(),e.checkVal(),s&&e.updateModelValue(e.$el.value))},10)),this.focusText=this.$el.value)},initMask:function(){this.tests=[],this.partialPosition=this.mask?this.mask.length:0,this.len=this.mask?this.mask.length:0,this.firstNonMaskPos=null,this.defs={9:"[0-9]",a:"[A-Za-z]","*":"[A-Za-z0-9]"};var e=h();this.androidChrome=/chrome/i.test(e)&&/android/i.test(e);for(var s=this.mask?this.mask.split(""):"",t=0;t<s.length;t++){var i=s[t];i==="?"?(this.len--,this.partialPosition=t):this.defs[i]?(this.tests.push(new RegExp(this.defs[i])),this.firstNonMaskPos===null&&(this.firstNonMaskPos=this.tests.length-1),t<this.partialPosition&&(this.lastRequiredNonMaskPos=this.tests.length-1)):this.tests.push(null)}this.buffer=[];for(var a=0;a<s.length;a++){var l=s[a];l!=="?"&&(this.defs[l]?this.buffer.push(this.getPlaceholder(a)):this.buffer.push(l))}this.defaultBuffer=this.buffer.join(""),this.updateValue(!1)},isValueUpdated:function(){return this.unmask?this.d_value!=this.getUnmaskedValue():this.defaultBuffer!==this.$el.value&&this.$el.value!==this.d_value}},computed:{inputClass:function(){return[this.cx("root"),this.class]},rootPTOptions:function(){return{root:d(this.ptm("pcInputText",this.ptmParams).root,this.ptmi("root",this.ptmParams))}},ptmParams:function(){return{context:{filled:this.$filled}}}},components:{InputText:k}};function V(n,e,s,t,i,a){var l=c("InputText");return m(),p(l,{id:n.id,value:i.currentVal,class:g(a.inputClass),readonly:n.readonly,disabled:n.disabled,invalid:n.invalid,size:n.size,name:n.name,variant:n.variant,placeholder:n.placeholder,fluid:n.$fluid,unstyled:n.unstyled,onInput:a.onInput,onCompositionend:a.onInput,onFocus:a.onFocus,onBlur:a.onBlur,onKeydown:a.onKeyDown,onKeypress:a.onKeyPress,onPaste:a.onPaste,pt:a.rootPTOptions},null,8,["id","value","class","readonly","disabled","invalid","size","name","variant","placeholder","fluid","unstyled","onInput","onCompositionend","onFocus","onBlur","onKeydown","onKeypress","onPaste","pt"])}$.render=V;export{$ as s};
