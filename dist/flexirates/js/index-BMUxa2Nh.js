import{d as l,a8 as s,a as u,g as d,k as c,h as e,m as t,o as y}from"./index-Cr_XsrqN.js";import{_ as h}from"./_plugin-vue_export-helper-DlAUqK2U.js";const b=l({__name:"index",props:{modelValue:{type:Boolean,required:!0,default:!1,type:Boolean},modelModifiers:{}},emits:["update:modelValue"],setup(i){const n=s(i,"modelValue");return(f,o)=>{const a=c;return y(),u(a,{visible:n.value,"onUpdate:visible":o[0]||(o[0]=r=>n.value=r),modal:!0,header:"Definitions",style:{width:"80vw"},draggable:!1,resizable:!1},{default:d(()=>o[1]||(o[1]=[e("div",{class:"terms-and-conditions-content"},[e("p",null,[e("strong",null,"account"),t(" means the account held at your Financial institution from which we are authorised to arrange for funds to be debited."),e("br"),e("strong",null,"service agreement"),t(" refers to this Demo Council and/or Bill Buddy Pty Ltd direct debit request (DDR) service agreement."),e("br"),e("strong",null,"business day"),t(" means a day other than a Saturday or a Sunday or a public holiday listed throughout Australia."),e("br"),e("strong",null,"debit day"),t(" means the day that payment by you to us is due."),e("br"),e("strong",null,"debit payment"),t(" means a particular transaction where a debit payment is made."),e("br"),e("strong",null,"direct debit request (DDR)"),t(" means the Demo Council and/or Bill Buddy Pty Ltd direct debit request application lodged by you to us."),e("br"),e("strong",null,"us"),t(" or "),e("strong",null,"we"),t(" means the Demo Council and/or Bill Buddy Pty Ltd, you have authorised by signing the direct debit request."),e("br"),e("strong",null,"you"),t(" means the customer who signed the direct debit request."),e("br"),e("strong",null,"your financial institution"),t(" means is the financial institution where you hold the account that you have authorised us to arrange to debit. ")]),e("p"),e("ol",null,[e("li",null,[e("strong",null,"DEBITING YOUR ACCOUNT"),e("ol",null,[e("li",null," 1.1 Request and authorise Demo Council direct debit request (User ID 487683) or Integrated Payment Solutions Pty Ltd (User ID 613603) a wholly owned subsidiary of Bill Buddy Pty Ltd, you have authorised us to arrange for funds to be debited from your account through the Bulk Electronic Clearing System from an account held at the financial institution identified below subject to the terms and conditions of the Direct Debit Service Agreement "),e("li",null," 1.2 You should refer to the direct debit request and this service agreement for the terms of the arrangement between us and you. "),e("li",null," 1.3 We will only arrange for funds to be debited from your account as authorised in the direct debit request. "),e("li",null," 1.4 If the debit day falls on a day that is not a business day, we may direct your financial institution to debit your account on the following business day. "),e("li",null," 1.5 Any debit which is dishonoured will be reversed from the rate account and any dishonour fee charged to council will be passed on to your account. This fee may vary from time to time according to charges by Council Bankers. "),e("li",null," 1.6 Upon the third dishonoured payment in your schedule, this FlexiRates agreement will be cancelled. You will be notified of this cancellation by email, and you will be required to pay your rates using another payment method. For rates payment options visit www.flexirates.com.au. ")]),e("br"),t(" If you are unsure about which day your account has or will be debited you should ask your financial institution. "),e("br"),e("br")]),e("li",null,[e("strong",null,"ALTERATIONS BY US"),e("ol",null,[e("li",null," 2.1 We may vary any details of this service agreement or the direct debit request at any time by giving you at least fourteen (14) days written notice. ")]),e("br")]),e("li",null,[e("strong",null,"ALTERATIONS BY YOU"),e("ol",null,[e("li",null," 3.1 Subject to 3.2 and 3.3, you may change the arrangements in your direct debit request. "),e("li",null," 3.2 If you wish to stop, defer or vary a debit payment you must notify us in writing at least fourteen (14) days before the next debit day. This notice should be given to us in the first instance. "),e("li",null," 3.3 You may also cancel your authority for us to debit your account at any time by giving us fourteen (14) days written notice before the next debit date. This notice should be given to us in the first instance. ")]),e("br")]),e("li",null,[e("strong",null,"YOUR OBLIGATIONS"),e("ol",null,[e("li",null," 4.1 It is your responsibility to ensure that there are sufficient clear funds available in your account to allow a debit payment to be made in accordance with the direct debit request. "),e("li",null,[t(" 4.2 If there are insufficient clear funds in your account to meet a debit payment: "),e("ol",{style:{"list-style-type":"lower-alpha"}},[e("li",null,"you may be charged a fee and/or interest by your financial institution;"),e("li",null,"you may also incur fees and charges imposed or incurred by us; and"),e("li",null," you must arrange for the debit payment to be made by another method or arrange sufficient clear funds to be in your account by an agreed time so that we can process the debit payment. ")])]),e("li",null," 4.3 You should check your account statement to verify that the amounts debited from your account are correct. "),e("li",null," 4.4 You must notify us in writing fourteen (14) days before the debit date if your account is transferred or closed. ")]),e("br")]),e("li",null,[e("strong",null,"DISPUTE"),e("ol",null,[e("li",null," 5.1 If you believe there has been an error in debiting your account, you should notify us directly on 1300 787 624 during business hours and confirm that notice in writing with us as soon as possible so that we can resolve your query more quickly. "),e("li",null," 5.2 If we conclude as a result of our investigations that your account has been incorrectly debited we will respond to your query by arranging a refund for the debited amount. We will also notify you in writing of the amount refunded. "),e("li",null," 5.3 If we conclude as a result of our investigations that your account has not been incorrectly debited we will respond to your query by providing you with reasons and any evidence for this finding. "),e("li",null," 5.4 Any queries you may have about an error made in debiting your account should be directed to us in the first instance so that we can attempt to resolve the matter between us and you. If we cannot resolve the matter you can still refer it to your financial institution which will obtain details from you of the disputed transaction and may lodge a claim on your behalf. ")]),e("br")]),e("li",null,[e("strong",null,"ACCOUNTS"),e("p",{class:"my-4"}," You should check: "),e("ol",{style:{"list-style-type":"lower-alpha"}},[e("li",null," with your financial institution whether direct debiting is available from your account as direct debiting is not available on all accounts offered by financial institutions. "),e("li",null," your account details which you have provided to us are correct by checking them against a recent account statement; and "),e("li",null," with your financial institution before completing the direct debit request if you have any queries about how to complete the direct debit request. ")]),e("br")]),e("li",null,[e("strong",null,"CONFIDENTIALITY"),e("ol",null,[e("li",null," 7.1 We will keep any information (including your account details) in your direct debit request confidential. We will make reasonable efforts to keep any such information that we have about you secure and to ensure that any of our employees or agents who have access to information about you do not make any unauthorised use, modification, reproduction or disclosure of that information. "),e("li",null,[t(" 7.2 We will only disclose information that we have about you: "),e("ol",{style:{"list-style-type":"lower-alpha"}},[e("li",null,"to the extent specifically required by law; or"),e("li",null," for the purpose of this service agreement (including disclosing information in connection with any query or claim). ")])])]),e("br")]),e("li",null,[e("strong",null,"NOTICE"),e("ol",null,[e("li",null," 8.1 If you wish to notify us in writing about anything relating this service agreement, you should write to: Direct debit, Demo Council, PO Box 7 Pakenham VIC 3810. Include your rates assessment number, name, address, a daytime contact phone number and your signature. "),e("li",null,[t(" 8.2 Alternatively changes can be lodged by email "),e("a",{href:"mailto:<EMAIL>"},"<EMAIL>"),t(" (subject: Direct debit). ")]),e("li",null," 8.3 We will notify you by sending a notice in the ordinary post to the address you have with your Demo Council account(s). "),e("li",null,"8.4 Any notice will be deemed to have been received two days after it is posted.")]),e("br")])]),e("p",null,[e("strong",{class:"my-2"},"DEMO COUNCIL PRIVACY STATEMENT"),e("br")]),e("p",{class:"my-2"}," Personal information collected by Council is used for municipal purposes as specified in the Local Government Act 1989. The personal information will be held securely and used solely by Council for these purposes and/or directly related purposes. Council may disclose this information to other organisations if required or permitted by legislation. The applicant understands that the personal information provided is for the above purpose and that he or she may apply to Council for access to and/or amendment of the information. Requests for access and/or correction should be made to Council's Privacy <NAME_EMAIL> ")],-1)])),_:1,__:[1]},8,["visible"])}}}),p=h(b,[["__scopeId","data-v-ad6eaeff"]]);export{p as T};
