import{a2 as d,an as o}from"./index-Cr_XsrqN.js";var a={name:"BaseEditableHolder",extends:d,emits:["update:modelValue","value-change"],props:{modelValue:{type:null,default:void 0},defaultValue:{type:null,default:void 0},name:{type:String,default:void 0},invalid:{type:Boolean,default:void 0},disabled:{type:Boolean,default:!1},formControl:{type:Object,default:void 0}},inject:{$parentInstance:{default:void 0},$pcForm:{default:void 0},$pcFormField:{default:void 0}},data:function(){return{d_value:this.defaultValue!==void 0?this.defaultValue:this.modelValue}},watch:{modelValue:function(i){this.d_value=i},defaultValue:function(i){this.d_value=i},$formName:{immediate:!0,handler:function(i){var t,l;this.formField=((t=this.$pcForm)===null||t===void 0||(l=t.register)===null||l===void 0?void 0:l.call(t,i,this.$formControl))||{}}},$formControl:{immediate:!0,handler:function(i){var t,l;this.formField=((t=this.$pcForm)===null||t===void 0||(l=t.register)===null||l===void 0?void 0:l.call(t,this.$formName,i))||{}}},$formDefaultValue:{immediate:!0,handler:function(i){this.d_value!==i&&(this.d_value=i)}},$formValue:{immediate:!1,handler:function(i){var t;(t=this.$pcForm)!==null&&t!==void 0&&t.getFieldState(this.$formName)&&i!==this.d_value&&(this.d_value=i)}}},formField:{},methods:{writeValue:function(i,t){var l,n;this.controlled&&(this.d_value=i,this.$emit("update:modelValue",i)),this.$emit("value-change",i),(l=(n=this.formField).onChange)===null||l===void 0||l.call(n,{originalEvent:t,value:i})},findNonEmpty:function(){for(var i=arguments.length,t=new Array(i),l=0;l<i;l++)t[l]=arguments[l];return t.find(o)}},computed:{$filled:function(){return o(this.d_value)},$invalid:function(){var i,t;return!this.$formNovalidate&&this.findNonEmpty(this.invalid,(i=this.$pcFormField)===null||i===void 0||(i=i.$field)===null||i===void 0?void 0:i.invalid,(t=this.$pcForm)===null||t===void 0||(t=t.getFieldState(this.$formName))===null||t===void 0?void 0:t.invalid)},$formName:function(){var i;return this.$formNovalidate?void 0:this.name||((i=this.$formControl)===null||i===void 0?void 0:i.name)},$formControl:function(){var i;return this.formControl||((i=this.$pcFormField)===null||i===void 0?void 0:i.formControl)},$formNovalidate:function(){var i;return(i=this.$formControl)===null||i===void 0?void 0:i.novalidate},$formDefaultValue:function(){var i,t;return this.findNonEmpty(this.d_value,(i=this.$pcFormField)===null||i===void 0?void 0:i.initialValue,(t=this.$pcForm)===null||t===void 0||(t=t.initialValues)===null||t===void 0?void 0:t[this.$formName])},$formValue:function(){var i,t;return this.findNonEmpty((i=this.$pcFormField)===null||i===void 0||(i=i.$field)===null||i===void 0?void 0:i.value,(t=this.$pcForm)===null||t===void 0||(t=t.getFieldState(this.$formName))===null||t===void 0?void 0:t.value)},controlled:function(){return this.$inProps.hasOwnProperty("modelValue")||!this.$inProps.hasOwnProperty("modelValue")&&!this.$inProps.hasOwnProperty("defaultValue")},filled:function(){return this.$filled}}},u={name:"BaseInput",extends:a,props:{size:{type:String,default:null},fluid:{type:Boolean,default:null},variant:{type:String,default:null}},inject:{$parentInstance:{default:void 0},$pcFluid:{default:void 0}},computed:{$variant:function(){var i;return(i=this.variant)!==null&&i!==void 0?i:this.$primevue.config.inputStyle||this.$primevue.config.inputVariant},$fluid:function(){var i;return(i=this.fluid)!==null&&i!==void 0?i:!!this.$pcFluid},hasFluid:function(){return this.$fluid}}};export{a,u as s};
