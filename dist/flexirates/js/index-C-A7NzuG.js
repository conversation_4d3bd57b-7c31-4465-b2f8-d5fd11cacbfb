import{A as T}from"./addCardOrBank-CpEoqTrU.js";import{_ as j}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{d as G,i as p,c as v,o as r,e,a as _,b as k,u as t,F as b,r as M,h as s,g as u,E as Y,G as C,m as w,t as z,s as H}from"./index-Cr_XsrqN.js";import{s as J}from"./index--SCaD3QG.js";import{s as K}from"./index-BH-zSNAS.js";import{a as Q}from"./index-fEC-lipk.js";import{t as W,c as X,e as Z,f as ee}from"./index.esm-Qe0UyvbF.js";import{s as l}from"./index-Bs8JoKI4.js";import{u as te}from"./useListRefresh-DLVs3qOd.js";import{u as ae}from"./useRequestList-B2228N3W.js";import{a as oe}from"./home-Bcs-QcZ3.js";import{a as le}from"./paymentMethod-CihfujB6.js";import{g as se}from"./transactions-DCpZnSzG.js";import{_ as ie}from"./properties.vue_vue_type_script_setup_true_lang-BDnvi4xa.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DsAxJM5H.js";import"./index-5-OuhCK9.js";import"./index-DdGtf4Je.js";import"./index-BiHyKo52.js";import"./register-payment-CcrZD2od.js";import"./country-ZCPlDRUV.js";import"./index-CQnajQNe.js";import"./dayjs.min-BN044Q0-.js";import"./index.vue_vue_type_style_index_0_lang-BcPs9O2U.js";import"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import"./index-Dvu_aI8r.js";import"./index-DhbL6JTo.js";import"./index-BwvvB-v3.js";import"./date-B2oFx4oA.js";import"./BaseDataTable-BjLz7Fg0.js";import"./index.vue_vue_type_style_index_0_lang-4VPfa-Ly.js";import"./index-BMUxa2Nh.js";import"./bank-pGAkdNCw.js";import"./getAssetsFile-BMGmg7rM.js";import"./Website-CKuRBN-m.js";import"./flex-login-logo-mini-BGXreUGR.js";import"./property-BTlta3pB.js";import"./register-payment-method-CcrZD2od.js";import"./logo-white-DjPdav-u.js";import"./logo-CO0W3x5n.js";const de={class:"payment-methods"},re={class:"skeleton-header"},me={class:"skeleton-title-section"},pe={class:"skeleton-table"},ue={class:"skeleton-table-header"},he={class:"flex flex-wrap gap-14 py-6"},ce={class:"flex items-center gap-2"},ye={class:"flex items-center gap-2"},ve={class:"flex flex-col gap-6 mb-6"},xe={class:"flex justify-end"},fe=G({name:"flexiratesPaymentMethodList",__name:"index",setup(ge){const U=ae({requestFn:le}),x=p(),h=p(!1),f=p(!1),P=p(null),{list:$=[],loading:A,refresh:c}=U;te("flexiratesPaymentMethodList",c);const m=p(!1),d=p({levelPaymentMethod:!0,paymentMethod:null,id:null}),L=W(X({levelPaymentMethod:ee(),paymentMethod:Z().test("is-valid-payment-method","Payment method is required",i=>typeof i=="number"&&!Number.isNaN(i)).required("Payment method is required")})),N=i=>{d.value.id=i,h.value=!0},S=async i=>{try{f.value=!0;const a={id:d.value.id,payment_method_id:i.paymentMethod,payment_method_type:i.levelPaymentMethod?1:2};(await oe(a)).code===0&&(h.value=!1,window.$toast.add({severity:"success",summary:"Success",detail:"Change payment method successful"}),c(),d.value={levelPaymentMethod:!0,paymentMethod:null,id:null})}catch(a){console.log(a)}finally{f.value=!1}},V=async()=>{const i=await se();x.value=i.data.map(a=>({label:a.payment_method,value:a.id})),x.value.push({label:"Add New Payment Method",value:""})},B=i=>{typeof i=="string"&&i===""&&(m.value=!0)},F=()=>{V(),m.value=!1,c()};return V(),(i,a)=>{const g=Q,q=K,R=J,D=H,E=j,O=T;return r(),v("div",de,[t(A)?(r(),v(b,{key:0},M(3,o=>s("div",{key:o,class:"skeleton-card"},[s("div",re,[s("div",me,[e(t(l),{width:"300px",height:"32px",class:"mb-2"}),e(t(l),{width:"200px",height:"16px",class:"mb-1"}),e(t(l),{width:"250px",height:"16px"})]),e(t(l),{width:"180px",height:"38px","border-radius":"8px"})]),s("div",pe,[s("div",ue,[e(t(l),{width:"60px",height:"20px"}),e(t(l),{width:"80px",height:"20px"}),e(t(l),{width:"120px",height:"20px"}),e(t(l),{width:"100px",height:"20px"}),e(t(l),{width:"80px",height:"20px"}),e(t(l),{width:"100px",height:"20px"}),e(t(l),{width:"120px",height:"20px"}),e(t(l),{width:"80px",height:"20px"})]),(r(),v(b,null,M(2,n=>s("div",{key:n,class:"skeleton-table-row"},[e(t(l),{width:"40px",height:"28px","border-radius":"4px"}),e(t(l),{width:"60px",height:"20px"}),e(t(l),{width:"80px",height:"20px"}),e(t(l),{width:"60px",height:"20px"}),e(t(l),{width:"60px",height:"20px"}),e(t(l),{width:"80px",height:"20px"}),e(t(l),{width:"100px",height:"20px"}),e(t(l),{width:"80px",height:"20px"})])),64))])])),64)):(r(!0),v(b,{key:1},M(t($),o=>(r(),_(ie,{key:o.id,property:o,onRefresh:t(c),onEditPaymentMethod:N,onAddCard:a[0]||(a[0]=n=>{d.value.levelPaymentMethod=!1,m.value=!0,P.value=n})},null,8,["property","onRefresh"]))),128)),e(E,{title:"Edit Payment Method",visible:h.value,"onUpdate:visible":a[3]||(a[3]=o=>h.value=o)},{content:u(()=>[e(t(Y),{"validation-schema":t(L),"initial-values":d.value,onSubmit:S},{default:u(()=>[e(t(C),{modelValue:d.value.levelPaymentMethod,"onUpdate:modelValue":a[1]||(a[1]=o=>d.value.levelPaymentMethod=o),as:"div",name:"levelPaymentMethod"},{default:u(({field:o})=>[s("div",he,[s("div",ce,[e(g,{modelValue:o.value,"onUpdate:modelValue":n=>o.value=n,"input-id":"primary",name:"levelPaymentMethod",value:!0},null,8,["modelValue","onUpdate:modelValue"]),a[5]||(a[5]=s("label",{for:"primary"},"Change Primary Payment Method",-1))]),s("div",ye,[e(g,{modelValue:o.value,"onUpdate:modelValue":n=>o.value=n,"input-id":"secondary",name:"levelPaymentMethod",value:!1},null,8,["modelValue","onUpdate:modelValue"]),a[6]||(a[6]=s("label",{for:"secondary"},"Change Secondary Payment Method",-1))])])]),_:1},8,["modelValue"]),e(t(C),{modelValue:d.value.paymentMethod,"onUpdate:modelValue":a[2]||(a[2]=o=>d.value.paymentMethod=o),as:"div",name:"paymentMethod"},{default:u(({field:o,errorMessage:n,handleChange:I})=>[s("div",ve,[a[7]||(a[7]=s("label",{class:"font-semibold"},"Select an existing Payment Method to change to : ",-1)),e(q,{modelValue:o.value,"onUpdate:modelValue":y=>o.value=y,options:x.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Method",class:"w-full",onValueChange:y=>{I(y),B(y)}},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"])]),n?(r(),_(R,{key:0,severity:"error",variant:"simple",class:"mb-2 -mt-4"},{default:u(()=>[w(z(n),1)]),_:2},1024)):k("",!0)]),_:1},8,["modelValue"]),a[8]||(a[8]=s("div",{class:"edit-tips mb-6"},[s("p",null,[w(" You can only select from existing payment methods."),s("br"),w(" To add a new one, go to the Payment Methods section. ")])],-1)),s("div",xe,[e(D,{label:"CONFIRM",severity:"warn",class:"btn tips-btn",type:"submit",loading:f.value},null,8,["loading"])])]),_:1,__:[8]},8,["validation-schema","initial-values"])]),_:1},8,["visible"]),m.value?(r(),_(O,{key:2,visible:m.value,"onUpdate:visible":a[4]||(a[4]=o=>m.value=o),"other-data":{property_id:P.value},"show-radio":!0,onClose:F},null,8,["visible","other-data"])):k("",!0)])}}}),it=ne(fe,[["__scopeId","data-v-d02f9a79"]]);export{it as default};
