import{s as O}from"./index--SCaD3QG.js";import{s as Q}from"./index-DdGtf4Je.js";import{d as W,x as X,B as Z,i as x,p as M,c as E,o as m,b as c,h as a,e as s,s as ee,f,g as t,u as i,G as w,a as C,m as U,t as y,E as A,a4 as le}from"./index-Cr_XsrqN.js";import{s as ae}from"./index-CdyCwuiS.js";import{t as S,c as j,d as k}from"./index.esm-Qe0UyvbF.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";const se={class:"profile-page flex flex-col gap-8"},te={key:0,class:"loading-overlay w-full flex items-center justify-center h-45"},ne={class:"loading-content"},ie={key:1,class:"flex flex-col gap-8"},de={class:"card px-8 py-6 rounded-lg"},ue={class:"flex items-center justify-between"},re={class:"content-wrapper"},me={class:"flex flex-col gap-2 lg:min-w-140"},pe={class:"flex flex-col gap-2 lg:min-w-140"},ve={class:"flex justify-between items-center gap-2"},ce=["title"],fe={class:"flex justify-between items-center gap-2"},xe={class:"lg:min-w-60 xl:min-w-140"},ge={class:"card p-4 rounded-md"},be={class:"flex items-center justify-between"},we={class:"content-wrapper"},ye={class:"flex flex-col gap-2 lg:min-w-140"},_e={class:"flex flex-col gap-2 lg:min-w-140"},Ve={class:"flex flex-col gap-2 lg:min-w-140"},Ce={class:"flex flex-col gap-2 lg:min-w-140"},Ue={class:"flex flex-col gap-2 lg:min-w-140"},ke={class:"flex flex-col gap-2 lg:min-w-140"},he={class:"flex flex-col md:flex-row justify-center gap-4"},Ee={key:2,class:"flex flex-col gap-8"},Pe={class:"card px-8 py-6 rounded-lg"},$e={class:"flex items-center justify-between"},Ne={class:"content-wrapper"},Fe={class:"flex flex-col gap-2 lg:min-w-140"},Le={class:"flex flex-col md:flex-row justify-center gap-4 mt-8"},Ae={key:3,class:"flex flex-col gap-8"},Se={class:"card px-8 py-6 rounded-lg"},je={class:"flex items-center justify-between"},De={class:"content-wrapper"},Te={class:"flex flex-col gap-2 lg:min-w-140"},qe={class:"flex flex-col md:flex-row justify-center gap-4 mt-8"},Be=W({__name:"index",setup(Ie){const q=X(),B=Z(),g=x(!0),P=x(!0),r=x(!1),I=x(),z=x(),G=x(),R=x(),h=x("personalDetails"),p=x({first_name:"",last_name:"",email:"",phone:""}),n=x({line_1:"",line_2:"",city:"",state:"",postcode:"",country:""}),$=x({email:""}),N=x({phone:""}),Y=S(j({name:k().required("Name is required")})),H=S(j({line_1:k().optional(),line_2:k().optional(),city:k().optional(),state:k().optional(),zip:k().optional(),country:k().optional()})),J=S(j({email:k().email("Invalid email address").required("Email is required")})),K=S(j({phone:k().required("Phone is required")})),F=x(!1),T=async()=>{F.value=!0;try{const v=await B.getUserInfo();v&&(p.value.first_name=v.first_name,p.value.last_name=v.last_name,p.value.email=v.email,p.value.phone=v.mobile,n.value.line_1=v.location.address_line_one,n.value.line_2=v.location.address_line_two,n.value.city=v.location.city,n.value.state=v.location.state,n.value.postcode=v.location.postcode,n.value.country=v.location.country)}finally{F.value=!1}},D=async v=>{r.value=!0;try{let e={};switch(v){case"email":e={email:$.value.email};break;case"mobile":e={mobile:N.value.phone};break;case"location":e={first_name:p.value.first_name,last_name:p.value.last_name,location:{...n.value,address_line_1:n.value.line_1,address_line_2:n.value.line_2}},e==null||delete e.location.line_1,e==null||delete e.location.line_2;break;default:break}const{code:L}=await le(e);console.log("code",L),L===0&&(window.$toast.add({severity:"success",summary:"Update Profile",detail:"Profile updated successfully"}),q.push({name:"profile"}),T())}finally{r.value=!1}};return M(()=>{T()}),(v,e)=>{const L=ae,b=ee,_=Q,V=O;return m(),E("div",se,[F.value?(m(),E("div",te,[a("div",ne,[s(L,{style:{width:"50px",height:"50px"},"stroke-width":"4"}),e[21]||(e[21]=a("p",{class:"loading-text"}," Loading profile data... ",-1))])])):c("",!0),h.value==="personalDetails"&&!F.value?(m(),E("div",ie,[a("div",de,[a("div",ue,[e[22]||(e[22]=a("div",{class:"text-3xl font-bold profile-subtitle"}," Personal Details ",-1)),s(b,{icon:g.value?"pi pi-angle-up":"pi pi-angle-down",text:"",onClick:e[0]||(e[0]=l=>g.value=!g.value)},null,8,["icon"])]),a("div",{class:f(["collapsible-content",{expanded:g.value}])},[a("div",re,[s(i(A),{ref_key:"profileForm",ref:I,class:"flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8","validation-schema":i(Y),"initial-values":p.value},{default:t(()=>[s(i(w),{modelValue:p.value.first_name,"onUpdate:modelValue":e[1]||(e[1]=l=>p.value.first_name=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"first_name",label:"First Name"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[23]||(e[23]=a("label",{for:"first_name",class:"w-60"},"First Name",-1)),a("div",me,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["max-w-full lg:w-full",{"p-invalid":o}]),disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[23]},8,["modelValue"]),s(i(w),{modelValue:p.value.last_name,"onUpdate:modelValue":e[2]||(e[2]=l=>p.value.last_name=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"last_name",label:"Last Name"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[24]||(e[24]=a("label",{for:"last_name",class:"w-60"},"Last Name",-1)),a("div",pe,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[24]},8,["modelValue"]),s(i(w),{as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10",name:"email",label:"Email"},{default:t(()=>[e[25]||(e[25]=a("label",{for:"email",class:"lg:w-60"},"Email",-1)),a("div",ve,[a("div",{class:"max-w-45 lg:max-w-60 xl:min-w-140 line-clamp-1 text-ellipsis",title:p.value.email},y(p.value.email),9,ce),s(b,{label:"Update Email Address",class:"!p-0 personal-update-button",text:"",disabled:r.value,onClick:e[3]||(e[3]=l=>h.value="email")},null,8,["disabled"])])]),_:1,__:[25]}),s(i(w),{as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10",name:"phone",label:"Phone"},{default:t(()=>[e[26]||(e[26]=a("label",{for:"phone",class:"lg:w-60"},"Phone",-1)),a("div",fe,[a("div",xe,y(p.value.phone),1),s(b,{label:"Update Mobile Number",class:"!p-0 personal-update-button",text:"",disabled:r.value,onClick:e[4]||(e[4]=l=>h.value="phone")},null,8,["disabled"])])]),_:1,__:[26]})]),_:1},8,["validation-schema","initial-values"])])],2)]),a("div",ge,[a("div",be,[e[27]||(e[27]=a("div",{class:"text-3xl font-bold profile-subtitle"}," Location ",-1)),s(b,{icon:P.value?"pi pi-angle-up":"pi pi-angle-down",text:"",onClick:e[5]||(e[5]=l=>P.value=!P.value)},null,8,["icon"])]),a("div",{class:f(["collapsible-content",{expanded:P.value}])},[a("div",we,[s(i(A),{ref_key:"locationForm",ref:z,class:"flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8","validation-schema":i(H),"initial-values":n.value},{default:t(()=>[s(i(w),{modelValue:n.value.line_1,"onUpdate:modelValue":e[6]||(e[6]=l=>n.value.line_1=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"line_1"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[28]||(e[28]=a("label",{for:"line_1",class:"w-60"},"Mailing Address (optional)",-1)),a("div",ye,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"Address Line 1",disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[28]},8,["modelValue"]),s(i(w),{modelValue:n.value.line_2,"onUpdate:modelValue":e[7]||(e[7]=l=>n.value.line_2=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"line_2"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[29]||(e[29]=a("label",{for:"line_2",class:"w-60"},null,-1)),a("div",_e,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"Address Line 2",disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[29]},8,["modelValue"]),s(i(w),{modelValue:n.value.city,"onUpdate:modelValue":e[8]||(e[8]=l=>n.value.city=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"city"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[30]||(e[30]=a("label",{for:"city",class:"w-60"},null,-1)),a("div",Ve,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"City",disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[30]},8,["modelValue"]),s(i(w),{modelValue:n.value.state,"onUpdate:modelValue":e[9]||(e[9]=l=>n.value.state=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"state"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[31]||(e[31]=a("label",{for:"state",class:"w-60"},null,-1)),a("div",Ce,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"State",disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[31]},8,["modelValue"]),s(i(w),{modelValue:n.value.postcode,"onUpdate:modelValue":e[10]||(e[10]=l=>n.value.postcode=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"zip"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[32]||(e[32]=a("label",{for:"postcode",class:"w-60"},null,-1)),a("div",Ue,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"Postcode",disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[32]},8,["modelValue"]),s(i(w),{modelValue:n.value.country,"onUpdate:modelValue":e[11]||(e[11]=l=>n.value.country=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2",name:"country"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[33]||(e[33]=a("label",{for:"country",class:"w-60"},null,-1)),a("div",ke,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"Country",disabled:r.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","disabled","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[33]},8,["modelValue"])]),_:1},8,["validation-schema","initial-values"])])],2)]),a("div",he,[s(b,{label:"CANCEL",class:"md:w-80 h-12 cancel-button"}),s(b,{label:"UPDATE",class:"md:w-80 h-12",severity:"warn",loading:r.value,onClick:e[12]||(e[12]=l=>D("location"))},null,8,["loading"])])])):c("",!0),h.value==="email"?(m(),E("div",Ee,[a("div",Pe,[a("div",$e,[e[34]||(e[34]=a("div",{class:"text-3xl font-bold text-gray-700"}," Personal Details ",-1)),s(b,{icon:g.value?"pi pi-angle-up":"pi pi-angle-down",text:"",onClick:e[13]||(e[13]=l=>g.value=!g.value)},null,8,["icon"])]),a("div",{class:f(["collapsible-content",{expanded:g.value}])},[a("div",Ne,[s(i(A),{ref_key:"emailForm",ref:G,class:"flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8","validation-schema":i(J),"initial-values":$.value},{default:t(()=>[e[36]||(e[36]=a("div",{class:"text-xl font-bold text-gray-700"}," Update email ",-1)),e[37]||(e[37]=a("div",{class:"border-b-1 border-gray-300"},null,-1)),e[38]||(e[38]=a("p",{class:"text-gray-500 mb-1"}," Enter your new email address and we'll email you out a confirmation message to that address. ",-1)),e[39]||(e[39]=a("strong",{class:"mb-1"},"Please follow the link in the email we send to confirm your new address.",-1)),e[40]||(e[40]=a("p",{class:"text-gray-500 mb-8 mb-1"}," Your new email address won't be reflected until you've confirmed it. ",-1)),s(i(w),{modelValue:$.value.email,"onUpdate:modelValue":e[14]||(e[14]=l=>$.value.email=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10",name:"email"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[35]||(e[35]=a("label",{for:"email",class:"w-60"},"New Email Address",-1)),a("div",Fe,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),placeholder:"Enter your new email address",onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[35]},8,["modelValue"]),a("div",Le,[s(b,{label:"CANCEL",class:"md:w-80 h-12 cancel-button",onClick:e[15]||(e[15]=l=>h.value="personalDetails")}),s(b,{label:"UPDATE",class:"md:w-80 h-12",severity:"warn",loading:r.value,onClick:e[16]||(e[16]=l=>D("email"))},null,8,["loading"])])]),_:1,__:[36,37,38,39,40]},8,["validation-schema","initial-values"])])],2)])])):c("",!0),h.value==="phone"?(m(),E("div",Ae,[a("div",Se,[a("div",je,[e[41]||(e[41]=a("div",{class:"text-3xl font-bold text-gray-700"}," Personal Details ",-1)),s(b,{icon:g.value?"pi pi-angle-up":"pi pi-angle-down",text:"",onClick:e[17]||(e[17]=l=>g.value=!g.value)},null,8,["icon"])]),a("div",{class:f(["collapsible-content",{expanded:g.value}])},[a("div",De,[s(i(A),{ref_key:"phoneForm",ref:R,class:"flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8","validation-schema":i(K),"initial-values":N.value},{default:t(()=>[e[43]||(e[43]=a("div",{class:"text-xl font-bold text-gray-700"}," Update mobile number ",-1)),e[44]||(e[44]=a("div",{class:"border-b-1 border-gray-300"},null,-1)),e[45]||(e[45]=a("p",{class:"text-gray-500 mb-1"}," Enter your new mobile number and we'll send a confirmation code to that new number. ",-1)),e[46]||(e[46]=a("strong",{class:"mb-8"},"Please enter the code we send to confirm your new mobile number.",-1)),s(i(w),{modelValue:N.value.phone,"onUpdate:modelValue":e[18]||(e[18]=l=>N.value.phone=l),as:"div",class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10",name:"phone"},{default:t(({field:l,handleChange:d,errorMessage:o})=>[e[42]||(e[42]=a("label",{for:"phone",class:"w-60"},"Mobile Number",-1)),a("div",Te,[s(_,{modelValue:l.value,"onUpdate:modelValue":u=>l.value=u,class:f(["w-full",{"p-invalid":o}]),onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),o?(m(),C(V,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:t(()=>[U(y(o),1)]),_:2},1024)):c("",!0)])]),_:1,__:[42]},8,["modelValue"]),a("div",qe,[s(b,{label:"CANCEL",class:"md:w-80 h-12 cancel-button",onClick:e[19]||(e[19]=l=>h.value="personalDetails")}),s(b,{label:"UPDATE",class:"md:w-80 h-12",severity:"warn",loading:r.value,onClick:e[20]||(e[20]=l=>D("mobile"))},null,8,["loading"])])]),_:1,__:[43,44,45,46]},8,["validation-schema","initial-values"])])],2)])])):c("",!0)])}}}),Oe=oe(Be,[["__scopeId","data-v-4dca7b09"]]);export{Oe as default};
