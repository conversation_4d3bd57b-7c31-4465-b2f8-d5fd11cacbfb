import{d as j,K as q,i as y,C as F,p as H,c as l,o as n,h as e,e as p,m as A,t as o,u as f,g,s as K,b as v,F as $,r as T,f as r,n as k,k as O}from"./index-Cr_XsrqN.js";import{s as I}from"./index-BwvvB-v3.js";import{d as G}from"./dayjs.min-BN044Q0-.js";import{u as J,d as V,a as E,g as P}from"./notifications-DRnROiop.js";import{f as L}from"./date-B2oFx4oA.js";import{_ as Q}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";const X={class:"notifications-page"},Z={class:"page-header"},ee={class:"flex items-baseline gap-4"},te={class:"total-count"},se={class:"header-actions"},ae={class:"notifications-list"},ie={key:0,class:"notification-section"},oe=["onClick"],le={class:"notification-text"},ne={class:"notification-actions"},ce=["onClick"],de={class:"notification-time"},re={key:1,class:"notification-section"},ue=["checked","onChange"],_e=["onClick"],pe={class:"notification-text"},ve={class:"notification-actions"},he=["onClick"],me={class:"notification-time"},ye={key:2,class:"notification-section"},fe=["checked","onChange"],ge=["onClick"],ke={class:"notification-text"},Ce={class:"notification-actions"},xe=["onClick"],be={class:"notification-time"},we={key:3,class:"empty-state"},Ne={key:0,class:"notification-detail"},De={class:"detail-header"},Ae={class:"detail-meta"},$e={class:"detail-content"},Te={class:"detail-text"},Le={class:"detail-footer"},Ve={class:"detail-time"},Ee=j({__name:"index",setup(Be){const C=q(),u=y([]),h=y(!1),d=y(null),i=y({today:[],yesterday:[],week_ago:[]}),B=J(),{unreadCount:R}=F(B),x=a=>{const s=u.value.indexOf(a);s>-1?u.value.splice(s,1):u.value.push(a)},S=async()=>{Object.values(i.value).forEach(s=>{s.forEach(_=>{_.is_read=1})}),(await E({is_all:1})).code===0&&C.add({severity:"success",summary:"Tips",detail:"All have been successfully read."})},U=()=>{window.$confirm.require({message:"Are you sure you want to delete all notifications? This action cannot be undone.",header:"Delete All Notifications",icon:"pi pi-exclamation-triangle",acceptClass:"p-button-danger",accept:async()=>{i.value.today=[],i.value.yesterday=[],i.value.week_ago=[],u.value.splice(0),(await V({is_all:1})).code===0&&(C.add({severity:"success",summary:"Tips",detail:"All have been successfully deleted."}),D())}})},b=a=>{window.$confirm.require({message:"Are you sure you want to delete this notification?",header:"Delete Confirmation",icon:"pi pi-exclamation-triangle",accept:async()=>{(await V({ids:a})).code===0&&(C.add({severity:"success",summary:"Tips",detail:"All have been successfully deleted."}),D())}})},w=a=>{d.value=a,h.value=!0,a.is_read||(E({is_all:0,ids:`${a.id}`}),a.is_read=1)},Y=()=>{h.value=!1,d.value=null},z=a=>G(a).format("YYYY-MM-DD HH:mm:ss"),m=a=>{switch(a){case 2:return"#EB001B";default:return"#333333"}},N=a=>{switch(a){case 2:return"600";default:return"500"}},D=async()=>{const a=await P({page:1,page_size:1e3});i.value=a.data.data};return H(()=>{D()}),(a,s)=>{const _=K,M=I,W=O;return n(),l("div",X,[e("div",Z,[e("div",ee,[s[2]||(s[2]=e("h1",{class:"page-title"}," Notifications ",-1)),e("div",te,[s[1]||(s[1]=A(" Unread Notifications: ")),e("span",null,o(f(R)),1)])]),e("div",se,[p(_,{class:"!px-8 btn",onClick:U},{default:g(()=>s[3]||(s[3]=[A(" DELETE ALL ")])),_:1,__:[3]}),p(_,{severity:"warn",class:"!ml-4 !px-8 btn",onClick:S},{default:g(()=>s[4]||(s[4]=[A(" MARK ALL AS READ ")])),_:1,__:[4]})])]),s[10]||(s[10]=e("div",{class:"divider"},null,-1)),e("div",ae,[i.value.today.length>0?(n(),l("div",ie,[s[5]||(s[5]=e("h2",{class:"section-title"}," Today ",-1)),(n(!0),l($,null,T(i.value.today,t=>(n(),l("div",{key:t.id,class:r(["notification-item",{"is-read":t.is_read}])},[p(M,{modelValue:t.is_read,"onUpdate:modelValue":c=>t.is_read=c,binary:!0,size:"large",class:"notification-checkbox",onChange:c=>x(t.id)},null,8,["modelValue","onUpdate:modelValue","onChange"]),e("div",{class:"notification-content",onClick:c=>w(t)},[e("h3",{class:"notification-title",style:k({color:m(t.transaction_status),fontWeight:N(t.transaction_status)})},o(t.title),5),e("p",le,o(t.content),1)],8,oe),e("div",ne,[e("i",{class:r(["pi pi-envelope action-icon",{"is-read":t.is_read}])},null,2),e("i",{class:"pi pi-trash action-icon",onClick:c=>b(t.id)},null,8,ce),e("span",de,o(f(L)(t.created_at)),1)])],2))),128))])):v("",!0),i.value.yesterday.length>0?(n(),l("div",re,[s[6]||(s[6]=e("h2",{class:"section-title"}," Yesterday ",-1)),(n(!0),l($,null,T(i.value.yesterday,t=>(n(),l("div",{key:t.id,class:r(["notification-item",{"is-read":t.is_read}])},[e("input",{type:"checkbox",checked:u.value.includes(t.id),class:"notification-checkbox",onChange:c=>x(t.id)},null,40,ue),e("div",{class:"notification-content",onClick:c=>w(t)},[e("h3",{class:"notification-title",style:k({color:m(t.transaction_status),fontWeight:N(t.transaction_status)})},o(t.title),5),e("p",pe,o(t.content),1)],8,_e),e("div",ve,[e("i",{class:r(["pi pi-envelope action-icon",{"is-read":t.is_read}])},null,2),e("i",{class:r(["pi pi-trash action-icon",{"is-read":t.is_read}]),onClick:c=>b(t.id)},null,10,he),e("span",me,o(f(L)(t.created_at)),1)])],2))),128))])):v("",!0),i.value.week_ago.length>0?(n(),l("div",ye,[s[8]||(s[8]=e("h2",{class:"section-title"}," Last 7 days ",-1)),(n(!0),l($,null,T(i.value.week_ago,t=>(n(),l("div",{key:t.id,class:r(["notification-item",{"is-read":t.is_read}])},[e("input",{type:"checkbox",checked:u.value.includes(t.id),class:"notification-checkbox",onChange:c=>x(t.id)},null,40,fe),e("div",{class:"notification-content",onClick:c=>w(t)},[e("h3",{class:"notification-title",style:k({color:m(t.transaction_status),fontWeight:N(t.transaction_status)})},o(t.title),5),e("p",ke,o(t.content),1)],8,ge),e("div",Ce,[s[7]||(s[7]=e("i",{class:"pi pi-envelope action-icon"},null,-1)),e("i",{class:"pi pi-trash action-icon",onClick:c=>b(t.id)},null,8,xe),e("span",be,o(f(L)(t.created_at)),1)])],2))),128))])):v("",!0),i.value.today.length===0&&i.value.yesterday.length===0&&i.value.week_ago.length===0?(n(),l("div",we,s[9]||(s[9]=[e("p",null," No notifications found ",-1)]))):v("",!0)]),p(W,{visible:h.value,"onUpdate:visible":s[0]||(s[0]=t=>h.value=t),style:{width:"600px"},header:"Notification Details",modal:!0,class:"notification-detail-dialog"},{footer:g(()=>[p(_,{label:"Close",icon:"pi pi-times",onClick:Y})]),default:g(()=>[d.value?(n(),l("div",Ne,[e("div",De,[e("h3",{class:"detail-title",style:k({color:m(d.value.type)})},o(d.value.title),5),e("div",Ae,[e("span",{class:r(["detail-status",{"is-read":d.value.is_read}])},o(d.value.is_read?"Read":"Unread"),3)])]),e("div",$e,[e("p",Te,o(d.value.content),1)]),e("div",Le,[e("span",Ve,o(z(d.value.created_at)),1)])])):v("",!0)]),_:1},8,["visible"])])}}}),je=Q(Ee,[["__scopeId","data-v-5f0704f2"]]);export{je as default};
