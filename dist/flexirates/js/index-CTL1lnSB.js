import{d as C,i as k,p as R,c as P,o as j,h as i,t as _,e as D,s as N}from"./index-Cr_XsrqN.js";import{a as $,c as L,_ as U,b as F}from"./Website-CKuRBN-m.js";import{a as M}from"./support-BHa7CsES.js";import{_ as T}from"./_plugin-vue_export-helper-DlAUqK2U.js";function q(n,e,s,r){function o(t){return t instanceof s?t:new s(function(a){a(t)})}return new(s||(s=Promise))(function(t,a){function c(l){try{h(r.next(l))}catch(d){a(d)}}function p(l){try{h(r.throw(l))}catch(d){a(d)}}function h(l){l.done?t(l.value):o(l.value).then(c,p)}h((r=r.apply(n,[])).next())})}function B(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var K=function n(e,s){if(e===s)return!0;if(e&&s&&typeof e=="object"&&typeof s=="object"){if(e.constructor!==s.constructor)return!1;var r,o,t;if(Array.isArray(e)){if(r=e.length,r!=s.length)return!1;for(o=r;o--!==0;)if(!n(e[o],s[o]))return!1;return!0}if(e.constructor===RegExp)return e.source===s.source&&e.flags===s.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===s.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===s.toString();if(t=Object.keys(e),r=t.length,r!==Object.keys(s).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(s,t[o]))return!1;for(o=r;o--!==0;){var a=t[o];if(!n(e[a],s[a]))return!1}return!0}return e!==e&&s!==s},G=B(K);const S="__googleMapsScriptId";var u;(function(n){n[n.INITIALIZED=0]="INITIALIZED",n[n.LOADING=1]="LOADING",n[n.SUCCESS=2]="SUCCESS",n[n.FAILURE=3]="FAILURE"})(u||(u={}));class f{constructor({apiKey:e,authReferrerPolicy:s,channel:r,client:o,id:t=S,language:a,libraries:c=[],mapIds:p,nonce:h,region:l,retries:d=3,url:m="https://maps.googleapis.com/maps/api/js",version:g}){if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=e,this.authReferrerPolicy=s,this.channel=r,this.client=o,this.id=t||S,this.language=a,this.libraries=c,this.mapIds=p,this.nonce=h,this.region=l,this.retries=d,this.url=m,this.version=g,f.instance){if(!G(this.options,f.instance.options))throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(f.instance.options)}`);return f.instance}f.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?u.FAILURE:this.done?u.SUCCESS:this.loading?u.LOADING:u.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){let e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+=`&key=${this.apiKey}`),this.channel&&(e+=`&channel=${this.channel}`),this.client&&(e+=`&client=${this.client}`),this.libraries.length>0&&(e+=`&libraries=${this.libraries.join(",")}`),this.language&&(e+=`&language=${this.language}`),this.region&&(e+=`&region=${this.region}`),this.version&&(e+=`&v=${this.version}`),this.mapIds&&(e+=`&map_ids=${this.mapIds.join(",")}`),this.authReferrerPolicy&&(e+=`&auth_referrer_policy=${this.authReferrerPolicy}`),e}deleteScript(){const e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((e,s)=>{this.loadCallback(r=>{r?s(r.error):e(window.google)})})}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){var e,s;if(document.getElementById(this.id)){this.callback();return}const r={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(r).forEach(t=>!r[t]&&delete r[t]),!((s=(e=window==null?void 0:window.google)===null||e===void 0?void 0:e.maps)===null||s===void 0)&&s.importLibrary||(t=>{let a,c,p,h="The Google Maps JavaScript API",l="google",d="importLibrary",m="__ib__",g=document,v=window;v=v[l]||(v[l]={});const y=v.maps||(v.maps={}),I=new Set,b=new URLSearchParams,O=()=>a||(a=new Promise((w,E)=>q(this,void 0,void 0,function*(){var x;yield c=g.createElement("script"),c.id=this.id,b.set("libraries",[...I]+"");for(p in t)b.set(p.replace(/[A-Z]/g,A=>"_"+A[0].toLowerCase()),t[p]);b.set("callback",l+".maps."+m),c.src=this.url+"?"+b,y[m]=w,c.onerror=()=>a=E(Error(h+" could not load.")),c.nonce=this.nonce||((x=g.querySelector("script[nonce]"))===null||x===void 0?void 0:x.nonce)||"",g.head.append(c)})));y[d]?console.warn(h+" only loads once. Ignoring:",t):y[d]=(w,...E)=>I.add(w)&&O().then(()=>y[d](w,...E))})(r);const o=this.libraries.map(t=>this.importLibrary(t));o.length||o.push(this.importLibrary("core")),Promise.all(o).then(()=>this.callback(),t=>{const a=new ErrorEvent("error",{error:t});this.loadErrorCallback(a)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){const s=this.errors.length*Math.pow(2,this.errors.length);console.error(`Failed to load Google Maps script, retrying in ${s} ms.`),setTimeout(()=>{this.deleteScript(),this.setScript()},s)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(e=>{e(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version){console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),this.callback();return}this.loading=!0,this.setScript()}}}const Z={class:"flexirates-wrap"},z={class:"mt-4"},J={class:"message flex flex-col gap-2"},V={class:"flex flex-col md:flex-row md:items-center gap-2"},W={class:"message-content"},X={class:"flex flex-col md:flex-row md:items-center gap-2"},H={class:"message-content"},Q={class:"flex flex-col md:flex-row md:items-center gap-2"},Y={class:"message-content underline"},ee=["href"],te={class:"flex flex-col md:flex-row md:items-center gap-2"},se={class:"message-content"},ie={class:"cancel flex flex-col md:flex-row justify-between"},re={class:"mt-4 md:mt-0"},oe=C({__name:"index",setup(n){const e=k(null),s=k(),r=async()=>{const o=new f({apiKey:"AIzaSyDaX67ixHeBCcXYyEeKUy8qEzWahD85qDk",version:"weekly",libraries:["places","marker"]});try{await o.importLibrary("maps").then(({Map:t})=>{e.value=new t(document.getElementById("map"),{center:{lat:-38.06577967561586,lng:145.4138108815013},zoom:15})})}catch(t){console.error("加载Google Maps失败:",t)}};return R(()=>{Promise.all([M().then(o=>{s.value=o.data}),r()])}),(o,t)=>{var c,p,h,l,d;const a=N;return j(),P("div",Z,[t[6]||(t[6]=i("div",{class:"flexirates-title"},[i("div",{class:"flexirates-title-text"}," Need help? ")],-1)),i("div",z,[i("div",J,[i("div",V,[t[1]||(t[1]=i("div",{class:"flex items-center gap-4 md:gap-12"},[i("img",{src:$,alt:""}),i("div",{class:"message-label"}," Phone: ")],-1)),i("div",W,_((c=s.value)==null?void 0:c.phone),1)]),i("div",X,[t[2]||(t[2]=i("div",{class:"flex items-center gap-4 md:gap-12"},[i("img",{src:L,alt:""}),i("div",{class:"message-label"}," Email: ")],-1)),i("div",H,_((p=s.value)==null?void 0:p.email),1)]),i("div",Q,[t[3]||(t[3]=i("div",{class:"flex items-center gap-4 md:gap-12"},[i("img",{src:U,alt:""}),i("div",{class:"message-label"}," Website: ")],-1)),i("div",Y,[i("a",{href:(h=s.value)==null?void 0:h.website,target:"_blank"},_((l=s.value)==null?void 0:l.website),9,ee)])]),i("div",te,[t[4]||(t[4]=i("div",{class:"flex items-center gap-4 md:gap-12"},[i("img",{src:F,alt:""}),i("div",{class:"message-label"}," Office Address: ")],-1)),i("div",se,_((d=s.value)==null?void 0:d.office_address),1)])])]),t[7]||(t[7]=i("div",{class:"mt-6"},[i("div",{id:"map",class:"map"})],-1)),i("div",ie,[t[5]||(t[5]=i("div",{class:"w-full md:w-2/3"},[i("div",{class:"text-xl font-bold"}," I'd like to cancel my payment arrangement. "),i("div",null," The below button will take you to a page to cancel your existing FlexiRates arrangment online. After a cancellation, you will need to resume payments by the quarterly instalments, unless you set up a new arrangement (on FlexiRates or by calling us). ")],-1)),i("div",re,[D(a,{label:"REQUEST CANCELLATION",onClick:t[0]||(t[0]=m=>o.$router.push({name:"supportCancellation"}))})])])])}}}),de=T(oe,[["__scopeId","data-v-c563e79d"]]);export{de as default};
