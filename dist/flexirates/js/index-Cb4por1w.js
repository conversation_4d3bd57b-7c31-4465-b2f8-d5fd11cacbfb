import{_}from"./BaseDataTable-BjLz7Fg0.js";import{d as k,i as a,p as b,c as A,o as D,h as e,e as o,s as M,g as u,t as m,u as l}from"./index-Cr_XsrqN.js";import{d as y}from"./dayjs.min-BN044Q0-.js";import{u as Y}from"./useExport-BWofQ1KN.js";import{A as L,g as P,r as R,e as B}from"./activityLog-fVWqXgA-.js";import{_ as C}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import"./index-CQnajQNe.js";import"./index-BH-zSNAS.js";import"./index-DdGtf4Je.js";import"./index-BiHyKo52.js";import"./index-fEC-lipk.js";import"./index-Dvu_aI8r.js";import"./index-DhbL6JTo.js";import"./index-BwvvB-v3.js";import"./date-B2oFx4oA.js";import"./useDict-B_iRj5rJ.js";import"./useRequestList-B2228N3W.js";import"./dict-CFM3a_vV.js";const E={class:"flexirates-wrap"},S={class:"content"},$={class:"update-password"},T={class:"flex justify-between items-center pt-10"},U={class:"authenticated"},j={class:"activity-log"},I={class:"flex justify-between items-center"},N=k({__name:"index",setup(O){const v=a([]),n=a(!1),f=a([{field:"device_name",header:"Devices Name",style:{width:"150px"}},{field:"since",header:"Active Since",style:{width:"120px"},template:"since"},{field:"activity",header:"Last Activity",style:{width:"120px"},template:"activity"},{field:"location",header:"Location",style:{width:"120px"}},{field:"revoke",header:"Revoke",style:{width:"120px"},template:"revoke"}]),r=a(),{handleExport:g,isExporting:w}=Y({exportFn:B,getParams:()=>({activity_types:r.value})}),d=a(!1),p=async()=>{d.value=!0;const i=await P();i.code===0&&(v.value=i.data),d.value=!1},h=async i=>{n.value=!0,(await R({id:i})).code===0&&(n.value=!1,window.$toast.add({severity:"success",summary:"Success",detail:"Revoke successfully!"}),p())};return b(()=>{p()}),(i,t)=>{const c=M,x=_;return D(),A("div",E,[t[8]||(t[8]=e("div",{class:"flexirates-title"},[e("div",{class:"flexirates-title-text"}," Security ")],-1)),e("div",S,[e("div",$,[t[4]||(t[4]=e("div",{class:"subtitle"}," Update Password ",-1)),e("div",T,[t[3]||(t[3]=e("div",{class:"text-light-grey"}," Would you like to change your password? ",-1)),e("div",null,[o(c,{label:"UPDATE PASSWORD",severity:"warn",class:"btn",onClick:t[0]||(t[0]=s=>i.$router.push({name:"securityUpdatePassword"}))})])])]),e("div",U,[t[5]||(t[5]=e("div",{class:"subtitle"}," Your Authenticated Devices ",-1)),t[6]||(t[6]=e("div",{class:"text-light-grey py-10"}," This list shows devices that have been authenticated against your account.can revoke a devices authentication token. If you wish to continue using Bdy on that device, you will be prompted to enter your authentication details again. ",-1)),e("div",null,[o(x,{columns:f.value,value:v.value,paginator:!1,rows:20,lazy:!0,"data-key":"id","show-search-bar":!1,"show-multiple-column":!1,scrollable:!0,"table-style":"min-width: 50rem",loading:d.value},{since:u(({data:s})=>[e("span",null,m(l(y)(s.active_since).format("DD MMMM YYYY")),1)]),activity:u(({data:s})=>[e("span",null,m(s.last_activity?l(y)(s.last_activity).format("DD MMMM YYYY hh:mm A"):""),1)]),revoke:u(({data:s})=>[e("span",null,[o(c,{label:"Revoke",class:"btn",severity:"warn",loading:n.value,onClick:V=>h(s.id)},null,8,["loading","onClick"])])]),_:1},8,["columns","value","loading"])])]),e("div",j,[e("div",I,[t[7]||(t[7]=e("div",{class:"title"},[e("div",{class:"subtitle"}," Activity Log "),e("p",null," Review recent activity on your account, including logins, security changes, and payment updates. ")],-1)),e("div",null,[o(c,{label:"EXPORT",class:"w-40",loading:l(w),onClick:t[1]||(t[1]=s=>l(g)("csv"))},null,8,["loading"])])]),o(L,{params:r.value,"onUpdate:params":t[2]||(t[2]=s=>r.value=s)},null,8,["params"])])])])}}}),re=C(N,[["__scopeId","data-v-18b422e6"]]);export{re as default};
