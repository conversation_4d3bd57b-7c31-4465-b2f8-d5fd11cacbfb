import{d as H,U as I,i as d,p as P,T as G,w as L,c as f,b as M,h as n,f as z,J as T,o as h}from"./index-Cr_XsrqN.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const F={class:"recaptcha-wrap"},N={key:0,class:"recaptcha-loading"},B={key:1,class:"recaptcha-timeout"},U={key:2,class:"recaptcha-error"},K=H({__name:"index",props:{siteKey:{default:"6Lc7EhQrAAAAAIsN6MfROAMUqehxNI27h2gPHcG8"},theme:{default:"light"},size:{default:"normal"},tabindex:{default:0}},emits:["verify","expired","error"],setup(A,{expose:k,emit:x}){const p=A,o=x,{locale:y}=I(),c=d(null),t=d(null),s=d(!1),a=d(!0),v=d(!1);let i=null;const w=()=>new Promise((e,r)=>{if(window.grecaptcha){e();return}const u=document.createElement("script");u.src=`https://www.google.com/recaptcha/api.js?hl=${y.value}&render=explicit`,u.async=!0,u.defer=!0,u.onload=()=>e(),u.onerror=R=>r(R),document.head.appendChild(u)}),m=async()=>{if(await T(),!(!window.grecaptcha||!c.value)){if(t.value!==null){window.grecaptcha.reset(t.value);return}t.value=window.grecaptcha.render(c.value,{sitekey:p.siteKey,theme:p.theme,size:p.size,tabindex:p.tabindex,callback:e=>o("verify",e),"expired-callback":()=>o("expired"),"error-callback":()=>o("error")})}},_=()=>{window.grecaptcha&&t.value!==null&&window.grecaptcha.reset(t.value)},b=()=>window.grecaptcha&&t.value!==null?window.grecaptcha.getResponse(t.value):"",g=()=>{i!==null&&window.clearTimeout(i),i=window.setTimeout(()=>{a.value&&(a.value=!1,v.value=!0)},3e4)},l=()=>{i!==null&&(window.clearTimeout(i),i=null)},C=async()=>{try{a.value=!0,v.value=!1,s.value=!1,g(),await w();const e=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(e),m(),a.value=!1,l())},100)}catch(e){console.error("Failed to load reCAPTCHA on retry:",e),a.value=!1,s.value=!0,l(),o("error")}};return P(async()=>{try{a.value=!0,g(),await w();const e=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(e),m(),a.value=!1,l())},100)}catch(e){console.error("Failed to load reCAPTCHA:",e),a.value=!1,s.value=!0,l(),o("error")}}),G(()=>{if(window.grecaptcha&&t.value!==null){const e=c.value;e&&(e.innerHTML=""),t.value=null}l()}),L(y,()=>{t.value!==null&&(t.value=null,c.value&&(c.value.innerHTML=""),s.value=!1,v.value=!1,a.value=!0,g(),w().then(async()=>{await T(),m(),a.value=!1,l()}).catch(e=>{console.error("Failed to load reCAPTCHA after locale change:",e),a.value=!1,s.value=!0,l(),o("error")}))}),k({reset:_,execute:b}),(e,r)=>(h(),f("div",F,[a.value?(h(),f("div",N,r[0]||(r[0]=[n("div",{class:"loading-spinner"},null,-1),n("p",null,"Loading Google reCAPTCHA...",-1)]))):v.value?(h(),f("div",B,[r[1]||(r[1]=n("p",null,"Loading Google reCAPTCHA timed out after 30 seconds.",-1)),n("button",{class:"retry-button",onClick:C}," Retry ")])):s.value?(h(),f("div",U,[r[2]||(r[2]=n("p",null,"Failed to load Google reCAPTCHA. Please check your internet connection and try again.",-1)),n("button",{class:"retry-button",onClick:C}," Retry ")])):M("",!0),n("div",{ref_key:"recaptchaContainer",ref:c,class:z(e.$attrs.class)},null,2)]))}}),j=E(K,[["__scopeId","data-v-7a5ca47d"]]);export{j as G};
