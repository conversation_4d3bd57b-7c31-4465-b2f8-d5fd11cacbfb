import{i as U,p as de,T as ce,a0 as te,a1 as ie,ab as B,c as m,o as l,l as $,e as k,H as b,ba as mt,a2 as ne,a3 as ft,a as S,b as w,t as K,f as L,aP as q,d as H,y as ht,x as me,U as pt,j as A,h as d,F as z,r as Y,B as vt,C as R,bW as re,V as fe,w as Et,bX as At,g as C,s as bt,u as g,bY as Dt,b5 as gt,bd as $t,an as G,av as se,bn as ut,b6 as zt,b8 as Bt,aI as oe,a$ as W,a_ as Tt,aR as F,ax as le,bq as Ot,ak as It,bZ as Vt,aO as yt,z as kt,b0 as ae,bc as Ft,bx as Rt,by as Ut,bA as Ht,aL as Nt,b_ as jt,b$ as Gt,c0 as qt}from"./index-Cr_XsrqN.js";import{s as Wt}from"./index-CdyCwuiS.js";import{s as Zt,a as Xt}from"./index-Dvu_aI8r.js";import{s as Yt}from"./index-BquGHVpa.js";import{u as Jt,_ as Qt,n as dt,g as ei,f as ti,a as ii}from"./getAssetsFile-BMGmg7rM.js";import{B as ni}from"./BasePopover-kpUwiT6m.js";import{u as ri}from"./notifications-DRnROiop.js";import{_ as he}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as wt}from"./index-CYbSr7Mb.js";import{f as Lt}from"./logo-CO0W3x5n.js";import{C as ai,O as si}from"./index-CQnajQNe.js";import"./Website-CKuRBN-m.js";import"./flex-login-logo-mini-BGXreUGR.js";import"./property-BTlta3pB.js";import"./register-payment-method-CcrZD2od.js";import"./register-payment-CcrZD2od.js";import"./logo-white-DjPdav-u.js";import"./index-6Y5dfxc3.js";function oi(t){const e=U(!1),i=U(!1),r=()=>{t.value&&(e.value=t.value.scrollHeight>t.value.clientHeight,i.value=t.value.scrollWidth>t.value.clientWidth)};let s=null;return de(()=>{t.value&&(r(),s=new ResizeObserver(()=>{r()}),s.observe(t.value),t.value.addEventListener("scroll",r),window.addEventListener("resize",r))}),ce(()=>{s&&s.disconnect(),t.value&&t.value.removeEventListener("scroll",r),window.removeEventListener("resize",r)}),{hasVerticalScrollbar:e,hasHorizontalScrollbar:i,checkForScrollbars:r}}var li=te`
    .p-overlaybadge {
        position: relative;
    }

    .p-overlaybadge .p-badge {
        position: absolute;
        inset-block-start: 0;
        inset-inline-end: 0;
        transform: translate(50%, -50%);
        transform-origin: 100% 0;
        margin: 0;
        outline-width: dt('overlaybadge.outline.width');
        outline-style: solid;
        outline-color: dt('overlaybadge.outline.color');
    }

    .p-overlaybadge .p-badge:dir(rtl) {
        transform: translate(-50%, -50%);
    }
`,ui={root:"p-overlaybadge"},di=ie.extend({name:"overlaybadge",style:li,classes:ui}),ci={name:"OverlayBadge",extends:mt,style:di,provide:function(){return{$pcOverlayBadge:this,$parentInstance:this}}},St={name:"OverlayBadge",extends:ci,inheritAttrs:!1,components:{Badge:mt}};function mi(t,e,i,r,s,n){var f=B("Badge");return l(),m("div",b({class:t.cx("root")},t.ptmi("root")),[$(t.$slots,"default"),k(f,b(t.$props,{pt:t.ptm("pcBadge")}),null,16,["pt"])],16)}St.render=mi;var fi=te`
    .p-avatar {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: dt('avatar.width');
        height: dt('avatar.height');
        font-size: dt('avatar.font.size');
        background: dt('avatar.background');
        color: dt('avatar.color');
        border-radius: dt('avatar.border.radius');
    }

    .p-avatar-image {
        background: transparent;
    }

    .p-avatar-circle {
        border-radius: 50%;
    }

    .p-avatar-circle img {
        border-radius: 50%;
    }

    .p-avatar-icon {
        font-size: dt('avatar.icon.size');
        width: dt('avatar.icon.size');
        height: dt('avatar.icon.size');
    }

    .p-avatar img {
        width: 100%;
        height: 100%;
    }

    .p-avatar-lg {
        width: dt('avatar.lg.width');
        height: dt('avatar.lg.width');
        font-size: dt('avatar.lg.font.size');
    }

    .p-avatar-lg .p-avatar-icon {
        font-size: dt('avatar.lg.icon.size');
        width: dt('avatar.lg.icon.size');
        height: dt('avatar.lg.icon.size');
    }

    .p-avatar-xl {
        width: dt('avatar.xl.width');
        height: dt('avatar.xl.width');
        font-size: dt('avatar.xl.font.size');
    }

    .p-avatar-xl .p-avatar-icon {
        font-size: dt('avatar.xl.icon.size');
        width: dt('avatar.xl.icon.size');
        height: dt('avatar.xl.icon.size');
    }

    .p-avatar-group {
        display: flex;
        align-items: center;
    }

    .p-avatar-group .p-avatar + .p-avatar {
        margin-inline-start: dt('avatar.group.offset');
    }

    .p-avatar-group .p-avatar {
        border: 2px solid dt('avatar.group.border.color');
    }

    .p-avatar-group .p-avatar-lg + .p-avatar-lg {
        margin-inline-start: dt('avatar.lg.group.offset');
    }

    .p-avatar-group .p-avatar-xl + .p-avatar-xl {
        margin-inline-start: dt('avatar.xl.group.offset');
    }
`,hi={root:function(e){var i=e.props;return["p-avatar p-component",{"p-avatar-image":i.image!=null,"p-avatar-circle":i.shape==="circle","p-avatar-lg":i.size==="large","p-avatar-xl":i.size==="xlarge"}]},label:"p-avatar-label",icon:"p-avatar-icon"},pi=ie.extend({name:"avatar",style:fi,classes:hi}),vi={name:"BaseAvatar",extends:ne,props:{label:{type:String,default:null},icon:{type:String,default:null},image:{type:String,default:null},size:{type:String,default:"normal"},shape:{type:String,default:"square"},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:pi,provide:function(){return{$pcAvatar:this,$parentInstance:this}}};function Z(t){"@babel/helpers - typeof";return Z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Z(t)}function ct(t,e,i){return(e=bi(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function bi(t){var e=gi(t,"string");return Z(e)=="symbol"?e:e+""}function gi(t,e){if(Z(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var r=i.call(t,e);if(Z(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Pt={name:"Avatar",extends:vi,inheritAttrs:!1,emits:["error"],methods:{onError:function(e){this.$emit("error",e)}},computed:{dataP:function(){return ft(ct(ct({},this.shape,this.shape),this.size,this.size))}}},Ii=["aria-labelledby","aria-label","data-p"],yi=["data-p"],ki=["data-p"],wi=["src","alt","data-p"];function Li(t,e,i,r,s,n){return l(),m("div",b({class:t.cx("root"),"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel},t.ptmi("root"),{"data-p":n.dataP}),[$(t.$slots,"default",{},function(){return[t.label?(l(),m("span",b({key:0,class:t.cx("label")},t.ptm("label"),{"data-p":n.dataP}),K(t.label),17,yi)):t.$slots.icon?(l(),S(q(t.$slots.icon),{key:1,class:L(t.cx("icon"))},null,8,["class"])):t.icon?(l(),m("span",b({key:2,class:[t.cx("icon"),t.icon]},t.ptm("icon"),{"data-p":n.dataP}),null,16,ki)):t.image?(l(),m("img",b({key:3,src:t.image,alt:t.ariaLabel,onError:e[0]||(e[0]=function(){return n.onError&&n.onError.apply(n,arguments)})},t.ptm("image"),{"data-p":n.dataP}),null,16,wi)):w("",!0)]})],16,Ii)}Pt.render=Li;const Si={class:"app-breadcrumbs"},Pi={class:"breadcrumb-container"},Ci=["onClick"],xi={key:0,class:"pi pi-angle-right separator"},Mi=H({__name:"appBreadcrumbs",setup(t){const e=ht(),i=me(),{t:r}=pt(),s=A(()=>e.matched.filter(c=>{var a;return!((a=c.meta)!=null&&a.isHideBreadcrumb)})),n=c=>{c&&i.push({name:c})},f=c=>{var a,u;return((a=c==null?void 0:c.meta)==null?void 0:a.breadcrumbTitle)||r((u=c==null?void 0:c.meta)==null?void 0:u.i18nKey)||(c==null?void 0:c.name)},v=c=>{var a;return(c==null?void 0:c.name)||((a=c==null?void 0:c.redirect)==null?void 0:a.name)};return(c,a)=>(l(),m("div",Si,[d("div",Pi,[(l(!0),m(z,null,Y(s.value,(u,p)=>(l(),m("div",{key:p,class:"breadcrumb-item"},[d("h1",{class:L({clickable:v(u),active:p===s.value.length-1}),onClick:I=>u!=null&&u.name?n(u==null?void 0:u.name):null},K(f(u)),11,Ci),p<s.value.length-1?(l(),m("i",xi)):w("",!0)]))),128))])]))}}),_i=he(Mi,[["__scopeId","data-v-8f781fd4"]]),Ki={class:"app-header"},Ei={class:"flex flex-col p-4 gap-4 w-full rounded-xl text-white",style:{"background-color":"rgba(0,0,0,.5)"}},Ai={class:"flex items-center gap-5"},Di={class:"font-bold text-base text-white dark:text-black"},$i={class:"flex flex-col gap-2"},zi={class:"text-sm font-bold text-white dark:text-black"},Bi={class:"flex gap-4 mb-4 justify-end"},Ti={class:"header-start"},Oi={class:"header-end"},Vi={class:"user-info"},Fi={class:"user-nickname"},Ri={class:"user-profile"},Ui={class:"user-setting-popover"},Hi={class:"user-setting-content"},Ni={class:"user-setting-avatar"},ji={class:"user-setting-profile"},Gi={class:"user-setting-profile"},qi={class:"user-setting-profile-name"},Wi={class:"user-setting-profile-email"},Zi={class:"user-setting-profile-edit"},Xi={class:"user-notice"},Yi=["src"],Ji=["src"],Qi=H({__name:"appHeader",setup(t){const e=vt(),{user:i}=R(e),r=re(),s=fe(),{isSidebarSlim:n}=R(s),f=U(!0),v=U(0),{locale:c}=R(r);Et(c,x=>{r.setLocale(x)});const a=A(()=>{var x;return((x=i.value)==null?void 0:x.avatar)||Jt}),u=ri(),{unreadCount:p}=R(u),I=A(()=>p.value>0);de(()=>{u.startPolling()}),ce(()=>{u.stopPolling()});const O=()=>{const x=document.createElement("input");x.type="file",x.accept="image/png, image/jpeg, image/jpg, image/gif",x.onchange=async y=>{var h,M,T;const o=(h=y.target.files)==null?void 0:h[0];if(o)try{window.$toast.add({severity:"info",summary:"Uploading avatar",group:"headless"}),v.value=0;const _=new FormData;_.append("upload_files[]",o);const D=await Dt(_,{onUploadProgress:P=>{if(P.total){const E=Math.round(P.loaded*100/P.total);v.value=E}}});if(v.value=100,D&&D.data&&D.data.file_path&&D.data.file_path.length>0){const P=D.data.file_path[0];await e.updateUserInfo({email:((M=e.user)==null?void 0:M.email)||"",mfa_check:((T=e.user)==null?void 0:T.mfa_check)||0,avatar:P}),window.$toast.add({severity:"success",summary:"Avatar uploaded successfully"}),e.getUserInfo()}window.$toast.removeGroup("headless")}catch(_){window.$toast.removeGroup("headless"),window.$toast.add({severity:"error",summary:"Avatar upload failed",detail:_ instanceof Error?_.message:"Unknown error"})}},x.click()},N=()=>{s.setSidebarMode(n.value?"expanded":"slim")},J=()=>{r.toggleMobileMenu()};return(x,y)=>{const o=Yt,h=bt,M=At,T=Zt,_=Pt,D=St;return l(),m("div",Ki,[k(M,{position:"top-center",group:"headless",onClose:y[0]||(y[0]=P=>f.value=!1)},{container:C(({message:P,closeCallback:E})=>[d("section",Ei,[d("div",Ai,[y[5]||(y[5]=d("i",{class:"pi pi-cloud-upload text-white dark:text-black text-2xl"},null,-1)),d("span",Di,K(P.summary),1)]),d("div",$i,[k(o,{value:v.value,"show-value":!1,style:{height:"4px"},"pt:value:class":"!bg-primary-50 dark:!bg-primary-900",class:"!bg-primary/80"},null,8,["value"]),d("label",zi,K(v.value)+"% uploaded",1)]),d("div",Bi,[k(h,{label:"Close",size:"small",onClick:E},null,8,["onClick"])])])]),_:1}),d("div",Ti,[k(h,{class:"mobile-menu-toggle",severity:"secondary",onClick:J},{default:C(()=>y[6]||(y[6]=[d("i",{class:"pi pi-bars"},null,-1)])),_:1,__:[6]}),k(h,{class:"sidebar-toggle",severity:"secondary",onClick:N},{default:C(()=>[d("i",{class:L(["pi",g(n)?"pi-angle-right":"pi-angle-left"])},null,2)]),_:1}),k(T,{layout:"vertical"}),k(_i)]),d("div",Oi,[k(ni,{trigger:"click",placement:"bottom-end","popper-class":"user-setting-popper",width:"340px"},{reference:C(({isOpen:P})=>{var E,V;return[d("div",Vi,[d("div",Fi,K(`${(E=g(i))==null?void 0:E.first_name} ${(V=g(i))==null?void 0:V.last_name}`),1),d("div",{class:L(["flex items-center justify-center transition-transform duration-300",{"rotate-180":P}])},y[7]||(y[7]=[d("img",{style:{width:"24px"},src:Qt,alt:"avatar"},null,-1)]),2),d("div",Ri,[k(_,{image:a.value,shape:"circle",class:"user-avatar"},null,8,["image"])])])]}),default:C(()=>{var P,E,V;return[d("div",Ui,[d("div",Hi,[d("div",Ni,[k(_,{image:a.value,shape:"circle",class:"user-avatar"},null,8,["image"]),d("div",{class:"user-profile-info",onClick:O}," Add Photo ")]),d("div",ji,[d("div",Gi,[d("div",qi,K((P=g(i))==null?void 0:P.first_name)+" "+K((E=g(i))==null?void 0:E.last_name),1),d("div",Wi,K((V=g(i))==null?void 0:V.email),1),d("div",Zi,[y[8]||(y[8]=d("i",{class:"pi pi-pencil"},null,-1)),d("span",{onClick:y[1]||(y[1]=Q=>x.$router.push({name:"profile"}))},"Edit Profile")])])])]),k(h,{label:"LOGOUT",severity:"warn",class:"user-setting-logout",onClick:y[2]||(y[2]=Q=>g(e).logout())})])]}),_:1}),d("div",Xi,[I.value?(l(),S(D,{key:0,value:g(p)>99?"99+":g(p)},{default:C(()=>[d("img",{class:"notice-image",src:g(dt),alt:"notice",onClick:y[3]||(y[3]=P=>x.$router.push({name:"notifications"}))},null,8,Yi)]),_:1},8,["value"])):(l(),m("img",{key:1,class:"notice-image",src:g(dt),alt:"notice",onClick:y[4]||(y[4]=P=>x.$router.push({name:"notifications"}))},null,8,Ji))])])])}}});var en=te`
    .p-tieredmenu {
        background: dt('tieredmenu.background');
        color: dt('tieredmenu.color');
        border: 1px solid dt('tieredmenu.border.color');
        border-radius: dt('tieredmenu.border.radius');
        min-width: 12.5rem;
    }

    .p-tieredmenu-root-list,
    .p-tieredmenu-submenu {
        margin: 0;
        padding: dt('tieredmenu.list.padding');
        list-style: none;
        outline: 0 none;
        display: flex;
        flex-direction: column;
        gap: dt('tieredmenu.list.gap');
    }

    .p-tieredmenu-submenu {
        position: absolute;
        min-width: 100%;
        z-index: 1;
        background: dt('tieredmenu.background');
        color: dt('tieredmenu.color');
        border: 1px solid dt('tieredmenu.border.color');
        border-radius: dt('tieredmenu.border.radius');
        box-shadow: dt('tieredmenu.shadow');
    }

    .p-tieredmenu-item {
        position: relative;
    }

    .p-tieredmenu-item-content {
        transition:
            background dt('tieredmenu.transition.duration'),
            color dt('tieredmenu.transition.duration');
        border-radius: dt('tieredmenu.item.border.radius');
        color: dt('tieredmenu.item.color');
    }

    .p-tieredmenu-item-link {
        cursor: pointer;
        display: flex;
        align-items: center;
        text-decoration: none;
        overflow: hidden;
        position: relative;
        color: inherit;
        padding: dt('tieredmenu.item.padding');
        gap: dt('tieredmenu.item.gap');
        user-select: none;
        outline: 0 none;
    }

    .p-tieredmenu-item-label {
        line-height: 1;
    }

    .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.color');
    }

    .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.color');
        margin-left: auto;
        font-size: dt('tieredmenu.submenu.icon.size');
        width: dt('tieredmenu.submenu.icon.size');
        height: dt('tieredmenu.submenu.icon.size');
    }

    .p-tieredmenu-submenu-icon:dir(rtl) {
        margin-left: 0;
        margin-right: auto;
    }

    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content {
        color: dt('tieredmenu.item.focus.color');
        background: dt('tieredmenu.item.focus.background');
    }

    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.focus.color');
    }

    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.focus.color');
    }

    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover {
        color: dt('tieredmenu.item.focus.color');
        background: dt('tieredmenu.item.focus.background');
    }

    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.focus.color');
    }

    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.focus.color');
    }

    .p-tieredmenu-item-active > .p-tieredmenu-item-content {
        color: dt('tieredmenu.item.active.color');
        background: dt('tieredmenu.item.active.background');
    }

    .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.active.color');
    }

    .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.active.color');
    }

    .p-tieredmenu-separator {
        border-block-start: 1px solid dt('tieredmenu.separator.border.color');
    }

    .p-tieredmenu-overlay {
        box-shadow: dt('tieredmenu.shadow');
    }

    .p-tieredmenu-enter-from,
    .p-tieredmenu-leave-active {
        opacity: 0;
    }

    .p-tieredmenu-enter-active {
        transition: opacity 250ms;
    }

    .p-tieredmenu-mobile .p-tieredmenu-submenu {
        position: static;
        box-shadow: none;
        border: 0 none;
        padding-inline-start: dt('tieredmenu.submenu.mobile.indent');
        padding-inline-end: 0;
    }

    .p-tieredmenu-mobile .p-tieredmenu-submenu:dir(rtl) {
        padding-inline-start: 0;
        padding-inline-end: dt('tieredmenu.submenu.mobile.indent');
    }

    .p-tieredmenu-mobile .p-tieredmenu-submenu-icon {
        transition: transform 0.2s;
        transform: rotate(90deg);
    }

    .p-tieredmenu-mobile .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {
        transform: rotate(-90deg);
    }
`,tn={submenu:function(e){var i=e.instance,r=e.processedItem;return{display:i.isItemActive(r)?"flex":"none"}}},nn={root:function(e){var i=e.props,r=e.instance;return["p-tieredmenu p-component",{"p-tieredmenu-overlay":i.popup,"p-tieredmenu-mobile":r.queryMatches}]},start:"p-tieredmenu-start",rootList:"p-tieredmenu-root-list",item:function(e){var i=e.instance,r=e.processedItem;return["p-tieredmenu-item",{"p-tieredmenu-item-active":i.isItemActive(r),"p-focus":i.isItemFocused(r),"p-disabled":i.isItemDisabled(r)}]},itemContent:"p-tieredmenu-item-content",itemLink:"p-tieredmenu-item-link",itemIcon:"p-tieredmenu-item-icon",itemLabel:"p-tieredmenu-item-label",submenuIcon:"p-tieredmenu-submenu-icon",submenu:"p-tieredmenu-submenu",separator:"p-tieredmenu-separator",end:"p-tieredmenu-end"},rn=ie.extend({name:"tieredmenu",style:en,classes:nn,inlineStyles:tn}),an={name:"BaseTieredMenu",extends:ne,props:{popup:{type:Boolean,default:!1},model:{type:Array,default:null},appendTo:{type:[String,Object],default:"body"},breakpoint:{type:String,default:"960px"},autoZIndex:{type:Boolean,default:!0},baseZIndex:{type:Number,default:0},disabled:{type:Boolean,default:!1},tabindex:{type:Number,default:0},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:rn,provide:function(){return{$pcTieredMenu:this,$parentInstance:this}}},Ct={name:"TieredMenuSub",hostName:"TieredMenu",extends:ne,emits:["item-click","item-mouseenter","item-mousemove"],container:null,props:{menuId:{type:String,default:null},focusedItemId:{type:String,default:null},items:{type:Array,default:null},visible:{type:Boolean,default:!1},level:{type:Number,default:0},templates:{type:Object,default:null},activeItemPath:{type:Object,default:null},tabindex:{type:Number,default:0}},methods:{getItemId:function(e){return"".concat(this.menuId,"_").concat(e.key)},getItemKey:function(e){return this.getItemId(e)},getItemProp:function(e,i,r){return e&&e.item?It(e.item[i],r):void 0},getItemLabel:function(e){return this.getItemProp(e,"label")},getItemLabelId:function(e){return"".concat(this.menuId,"_").concat(e.key,"_label")},getPTOptions:function(e,i,r){return this.ptm(r,{context:{item:e.item,index:i,active:this.isItemActive(e),focused:this.isItemFocused(e),disabled:this.isItemDisabled(e)}})},isItemActive:function(e){return this.activeItemPath.some(function(i){return i.key===e.key})},isItemVisible:function(e){return this.getItemProp(e,"visible")!==!1},isItemDisabled:function(e){return this.getItemProp(e,"disabled")},isItemFocused:function(e){return this.focusedItemId===this.getItemId(e)},isItemGroup:function(e){return G(e.items)},onEnter:function(){Vt(this.container,this.level)},onItemClick:function(e,i){this.getItemProp(i,"command",{originalEvent:e,item:i.item}),this.$emit("item-click",{originalEvent:e,processedItem:i,isFocus:!0})},onItemMouseEnter:function(e,i){this.$emit("item-mouseenter",{originalEvent:e,processedItem:i})},onItemMouseMove:function(e,i){this.$emit("item-mousemove",{originalEvent:e,processedItem:i})},getAriaSetSize:function(){var e=this;return this.items.filter(function(i){return e.isItemVisible(i)&&!e.getItemProp(i,"separator")}).length},getAriaPosInset:function(e){var i=this;return e-this.items.slice(0,e).filter(function(r){return i.isItemVisible(r)&&i.getItemProp(r,"separator")}).length+1},getMenuItemProps:function(e,i){return{action:b({class:this.cx("itemLink"),tabindex:-1},this.getPTOptions(e,i,"itemLink")),icon:b({class:[this.cx("itemIcon"),this.getItemProp(e,"icon")]},this.getPTOptions(e,i,"itemIcon")),label:b({class:this.cx("itemLabel")},this.getPTOptions(e,i,"itemLabel")),submenuicon:b({class:this.cx("submenuIcon")},this.getPTOptions(e,i,"submenuIcon"))}},containerRef:function(e){this.container=e}},components:{AngleRightIcon:Xt},directives:{ripple:$t}},sn=["tabindex"],on=["id","aria-label","aria-disabled","aria-expanded","aria-haspopup","aria-level","aria-setsize","aria-posinset","data-p-active","data-p-focused","data-p-disabled"],ln=["onClick","onMouseenter","onMousemove"],un=["href","target"],dn=["id"],cn=["id"];function mn(t,e,i,r,s,n){var f=B("AngleRightIcon"),v=B("TieredMenuSub",!0),c=yt("ripple");return l(),S(ae,b({name:"p-tieredmenu",onEnter:n.onEnter},t.ptm("menu.transition")),{default:C(function(){return[i.level===0||i.visible?(l(),m("ul",{key:0,ref:n.containerRef,tabindex:i.tabindex},[(l(!0),m(z,null,Y(i.items,function(a,u){return l(),m(z,{key:n.getItemKey(a)},[n.isItemVisible(a)&&!n.getItemProp(a,"separator")?(l(),m("li",b({key:0,id:n.getItemId(a),style:n.getItemProp(a,"style"),class:[t.cx("item",{processedItem:a}),n.getItemProp(a,"class")],role:"menuitem","aria-label":n.getItemLabel(a),"aria-disabled":n.isItemDisabled(a)||void 0,"aria-expanded":n.isItemGroup(a)?n.isItemActive(a):void 0,"aria-haspopup":n.isItemGroup(a)&&!n.getItemProp(a,"to")?"menu":void 0,"aria-level":i.level+1,"aria-setsize":n.getAriaSetSize(),"aria-posinset":n.getAriaPosInset(u)},{ref_for:!0},n.getPTOptions(a,u,"item"),{"data-p-active":n.isItemActive(a),"data-p-focused":n.isItemFocused(a),"data-p-disabled":n.isItemDisabled(a)}),[d("div",b({class:t.cx("itemContent"),onClick:function(I){return n.onItemClick(I,a)},onMouseenter:function(I){return n.onItemMouseEnter(I,a)},onMousemove:function(I){return n.onItemMouseMove(I,a)}},{ref_for:!0},n.getPTOptions(a,u,"itemContent")),[i.templates.item?(l(),S(q(i.templates.item),{key:1,item:a.item,hasSubmenu:n.getItemProp(a,"items"),label:n.getItemLabel(a),props:n.getMenuItemProps(a,u)},null,8,["item","hasSubmenu","label","props"])):kt((l(),m("a",b({key:0,href:n.getItemProp(a,"url"),class:t.cx("itemLink"),target:n.getItemProp(a,"target"),tabindex:"-1"},{ref_for:!0},n.getPTOptions(a,u,"itemLink")),[i.templates.itemicon?(l(),S(q(i.templates.itemicon),{key:0,item:a.item,class:L(t.cx("itemIcon"))},null,8,["item","class"])):n.getItemProp(a,"icon")?(l(),m("span",b({key:1,class:[t.cx("itemIcon"),n.getItemProp(a,"icon")]},{ref_for:!0},n.getPTOptions(a,u,"itemIcon")),null,16)):w("",!0),d("span",b({id:n.getItemLabelId(a),class:t.cx("itemLabel")},{ref_for:!0},n.getPTOptions(a,u,"itemLabel")),K(n.getItemLabel(a)),17,dn),n.getItemProp(a,"items")?(l(),m(z,{key:2},[i.templates.submenuicon?(l(),S(q(i.templates.submenuicon),b({key:0,class:t.cx("submenuIcon"),active:n.isItemActive(a)},{ref_for:!0},n.getPTOptions(a,u,"submenuIcon")),null,16,["class","active"])):(l(),S(f,b({key:1,class:t.cx("submenuIcon")},{ref_for:!0},n.getPTOptions(a,u,"submenuIcon")),null,16,["class"]))],64)):w("",!0)],16,un)),[[c]])],16,ln),n.isItemVisible(a)&&n.isItemGroup(a)?(l(),S(v,b({key:0,id:n.getItemId(a)+"_list",class:t.cx("submenu"),style:t.sx("submenu",!0,{processedItem:a}),"aria-labelledby":n.getItemLabelId(a),role:"menu",menuId:i.menuId,focusedItemId:i.focusedItemId,items:a.items,templates:i.templates,activeItemPath:i.activeItemPath,level:i.level+1,visible:n.isItemActive(a)&&n.isItemGroup(a),pt:t.pt,unstyled:t.unstyled,onItemClick:e[0]||(e[0]=function(p){return t.$emit("item-click",p)}),onItemMouseenter:e[1]||(e[1]=function(p){return t.$emit("item-mouseenter",p)}),onItemMousemove:e[2]||(e[2]=function(p){return t.$emit("item-mousemove",p)})},{ref_for:!0},t.ptm("submenu")),null,16,["id","class","style","aria-labelledby","menuId","focusedItemId","items","templates","activeItemPath","level","visible","pt","unstyled"])):w("",!0)],16,on)):w("",!0),n.isItemVisible(a)&&n.getItemProp(a,"separator")?(l(),m("li",b({key:1,id:n.getItemId(a),style:n.getItemProp(a,"style"),class:[t.cx("separator"),n.getItemProp(a,"class")],role:"separator"},{ref_for:!0},t.ptm("separator")),null,16,cn)):w("",!0)],64)}),128))],8,sn)):w("",!0)]}),_:1},16,["onEnter"])}Ct.render=mn;var xt={name:"TieredMenu",extends:an,inheritAttrs:!1,emits:["focus","blur","before-show","before-hide","hide","show"],outsideClickListener:null,matchMediaListener:null,scrollHandler:null,resizeListener:null,target:null,container:null,menubar:null,searchTimeout:null,searchValue:null,data:function(){return{focused:!1,focusedItemInfo:{index:-1,level:0,parentKey:""},activeItemPath:[],visible:!this.popup,submenuVisible:!1,dirty:!1,query:null,queryMatches:!1}},watch:{activeItemPath:function(e){this.popup||(G(e)?(this.bindOutsideClickListener(),this.bindResizeListener()):(this.unbindOutsideClickListener(),this.unbindResizeListener()))}},mounted:function(){this.bindMatchMediaListener()},beforeUnmount:function(){this.unbindOutsideClickListener(),this.unbindResizeListener(),this.unbindMatchMediaListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.container&&this.autoZIndex&&W.clear(this.container),this.target=null,this.container=null},methods:{getItemProp:function(e,i){return e?It(e[i]):void 0},getItemLabel:function(e){return this.getItemProp(e,"label")},isItemDisabled:function(e){return this.getItemProp(e,"disabled")},isItemVisible:function(e){return this.getItemProp(e,"visible")!==!1},isItemGroup:function(e){return G(this.getItemProp(e,"items"))},isItemSeparator:function(e){return this.getItemProp(e,"separator")},getProccessedItemLabel:function(e){return e?this.getItemLabel(e.item):void 0},isProccessedItemGroup:function(e){return e&&G(e.items)},toggle:function(e){this.visible?this.hide(e,!0):this.show(e)},show:function(e,i){this.popup&&(this.$emit("before-show"),this.visible=!0,this.target=this.target||e.currentTarget,this.relatedTarget=e.relatedTarget||null),i&&F(this.menubar)},hide:function(e,i){this.popup&&(this.$emit("before-hide"),this.visible=!1),this.activeItemPath=[],this.focusedItemInfo={index:-1,level:0,parentKey:""},i&&F(this.relatedTarget||this.target||this.menubar),this.dirty=!1},onFocus:function(e){this.focused=!0,this.popup||(this.focusedItemInfo=this.focusedItemInfo.index!==-1?this.focusedItemInfo:{index:this.findFirstFocusedItemIndex(),level:0,parentKey:""}),this.$emit("focus",e)},onBlur:function(e){this.focused=!1,this.focusedItemInfo={index:-1,level:0,parentKey:""},this.searchValue="",this.dirty=!1,this.$emit("blur",e)},onKeyDown:function(e){if(this.disabled){e.preventDefault();return}var i=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"ArrowLeft":this.onArrowLeftKey(e);break;case"ArrowRight":this.onArrowRightKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Space":this.onSpaceKey(e);break;case"Enter":case"NumpadEnter":this.onEnterKey(e);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e);break;case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!i&&Ot(e.key)&&this.searchItems(e,e.key);break}},onItemChange:function(e,i){var r=e.processedItem,s=e.isFocus;if(!le(r)){var n=r.index,f=r.key,v=r.level,c=r.parentKey,a=r.items,u=G(a),p=this.activeItemPath.filter(function(I){return I.parentKey!==c&&I.parentKey!==f});u&&(p.push(r),this.submenuVisible=!0),this.focusedItemInfo={index:n,level:v,parentKey:c},u&&(this.dirty=!0),s&&F(this.menubar),!(i==="hover"&&this.queryMatches)&&(this.activeItemPath=p)}},onOverlayClick:function(e){si.emit("overlay-click",{originalEvent:e,target:this.target})},onItemClick:function(e){var i=e.originalEvent,r=e.processedItem,s=this.isProccessedItemGroup(r),n=le(r.parent),f=this.isSelected(r);if(f){var v=r.index,c=r.key,a=r.level,u=r.parentKey;this.activeItemPath=this.activeItemPath.filter(function(I){return c!==I.key&&c.startsWith(I.key)}),this.focusedItemInfo={index:v,level:a,parentKey:u},this.dirty=!n,F(this.menubar)}else if(s)this.onItemChange(e);else{var p=n?r:this.activeItemPath.find(function(I){return I.parentKey===""});this.hide(i),this.changeFocusedItemIndex(i,p?p.index:-1),F(this.menubar)}},onItemMouseEnter:function(e){this.dirty&&this.onItemChange(e,"hover")},onItemMouseMove:function(e){this.focused&&this.changeFocusedItemIndex(e,e.processedItem.index)},onArrowDownKey:function(e){var i=this.focusedItemInfo.index!==-1?this.findNextItemIndex(this.focusedItemInfo.index):this.findFirstFocusedItemIndex();this.changeFocusedItemIndex(e,i),e.preventDefault()},onArrowUpKey:function(e){if(e.altKey){if(this.focusedItemInfo.index!==-1){var i=this.visibleItems[this.focusedItemInfo.index],r=this.isProccessedItemGroup(i);!r&&this.onItemChange({originalEvent:e,processedItem:i})}this.popup&&this.hide(e,!0),e.preventDefault()}else{var s=this.focusedItemInfo.index!==-1?this.findPrevItemIndex(this.focusedItemInfo.index):this.findLastFocusedItemIndex();this.changeFocusedItemIndex(e,s),e.preventDefault()}},onArrowLeftKey:function(e){var i=this,r=this.visibleItems[this.focusedItemInfo.index],s=this.activeItemPath.find(function(f){return f.key===r.parentKey}),n=le(r.parent);n||(this.focusedItemInfo={index:-1,parentKey:s?s.parentKey:""},this.searchValue="",this.onArrowDownKey(e)),this.activeItemPath=this.activeItemPath.filter(function(f){return f.parentKey!==i.focusedItemInfo.parentKey}),e.preventDefault()},onArrowRightKey:function(e){var i=this.visibleItems[this.focusedItemInfo.index],r=this.isProccessedItemGroup(i);r&&(this.onItemChange({originalEvent:e,processedItem:i}),this.focusedItemInfo={index:-1,parentKey:i.key},this.searchValue="",this.onArrowDownKey(e)),e.preventDefault()},onHomeKey:function(e){this.changeFocusedItemIndex(e,this.findFirstItemIndex()),e.preventDefault()},onEndKey:function(e){this.changeFocusedItemIndex(e,this.findLastItemIndex()),e.preventDefault()},onEnterKey:function(e){if(this.focusedItemInfo.index!==-1){var i=se(this.menubar,'li[id="'.concat("".concat(this.focusedItemId),'"]')),r=i&&se(i,'[data-pc-section="itemlink"]');if(r?r.click():i&&i.click(),!this.popup){var s=this.visibleItems[this.focusedItemInfo.index],n=this.isProccessedItemGroup(s);!n&&(this.focusedItemInfo.index=this.findFirstFocusedItemIndex())}}e.preventDefault()},onSpaceKey:function(e){this.onEnterKey(e)},onEscapeKey:function(e){if(this.popup||this.focusedItemInfo.level!==0){var i=this.focusedItemInfo;this.hide(e,!1),this.focusedItemInfo={index:Number(i.parentKey.split("_")[0]),level:0,parentKey:""},this.popup&&F(this.target)}e.preventDefault()},onTabKey:function(e){if(this.focusedItemInfo.index!==-1){var i=this.visibleItems[this.focusedItemInfo.index],r=this.isProccessedItemGroup(i);!r&&this.onItemChange({originalEvent:e,processedItem:i})}this.hide()},onEnter:function(e){this.autoZIndex&&W.set("menu",e,this.baseZIndex+this.$primevue.config.zIndex.menu),Tt(e,{position:"absolute",top:"0"}),this.alignOverlay(),F(this.menubar),this.scrollInView()},onAfterEnter:function(){this.bindOutsideClickListener(),this.bindScrollListener(),this.bindResizeListener(),this.$emit("show")},onLeave:function(){this.unbindOutsideClickListener(),this.unbindScrollListener(),this.unbindResizeListener(),this.$emit("hide"),this.container=null,this.dirty=!1},onAfterLeave:function(e){this.autoZIndex&&W.clear(e)},alignOverlay:function(){Bt(this.container,this.target);var e=oe(this.target);e>oe(this.container)&&(this.container.style.minWidth=oe(this.target)+"px")},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){var r=e.container&&!e.container.contains(i.target),s=e.popup?!(e.target&&(e.target===i.target||e.target.contains(i.target))):!0;r&&s&&e.hide()},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new ai(this.target,function(i){e.hide(i,!0)})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(i){zt()||e.hide(i,!0)},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},bindMatchMediaListener:function(){var e=this;if(!this.matchMediaListener){var i=matchMedia("(max-width: ".concat(this.breakpoint,")"));this.query=i,this.queryMatches=i.matches,this.matchMediaListener=function(){e.queryMatches=i.matches},this.query.addEventListener("change",this.matchMediaListener)}},unbindMatchMediaListener:function(){this.matchMediaListener&&(this.query.removeEventListener("change",this.matchMediaListener),this.matchMediaListener=null)},isItemMatched:function(e){var i;return this.isValidItem(e)&&((i=this.getProccessedItemLabel(e))===null||i===void 0?void 0:i.toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase()))},isValidItem:function(e){return!!e&&!this.isItemDisabled(e.item)&&!this.isItemSeparator(e.item)&&this.isItemVisible(e.item)},isValidSelectedItem:function(e){return this.isValidItem(e)&&this.isSelected(e)},isSelected:function(e){return this.activeItemPath.some(function(i){return i.key===e.key})},findFirstItemIndex:function(){var e=this;return this.visibleItems.findIndex(function(i){return e.isValidItem(i)})},findLastItemIndex:function(){var e=this;return ut(this.visibleItems,function(i){return e.isValidItem(i)})},findNextItemIndex:function(e){var i=this,r=e<this.visibleItems.length-1?this.visibleItems.slice(e+1).findIndex(function(s){return i.isValidItem(s)}):-1;return r>-1?r+e+1:e},findPrevItemIndex:function(e){var i=this,r=e>0?ut(this.visibleItems.slice(0,e),function(s){return i.isValidItem(s)}):-1;return r>-1?r:e},findSelectedItemIndex:function(){var e=this;return this.visibleItems.findIndex(function(i){return e.isValidSelectedItem(i)})},findFirstFocusedItemIndex:function(){var e=this.findSelectedItemIndex();return e<0?this.findFirstItemIndex():e},findLastFocusedItemIndex:function(){var e=this.findSelectedItemIndex();return e<0?this.findLastItemIndex():e},searchItems:function(e,i){var r=this;this.searchValue=(this.searchValue||"")+i;var s=-1,n=!1;return this.focusedItemInfo.index!==-1?(s=this.visibleItems.slice(this.focusedItemInfo.index).findIndex(function(f){return r.isItemMatched(f)}),s=s===-1?this.visibleItems.slice(0,this.focusedItemInfo.index).findIndex(function(f){return r.isItemMatched(f)}):s+this.focusedItemInfo.index):s=this.visibleItems.findIndex(function(f){return r.isItemMatched(f)}),s!==-1&&(n=!0),s===-1&&this.focusedItemInfo.index===-1&&(s=this.findFirstFocusedItemIndex()),s!==-1&&this.changeFocusedItemIndex(e,s),this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(function(){r.searchValue="",r.searchTimeout=null},500),n},changeFocusedItemIndex:function(e,i){this.focusedItemInfo.index!==i&&(this.focusedItemInfo.index=i,this.scrollInView())},scrollInView:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:-1,i=e!==-1?"".concat(this.$id,"_").concat(e):this.focusedItemId,r=se(this.menubar,'li[id="'.concat(i,'"]'));r&&r.scrollIntoView&&r.scrollIntoView({block:"nearest",inline:"start"})},createProcessedItems:function(e){var i=this,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"",f=[];return e&&e.forEach(function(v,c){var a=(n!==""?n+"_":"")+c,u={item:v,index:c,level:r,key:a,parent:s,parentKey:n};u.items=i.createProcessedItems(v.items,r+1,u,a),f.push(u)}),f},containerRef:function(e){this.container=e},menubarRef:function(e){this.menubar=e?e.$el:void 0}},computed:{processedItems:function(){return this.createProcessedItems(this.model||[])},visibleItems:function(){var e=this,i=this.activeItemPath.find(function(r){return r.key===e.focusedItemInfo.parentKey});return i?i.items:this.processedItems},focusedItemId:function(){return this.focusedItemInfo.index!==-1?"".concat(this.$id).concat(G(this.focusedItemInfo.parentKey)?"_"+this.focusedItemInfo.parentKey:"","_").concat(this.focusedItemInfo.index):null}},components:{TieredMenuSub:Ct,Portal:gt}},fn=["id"];function hn(t,e,i,r,s,n){var f=B("TieredMenuSub"),v=B("Portal");return l(),S(v,{appendTo:t.appendTo,disabled:!t.popup},{default:C(function(){return[k(ae,b({name:"p-connected-overlay",onEnter:n.onEnter,onAfterEnter:n.onAfterEnter,onLeave:n.onLeave,onAfterLeave:n.onAfterLeave},t.ptm("transition")),{default:C(function(){return[s.visible?(l(),m("div",b({key:0,ref:n.containerRef,id:t.$id,class:t.cx("root"),onClick:e[0]||(e[0]=function(){return n.onOverlayClick&&n.onOverlayClick.apply(n,arguments)})},t.ptmi("root")),[t.$slots.start?(l(),m("div",b({key:0,class:t.cx("start")},t.ptm("start")),[$(t.$slots,"start")],16)):w("",!0),k(f,b({ref:n.menubarRef,id:t.$id+"_list",class:t.cx("rootList"),tabindex:t.disabled?-1:t.tabindex,role:"menubar","aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledby,"aria-disabled":t.disabled||void 0,"aria-orientation":"vertical","aria-activedescendant":s.focused?n.focusedItemId:void 0,menuId:t.$id,focusedItemId:s.focused?n.focusedItemId:void 0,items:n.processedItems,templates:t.$slots,activeItemPath:s.activeItemPath,level:0,visible:s.submenuVisible,pt:t.pt,unstyled:t.unstyled,onFocus:n.onFocus,onBlur:n.onBlur,onKeydown:n.onKeyDown,onItemClick:n.onItemClick,onItemMouseenter:n.onItemMouseEnter,onItemMousemove:n.onItemMouseMove},t.ptm("rootList")),null,16,["id","class","tabindex","aria-label","aria-labelledby","aria-disabled","aria-activedescendant","menuId","focusedItemId","items","templates","activeItemPath","visible","pt","unstyled","onFocus","onBlur","onKeydown","onItemClick","onItemMouseenter","onItemMousemove"]),t.$slots.end?(l(),m("div",b({key:1,class:t.cx("end")},t.ptm("end")),[$(t.$slots,"end")],16)):w("",!0)],16,fn)):w("",!0)]}),_:3},16,["onEnter","onAfterEnter","onLeave","onAfterLeave"])]}),_:3},8,["appendTo","disabled"])}xt.render=hn;const pn=["onClick"],vn=["src"],bn={class:"menu-item-label"},gn=["href"],In=["src"],yn={class:"menu-item-label"},kn=["src"],wn={key:0,class:"menu-item-label"},Mt=H({__name:"appMenuItem",props:{item:{},slim:{type:Boolean}},setup(t){const e=t,i=re(),r=me(),s=ht(),n=U(),f=U(s.fullPath.startsWith(e.item.path)),v=A(()=>typeof e.item.path=="string"&&e.item.path.startsWith("http")),c=A(()=>{var o,h,M;return!!((h=(o=e.item)==null?void 0:o.children)!=null&&h.length)&&((M=e.item)==null?void 0:M.children.length)>1}),a=A(()=>{var o;return((o=e.item)==null?void 0:o.children)&&e.item.children.length>1}),u=A(()=>{var o,h;return((h=(o=e.item)==null?void 0:o.children)==null?void 0:h.length)===1?e.item.children[0]:null}),p=A(()=>{if(!e.item.path)return!1;if(typeof e.item.name=="string"){if(s.name===e.item.name)return!0;const o=`/${e.item.name.toLowerCase()}`,h=s.path.toLowerCase();return h===o||h.startsWith(`${o}/`)}return!1}),I=A(()=>{var o;return(o=e.item.children)!=null&&o.length?e.item.children.some(h=>h.name?s.name===h.name||typeof h.name=="string"&&s.path.toLowerCase().startsWith(`/${h.name.toLowerCase()}`):!1):!1}),O=A(()=>{var o,h;return c.value?N(e.item.children||[]):[{...e.item,label:(o=e.item.meta)==null?void 0:o.label,icon:(h=e.item.meta)==null?void 0:h.icon,command:()=>{v.value?window.open(e.item.path,"_blank"):e.item.name&&r.push({name:e.item.name})}}]}),N=o=>o.map(h=>{var T,_;const M=typeof h.path=="string"&&h.path.startsWith("http");return{...h,label:(T=h.meta)==null?void 0:T.label,icon:(_=h.meta)==null?void 0:_.icon,items:h.children?N(h.children):void 0,command:()=>{M?window.open(h.path,"_blank"):h.name&&r.push({name:h.name})}}}),J=(o,h)=>{var M;if(v.value){window.open(e.item.path,"_blank");return}if(e.slim){if(u.value){if(typeof u.value.path=="string"&&u.value.path.startsWith("http")){window.open(u.value.path,"_blank");return}else if(u.value.name){r.push({name:u.value.name});return}}if(a.value){x(o);return}}if(c.value){f.value=!f.value;return}if(h){o.preventDefault();return}(M=e.item)!=null&&M.name&&(r.push({name:e.item.name}),i.closeMobileMenu())},x=o=>{n.value&&e.slim&&n.value.toggle(o)},y=o=>o.includes("/")?ei(o):o;return(o,h)=>{var D,P,E,V,Q,pe,ve,be,ge,Ie,ye,ke,we,Le,Se,Pe,Ce,xe,Me,_e,Ke,Ee,Ae,De,$e,ze,Be,Te,Oe,Ve,Fe,Re,Ue,He;const M=B("router-link"),T=xt,_=B("app-menu-item",!0);return l(),m("div",{class:L(["app-flexirates-menu-item",{"has-submenu":c.value,"menu-item-slim":o.slim}])},[o.item.path&&!c.value&&!o.slim&&!v.value?(l(),S(M,{key:0,to:{name:o.item.name},custom:""},{default:C(({isActive:j})=>{var ee,Ne,je,Ge,qe,We,Ze,Xe,Ye,Je,Qe,et,tt,it,nt,rt,at,st,ot,lt;return[d("div",{class:L(["menu-item-link",{active:o.item.path===g(s).fullPath||j,"is-support":o.item.name==="support"}]),onClick:Kt=>J(Kt,j)},[d("div",{class:L(["menu-item-icon-wrapper",{"is-background":(je=(Ne=(ee=o.item)==null?void 0:ee.meta)==null?void 0:Ne.icon)==null?void 0:je.includes("question.png")}])},[(qe=(Ge=o.item)==null?void 0:Ge.meta)!=null&&qe.icon&&((Xe=String((Ze=(We=o.item)==null?void 0:We.meta)==null?void 0:Ze.icon))!=null&&Xe.includes("pi"))?(l(),m("i",{key:0,class:L(["menu-item-icon",[o.item.meta.icon]])},null,2)):(Je=(Ye=o.item)==null?void 0:Ye.meta)!=null&&Je.icon&&((tt=String((et=(Qe=o.item)==null?void 0:Qe.meta)==null?void 0:et.icon))!=null&&tt.includes("/"))?(l(),m("img",{key:1,class:L(["menu-item-icon-img",{"is-background":(rt=(nt=(it=o.item)==null?void 0:it.meta)==null?void 0:nt.icon)==null?void 0:rt.includes("question.png")}]),src:y((st=(at=o.item)==null?void 0:at.meta)==null?void 0:st.icon)},null,10,vn)):w("",!0)],2),d("span",bn,K((lt=(ot=o.item)==null?void 0:ot.meta)==null?void 0:lt.label),1)],10,pn)]}),_:1},8,["to"])):v.value&&!o.slim?(l(),m("a",{key:1,href:o.item.path,target:"_blank",class:"menu-item-link external-link"},[d("div",{class:L(["menu-item-icon-wrapper",{"is-background":(E=(P=(D=o.item)==null?void 0:D.meta)==null?void 0:P.icon)==null?void 0:E.includes("question.png")}])},[(Q=(V=o.item)==null?void 0:V.meta)!=null&&Q.icon&&((be=String((ve=(pe=o.item)==null?void 0:pe.meta)==null?void 0:ve.icon))!=null&&be.includes("pi"))?(l(),m("i",{key:0,class:L(["menu-item-icon",[o.item.meta.icon]])},null,2)):(Ie=(ge=o.item)==null?void 0:ge.meta)!=null&&Ie.icon&&((we=String((ke=(ye=o.item)==null?void 0:ye.meta)==null?void 0:ke.icon))!=null&&we.includes("/"))?(l(),m("img",{key:1,class:"menu-item-icon-img",src:y((Se=(Le=o.item)==null?void 0:Le.meta)==null?void 0:Se.icon)},null,8,In)):w("",!0)],2),d("span",yn,K((Ce=(Pe=o.item)==null?void 0:Pe.meta)==null?void 0:Ce.label),1),h[1]||(h[1]=d("i",{class:"pi pi-external-link external-link-icon"},null,-1))],8,gn)):(l(),m("div",{key:2,class:L(["menu-item-link",{expanded:f.value&&!o.slim,active:p.value||I.value,"external-link":v.value}]),onClick:h[0]||(h[0]=j=>J(j,p.value))},[d("div",{class:L(["menu-item-icon-wrapper",{"is-background":(_e=(Me=(xe=o.item)==null?void 0:xe.meta)==null?void 0:Me.icon)==null?void 0:_e.includes("question.png")}])},[(Ee=(Ke=o.item)==null?void 0:Ke.meta)!=null&&Ee.icon&&(($e=String((De=(Ae=o.item)==null?void 0:Ae.meta)==null?void 0:De.icon))!=null&&$e.includes("pi"))?(l(),m("i",{key:0,class:L(["menu-item-icon",[o.item.meta.icon]])},null,2)):(Be=(ze=o.item)==null?void 0:ze.meta)!=null&&Be.icon&&((Ve=String((Oe=(Te=o.item)==null?void 0:Te.meta)==null?void 0:Oe.icon))!=null&&Ve.includes("/"))?(l(),m("img",{key:1,class:"menu-item-icon-img",src:y((Re=(Fe=o.item)==null?void 0:Fe.meta)==null?void 0:Re.icon)},null,8,kn)):w("",!0)],2),o.slim?w("",!0):(l(),m("span",wn,K((He=(Ue=o.item)==null?void 0:Ue.meta)==null?void 0:He.label),1)),c.value&&!o.slim?(l(),m("i",{key:1,class:L(["submenu-icon pi pi-angle-down",{expanded:f.value}])},null,2)):w("",!0)],2)),o.slim&&a.value?(l(),S(T,{key:3,ref_key:"menuPopover",ref:n,model:O.value,popup:""},null,8,["model"])):w("",!0),c.value&&!o.slim?(l(),m("div",{key:4,class:L(["submenu",{expanded:f.value}])},[(l(!0),m(z,null,Y(o.item.children,(j,ee)=>(l(),S(_,{key:ee,item:j},null,8,["item"]))),128))],2)):w("",!0)],2)}}}),Ln={class:"app-menu-wrap"},Sn={key:1,class:"menu-separator"},Pn=H({__name:"appMenu",props:{slim:{type:Boolean,default:!1},items:{type:Array,required:!0,default:()=>[]}},setup(t){return(e,i)=>(l(),m("div",Ln,[d("nav",{class:L(["app-flexirates-menu",{"menu-slim":t.slim}])},[(l(!0),m(z,null,Y(t.items,(r,s)=>{var n;return l(),m(z,{key:s},[(n=r==null?void 0:r.meta)!=null&&n.isSeparator?(l(),m("div",Sn)):(l(),S(Mt,{key:0,item:r,slim:t.slim},null,8,["item","slim"]))],64)}),128))],2)]))}});var Cn=te`
    .p-drawer {
        display: flex;
        flex-direction: column;
        transform: translate3d(0px, 0px, 0px);
        position: relative;
        transition: transform 0.3s;
        background: dt('drawer.background');
        color: dt('drawer.color');
        border: 1px solid dt('drawer.border.color');
        box-shadow: dt('drawer.shadow');
    }

    .p-drawer-content {
        overflow-y: auto;
        flex-grow: 1;
        padding: dt('drawer.content.padding');
    }

    .p-drawer-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-shrink: 0;
        padding: dt('drawer.header.padding');
    }

    .p-drawer-footer {
        padding: dt('drawer.footer.padding');
    }

    .p-drawer-title {
        font-weight: dt('drawer.title.font.weight');
        font-size: dt('drawer.title.font.size');
    }

    .p-drawer-full .p-drawer {
        transition: none;
        transform: none;
        width: 100vw !important;
        height: 100vh !important;
        max-height: 100%;
        top: 0px !important;
        left: 0px !important;
        border-width: 1px;
    }

    .p-drawer-left .p-drawer-enter-from,
    .p-drawer-left .p-drawer-leave-to {
        transform: translateX(-100%);
    }

    .p-drawer-right .p-drawer-enter-from,
    .p-drawer-right .p-drawer-leave-to {
        transform: translateX(100%);
    }

    .p-drawer-top .p-drawer-enter-from,
    .p-drawer-top .p-drawer-leave-to {
        transform: translateY(-100%);
    }

    .p-drawer-bottom .p-drawer-enter-from,
    .p-drawer-bottom .p-drawer-leave-to {
        transform: translateY(100%);
    }

    .p-drawer-full .p-drawer-enter-from,
    .p-drawer-full .p-drawer-leave-to {
        opacity: 0;
    }

    .p-drawer-full .p-drawer-enter-active,
    .p-drawer-full .p-drawer-leave-active {
        transition: opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    .p-drawer-left .p-drawer {
        width: 20rem;
        height: 100%;
        border-inline-end-width: 1px;
    }

    .p-drawer-right .p-drawer {
        width: 20rem;
        height: 100%;
        border-inline-start-width: 1px;
    }

    .p-drawer-top .p-drawer {
        height: 10rem;
        width: 100%;
        border-block-end-width: 1px;
    }

    .p-drawer-bottom .p-drawer {
        height: 10rem;
        width: 100%;
        border-block-start-width: 1px;
    }

    .p-drawer-left .p-drawer-content,
    .p-drawer-right .p-drawer-content,
    .p-drawer-top .p-drawer-content,
    .p-drawer-bottom .p-drawer-content {
        width: 100%;
        height: 100%;
    }

    .p-drawer-open {
        display: flex;
    }

    .p-drawer-mask:dir(rtl) {
        flex-direction: row-reverse;
    }
`,xn={mask:function(e){var i=e.position,r=e.modal;return{position:"fixed",height:"100%",width:"100%",left:0,top:0,display:"flex",justifyContent:i==="left"?"flex-start":i==="right"?"flex-end":"center",alignItems:i==="top"?"flex-start":i==="bottom"?"flex-end":"center",pointerEvents:r?"auto":"none"}},root:{pointerEvents:"auto"}},Mn={mask:function(e){var i=e.instance,r=e.props,s=["left","right","top","bottom"],n=s.find(function(f){return f===r.position});return["p-drawer-mask",{"p-overlay-mask p-overlay-mask-enter":r.modal,"p-drawer-open":i.containerVisible,"p-drawer-full":i.fullScreen},n?"p-drawer-".concat(n):""]},root:function(e){var i=e.instance;return["p-drawer p-component",{"p-drawer-full":i.fullScreen}]},header:"p-drawer-header",title:"p-drawer-title",pcCloseButton:"p-drawer-close-button",content:"p-drawer-content",footer:"p-drawer-footer"},_n=ie.extend({name:"drawer",style:Cn,classes:Mn,inlineStyles:xn}),Kn={name:"BaseDrawer",extends:ne,props:{visible:{type:Boolean,default:!1},position:{type:String,default:"left"},header:{type:null,default:null},baseZIndex:{type:Number,default:0},autoZIndex:{type:Boolean,default:!0},dismissable:{type:Boolean,default:!0},showCloseIcon:{type:Boolean,default:!0},closeButtonProps:{type:Object,default:function(){return{severity:"secondary",text:!0,rounded:!0}}},closeIcon:{type:String,default:void 0},modal:{type:Boolean,default:!0},blockScroll:{type:Boolean,default:!1}},style:_n,provide:function(){return{$pcDrawer:this,$parentInstance:this}}};function X(t){"@babel/helpers - typeof";return X=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},X(t)}function ue(t,e,i){return(e=En(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function En(t){var e=An(t,"string");return X(e)=="symbol"?e:e+""}function An(t,e){if(X(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var r=i.call(t,e);if(X(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var _t={name:"Drawer",extends:Kn,inheritAttrs:!1,emits:["update:visible","show","after-show","hide","after-hide","before-hide"],data:function(){return{containerVisible:this.visible}},container:null,mask:null,content:null,headerContainer:null,footerContainer:null,closeButton:null,outsideClickListener:null,documentKeydownListener:null,watch:{dismissable:function(e){e?this.enableDocumentSettings():this.disableDocumentSettings()}},updated:function(){this.visible&&(this.containerVisible=this.visible)},beforeUnmount:function(){this.disableDocumentSettings(),this.mask&&this.autoZIndex&&W.clear(this.mask),this.container=null,this.mask=null},methods:{hide:function(){this.$emit("update:visible",!1)},onEnter:function(){this.$emit("show"),this.focus(),this.bindDocumentKeyDownListener(),this.autoZIndex&&W.set("modal",this.mask,this.baseZIndex||this.$primevue.config.zIndex.modal)},onAfterEnter:function(){this.enableDocumentSettings(),this.$emit("after-show")},onBeforeLeave:function(){this.modal&&!this.isUnstyled&&Nt(this.mask,"p-overlay-mask-leave"),this.$emit("before-hide")},onLeave:function(){this.$emit("hide")},onAfterLeave:function(){this.autoZIndex&&W.clear(this.mask),this.unbindDocumentKeyDownListener(),this.containerVisible=!1,this.disableDocumentSettings(),this.$emit("after-hide")},onMaskClick:function(e){this.dismissable&&this.modal&&this.mask===e.target&&this.hide()},focus:function(){var e=function(s){return s&&s.querySelector("[autofocus]")},i=this.$slots.header&&e(this.headerContainer);i||(i=this.$slots.default&&e(this.container),i||(i=this.$slots.footer&&e(this.footerContainer),i||(i=this.closeButton))),i&&F(i)},enableDocumentSettings:function(){this.dismissable&&!this.modal&&this.bindOutsideClickListener(),this.blockScroll&&Ht()},disableDocumentSettings:function(){this.unbindOutsideClickListener(),this.blockScroll&&Ut()},onKeydown:function(e){e.code==="Escape"&&this.hide()},containerRef:function(e){this.container=e},maskRef:function(e){this.mask=e},contentRef:function(e){this.content=e},headerContainerRef:function(e){this.headerContainer=e},footerContainerRef:function(e){this.footerContainer=e},closeButtonRef:function(e){this.closeButton=e?e.$el:void 0},bindDocumentKeyDownListener:function(){this.documentKeydownListener||(this.documentKeydownListener=this.onKeydown,document.addEventListener("keydown",this.documentKeydownListener))},unbindDocumentKeyDownListener:function(){this.documentKeydownListener&&(document.removeEventListener("keydown",this.documentKeydownListener),this.documentKeydownListener=null)},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){e.isOutsideClicked(i)&&e.hide()},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},isOutsideClicked:function(e){return this.container&&!this.container.contains(e.target)}},computed:{fullScreen:function(){return this.position==="full"},closeAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.close:void 0},dataP:function(){return ft(ue(ue(ue({"full-screen":this.position==="full"},this.position,this.position),"open",this.containerVisible),"modal",this.modal))}},directives:{focustrap:Rt},components:{Button:bt,Portal:gt,TimesIcon:Ft}},Dn=["data-p"],$n=["aria-modal","data-p"];function zn(t,e,i,r,s,n){var f=B("Button"),v=B("Portal"),c=yt("focustrap");return l(),S(v,null,{default:C(function(){return[s.containerVisible?(l(),m("div",b({key:0,ref:n.maskRef,onMousedown:e[0]||(e[0]=function(){return n.onMaskClick&&n.onMaskClick.apply(n,arguments)}),class:t.cx("mask"),style:t.sx("mask",!0,{position:t.position,modal:t.modal}),"data-p":n.dataP},t.ptm("mask")),[k(ae,b({name:"p-drawer",onEnter:n.onEnter,onAfterEnter:n.onAfterEnter,onBeforeLeave:n.onBeforeLeave,onLeave:n.onLeave,onAfterLeave:n.onAfterLeave,appear:""},t.ptm("transition")),{default:C(function(){return[t.visible?kt((l(),m("div",b({key:0,ref:n.containerRef,class:t.cx("root"),style:t.sx("root"),role:"complementary","aria-modal":t.modal,"data-p":n.dataP},t.ptmi("root")),[t.$slots.container?$(t.$slots,"container",{key:0,closeCallback:n.hide}):(l(),m(z,{key:1},[d("div",b({ref:n.headerContainerRef,class:t.cx("header")},t.ptm("header")),[$(t.$slots,"header",{class:L(t.cx("title"))},function(){return[t.header?(l(),m("div",b({key:0,class:t.cx("title")},t.ptm("title")),K(t.header),17)):w("",!0)]}),t.showCloseIcon?$(t.$slots,"closebutton",{key:0,closeCallback:n.hide},function(){return[k(f,b({ref:n.closeButtonRef,type:"button",class:t.cx("pcCloseButton"),"aria-label":n.closeAriaLabel,unstyled:t.unstyled,onClick:n.hide},t.closeButtonProps,{pt:t.ptm("pcCloseButton"),"data-pc-group-section":"iconcontainer"}),{icon:C(function(a){return[$(t.$slots,"closeicon",{},function(){return[(l(),S(q(t.closeIcon?"span":"TimesIcon"),b({class:[t.closeIcon,a.class]},t.ptm("pcCloseButton").icon),null,16,["class"]))]})]}),_:3},16,["class","aria-label","unstyled","onClick","pt"])]}):w("",!0)],16),d("div",b({ref:n.contentRef,class:t.cx("content")},t.ptm("content")),[$(t.$slots,"default")],16),t.$slots.footer?(l(),m("div",b({key:0,ref:n.footerContainerRef,class:t.cx("footer")},t.ptm("footer")),[$(t.$slots,"footer")],16)):w("",!0)],64))],16,$n)),[[c]]):w("",!0)]}),_:3},16,["onEnter","onAfterEnter","onBeforeLeave","onLeave","onAfterLeave"])],16,Dn)):w("",!0)]}),_:3})}_t.render=zn;const Bn={class:"mobile-menu-header"},Tn={class:"mobile-menu-content"},On={key:1,class:"menu-separator"},Vn=H({__name:"mobileMenu",props:{items:{type:Array,required:!0,default:()=>[]}},setup(t){const e=me(),i=re(),{isMobileMenuVisible:r}=R(i),s=n=>{(n.children&&n.children.length===0||n.children===void 0||n.children===null)&&i.closeMobileMenu()};return(n,f)=>{const v=wt,c=_t;return l(),S(c,{visible:g(r),"onUpdate:visible":f[1]||(f[1]=a=>jt(r)?r.value=a:null),position:"left",class:"flexirates-mobile-menu-drawer",modal:!0,dismissable:!0,"show-close-icon":!1},{default:C(()=>[d("div",Bn,[k(v,{src:g(Lt),alt:"Logo",width:"160",onClick:f[0]||(f[0]=a=>{g(e).push("/"),g(i).closeMobileMenu()})},null,8,["src"])]),d("div",Tn,[(l(!0),m(z,null,Y(t.items,(a,u)=>{var p;return l(),m(z,{key:u},[(p=a==null?void 0:a.meta)!=null&&p.isSeparator?(l(),m("div",On)):(l(),S(Mt,{key:0,item:a,slim:!1,onClick:I=>s(a)},null,8,["item","onClick"]))],64)}),128))])]),_:1},8,["visible"])}}}),Fn=he(Vn,[["__scopeId","data-v-9343ef64"]]),Rn={class:"sidebar-header"},Un={class:"sidebar-footer"},Hn=H({__name:"appSidebar",setup(t){const{t:e}=pt(),i=fe(),{isSidebarSlim:r,isDarkTheme:s}=R(i),n=vt(),{userMenu:f}=R(n),v=a=>{var p,I,O;if(!a)return null;const u=(p=a==null?void 0:a.meta)!=null&&p.i18nKey?e(a.meta.i18nKey):(I=a==null?void 0:a.meta)==null?void 0:I.label;return{...a,meta:{...a.meta,label:u},children:(O=a.children)==null?void 0:O.map(v).filter(N=>N!==null)}},c=A(()=>f.value.map(a=>v(a)));return(a,u)=>{const p=wt;return l(),m("aside",{class:L(["app-flexirates-sidebar",{"sidebar-slim":g(r),"theme-dark":g(s)}])},[d("div",Rn,[g(r)?(l(),S(p,{key:1,src:g(ti),alt:"Image",width:"140px",class:"app-flexirates-merchant-sidebar-mini-logo",onClick:u[1]||(u[1]=I=>a.$router.push({path:"/"}))},null,8,["src"])):(l(),S(p,{key:0,src:g(Lt),alt:"Image",width:"100%",class:"app-flexirates-merchant-sidebar-logo",onClick:u[0]||(u[0]=I=>a.$router.push({path:"/"}))},null,8,["src"])),u[2]||(u[2]=d("div",{class:"sidebar-header-bottom-line"},null,-1))]),k(Pn,{items:c.value,slim:g(r)},null,8,["items","slim"]),k(Fn,{items:c.value},null,8,["items"]),d("div",Un,[k(p,{src:g(ii),width:"200px",height:"24px",alt:"Image"},null,8,["src"])])],2)}}}),Nn={class:"layout-flexirates-sidebar"},jn={class:"loading-container"},Gn=H({__name:"index",setup(t){const e=U(null),i=re(),r=fe(),{isSidebarVisible:s,isSidebarSlim:n}=R(r),f=U(!1),v=()=>{f.value=window.innerWidth<992};de(()=>{v(),window.addEventListener("resize",v)}),ce(()=>{window.removeEventListener("resize",v)});const{hasVerticalScrollbar:c}=oi(e);return document.title="Flexirates",(a,u)=>{const p=B("router-view");return l(),m("div",{class:L(["layout-flexirates-wrapper",{"sidebar-hidden":!g(s),"sidebar-slim":g(n),"mobile-layout":f.value}])},[d("div",Nn,[k(Hn)]),d("div",{class:L(["layout-flexirates-header",{"sidebar-hidden":!g(s),"sidebar-slim":g(n)}])},[k(Qi)],2),d("div",{class:L(["layout-flexirates-content-wrapper",{"has-vertical-scrollbar":g(c)}])},[d("div",{ref_key:"contentRef",ref:e,class:"layout-flexirates-content"},[k(p,null,{default:C(({Component:I,route:O})=>[I?(l(),S(ae,{key:0,name:"fade",mode:"out-in"},{default:C(()=>[(l(),S(Gt,{include:g(i).keepAliveComponents,max:10},[(l(),S(qt,null,{fallback:C(()=>[d("div",jn,[k(g(Wt),{style:{width:"50px",height:"50px"},"stroke-width":"4",fill:"var(--surface-ground)","animation-duration":".5s"}),u[0]||(u[0]=d("p",null,"Loading...",-1))])]),default:C(()=>[(l(),S(q(I),{key:O.fullPath}))]),_:2},1024))],1032,["include"]))]),_:2},1024)):w("",!0)]),_:1})],512)],2)],2)}}}),cr=he(Gn,[["__scopeId","data-v-f711c884"]]);export{cr as default};
