import{_ as _e}from"./index.vue_vue_type_style_index_0_lang-BcPs9O2U.js";import{a0 as he,a1 as be,aA as z,c as Q,o as Z,l as xe,H as ge,d as we,y as Pe,i as u,j as H,p as Se,h as t,e as r,u as n,g as d,s as Ae,n as T,f as y,t as i}from"./index-Cr_XsrqN.js";import{a as Ee}from"./index-BiHyKo52.js";import{a as De}from"./index-fEC-lipk.js";import{F as X}from"./index-B1qA3Nva.js";import{d as C}from"./dayjs.min-BN044Q0-.js";import{_ as J}from"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import{u as K}from"./useDict-B_iRj5rJ.js";import{u as Te}from"./useExport-BWofQ1KN.js";import{u as Ce}from"./useRequestList-B2228N3W.js";import{S as h}from"./BaseRangeDatePicker.vue_vue_type_style_index_0_lang-jkTsn2Nm.js";import{a as $e,g as Ve,b as Le,c as Me,e as ke}from"./transactions-DCpZnSzG.js";import{f as Re}from"./date-B2oFx4oA.js";import{a as Be}from"./dict-CFM3a_vV.js";import{_ as Fe}from"./search.vue_vue_type_style_index_0_lang-Dkooslgm.js";import Ye from"./transactionTable-CBMAOKKT.js";import{_ as Ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import"./index-CQnajQNe.js";import"./index-BH-zSNAS.js";import"./index-DdGtf4Je.js";import"./index-Dvu_aI8r.js";import"./index-DhbL6JTo.js";import"./index-BwvvB-v3.js";import"./seatchItem.vue_vue_type_script_setup_true_lang-C3LrCJS1.js";var Oe=he`
    .p-radiobutton-group {
        display: inline-flex;
    }
`,Ue={root:"p-radiobutton-group p-component"},je=be.extend({name:"radiobuttongroup",style:Oe,classes:Ue}),Ge={name:"BaseRadioButtonGroup",extends:Ee,style:je,provide:function(){return{$pcRadioButtonGroup:this,$parentInstance:this}}},ee={name:"RadioButtonGroup",extends:Ge,inheritAttrs:!1,data:function(){return{groupName:this.name}},watch:{name:function(b){this.groupName=b||z("radiobutton-group-")}},mounted:function(){this.groupName=this.groupName||z("radiobutton-group-")}};function qe(p,b,$,V,w,L){return Z(),Q("div",ge({class:p.cx("root")},p.ptmi("root")),[xe(p.$slots,"default")],16)}ee.render=qe;const Ie={class:"transactions-content"},We={class:"bg-white p-4 w-full rounded-lg"},ze={class:"underline font-medium"},He={class:"flex justify-center w-full"},Xe={class:"dialog"},Je={class:"detail-content"},Ke={class:"detail-content-item"},Qe={class:"detail-content-item"},Ze={class:"detail-content-text font-semibold"},et={class:"detail-content-item"},tt={class:"detail-content-text font-semibold"},st={class:"detail-content-item"},at={class:"detail-content-text font-semibold"},lt={class:"detail-content-item"},ot={class:"detail-content-text font-semibold"},nt={class:"detail-content-item"},it={class:"detail-content-text font-semibold"},rt={class:"detail-content-item"},dt={class:"detail-content-text font-semibold"},ut={class:"w-58"},ct={class:"text-center text-[#031f73] text-2xl font-semibold"},pt={class:"mt-8 flex flex-col gap-3"},mt={class:"pop"},ft={class:"flex justify-between gap-10"},vt={class:"flex flex-col gap-4"},yt={class:"flex flex-col gap-2"},_t={class:"flex items-center gap-2"},ht={class:"flex flex-col gap-4"},bt={class:"flex flex-col gap-2"},xt={class:"flex items-center gap-2"},gt={class:"mt-6"},wt=we({name:"FlexiratesTransactionsList",__name:"index",setup(p){const b=Pe(),$=Ce({requestFn:Me}),{list:V,loading:w,total:L,refresh:P,search:M,onPageChange:te,failed:se,failureMessage:ae,setSearchParams:x}=$,le=u([{field:"status",header:"Status",style:{minWidth:"110px"},template:"status"},{field:"property_name",header:"Property",style:{minWidth:"110px"},template:"property_name"},{field:"address",header:"Address",style:{minWidth:"150px"},template:"address"},{field:"created_at",header:"Date",style:{minWidth:"110px"},template:"date",sortable:!0,sortField:"created_at"},{field:"payment_method",header:"Payment Method",style:{minWidth:"110px"},template:"payment_method"},{field:"amount",header:"Amount",style:{width:"140px"},template:"amount"},{field:"action",header:"",style:{width:"140px"},template:"action",isCustom:!0}]),m=u({property_ids:null,status:null,"created_at[]":[]}),oe=H(()=>[{name:"property_ids",label:"Property",type:h.MULTISELECT,options:R.value,placeholder:"Please select property",defaultValue:[],width:"380px"},{name:"status",label:"Status",type:h.SELECT,placeholder:"All",options:B.value,defaultValue:""},{name:"created_at[]",label:"Date range",type:h.DATE_RANGE,placeholder:"Please select date range",defaultValue:[]}]),k=u([]),R=u([]),B=u([]),S=u([]);K("trans_amount_range",l=>{k.value=l});const{getLabel:F}=K("trans_status",l=>{B.value=Be(l,{label:"All",value:null})}),ne=H(()=>[{name:"amount_range",label:"Amount",type:h.SELECT,placeholder:"Please select amount range.",options:k.value,defaultValue:null},{name:"payment_method_ids",label:"Payment Method",type:h.MULTISELECT,placeholder:"Please select payment method.",options:S.value,maxLabels:5,defaultValue:[]}]),ie=()=>{x(m.value),M()},re=()=>{P()},de=l=>{const{sortField:e,sortOrder:a}=l;x({sort_by:e,sort_order:a===1?"asc":"desc"}),M()},ue=l=>{switch(l){case 1:return"pi-verified";case 2:return"pi-times-circle";default:return"pi-hourglass"}},Y=l=>{switch(l){case 1:return"#39b54a";case 2:return"#eb001b";default:return"#545454"}},ce=l=>{var e;return(e=S.value.find(a=>a.value===l))==null?void 0:e.label},A=u(!1),c=u({}),N=u(!1),pe=async l=>{try{N.value=!0;const e=await Le(l);c.value=e.data}catch(e){console.log(e)}finally{N.value=!1}A.value=!0},E=u(),f=u({type:"csv",schedule:"all"}),me=l=>{E.value.toggle(l)},_=u(!1),fe=l=>{E.value.hide(l),_.value=!0},{isExporting:ve,handleExport:ye}=Te({exportFn:ke,getParams:()=>{var e;const l={export_cols:["status","property_name","date","amount","payment_method"],export_type:f.value.type};if(f.value.schedule==="all")return l;{const a={...m.value};for(const o in a){if(o==="created_at[]"&&Array.isArray(a[o])&&a[o].length===2){const v=a[o],g=C(v[0]).format("YYYY-MM-DD"),s=v!=null&&v[1]?C(v[1]).format("YYYY-MM-DD"):g;a[o]=[g,s]}(a[o]===null||a[o]===void 0||a[o]==="")&&delete a[o],Array.isArray(a[o])&&((e=a[o])==null?void 0:e.length)===0&&delete a[o]}return Object.assign(l,a),l}},onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file...",life:3e3})},onExportSuccess:()=>{_.value=!1}});return Se(()=>{const l=b.query;l&&Object.keys(l).length>0?(m.value={property_ids:l.id?[Number(l.id)]:[]},x(m.value),P()):(m.value={property_ids:[],status:"","created_at[]":[]},x(m.value),P()),Promise.all([$e({is_all:1}).then(e=>{R.value=e.data.map(a=>({label:a.street_address,value:a.id}))}),Ve().then(e=>{S.value=e.data.map(a=>({label:a.payment_method,value:a.id}))})])}),(l,e)=>{const a=Ae,o=De,v=ee,g=_e;return Z(),Q("div",null,[e[20]||(e[20]=t("div",{class:"bg-white dark:bg-gray-800 transaction-header rounded-2xl flex items-center mb-6"},[t("div",{class:"text-3xl font-bold"}," Transactions ")],-1)),t("div",Ie,[t("div",null,[r(Fe,{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=s=>m.value=s),loading:n(w),"basic-search-fields":oe.value,"advanced-search-fields":ne.value,class:"invoice-list-search",onSearch:ie},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"])]),t("div",We,[r(Ye,{class:"flexirates-transactions-table",value:n(V),columns:le.value,"show-edit-column":!1,"show-search-bar":!1,scrollable:!0,"show-multiple-column":!1,loading:n(w),paginator:!1,rows:50,"total-records":n(L),lazy:!0,"data-key":"id","sort-mode":"single","search-placeholder":"Search","type-placeholder":"Filter By",failed:n(se),"failure-message":n(ae),"striped-rows":!1,onPage:n(te),onSort:de,onRefresh:re},{status:d(({data:s})=>[t("div",{class:"flex items-center gap-2",style:T({color:Y(s.status)})},[t("i",{class:y(["pi",ue(s.status)])},null,2),t("span",ze,i(n(F)(s.status)),1)],4)]),property_name:d(({data:s})=>[t("span",{class:y(["underline",{"text-[#eb001b]":s.status===2}])},i(s==null?void 0:s.property_name),3)]),address:d(({data:s})=>[t("span",{class:y(["underline",{"text-[#eb001b]":s.status===2}])},i(s.property_address),3)]),payment_method:d(({data:s})=>[t("span",{class:y({"text-[#eb001b]":s.status===2})},i(ce(s.payment_method_id)),3)]),date:d(({data:s})=>[t("span",{class:y({"text-[#eb001b]":s.status===2})},i(n(C)(s.created_at).format("DD MMM YYYY")),3)]),amount:d(({data:s})=>[t("span",{class:y({"text-[#eb001b]":s.status===2})},i(s.payment_currency)+" "+i(n(X).formatAmount(s==null?void 0:s.payment_amount)),3)]),action:d(({data:s})=>[r(a,{severity:"secondary",variant:"text",label:"View Details",class:"underline",style:T({color:s.status===2?"#eb001b":""}),onClick:D=>pe(s.id)},null,8,["style","onClick"])]),actionHeader:d(()=>[t("div",He,[r(a,{label:"EXPORT",class:"btn",onClick:e[1]||(e[1]=s=>me(s))})])]),_:1},8,["value","columns","loading","total-records","failed","failure-message","onPage"])])]),t("div",Xe,[r(J,{visible:A.value,title:"Transaction Details","onUpdate:visible":e[2]||(e[2]=s=>A.value=s)},{content:d(()=>{var s,D,O,U,j,G,q,I,W;return[t("div",Je,[t("div",Ke,[e[9]||(e[9]=t("div",{class:"detail-content-label"}," Status: ",-1)),t("div",{class:"detail-content-text font-semibold",style:T({color:Y((s=c.value)==null?void 0:s.status)})},i(n(F)((D=c.value)==null?void 0:D.status)),5)]),t("div",Qe,[e[10]||(e[10]=t("div",{class:"detail-content-label"}," Amount: ",-1)),t("div",Ze,i(`${(O=c.value)==null?void 0:O.payment_currency} ${n(X).formatAmount((U=c.value)==null?void 0:U.payment_amount)}`),1)]),t("div",et,[e[11]||(e[11]=t("div",{class:"detail-content-label"}," Date: ",-1)),t("div",tt,i(n(Re)((j=c.value)==null?void 0:j.created_at)),1)]),t("div",st,[e[12]||(e[12]=t("div",{class:"detail-content-label"}," Payment Method: ",-1)),t("div",at,i((G=c.value)==null?void 0:G.payment_method),1)]),t("div",lt,[e[13]||(e[13]=t("div",{class:"detail-content-label"}," Nickname: ",-1)),t("div",ot,i((q=c.value)==null?void 0:q.property_name),1)]),t("div",nt,[e[14]||(e[14]=t("div",{class:"detail-content-label"}," Property Address: ",-1)),t("div",it,i((I=c.value)==null?void 0:I.property_address),1)]),t("div",rt,[e[15]||(e[15]=t("div",{class:"detail-content-label"}," Postcode: ",-1)),t("div",dt,i((W=c.value)==null?void 0:W.property_postcode),1)])])]}),_:1},8,["visible"]),r(J,{visible:_.value,"onUpdate:visible":e[5]||(e[5]=s=>_.value=s)},{content:d(()=>[t("div",ut,[t("div",ct," Are you sure you want to export "+i(f.value.schedule==="all"?"all transactions":"this current selection")+"? ",1),t("div",pt,[r(a,{label:"YES",severity:"warn",class:"w-full",loading:n(ve),onClick:e[3]||(e[3]=s=>n(ye)(f.value.type))},null,8,["loading"]),r(a,{label:"CANCEL",class:"w-full",onClick:e[4]||(e[4]=s=>_.value=!1)})])])]),_:1},8,["visible"])]),t("div",mt,[r(g,{ref_key:"exportPopRef",ref:E,title:"Export Options"},{content:d(()=>[t("div",ft,[t("div",vt,[e[17]||(e[17]=t("div",{class:"font-semibold text-xl"}," File Type ",-1)),t("div",yt,[r(v,{modelValue:f.value.type,"onUpdate:modelValue":e[6]||(e[6]=s=>f.value.type=s),name:"type",class:"flex flex-col gap-4"},{default:d(()=>[t("div",_t,[r(o,{"input-id":"csv",value:"csv"}),e[16]||(e[16]=t("label",{for:"csv"},".csv",-1))])]),_:1},8,["modelValue"])])]),t("div",ht,[e[19]||(e[19]=t("div",{class:"font-semibold text-xl"}," Schedules ",-1)),t("div",bt,[r(v,{modelValue:f.value.schedule,"onUpdate:modelValue":e[7]||(e[7]=s=>f.value.schedule=s),name:"schedule",class:"flex flex-col gap-4"},{default:d(()=>[t("div",xt,[r(o,{"input-id":"all",value:"all"}),e[18]||(e[18]=t("label",{for:"cheese"},"All",-1))])]),_:1},8,["modelValue"])])])]),t("div",gt,[r(a,{label:"EXPORT",severity:"warn",class:"btn w-full",onClick:e[8]||(e[8]=s=>fe(s))})])]),_:1},512)])])}}}),Ht=Ne(wt,[["__scopeId","data-v-7c7f57ca"]]);export{Ht as default};
