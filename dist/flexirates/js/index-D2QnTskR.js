var T=(L=>(L[L.WEEKLY=1]="WEEKLY",L[L.FORTNIGHTLY=2]="FORTNIGHTLY",L[L.MONTHLY=3]="MONTHLY",L[L.QUARTERLY=4]="QUARTERLY",L[L.FULL_AMOUNT=5]="FULL_AMOUNT",L))(T||{}),_=(L=>(L[L.CALCULATED_INSTALLMENTS=1]="CALCULATED_INSTALLMENTS",L[L.CUSTOM_INSTALLMENT_AMOUNT=2]="CUSTOM_INSTALLMENT_AMOUNT",L[L.PAY_FULL_AMOUNT_NOW=3]="PAY_FULL_AMOUNT_NOW",L))(_||{}),E=(L=>(L[L.SKIP_NEXT_PAYMENT=1]="SKIP_NEXT_PAYMENT",L[L.PAUSE_SCHEDULE=2]="PAUSE_SCHEDULE",L[L.EDIT_SCHEDULE_DETAILS=3]="EDIT_SCHEDULE_DETAILS",L))(E||{});const A=Object.freeze(Object.defineProperty({__proto__:null,EditPlanType:E,PaymentPlan:_,Plan:T},Symbol.toStringTag,{value:"Module"}));export{E,_ as P,T as a,A as i};
