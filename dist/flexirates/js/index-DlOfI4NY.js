import{d as _,i as m,p as b,q as g,c as f,h as s,v as h,t as a,e as l,g as r,u as c,s as p,n as k,x as B,m as v,o as N}from"./index-Cr_XsrqN.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const x={class:"not-found"},y={class:"content"},C={class:"error-container"},I={class:"error-message"},V={class:"error-description"},w={class:"actions"},H={class:"progress-container"},S={class:"progress-text"},T={class:"progress-bar"},D=_({__name:"index",setup(F){const d=B(),i=()=>{d.push({path:"/"})},u=()=>{d.back()},n=m(0);let t=null;return b(()=>{t=setInterval(()=>{n.value+=1,n.value>=100&&(t&&(clearInterval(t),t=null),i())},50)}),g(()=>{t&&(clearInterval(t),t=null)}),(o,e)=>(N(),f("div",x,[s("div",y,[s("div",C,[e[2]||(e[2]=h('<div class="error-code" data-v-d6db7782><span class="digit" data-v-d6db7782>4</span><div class="planet-container" data-v-d6db7782><div class="planet" data-v-d6db7782></div><div class="orbit" data-v-d6db7782><div class="satellite" data-v-d6db7782></div></div></div><span class="digit" data-v-d6db7782>4</span></div>',1)),s("h2",I,a(o.$t("message.pageNotFound")),1),s("p",V,a(o.$t("message.pageNotFoundDescription")),1),s("div",w,[l(c(p),{class:"p-button-primary go-back-btn",onClick:u},{default:r(()=>[e[0]||(e[0]=s("i",{class:"pi pi-arrow-left"},null,-1)),v(" "+a(o.$t("button.goBack")),1)]),_:1,__:[0]}),l(c(p),{class:"p-button-outlined home-btn",onClick:i},{default:r(()=>[e[1]||(e[1]=s("i",{class:"pi pi-home"},null,-1)),v(" "+a(o.$t("button.backToHome")),1)]),_:1,__:[1]})]),s("div",H,[s("div",S,a(o.$t("message.redirectingToHome"))+" "+a(n.value)+"% ",1),s("div",T,[s("div",{class:"progress-fill",style:k({width:`${n.value}%`})},null,4)])])])])]))}}),E=$(D,[["__scopeId","data-v-d6db7782"]]);export{E as default};
