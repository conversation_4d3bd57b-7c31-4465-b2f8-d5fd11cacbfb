import{s as d}from"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import{d as _,i as f,c as r,o as i,e as m,g as u,h as t,b as v,t as h,l as x}from"./index-Cr_XsrqN.js";const g={class:"px-2 py-4"},b={key:0,class:"popover-header flex justify-between items-center border-b border-[#545454] pb-2"},y={class:"font-semibold text-xl text-[#031f73]"},k={class:"popover-content"},N=_({__name:"index",props:{title:{type:String,default:""}},setup(c,{expose:a}){const s=c,o=f(),l=e=>{o.value.toggle(e)},n=e=>{o.value.hide(e)};return a({toggle:l,hide:n}),(e,C)=>{const p=d;return i(),r("div",null,[m(p,{ref_key:"op",ref:o,class:"custom-flexirates-popover"},{default:u(()=>[t("div",g,[s.title?(i(),r("div",b,[t("div",y,h(s.title),1),t("div",null,[t("i",{class:"pi-times pi cursor-pointer",style:{color:"#0073cf","font-weight":"700","font-size":"16px"},onClick:n})])])):v("",!0),t("div",k,[x(e.$slots,"content")])])]),_:3},512)])}}});export{N as _};
