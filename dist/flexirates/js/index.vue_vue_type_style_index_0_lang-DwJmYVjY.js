import{d as _,a as u,o as l,g as h,h as e,c as v,b,t as g,l as n,k as f}from"./index-Cr_XsrqN.js";const k={class:"dialog-container"},B={key:0,class:"dialog-header"},C={class:"dialog-title"},x={class:"dialog-controls"},y={class:"dialog-content"},N=_({__name:"index",props:{visible:{type:Boolean,default:!1},title:{default:""}},emits:["update:visible","close"],setup(c,{expose:d,emit:r}){const i=c,s=r,t=()=>{s("update:visible",!1),s("close")};return d({handleClose:t}),(o,a)=>{const p=f;return l(),u(p,{visible:o.visible,modal:!0,closable:!0,class:"custom-flexirates-dialog","onUpdate:visible":a[0]||(a[0]=m=>s("update:visible",m)),onHide:t},{container:h(()=>[e("div",k,[i.title?(l(),v("header",B,[e("h2",C,g(i.title),1),e("div",x,[n(o.$slots,"header-status"),e("i",{class:"pi pi-times close-icon",onClick:t})])])):b("",!0),e("div",y,[n(o.$slots,"content")])])]),_:3},8,["visible"])}}});export{N as _};
