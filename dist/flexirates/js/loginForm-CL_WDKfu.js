import{s as se}from"./index-5-OuhCK9.js";import{i as _,j as W,p as X,T as Y,d as ne,U as ie,B as re,V as ue,c as $,h as a,e as m,m as C,f as B,u,E as de,g as p,a as h,b as v,W as ce,x as me,s as pe,z,A as H,G as R,H as J,t as k,I as K,F as ve,r as fe,o as d,X as ge}from"./index-Cr_XsrqN.js";import{s as ye}from"./index-BwvvB-v3.js";import{s as he}from"./index-CEK2t3I_.js";import{s as _e}from"./index--SCaD3QG.js";import{s as ke}from"./index-DdGtf4Je.js";import{t as be,c as we,d as V,f as xe}from"./index.esm-Qe0UyvbF.js";import{G as Ce}from"./index-Cm8e2Mjp.js";import{_ as Ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-CQnajQNe.js";import"./index-6Y5dfxc3.js";function Se(G={}){const{duration:T=60,storageKey:f="countdown_timer",onFinish:w}=G,g=_(!1),s=_(0);let r=null;const U=W(()=>{if(s.value<=0)return"";const c=Math.floor(s.value/60),n=s.value%60;return c>0?`${c}:${n.toString().padStart(2,"0")}`:`${n}s`}),I=()=>{if(s.value>0){const c=Date.now()+s.value*1e3;localStorage.setItem(f,JSON.stringify({endTime:c,duration:T}))}else localStorage.removeItem(f)},P=()=>{const c=localStorage.getItem(f);if(c)try{const{endTime:n}=JSON.parse(c),N=Date.now(),q=Math.round((n-N)/1e3);q>0?(s.value=q,g.value=!0,F()):localStorage.removeItem(f)}catch(n){console.error("Error restoring countdown state:",n),localStorage.removeItem(f)}},D=()=>{g.value||(s.value=T,g.value=!0,I(),F())},F=()=>{r&&clearInterval(r),r=window.setInterval(()=>{s.value--,I(),s.value<=0&&(S(),w&&w())},1e3)},S=()=>{g.value=!1,r&&(clearInterval(r),r=null),localStorage.removeItem(f)},e=()=>{S(),s.value=0};return X(()=>{P(),window.addEventListener("storage",c=>{c.key===f&&P()})}),Y(()=>{r&&(clearInterval(r),r=null)}),{start:D,stop:S,reset:e,counting:g,remainingSeconds:s,formattedTime:U}}const Ee={class:"login-form-wrap"},$e={class:"login-form-content"},Re={key:0,class:"flexirates-register-link"},Te={key:1,class:"flexirates-register-link"},Ie={class:"login-form-type"},Pe={class:"login-form-email"},De={class:"flex gap-4 justify-between tools"},Fe={class:"flex items-center gap-2"},Ne={for:"rememberMe",class:"font-light"},Ue={class:"login-form-phone"},qe={class:"mobile-input-container"},Me={key:0,class:"country-dropdown-menu"},Le=["onClick"],Ae={class:"country-code"},Be={class:"form-tools"},Ge={class:"form-tools-item"},Oe=ne({__name:"loginForm",emits:["changeShowType","sendVerificationCode"],setup(G,{emit:T}){const f=T,{t:w}=ie(),g=_(!1),s=_(!1),r=_(!1),U=me(),I=re(),{isDarkTheme:P}=ue(),{counting:D,formattedTime:F,start:S}=Se({storageKey:"flexirates_verification_code_countdown"}),e=_({email:"",password:"",rememberMe:!1,google_token:"",type:"Email",country_code:"61",mobile_phone:"",verification_code:"",phoneStep:"sendCode"}),c=_(null),n=_(null),N=_(!1);(async()=>{const{data:t}=await ce();N.value=t.allowRegister})();const Q=be(we({type:V().optional(),email:V().when("type",{is:t=>t==="Email",then:t=>t.required("Email is required").email("Email is invalid"),otherwise:t=>t.optional()}),password:V().when("type",{is:t=>t==="Email",then:t=>t.required("Password is required"),otherwise:t=>t.optional()}),google_token:V().when("type",{is:()=>e.value.type==="Email",then:t=>t.required("Google token is required"),otherwise:t=>t.optional()}),rememberMe:xe().optional(),mobile_phone:V().when("type",{is:()=>e.value.type==="Phone",then:t=>t.required("Phone number is required").matches(/^04\d{2} \d{3} \d{3}$/,"Phone number is invalid"),otherwise:t=>t.optional()}),verification_code:V().when("type",{is:()=>e.value.type==="Phone",then:t=>t.required("Verification code is required"),otherwise:t=>t.optional()})})),Z=t=>{t&&(e.value.google_token=t,n.value&&n.value.setFieldValue("google_token",t))},ee=()=>{e.value.google_token="",n.value&&n.value.setFieldValue("google_token","")},oe=()=>{e.value.google_token="",n.value&&n.value.setFieldValue("google_token","")},M=[{name:"AU",code:"+61"}],te=W(()=>M.find(t=>t.code===e.value.country_code)||M[0]),O=async()=>{var t;(t=n.value)==null||t.validateField("mobile_phone").then(async o=>{if(o.valid)try{s.value=!0;const{code:x}=await ge({mobile:e.value.mobile_phone.replace(/\s/g,""),country_code:e.value.country_code});x===0&&(S(),window.$toast.add({severity:"success",summary:"Success",detail:"Verification code sent successfully"}),e.value.phoneStep="verifyCode")}finally{s.value=!1}})},L=async()=>{var o,x,b;const t=await((o=n.value)==null?void 0:o.validate());if(t!=null&&t.valid)try{g.value=!0;const y={...e.value};e.value.type==="Email"?(y.mobile_phone="",y.verification_code="",y.country_code=""):(y.mobile_phone=(x=y.mobile_phone)==null?void 0:x.replace(/\s/g,""),y.email="",y.password=""),await I.login(y),U.push({path:"/home"})}catch{e.value.google_token="",(b=c.value)==null||b.reset()}finally{g.value=!1}};X(()=>{document.addEventListener("click",j)}),Y(()=>{document.removeEventListener("click",j)});const A=_(null),j=t=>{A.value&&!A.value.contains(t.target)&&(r.value=!1)};return(t,o)=>{const x=ke,b=_e,y=he,le=ye,E=pe,ae=se;return d(),$("div",Ee,[a("div",$e,[o[18]||(o[18]=a("h1",{class:"title !mt-0"}," Welcome back ",-1)),o[19]||(o[19]=a("p",{class:"subtitle"},[C(" Login "),a("span",{class:"subtitle-account"},"To Your Account")],-1)),N.value?(d(),$("p",Re,[o[13]||(o[13]=C(" Don’t have an account? ")),a("span",{class:"flexirates-register-link-text",onClick:o[0]||(o[0]=l=>f("changeShowType","register"))}," Register here ")])):(d(),$("p",Te)),a("div",Ie,[a("button",{class:B(["login-form-type-button",{active:e.value.type==="Email"}]),onClick:o[1]||(o[1]=l=>e.value.type="Email")}," Email ",2),a("button",{class:B(["login-form-type-button",{active:e.value.type==="Phone"}]),onClick:o[2]||(o[2]=l=>e.value.type="Phone")}," Mobile ",2)]),m(u(de),{ref_key:"formRef",ref:n,"validation-schema":u(Q),class:"login-form",onSubmit:L},{default:p(()=>[z(a("div",Pe,[m(u(R),{modelValue:e.value.email,"onUpdate:modelValue":o[3]||(o[3]=l=>e.value.email=l),as:"div",class:"flex flex-col gap-2",name:"email"},{default:p(({field:l,errorMessage:i})=>[m(x,J(l,{class:"form-input",name:"email",type:"text",placeholder:u(w)("login.email")}),null,16,["placeholder"]),i?(d(),h(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[C(k(i),1)]),_:2},1024)):v("",!0)]),_:1},8,["modelValue"]),m(u(R),{modelValue:e.value.password,"onUpdate:modelValue":o[4]||(o[4]=l=>e.value.password=l),as:"div",class:"flex flex-col gap-2",name:"password"},{default:p(({field:l,errorMessage:i})=>[m(y,J(l,{type:"text",placeholder:u(w)("login.password"),"toggle-mask":"",fluid:"",feedback:!1}),null,16,["placeholder"]),i?(d(),h(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[C(k(i),1)]),_:2},1024)):v("",!0)]),_:1},8,["modelValue"]),a("div",De,[a("div",Fe,[m(le,{modelValue:e.value.rememberMe,"onUpdate:modelValue":o[5]||(o[5]=l=>e.value.rememberMe=l),name:"rememberMe",binary:""},null,8,["modelValue"]),a("label",Ne,k(u(w)("login.rememberMe")),1)]),m(E,{text:"",label:u(w)("login.forgotPassword"),style:{"--p-button-label-font-weight":"300"},class:"!p-0",onClick:o[6]||(o[6]=l=>f("changeShowType","forgetPassword"))},null,8,["label"])]),m(u(R),{modelValue:e.value.google_token,"onUpdate:modelValue":o[7]||(o[7]=l=>e.value.google_token=l),as:"div",class:"flex flex-col",name:"google_token"},{default:p(({errorMessage:l})=>[o[14]||(o[14]=a("p",{class:"text-lg !m-0 recaptcha-title"}," *CAPTCHA ",-1)),m(Ce,{ref_key:"recaptchaRef",ref:c,name:"google_token",class:"mb-2 mt-2",theme:u(P)?"dark":"light",onVerify:Z,onExpired:ee,onError:oe},null,8,["theme"]),l?(d(),h(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[C(k(l),1)]),_:2},1024)):v("",!0)]),_:1,__:[14]},8,["modelValue"])],512),[[H,e.value.type==="Email"]]),z(a("div",Ue,[m(u(R),{modelValue:e.value.mobile_phone,"onUpdate:modelValue":o[10]||(o[10]=l=>e.value.mobile_phone=l),name:"mobile_phone",as:"div",class:"flex flex-col gap-2"},{default:p(({errorMessage:l})=>[o[16]||(o[16]=a("label",{class:"text-left font-medium mb-1"},"Mobile",-1)),a("div",qe,[a("div",{ref_key:"countryCodeSelectorRef",ref:A,class:"country-code-selector",onClick:o[8]||(o[8]=K(i=>r.value=!r.value,["stop"]))},[a("span",null,k(te.value.name),1),o[15]||(o[15]=a("i",{class:"pi pi-chevron-down"},null,-1)),r.value?(d(),$("div",Me,[(d(),$(ve,null,fe(M,i=>a("div",{key:i.code,class:B(["country-dropdown-item",{active:e.value.country_code===i.code}]),onClick:K(je=>{e.value.country_code=i.code,r.value=!1},["stop"])},[a("span",null,k(i.name),1),a("span",Ae,k(i.code),1)],10,Le)),64))])):v("",!0)],512),m(ae,{id:"phone",modelValue:e.value.mobile_phone,"onUpdate:modelValue":o[9]||(o[9]=i=>e.value.mobile_phone=i),class:"mobile-phone-input",mask:"9999 999 999",placeholder:"04xx xxx xxx"},null,8,["modelValue"])]),l?(d(),h(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[C(k(l),1)]),_:2},1024)):v("",!0)]),_:1,__:[16]},8,["modelValue"]),e.value.phoneStep==="verifyCode"?(d(),h(u(R),{key:0,modelValue:e.value.verification_code,"onUpdate:modelValue":o[12]||(o[12]=l=>e.value.verification_code=l),name:"verification_code",as:"div",class:"flex flex-col gap-2"},{default:p(({errorMessage:l})=>[o[17]||(o[17]=a("label",{class:"text-left font-medium mb-1"},"Verification Code",-1)),m(x,{modelValue:e.value.verification_code,"onUpdate:modelValue":o[11]||(o[11]=i=>e.value.verification_code=i),maxlength:"6"},null,8,["modelValue"]),l?(d(),h(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[C(k(l),1)]),_:2},1024)):v("",!0)]),_:1,__:[17]},8,["modelValue"])):v("",!0)],512),[[H,e.value.type==="Phone"]])]),_:1},8,["validation-schema"])]),a("div",Be,[e.value.type==="Phone"&&e.value.phoneStep==="verifyCode"?(d(),h(E,{key:0,class:"w-full",severity:"warn",text:"",label:u(D)?`RESEND IN ${u(F)}`:"RESEND",disabled:u(D),loading:s.value,onClick:O},null,8,["label","disabled","loading"])):v("",!0),a("div",Ge,[e.value.type==="Email"?(d(),h(E,{key:0,class:"login-submit w-full",loading:g.value,severity:"info",label:"LOG IN",onClick:L},null,8,["loading"])):v("",!0),e.value.type==="Phone"&&e.value.phoneStep==="sendCode"?(d(),h(E,{key:1,loading:s.value,class:"send-access-code-btn w-full",severity:"info",label:"SEND ACCESS CODE",onClick:O},null,8,["loading"])):v("",!0),e.value.type==="Phone"&&e.value.phoneStep==="verifyCode"?(d(),h(E,{key:2,class:"login-submit w-full",loading:g.value,severity:"info",label:"LOG IN",onClick:L},null,8,["loading"])):v("",!0),o[20]||(o[20]=a("div",{class:"h-3"},null,-1))])])])}}}),lo=Ve(Oe,[["__scopeId","data-v-a014c81b"]]);export{lo as default};
