import{s as f}from"./index-CYbSr7Mb.js";import{d as u,i as m,y as c,c as s,h as i,z as _,b as n,e as t,u as p,A as w,f as v,o as a}from"./index-Cr_XsrqN.js";import{_ as h}from"./logo-white-DjPdav-u.js";import{f as y}from"./logo-CO0W3x5n.js";import k from"./bottomInfo-DUKVzEvj.js";import x from"./forgetPassword-CcHtSOqN.js";import C from"./loginForm-CL_WDKfu.js";import P from"./registerForm-CHZ-1o_n.js";import{_ as T}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-6Y5dfxc3.js";import"./flex-login-logo-mini-BGXreUGR.js";import"./index-DdGtf4Je.js";import"./index-BiHyKo52.js";import"./index--SCaD3QG.js";import"./index-CEK2t3I_.js";import"./index-CQnajQNe.js";import"./index.esm-Qe0UyvbF.js";import"./index-5-OuhCK9.js";import"./index-BwvvB-v3.js";import"./index-Cm8e2Mjp.js";import"./property-4kw8xdA_.js";import"./register-OY6L0HKY.js";import"./index-D2QnTskR.js";import"./dayjs.min-BN044Q0-.js";const b={class:"logo-wrap"},I={class:"logo"},S={class:"login-wrap"},V={key:0,class:"forget-password-wrap"},B={key:1,class:"register-wrap"},F=u({__name:"loginView",setup(N){document.title="Flexirates";const l=c(),g=m(l.query.forgetPasswordToken||""),o=m("login");return g.value&&(o.value="forgetPassword"),(z,e)=>{const d=f;return a(),s("div",{class:v(["login-wrapper",{"show-login":["login","forgetPassword"].includes(o.value),"show-register":o.value==="register"}])},[i("div",b,[i("div",I,[t(d,{src:o.value==="register"?p(h):p(y),width:"220px",alt:"Image"},null,8,["src"])])]),_(i("div",S,[t(C,{onChangeShowType:e[0]||(e[0]=r=>o.value=r)})],512),[[w,o.value==="login"]]),o.value==="forgetPassword"?(a(),s("div",V,[t(x,{onChangeShowType:e[1]||(e[1]=r=>o.value=r)})])):n("",!0),o.value==="register"?(a(),s("div",B,[t(P,{onChangeShowType:e[2]||(e[2]=r=>o.value=r)})])):n("",!0),t(k)],2)}}}),so=T(F,[["__scopeId","data-v-1b6333b3"]]);export{so as default};
