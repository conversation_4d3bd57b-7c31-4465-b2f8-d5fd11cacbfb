import{a6 as i,a7 as s,Y as l,i as r}from"./index-Cr_XsrqN.js";const g=t=>i("/notify/list",{params:t}),y=t=>s("/notify/delete",t),p=t=>s("/notify/updateRead",t),c=()=>i("/notify/unreadCount"),v=l("notifications",()=>{const t=r(0);let n=null;const o=async()=>{var e;try{const a=await c();t.value=((e=a==null?void 0:a.data)==null?void 0:e.count)||0}catch{t.value=0}};return{unreadCount:t,getUnreadCount:o,startPolling:()=>{o(),n&&clearInterval(n),n=window.setInterval(o,5e3)},stopPolling:()=>{n&&clearInterval(n),n=null}}});export{p as a,y as d,g,v as u};
