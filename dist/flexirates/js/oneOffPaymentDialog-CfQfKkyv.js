import{d as T,a9 as I,a8 as G,i as s,w as H,c as M,o as u,e as n,a as g,b as h,u as m,g as r,h as o,E as J,G as P,m as C,t as y,s as K}from"./index-Cr_XsrqN.js";import{s as Q}from"./index-fEC-lipk.js";import{s as W}from"./index--SCaD3QG.js";import{s as X}from"./index-BH-zSNAS.js";import{_ as Y}from"./index.vue_vue_type_style_index_0_lang-4VPfa-Ly.js";import{t as Z,c as ee,e as te,a as N}from"./index.esm-Qe0UyvbF.js";import{A as ae}from"./addCardOrBank-CpEoqTrU.js";import{o as oe}from"./home-Bcs-QcZ3.js";import{a as le,g as se}from"./transactions-DCpZnSzG.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-DdGtf4Je.js";import"./index-CQnajQNe.js";import"./index-DsAxJM5H.js";import"./index-5-OuhCK9.js";import"./register-payment-CcrZD2od.js";import"./index.vue_vue_type_style_index_0_lang-DwJmYVjY.js";import"./paymentMethod-CihfujB6.js";import"./country-ZCPlDRUV.js";const re={class:"w-full md:w-[650px]"},de={class:"flex flex-col gap-6 pt-2"},ie={class:"flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-0 relative"},ue={class:"flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-0 relative"},me={class:"flex items-center"},pe={key:0},ce={key:1},fe={class:"flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-0 relative"},ye={class:"flex justify-end mt-2"},ve=T({__name:"oneOffPaymentDialog",props:I({propertyId:{}},{modelValue:{type:Boolean,required:!0,default:!1,type:Boolean},modelModifiers:{}}),emits:I(["success"],["update:modelValue"]),setup(k,{emit:S}){const p=k,q=S,v=G(k,"modelValue"),V=s(!1),B=s(),c=s(!1),D=s(!!p.propertyId),d=s({property:p.propertyId||null,paymentMethod:null,amount:null}),F=Z(ee({property:N().required("Property method is required"),amount:N().required("Amount is required"),paymentMethod:te().test("is-valid-payment-method","Payment method is required",t=>typeof t=="number"&&!Number.isNaN(t)||typeof t=="string"&&t.trim()!=="").required("Payment method is required")})),_=s(!1),A=s([]),b=s(!1),x=s([]),$=async()=>{_.value=!0;try{const t=await le();A.value=t.data.map(e=>({label:e.street_address??"",value:e.id??0}))}catch(t){console.log(t)}finally{_.value=!1}},U=async()=>{b.value=!0;try{const t=await se();x.value=t.data.map(e=>({label:e.payment_method,value:e.id})),x.value.push({label:"Add New Payment Method",value:""})}catch(t){console.log(t)}finally{b.value=!1}},L=t=>{typeof t=="string"&&t===""&&(c.value=!0)},j=()=>{c.value=!1,U()},E=async t=>{try{V.value=!0;const e={property_id:t.property,payment_method_id:t.paymentMethod,amount:t.amount};(await oe(e)).code===0&&(v.value=!1,window.$toast.add({severity:"success",summary:"Success",detail:"ONE-OFF payment successful"}),q("success"))}catch(e){console.log(e)}finally{V.value=!1}};return H(()=>v.value,t=>{t&&($(),U(),console.log("props.propertyId",p.propertyId),p.propertyId&&(d.value.property=p.propertyId))},{immediate:!0}),(t,e)=>{const w=X,O=W,z=Q,R=K;return u(),M("div",null,[n(m(Y),{modelValue:v.value,"onUpdate:modelValue":e[3]||(e[3]=a=>v.value=a),header:"Make a One-off Payment"},{default:r(()=>[o("div",re,[n(m(J),{ref_key:"onceOffFormRef",ref:B,"initial-values":d.value,"validation-schema":m(F),onSubmit:E},{default:r(()=>[o("div",de,[n(m(P),{modelValue:d.value.property,"onUpdate:modelValue":e[0]||(e[0]=a=>d.value.property=a),as:"div",name:"property"},{default:r(({field:a,errorMessage:i,handleChange:f})=>[o("div",ie,[e[5]||(e[5]=o("div",{class:"font-semibold text-[16px] w-88"}," Select Property ",-1)),n(w,{modelValue:a.value,"onUpdate:modelValue":l=>a.value=l,options:A.value,"option-label":"label","option-value":"value",placeholder:"Select a property",fluid:"",size:"large",disabled:D.value,loading:_.value,onValueChange:f},null,8,["modelValue","onUpdate:modelValue","options","disabled","loading","onValueChange"]),i?(u(),g(O,{key:0,severity:"error",variant:"simple",class:"absolute top-[38px] left-60"},{default:r(()=>[C(y(i),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),n(m(P),{modelValue:d.value.paymentMethod,"onUpdate:modelValue":e[1]||(e[1]=a=>d.value.paymentMethod=a),as:"div",name:"paymentMethod"},{default:r(({field:a,errorMessage:i,handleChange:f})=>[o("div",ue,[e[6]||(e[6]=o("div",{class:"font-semibold text-[16px] w-88"}," Payment Method ",-1)),n(w,{modelValue:a.value,"onUpdate:modelValue":l=>a.value=l,options:x.value,"option-label":"label","option-value":"value",class:"w-full",placeholder:"Select or add payment method",fluid:"",size:"large",loading:b.value,onValueChange:l=>{f(l),L(l)}},{option:r(l=>[o("div",me,[l.option.value?(u(),M("div",pe,y(l.option.label),1)):(u(),M("div",ce,y(l.option.label),1))])]),_:2},1032,["modelValue","onUpdate:modelValue","options","loading","onValueChange"]),i?(u(),g(O,{key:0,severity:"error",variant:"simple",class:"absolute top-[38px] left-60"},{default:r(()=>[C(y(i),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),n(m(P),{modelValue:d.value.amount,"onUpdate:modelValue":e[2]||(e[2]=a=>d.value.amount=a),as:"div",name:"amount"},{default:r(({field:a,errorMessage:i,handleChange:f})=>[o("div",fe,[e[7]||(e[7]=o("div",{class:"font-semibold text-[16px] w-88"}," Amount ",-1)),n(z,{modelValue:a.value,"onUpdate:modelValue":l=>a.value=l,placeholder:"Enter amount",fluid:"",size:"large",mode:"currency",currency:"AUD",locale:"en-AU",onValueChange:f},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),i?(u(),g(O,{key:0,severity:"error",variant:"simple",class:"absolute top-[38px] left-60"},{default:r(()=>[C(y(i),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),e[8]||(e[8]=o("div",{class:"once-off-tips"},[o("div",null," Important: "),o("p",null," Making a one-off payment will not affect your current property payment schedule.Once the payment is settled, it will appear in your Transactions List, and your total remaining payments will be reduced accordingly. ")],-1)),o("div",ye,[n(R,{label:"CONFIRM",severity:"warn",class:"confirm-btn",type:"submit",loading:V.value},null,8,["loading"])])])]),_:1},8,["initial-values","validation-schema"])])]),_:1},8,["modelValue"]),c.value?(u(),g(ae,{key:0,visible:c.value,"onUpdate:visible":e[4]||(e[4]=a=>c.value=a),"show-radio":!0,onClose:j},null,8,["visible"])):h("",!0)])}}}),De=ne(ve,[["__scopeId","data-v-9018f740"]]);export{De as default};
