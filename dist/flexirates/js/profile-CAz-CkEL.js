import{a1 as Ne,a2 as Me,a as te,l as be,o as q,g as N,H as z,aP as Je,D as je,j as $e,ad as Qe,p as Ge,J as Ke,ak as ve,aq as pe,bS as Xe,ax as Ye,bT as Pe,i as x,w as Ze,an as qe,bU as er,c as he,I as ke,d as rr,K as tr,B as nr,U as ar,h as I,e as T,t as K,b as Fe,u as U,m as Ae,s as or}from"./index-Cr_XsrqN.js";import{s as ir}from"./index-BwvvB-v3.js";import{s as lr}from"./index--SCaD3QG.js";import{s as ur}from"./index-DdGtf4Je.js";import{s as sr}from"./index-CdyCwuiS.js";import{_ as cr}from"./BaseFileUpload-ptYtZfQ1.js";import{o as fr,c as vr,s as de}from"./types-C7gnwoCB.js";import{_ as pr}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-BquGHVpa.js";var dr={root:"p-formfield p-component"},mr=Ne.extend({name:"formfield",classes:dr}),yr={name:"BaseFormField",extends:Me,style:mr,props:{name:{type:String,default:void 0},resolver:{type:Function,default:void 0},initialValue:{type:null,default:void 0},validateOnValueUpdate:{type:Boolean,default:void 0},validateOnBlur:{type:Boolean,default:void 0},validateOnMount:{type:Boolean,default:void 0},validateOnSubmit:{type:Boolean,default:void 0},as:{type:[String,Object],default:"DIV"},asChild:{type:Boolean,default:!1}},provide:function(){return{$pcFormField:this,$parentInstance:this}}};function Q(e){"@babel/helpers - typeof";return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Q(e)}function Ie(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable})),t.push.apply(t,n)}return t}function Ve(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Ie(Object(t),!0).forEach(function(n){br(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ie(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function br(e,r,t){return(r=hr(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function hr(e){var r=gr(e,"string");return Q(r)=="symbol"?r:r+""}function gr(e,r){if(Q(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(Q(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}var xe={name:"FormField",extends:yr,inheritAttrs:!1,inject:{$pcForm:{default:void 0}},watch:{formControl:{immediate:!0,handler:function(r){var t,n;(t=this.$pcForm)===null||t===void 0||(n=t.register)===null||n===void 0||n.call(t,this.name,r)}}},computed:{formControl:function(){return{name:this.name,resolver:this.resolver,initialValue:this.initialValue,validateOnValueUpdate:this.validateOnValueUpdate,validateOnBlur:this.validateOnBlur,validateOnMount:this.validateOnMount,validateOnSubmit:this.validateOnSubmit}},field:function(){var r;return((r=this.$pcForm)===null||r===void 0||(r=r.fields)===null||r===void 0?void 0:r[this.name])||{}},fieldAttrs:function(){return Ve(Ve({},this.field.props),this.field.states)}}};function _r(e,r,t,n,u,c){return e.asChild?be(e.$slots,"default",z({key:1,class:e.cx("root"),props:c.field.props},c.fieldAttrs)):(q(),te(Je(e.as),z({key:0,class:e.cx("root")},e.ptmi("root")),{default:N(function(){return[be(e.$slots,"default",z({props:c.field.props},c.fieldAttrs))]}),_:3},16,["class"]))}xe.render=_r;function X(e){"@babel/helpers - typeof";return X=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},X(e)}function Be(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable})),t.push.apply(t,n)}return t}function me(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Be(Object(t),!0).forEach(function(n){Le(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Be(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Le(e,r,t){return(r=Or(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function Or(e){var r=Sr(e,"string");return X(r)=="symbol"?r:r+""}function Sr(e,r){if(X(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(X(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}function D(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,t=typeof Symbol=="function"?Symbol:{},n=t.iterator||"@@iterator",u=t.toStringTag||"@@toStringTag";function c(y,g,E,H){var M=g&&g.prototype instanceof o?g:o,C=Object.create(M.prototype);return B(C,"_invoke",function(S,a,i){var l,s,v,_=0,k=i||[],m=!1,b={p:0,n:0,v:e,a:R,f:R.bind(e,4),d:function($,F){return l=$,s=0,v=e,b.n=F,p}};function R(O,$){for(s=O,v=$,r=0;!m&&_&&!F&&r<k.length;r++){var F,w=k[r],G=b.p,A=w[2];O>3?(F=A===$)&&(s=w[4]||3,v=w[5]===e?w[3]:w[5],w[4]=3,w[5]=e):w[0]<=G&&((F=O<2&&G<w[1])?(s=0,b.v=$,b.n=w[1]):G<A&&(F=O<3||w[0]>$||$>A)&&(w[4]=O,w[5]=$,b.n=A,s=0))}if(F||O>1)return p;throw m=!0,$}return function(O,$,F){if(_>1)throw TypeError("Generator is already running");for(m&&$===1&&R($,F),s=$,v=F;(r=s<2?e:v)||!m;){l||(s?s<3?(s>1&&(b.n=-1),R(s,v)):b.n=v:b.v=v);try{if(_=2,l){if(s||(O="next"),r=l[O]){if(!(r=r.call(l,v)))throw TypeError("iterator result is not an object");if(!r.done)return r;v=r.value,s<2&&(s=0)}else s===1&&(r=l.return)&&r.call(l),s<2&&(v=TypeError("The iterator does not provide a '"+O+"' method"),s=1);l=e}else if((r=(m=b.n<0)?v:S.call(a,b))!==p)break}catch(w){l=e,s=1,v=w}finally{_=1}}return{value:r,done:m}}}(y,E,H),!0),C}var p={};function o(){}function f(){}function d(){}r=Object.getPrototypeOf;var h=[][n]?r(r([][n]())):(B(r={},n,function(){return this}),r),j=d.prototype=o.prototype=Object.create(h);function V(y){return Object.setPrototypeOf?Object.setPrototypeOf(y,d):(y.__proto__=d,B(y,u,"GeneratorFunction")),y.prototype=Object.create(j),y}return f.prototype=d,B(j,"constructor",d),B(d,"constructor",f),f.displayName="GeneratorFunction",B(d,u,"GeneratorFunction"),B(j),B(j,u,"Generator"),B(j,n,function(){return this}),B(j,"toString",function(){return"[object Generator]"}),(D=function(){return{w:c,m:V}})()}function B(e,r,t,n){var u=Object.defineProperty;try{u({},"",{})}catch{u=0}B=function(p,o,f,d){if(o)u?u(p,o,{value:f,enumerable:!d,configurable:!d,writable:!d}):p[o]=f;else{var h=function(V,y){B(p,V,function(g){return this._invoke(V,y,g)})};h("next",0),h("throw",1),h("return",2)}},B(e,r,t,n)}function Te(e,r,t,n,u,c,p){try{var o=e[c](p),f=o.value}catch(d){return void t(d)}o.done?r(f):Promise.resolve(f).then(n,u)}function J(e){return function(){var r=this,t=arguments;return new Promise(function(n,u){var c=e.apply(r,t);function p(f){Te(c,n,u,p,o,"next",f)}function o(f){Te(c,n,u,p,o,"throw",f)}p(void 0)})}}function ge(e,r){return Pr(e)||$r(e,r)||jr(e,r)||wr()}function wr(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jr(e,r){if(e){if(typeof e=="string")return Ee(e,r);var t={}.toString.call(e).slice(8,-1);return t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set"?Array.from(e):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Ee(e,r):void 0}}function Ee(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function $r(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n,u,c,p,o=[],f=!0,d=!1;try{if(c=(t=t.call(e)).next,r!==0)for(;!(f=(n=c.call(t)).done)&&(o.push(n.value),o.length!==r);f=!0);}catch(h){d=!0,u=h}finally{try{if(!f&&t.return!=null&&(p=t.return(),Object(p)!==p))return}finally{if(d)throw u}}return o}}function Pr(e){if(Array.isArray(e))return e}function kr(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;Qe()?Ge(e):r?e():Ke(e)}function Fr(e,r,t){var n=x(!0),u=Ze(e,function(c,p){n.value&&r(c,p)},t);return{stop:u,pause:function(){n.value=!1},resume:function(){n.value=!0}}}function ye(e){return Object.entries(e).reduce(function(r,t){var n=ge(t,2),u=n[0],c=n[1];return u.split(/[\.\[\]]+/).filter(Boolean).reduce(function(p,o,f,d){var h;return(h=p[o])!==null&&h!==void 0?h:p[o]=isNaN(d[f+1])?f===d.length-1?c:{}:[]},r),r},{})}function De(e,r){if(!e||!r)return null;try{var t=e[r];if(qe(t))return t}catch{}var n=r.split(/[\.\[\]]+/).filter(Boolean);return n.reduce(function(u,c){return u&&u[c]!==void 0?u[c]:void 0},e)}var Ar=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=je({}),n=je({}),u=$e(function(){return Object.values(t).every(function(S){return!S.invalid})}),c=$e(function(){return ye(t)}),p=function(a,i){return{value:i??De(r.initialValues,a),touched:!1,dirty:!1,pristine:!0,valid:!0,invalid:!1,error:null,errors:[]}},o=function(a,i){var l=ve(i,a);return l===!0||pe(l)&&l.includes(a)},f=function(){var S=J(D().m(function a(i,l){var s,v,_,k,m;return D().w(function(b){for(;;)switch(b.n){case 0:if(v={},!pe(r[i])){b.n=2;break}return b.n=1,y(r[i]);case 1:v=b.v,b.n=4;break;case 2:if(k=(s=r[i])!==null&&s!==void 0?s:l,!k){b.n=4;break}return b.n=3,y();case 3:v=b.v;case 4:if(_=Object.keys(n).filter(function(R){var O;return(O=n[R])===null||O===void 0||(O=O.options)===null||O===void 0?void 0:O[i]})||[],m=qe(_),!m){b.n=6;break}return b.n=5,y(_);case 5:v=b.v;case 6:return b.a(2,v)}},a)}));return function(i,l){return S.apply(this,arguments)}}(),d=function(a,i,l,s){var v,_;((v=i==null?void 0:i[l])!==null&&v!==void 0?v:o(a,(_=r[l])!==null&&_!==void 0?_:s))&&y(a)},h=function(a,i){var l,s;if(!a)return[];(l=n[a])===null||l===void 0||l._watcher.stop(),t[a]||(t[a]=p(a,i==null?void 0:i.initialValue));var v=z((s=ve(i,t[a]))===null||s===void 0?void 0:s.props,ve(i==null?void 0:i.props,t[a]),{name:a,onBlur:function(){t[a].touched=!0,d(a,i,"validateOnBlur")},onInput:function(m){t[a].value=m&&Object.hasOwn(m,"value")?m.value:m.target.value},onChange:function(m){t[a].value=m&&Object.hasOwn(m,"value")?m.value:m.target.type==="checkbox"||m.target.type==="radio"?m.target.checked:m.target.value},onInvalid:function(m){var b;t[a].invalid=!0,t[a].errors=m,t[a].error=(b=m==null?void 0:m[0])!==null&&b!==void 0?b:null}}),_=Fr(function(){return t[a].value},function(k,m){t[a].pristine&&(t[a].pristine=!1),k!==m&&(t[a].dirty=!0),d(a,i,"validateOnValueUpdate",!0)});return n[a]={props:v,states:t[a],options:i,_watcher:_},[t[a],v]},j=function(a){return function(){var i=J(D().m(function l(s){var v;return D().w(function(_){for(;;)switch(_.n){case 0:return _.n=1,f("validateOnSubmit",!0);case 1:return v=_.v,_.a(2,a(me({originalEvent:s,valid:Pe(u),states:Pe(c),reset:g},v)))}},l)}));return function(l){return i.apply(this,arguments)}}()},V=function(a){return function(){var i=J(D().m(function l(s){return D().w(function(v){for(;;)switch(v.n){case 0:return g(),v.a(2,a({originalEvent:s}))}},l)}));return function(l){return i.apply(this,arguments)}}()},y=function(){var S=J(D().m(function a(i){var l,s,v,_,k,m,b,R,O,$,F,w,G,A,ne,ae,oe,ie,le,ue,Z,W,L,ee,_e,se,re,Oe,ce;return D().w(function(P){for(;;)switch(P.n){case 0:return k=Object.entries(t).reduce(function(fe,He){var Se=ge(He,2),we=Se[0],We=Se[1];return fe.names.push(we),fe.values[we]=We.value,fe},{names:[],values:{}}),m=[k.names,ye(k.values)],b=m[0],R=m[1],P.n=1,(s=r.resolver)===null||s===void 0?void 0:s.call(r,{names:b,values:R});case 1:if(_e=l=P.v,ee=_e!==null,!ee){P.n=2;break}ee=l!==void 0;case 2:if(!ee){P.n=3;break}se=l,P.n=4;break;case 3:se={values:R};case 4:O=se,(_=(v=O).errors)!==null&&_!==void 0||(v.errors={}),$=[i].flat(),F=0,w=Object.entries(n);case 5:if(!(F<w.length)){P.n=12;break}if(G=ge(w[F],2),A=G[0],ne=G[1],!($.includes(A)||!i||Ye(O.errors))){P.n=11;break}if(le=(ae=ne.options)===null||ae===void 0?void 0:ae.resolver,!le){P.n=10;break}return Z=ne.states.value,P.n=6,le({values:Z,value:Z,name:A});case 6:if(Oe=ue=P.v,re=Oe!==null,!re){P.n=7;break}re=ue!==void 0;case 7:if(!re){P.n=8;break}ce=ue,P.n=9;break;case 8:ce={values:Z};case 9:W=ce,pe(W.errors)&&(W.errors=Le({},A,W.errors)),O=Xe(O,W);case 10:L=(oe=De(O.errors,A))!==null&&oe!==void 0?oe:[],t[A].invalid=L.length>0,t[A].valid=!t[A].invalid,t[A].errors=L,t[A].error=(ie=L==null?void 0:L[0])!==null&&ie!==void 0?ie:null;case 11:F++,P.n=5;break;case 12:return P.a(2,me(me({},O),{},{errors:ye(O.errors)}))}},a)}));return function(i){return S.apply(this,arguments)}}(),g=function(){Object.keys(t).forEach(function(){var a=J(D().m(function i(l){var s,v;return D().w(function(_){for(;;)switch(_.n){case 0:return v=n[l]._watcher,v.pause(),n[l].states=t[l]=p(l,(s=n[l])===null||s===void 0||(s=s.options)===null||s===void 0?void 0:s.initialValue),_.n=1,Ke();case 1:v.resume();case 2:return _.a(2)}},i)}));return function(i){return a.apply(this,arguments)}}())},E=function(a,i){t[a]!==void 0&&(t[a].value=i)},H=function(a){var i;return(i=n[a])===null||i===void 0?void 0:i.states},M=function(a){Object.keys(a).forEach(function(i){return E(i,a[i])})},C=function(){f("validateOnMount")};return kr(C),{defineField:h,setFieldValue:E,getFieldState:H,handleSubmit:j,handleReset:V,validate:y,setValues:M,reset:g,valid:u,states:c,fields:n}},Ir={root:"p-form p-component"},Vr=Ne.extend({name:"form",classes:Ir}),Br={name:"BaseForm",extends:Me,style:Vr,props:{resolver:{type:Function,default:null},initialValues:{type:Object,default:null},validateOnValueUpdate:{type:[Boolean,Array],default:!0},validateOnBlur:{type:[Boolean,Array],default:!1},validateOnMount:{type:[Boolean,Array],default:!1},validateOnSubmit:{type:[Boolean,Array],default:!0}},provide:function(){return{$pcForm:this,$parentInstance:this}}};function Y(e){"@babel/helpers - typeof";return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Y(e)}function Re(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable})),t.push.apply(t,n)}return t}function Tr(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Re(Object(t),!0).forEach(function(n){Er(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Re(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Er(e,r,t){return(r=Dr(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function Dr(e){var r=Rr(e,"string");return Y(r)=="symbol"?r:r+""}function Rr(e,r){if(Y(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(Y(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}function Cr(e,r){return Gr(e)||Mr(e,r)||Nr(e,r)||Ur()}function Ur(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Nr(e,r){if(e){if(typeof e=="string")return Ce(e,r);var t={}.toString.call(e).slice(8,-1);return t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set"?Array.from(e):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Ce(e,r):void 0}}function Ce(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function Mr(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n,u,c,p,o=[],f=!0,d=!1;try{if(c=(t=t.call(e)).next,r!==0)for(;!(f=(n=c.call(t)).done)&&(o.push(n.value),o.length!==r);f=!0);}catch(h){d=!0,u=h}finally{try{if(!f&&t.return!=null&&(p=t.return(),Object(p)!==p))return}finally{if(d)throw u}}return o}}function Gr(e){if(Array.isArray(e))return e}var ze={name:"Form",extends:Br,inheritAttrs:!1,emits:["submit","reset"],setup:function(r,t){var n=t.emit,u=x(null),c=Ar(r),p=function(){var j;(j=u.value)===null||j===void 0||j.requestSubmit()},o=function(j,V){if(!(V!=null&&V.novalidate)){var y=c.defineField(j,V),g=Cr(y,2),E=g[1];return E}return{}},f=c.handleSubmit(function(h){n("submit",h)}),d=c.handleReset(function(h){n("reset",h)});return Tr({formRef:u,submit:p,register:o,onSubmit:f,onReset:d},er(c,["handleSubmit","handleReset"]))}};function Kr(e,r,t,n,u,c){return q(),he("form",z({ref:"formRef",onSubmit:r[0]||(r[0]=ke(function(){return n.onSubmit&&n.onSubmit.apply(n,arguments)},["prevent"])),onReset:r[1]||(r[1]=ke(function(){return n.onReset&&n.onReset.apply(n,arguments)},["prevent"])),class:e.cx("root")},e.ptmi("root")),[be(e.$slots,"default",z({register:n.register,valid:e.valid,reset:e.reset},e.states))],16)}ze.render=Kr;function qr(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function xr(e){return!qr(e)}function Lr(e,r=!0){return e instanceof Object&&e.constructor===Object&&(r||Object.keys(e).length!==0)}var Ue=(e,r)=>Lr(e)&&e.hasOwnProperty(r)?e:r?{[r]:e}:e,zr=(e,r,t)=>async({values:n,name:u})=>{let{sync:c=!1,raw:p=!1}={};try{let o=await e[c?"parse":"parseAsync"](n,r);return{values:Ue(p?n:o,u),errors:{}}}catch(o){if(Array.isArray(o==null?void 0:o.errors))return{values:Ue(p?n:void 0,u),errors:o.errors.reduce((f,d)=>{let h=xr(d.path)?d.path.join("."):u;return h&&(f[h]||(f[h]=[]),f[h].push(d)),f},{})};throw o}};const Hr={class:"profile-page"},Wr={class:"profile-header"},Jr={class:"profile-info"},Qr={class:"avatar-wrapper"},Xr={class:"profile-name"},Yr={class:"profile-form"},Zr={key:0,class:"loading-container"},et={for:"email"},rt={class:"flex align-items-center"},tt={for:"mfa_check",class:"ml-2"},nt={class:"text-gray-500"},at={class:"flex gap-4 flex-2"},ot=rr({__name:"profile",setup(e){const r=tr(),t=nr(),{t:n}=ar(),u=x(!1),c=x(!1),p=x(zr(fr({name:de().min(1,n("user.profileSettings.nameRequired")),nickname:de().optional(),email:de().email(n("user.profileSettings.emailInvalid")),mfa_check:vr().optional()}))),o=x({avatar:"",name:"",email:"",merchant_id:"",mfa_check:!1}),f=x([]),d=async()=>{u.value=!0;try{const y=await t.getUserInfo();y&&(o.value.name=y.name,o.value.email=y.email,o.value.merchant_id=y.merchant_id,o.value.mfa_check=!!y.mfa_check,o.value.avatar=y.avatar||""),y.avatar?f.value=[{url:y.avatar}]:f.value=[]}finally{u.value=!1}},h=async({valid:y,values:g})=>{if(y){c.value=!0;try{await t.updateUserInfo({user_name:g.name,email:g.email,avatar:g.avatar,mfa_check:g.mfa_check?1:0}),d(),r.add({severity:"success",summary:n("common.success"),detail:n("user.profileSettings.updateSuccess"),life:3e3})}finally{c.value=!1}}},j=async y=>{var E;const g=((E=y.response.data)==null?void 0:E.file_path[0])||"";if(!g){r.add({severity:"error",summary:n("common.error"),detail:n("user.profileSettings.uploadError"),life:3e3});return}o.value.avatar=g,c.value=!0;try{await t.updateUserInfo({user_name:o.value.name,email:o.value.email,mfa_check:o.value.mfa_check?1:0,avatar:g}),await d(),o.value.avatar=g,r.add({severity:"success",summary:n("common.success"),detail:n("user.profileSettings.updateSuccess")})}finally{c.value=!1}},V=()=>{d()};return Ge(async()=>{d()}),(y,g)=>{const E=cr,H=sr,M=ur,C=xe,S=lr,a=ir,i=or;return q(),he("div",Hr,[I("div",Wr,[I("div",Jr,[I("div",Qr,[T(E,{modelValue:f.value,"onUpdate:modelValue":g[0]||(g[0]=l=>f.value=l),mode:"avatar",multiple:!1,"auto-upload":!0,accept:"image/jpeg,image/png,.jpg,.jpeg,.png","max-size":1024*1024*10,class:"avatar-uploader",onSuccess:j},null,8,["modelValue"]),I("div",Xr,[I("h2",null,K(o.value.name),1),I("p",null,K(o.value.email),1)])])])]),I("div",Yr,[u.value?(q(),he("div",Zr,[T(H)])):(q(),te(U(ze),{key:1,"initial-values":o.value,resolver:p.value,class:"max-w-200 space-y-6",onSubmit:h},{default:N(()=>[T(C,{name:"merchant_id",class:"flex flex-col gap-3"},{default:N(()=>[g[1]||(g[1]=I("label",{for:"merchant_id"},"merchant id",-1)),T(M,{"model-value":o.value.merchant_id,disabled:!0,maxlength:"50"},null,8,["model-value"])]),_:1,__:[1]}),T(C,{name:"name",required:"",class:"flex flex-col gap-3"},{default:N(l=>[g[2]||(g[2]=I("label",{for:"name"},"Name",-1)),T(M,{placeholder:U(n)("user.profileSettings.namePlaceholder"),maxlength:"50"},null,8,["placeholder"]),l!=null&&l.invalid?(q(),te(S,{key:0,severity:"error",size:"small",variant:"simple"},{default:N(()=>{var s;return[Ae(K((s=l.error)==null?void 0:s.message),1)]}),_:2},1024)):Fe("",!0)]),_:1,__:[2]}),T(C,{name:"email",required:"",class:"flex flex-col gap-3"},{default:N(l=>[I("label",et,K(U(n)("user.profileSettings.email")),1),T(M,{placeholder:U(n)("user.profileSettings.emailPlaceholder"),maxlength:"50"},null,8,["placeholder"]),l!=null&&l.invalid?(q(),te(S,{key:0,severity:"error",size:"small",variant:"simple"},{default:N(()=>{var s;return[Ae(K((s=l.error)==null?void 0:s.message),1)]}),_:2},1024)):Fe("",!0)]),_:1}),T(C,{name:"mfa_check",class:"flex flex-col gap-3 col-span-2"},{default:N(()=>[I("div",rt,[T(a,{"input-id":"mfa_check",name:"mfa_check",binary:""}),I("label",tt,K(U(n)("user.profileSettings.enable2FA")||"Enable Two-Factor Authentication"),1)]),I("small",nt,K(U(n)("user.profileSettings.2FADescription")||"Enable two-factor authentication to add an extra layer of security to your account."),1)]),_:1}),I("div",at,[T(i,{label:U(n)("user.profileSettings.saveChanges"),severity:"warn",loading:c.value,type:"submit"},null,8,["label","loading"]),T(i,{label:U(n)("user.profileSettings.cancel"),severity:"secondary",type:"button",onClick:V},null,8,["label"])])]),_:1},8,["initial-values","resolver"]))])])}}}),yt=pr(ot,[["__scopeId","data-v-62654bac"]]);export{yt as default};
