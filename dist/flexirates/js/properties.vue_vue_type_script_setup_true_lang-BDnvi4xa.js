import{_ as I}from"./index.vue_vue_type_style_index_0_lang-BcPs9O2U.js";import{_ as U}from"./BaseDataTable-BjLz7Fg0.js";import{d as Y,i as f,x as z,j as q,c as b,o as _,h as s,e as i,m as u,t as c,s as F,b as T,g as l,a as L,z as H,A as J,u as v,f as O,n as G,I as K,J as Q}from"./index-Cr_XsrqN.js";import{_ as X}from"./index.vue_vue_type_style_index_0_lang-4VPfa-Ly.js";import{d as Z}from"./dayjs.min-BN044Q0-.js";import{s as ee}from"./index-BwvvB-v3.js";import{T as te}from"./index-BMUxa2Nh.js";import{u as se}from"./paymentMethod-CihfujB6.js";import{b as oe}from"./bank-pGAkdNCw.js";const ae={class:"p-8 bg-white rounded-2xl"},ne={class:"flex justify-between items-center"},ie={class:"flex flex-col gap-1"},le={class:"text-2xl font-bold border-b-2 border-gray-500 pb-1 w-fit"},re={class:"text-gray-500 font-normal"},de={class:"text-sm text-gray-500 mt-2"},pe={class:"text-gray-500 font-normal"},ce={class:"text-sm text-gray-500"},ue={class:"text-gray-500 font-normal"},me={class:"flex flex-col gap-2"},ye={class:"mt-4"},he={class:"line-clamp-1 w-full"},fe={class:"line-clamp-1 w-full"},_e={class:"w-full flex justify-start items-center"},xe=["src"],be={class:"line-clamp-1 w-full"},ve={key:0,class:"mx-1"},ge={class:"w-full flex justify-start items-center"},we={key:0,class:"font-semibold text-[#39b54a] pl-3"},Ce={class:"w-full flex justify-end"},ke={class:"text-center p-4"},Pe={key:0,class:"flex border-t border-gray-200 pt-4"},De={class:"pop"},Me={class:"max-w-150 mt-4 text-gray-500"},Be={class:"flex gap-2"},Te={class:"mb-4"},Ve={class:"flex flex-col"},Re={class:"flex justify-end gap-4"},Ye=Y({__name:"properties",props:{property:{default:()=>({customer_id:"",id:0,nickname:"",payment_methods:[],postcode:"",property_number:"",street_address:"",suburb:""})},loading:{type:Boolean,default:!1}},emits:["editPaymentMethod","refresh","addCard"],setup(V,{emit:R}){const a=V,k=R,g=f(),P=z(),m=f(!1),y=f(!1),w=f(!1),d=f({property_id:0,id:0,levelPaymentMethod:!0}),S=q(()=>{var n,p,x,h;const o=(p=(n=a.property)==null?void 0:n.payment_methods)==null?void 0:p.some(r=>r==null?void 0:r.bsb),e=(h=(x=a.property)==null?void 0:x.payment_methods)==null?void 0:h.some(r=>r==null?void 0:r.account_no);return[{field:"",header:"",style:{width:"70px"},template:"cardIcon"},o&&{field:"bsb",header:"BSB",style:{width:"200px"},template:"bsb"},e&&{field:"account_no",header:"Account Number",style:{width:"200px"},template:"account_no"},{field:"expiry_date",header:"Expiry Date",style:{width:"10%"},template:"expiry_date"},{field:"status",header:"Status",style:{width:"10%"},template:"status"},{field:"last_payment",header:"Last Payment",template:"last_payment"},{field:"switch",header:"",style:{width:"15%"},template:"switch"},{field:"actions",header:"",template:"actions",style:{width:"10%"}}].filter(Boolean)}),$=async(o,e)=>{d.value.id=e,d.value.property_id=o,d.value.levelPaymentMethod=!1,y.value=!0},W=async()=>{var o,e;if(!d.value.levelPaymentMethod){window.$toast.add({severity:"error",summary:"Error",detail:"Please read and agree to the Terms and Conditions"});return}try{m.value=!0,(await se({property_id:d.value.property_id,id:d.value.id})).code===0&&(a.property.payment_methods=(e=(o=a.property)==null?void 0:o.payment_methods)==null?void 0:e.map(p=>(p.id===d.value.id?p.weight=1:p.weight=2,p)),window.$toast.add({severity:"success",summary:"Success",detail:"Update Weight successful"}),y.value=!1)}catch(n){console.log(n)}finally{m.value=!1}},A=o=>{P.push({name:"paymentMethodsCardDetail",params:{id:o}})},N=o=>{P.push({name:"paymentMethodsBankDetail",params:{id:o}})},D=()=>{k("addCard",a.property.id)},j=(o,e)=>{g.value.hide(),Q(()=>{g.value.toggle(o)})},M=o=>{var e;return`**** ${(e=String(o))==null?void 0:e.slice(-4)}`},E=o=>{switch(o){case 1:return{width:"40px",maxWidth:"40px",height:"28px",borderRadius:"4px"};case 2:return{width:"28px",maxWidth:"28px",height:"28px",borderRadius:"4px"};case 3:return{width:"42px",maxWidth:"42px",height:"28px",borderRadius:"4px"};case 4:return{width:"34px",maxWidth:"34px",height:"32px",borderRadius:"4px"};case 5:return{width:"34px",maxWidth:"34px",height:"28px",borderRadius:"4px"};case 0:case 6:return{width:"28px",maxWidth:"28px",height:"28px"}}return{width:"24px",height:"24px",borderRadius:"4px"}};return(o,e)=>{var h,r,B;const n=F,p=U,x=I;return _(),b("div",ae,[s("div",ne,[s("div",ie,[s("div",le,[e[6]||(e[6]=u(" Property Nickname: ")),s("span",re,c(a.property.nickname),1)]),s("div",de,[e[7]||(e[7]=s("span",{class:"font-bold text-blue-500"}," Property Number: ",-1)),e[8]||(e[8]=u()),s("span",pe,c(a.property.property_number),1)]),s("div",ce,[e[9]||(e[9]=s("span",{class:"font-bold text-blue-500"}," Address: ",-1)),e[10]||(e[10]=u()),s("span",ue,c(a.property.street_address),1)])]),s("div",me,[i(n,{label:"EDIT PAYMENT METHOD",severity:"warn",class:"!px-6",onClick:e[0]||(e[0]=t=>k("editPaymentMethod",a.property.id))})])]),s("div",ye,[i(p,{columns:S.value,value:((h=a.property)==null?void 0:h.payment_methods)||[],loading:!1,paginator:!1,"data-key":"id","show-search-bar":!1,"show-multiple-column":!1,scrollable:!0,"table-style":"min-width: 15rem"},{bsb:l(({data:t})=>[s("div",he,c(t.bsb?M(t.bsb):""),1)]),account_no:l(({data:t})=>[s("div",fe,c(t.account_no?M(t.account_no):""),1)]),cardIcon:l(({data:t})=>[s("div",_e,[s("img",{style:G(E(t.credit_brand)),src:v(oe)(t.credit_brand),alt:"icon"},null,12,xe)])]),status:l(({data:t})=>[s("span",{class:O(["font-semibold",{"text-[#eb001b]":t.status===3,"text-[#39b54a]":t.status===1}])},c(t==null?void 0:t.status_desc),3)]),expiry_date:l(({data:t})=>[s("div",be,[u(c(t.expiration_month)+" ",1),t.expiration_year?(_(),b("span",ve,"/")):T("",!0),u(" "+c(t.expiration_year),1)])]),last_payment:l(({data:t})=>[u(c(t.last_payment_date?v(Z)(t.last_payment_date).format("DD MMM YY"):""),1)]),switch:l(({data:t})=>[s("div",ge,[(t==null?void 0:t.weight)===1?(_(),b("span",we,"Primary")):(_(),L(n,{key:1,label:"Switch as Primary",variant:"text",loading:m.value,style:{color:"#0073cf"},onClick:C=>$(a.property.id,t.id)},null,8,["loading","onClick"])),H(i(n,{icon:"pi pi-info-circle",variant:"text",rounded:"",style:{"font-size":"1.5rem",color:"#eb001b"},onClick:C=>j(C,t.id)},null,8,["onClick"]),[[J,t.is_expiring_soon]])])]),actions:l(({data:t})=>[s("div",Ce,[i(n,{label:"View Details",class:"underline",variant:"text",onClick:C=>t.bsb?N(t.id):A(t.id)},null,8,["onClick"])])]),empty:l(()=>[s("div",ke,[e[11]||(e[11]=s("p",null,"No card records found",-1)),i(n,{label:"Add Card",icon:"pi pi-plus",onClick:D})])]),_:1},8,["columns","value"]),(((B=(r=a==null?void 0:a.property)==null?void 0:r.payment_methods)==null?void 0:B.length)||0)<=1?(_(),b("div",Pe,[i(n,{label:"Add Secondary Payment Method",text:"",icon:"pi pi-plus",onClick:D})])):T("",!0)]),s("div",De,[i(x,{ref_key:"popRef",ref:g,title:"Reminder"},{content:l(()=>e[12]||(e[12]=[s("div",{class:"w-60 mt-4"},[s("p",{class:"mb-4"}," Your Card is expiring with in the next 3 months. "),s("p",null,"Please add a new payment method to avoid any penalties.")],-1)])),_:1},512)]),i(v(X),{modelValue:y.value,"onUpdate:modelValue":e[4]||(e[4]=t=>y.value=t),header:"Confirm Payment Method Change"},{footer:l(()=>[s("div",Re,[i(n,{label:"Cancel",outlined:"",loading:m.value,onClick:e[3]||(e[3]=t=>y.value=!1)},null,8,["loading"]),i(n,{label:"Confirm",severity:"warn",loading:m.value,onClick:W},null,8,["loading"])])]),default:l(()=>[s("div",Me,[e[14]||(e[14]=s("p",{class:"mb-4"}," You're about to change your payment method—such as switching from a bank account to a card, or reassigning your primary and secondary payment preferences. ",-1)),e[15]||(e[15]=s("p",{class:"mb-4"}," Please note: This action may require you to set up a new Direct Debit Request (DDR) to authorise future payments from your selected method. ",-1)),s("div",Be,[s("p",Te,[i(v(ee),{modelValue:d.value.levelPaymentMethod,"onUpdate:modelValue":e[1]||(e[1]=t=>d.value.levelPaymentMethod=t),"input-id":"binary",binary:""},null,8,["modelValue"])]),s("div",Ve,[e[13]||(e[13]=s("label",{for:"binary"}," By ticking, you are confirming that you have read, understood and agree to the ",-1)),s("span",{class:"underline font-bold cursor-pointer w-fit",onClick:e[2]||(e[2]=K(t=>w.value=!0,["stop"]))}," Terms and Conditions. ")])])])]),_:1},8,["modelValue"]),i(te,{modelValue:w.value,"onUpdate:modelValue":e[5]||(e[5]=t=>w.value=t)},null,8,["modelValue"])])}}});export{Ye as _};
