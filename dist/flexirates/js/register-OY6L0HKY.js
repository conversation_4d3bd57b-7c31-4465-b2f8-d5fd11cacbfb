import{P as l}from"./index-D2QnTskR.js";import{d as p}from"./dayjs.min-BN044Q0-.js";import{Y as u,i as o,Z as i}from"./index-Cr_XsrqN.js";function c(e,n=new WeakMap){if(e===null||typeof e!="object")return e;if(n.has(e))return n.get(e);if(e instanceof Date){const t=new Date(e.getTime());return n.set(e,t),t}if(e instanceof RegExp){const t=new RegExp(e.source,e.flags);return n.set(e,t),t}if(Array.isArray(e)){const t=[];n.set(e,t);for(let r=0;r<e.length;r++)t[r]=c(e[r],n);return t}if(typeof e=="object"&&e.constructor===Object){const t={};n.set(e,t);for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=c(e[r],n));return t}const a=Object.create(Object.getPrototypeOf(e));n.set(e,a);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=c(e[t],n));return a}const O=u("flexiratesRegister",()=>{const e={banking_id:0,first_name:"",last_name:"",mobile_phone:"",email:"",property_number:"",verification_code:"",password:"",payment_plan:l.CALCULATED_INSTALLMENTS,payment_plan_schedule:0,first_payment_date:p().add(3,"day").toDate(),last_payment_date:"",flexiRates:0,paymentMethod:2,amount:"",regular_payment_amount:"0",no_of_regular_payment:0,bank:{nickname:"",bsb:"",account_no:"",account_name:""},card:{card_number:"",security_code:"",name_on_card:"",expiry_date:"",email:"",billingAddress:{country:"",first_name:"",last_name:"",company:"",address_line1:"",address_line2:"",city:"",state:"",postal_code:"",phone:""}},is_default_payment_method:!0,terms:!1,notice:!1},n=o(c(e)),a=o(1);return{model:n,currentStep:a,createBanking:async s=>await i(s),reset:()=>{n.value=c(e),a.value=1},setModel:s=>{n.value=s},setCurrentStep:s=>{a.value=s}}},{persist:{omit:["currentStep"]}});export{O as u};
