import{d as E,B as P,C as $,i as N,c as A,h as l,e as a,u as s,E as I,g as i,s as L,x as G,G as f,a as c,b as v,m as n,H as y,t as x,o as p}from"./index-Cr_XsrqN.js";import{s as j}from"./index-CEK2t3I_.js";import{s as z}from"./index--SCaD3QG.js";import{s as D}from"./index-DdGtf4Je.js";import{t as H,c as W,d as _}from"./index.esm-Qe0UyvbF.js";import{g as Y}from"./property-4kw8xdA_.js";import{u as Z}from"./register-OY6L0HKY.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CQnajQNe.js";import"./index-6Y5dfxc3.js";import"./index-BiHyKo52.js";import"./index-D2QnTskR.js";import"./dayjs.min-BN044Q0-.js";const K={class:"register-form-wrap"},M={class:"register-form-content"},O={class:"flex flex-col gap-4 lg:flex-row"},Q={class:"flex flex-col gap-4 lg:flex-row"},X={class:"flex flex-col gap-4 lg:flex-row"},h={class:"flex flex-col gap-4 lg:flex-row"},ee={class:"form-tools"},te={class:"form-tools-item"},le={class:"flex justify-center"},se=E({__name:"registerForm",emits:["changeShowType"],setup(oe,{emit:k}){const q=k,C=G(),S=P(),R=Z(),{model:r}=$(R),b=N(null),g=N(!1),U=H(W({firstName:_().required("First Name is required"),lastName:_().required("Last Name is required"),email:_().required("Email is required").email("Email is invalid"),mobile:_().required("Mobile is required"),property_number:_().required("Property Number is required"),verification_code:_().required("Verification Code is required"),password:_().required("Password required").min(8,"At least 8 characters").test("lowercase","Must include a lowercase letter",m=>/[a-z]/.test(m||"")).test("uppercase","Must include an uppercase letter",m=>/[A-Z]/.test(m||"")).test("number","Must include a number",m=>/\d/.test(m||"")).test("symbol","Must include a symbol",m=>/[!@#$%^&*(),.?":{}|<>]/.test(m||""))})),V=async()=>{var e;const m=await((e=b.value)==null?void 0:e.validate());if(m!=null&&m.valid)try{g.value=!0;const{data:u,code:d}=await Y({property_number:r.value.property_number,verification_code:r.value.verification_code});d===0&&(S.setRegisterAccountInfo(u),C.push({name:"flexiratesRegister"}))}finally{g.value=!1}};return(m,e)=>{const u=D,d=z,F=j,w=L;return p(),A("div",K,[l("div",M,[e[17]||(e[17]=l("h1",{class:"title !mt-0"}," Welcome ",-1)),a(s(I),{ref_key:"formRef",ref:b,"validation-schema":s(U),class:"register-form",onSubmit:V},{default:i(()=>[l("div",O,[a(s(f),{modelValue:s(r).first_name,"onUpdate:modelValue":e[0]||(e[0]=t=>s(r).first_name=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"firstName"},{default:i(({field:t,errorMessage:o})=>[e[8]||(e[8]=l("label",{for:"firstName"},[n(" First Name "),l("span",{class:"text-red-500"},"*")],-1)),a(u,y(t,{class:"form-input",name:"firstName",type:"text"}),null,16),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0)]),_:1,__:[8]},8,["modelValue"]),a(s(f),{modelValue:s(r).last_name,"onUpdate:modelValue":e[1]||(e[1]=t=>s(r).last_name=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"lastName"},{default:i(({field:t,errorMessage:o})=>[e[9]||(e[9]=l("label",{for:"lastName"},[n(" Last Name "),l("span",{class:"text-red-500"},"*")],-1)),a(u,y(t,{class:"form-input",type:"text"}),null,16),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0)]),_:1,__:[9]},8,["modelValue"])]),l("div",Q,[a(s(f),{modelValue:s(r).mobile_phone,"onUpdate:modelValue":e[2]||(e[2]=t=>s(r).mobile_phone=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"mobile"},{default:i(({field:t,errorMessage:o})=>[e[10]||(e[10]=l("label",{for:"mobile"},[n(" Mobile "),l("span",{class:"text-red-500"},"*")],-1)),a(u,y(t,{class:"form-input",name:"mobile",type:"text"}),null,16),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0)]),_:1,__:[10]},8,["modelValue"]),a(s(f),{modelValue:s(r).email,"onUpdate:modelValue":e[3]||(e[3]=t=>s(r).email=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"email"},{default:i(({field:t,errorMessage:o})=>[e[11]||(e[11]=l("label",{for:"email"},[n(" Email "),l("span",{class:"text-red-500"},"*")],-1)),a(u,y(t,{class:"form-input",type:"text","input-props":{autocomplete:"new-password"}}),null,16),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0)]),_:1,__:[11]},8,["modelValue"])]),l("div",X,[a(s(f),{modelValue:s(r).password,"onUpdate:modelValue":e[4]||(e[4]=t=>s(r).password=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"password"},{default:i(({field:t,errorMessage:o,handleChange:T})=>[e[12]||(e[12]=l("label",{for:"password"},[n(" Password "),l("span",{class:"text-red-500"},"*")],-1)),a(F,{modelValue:t.value,"onUpdate:modelValue":B=>t.value=B,feedback:!1,"toggle-mask":"",class:"form-input w-full","input-props":{autocomplete:"new-password"},onValueChange:T},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0)]),_:1,__:[12]},8,["modelValue"])]),e[16]||(e[16]=l("div",{class:"mt-2 lg:mt-4"},[l("h2",{class:"tips-title"}," Account Verification "),l("div",{class:"sub-title"}," This information can be found on your rate notice. ")],-1)),l("div",h,[a(s(f),{modelValue:s(r).property_number,"onUpdate:modelValue":e[5]||(e[5]=t=>s(r).property_number=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"property_number"},{default:i(({field:t,errorMessage:o})=>[e[13]||(e[13]=l("label",{for:"propertyNumber"},[n(" Property Number "),l("span",{class:"text-red-500"},"*")],-1)),a(u,y(t,{class:"form-input",name:"property_number",type:"text"}),null,16),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0),e[14]||(e[14]=l("div",{class:"property-number-tips"}," You will find your property number (ten digit number) in the top right corner on the front page of your rates notice. ",-1))]),_:1,__:[13,14]},8,["modelValue"]),a(s(f),{modelValue:s(r).verification_code,"onUpdate:modelValue":e[6]||(e[6]=t=>s(r).verification_code=t),as:"div",class:"flex flex-col lg:gap-2 flex-1",name:"verification_code"},{default:i(({field:t,errorMessage:o})=>[e[15]||(e[15]=l("label",{for:"verificationCode"},[n(" Verification Code "),l("span",{class:"text-red-500"},"*")],-1)),a(u,y(t,{class:"form-input",type:"text"}),null,16),o?(p(),c(d,{key:0,severity:"error",variant:"simple"},{default:i(()=>[n(x(o),1)]),_:2},1024)):v("",!0)]),_:1,__:[15]},8,["modelValue"])])]),_:1,__:[16]},8,["validation-schema"])]),l("div",ee,[l("div",te,[a(w,{class:"register-submit w-full",loading:g.value,severity:"warn",label:"REGISTER",onClick:V},null,8,["loading"])]),l("div",le,[a(w,{severity:"info",text:"",label:"Back to Login",onClick:e[7]||(e[7]=t=>q("changeShowType","login"))})])])])}}}),ye=J(se,[["__scopeId","data-v-43cb6cc2"]]);export{ye as default};
