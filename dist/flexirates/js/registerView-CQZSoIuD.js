import{d as Se,i as R,B as Ye,C as fe,D as qe,j as Ee,w as be,p as Oe,y as Re,c as g,h as o,e as i,b as p,u as l,m as y,t as u,g as m,E as ve,a as V,f as Y,x as $e,s as Be,G as h,F as D,H as J,I as He,o as r,J as Ie}from"./index-Cr_XsrqN.js";import{s as We}from"./index-CdyCwuiS.js";import{s as Ge}from"./index-BwvvB-v3.js";import{s as Ke}from"./index-5-OuhCK9.js";import{s as Qe}from"./index-DdGtf4Je.js";import{s as ze,a as je}from"./index-fEC-lipk.js";import{s as Je}from"./index-DhbL6JTo.js";import{s as Xe}from"./index--SCaD3QG.js";import{s as Ze}from"./index-BH-zSNAS.js";import{s as ea}from"./index-CYbSr7Mb.js";import{D as A,C as $}from"./decimal-D7roSeZH.js";import{F as W}from"./index-B1qA3Nva.js";import{t as Ve,c as ge,a as he,b as aa,d as C}from"./index.esm-Qe0UyvbF.js";import{d as N}from"./dayjs.min-BN044Q0-.js";import{_ as ta}from"./register-payment-method-CcrZD2od.js";import{f as la}from"./logo-CO0W3x5n.js";import{T as na}from"./index-BMUxa2Nh.js";import{u as oa}from"./country-ZCPlDRUV.js";import{u as sa}from"./register-OY6L0HKY.js";import da from"./bottomInfo-DUKVzEvj.js";import{P as G,a as k}from"./index-D2QnTskR.js";import{_ as ia}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BiHyKo52.js";import"./index-CQnajQNe.js";import"./index-6Y5dfxc3.js";import"./flex-login-logo-mini-BGXreUGR.js";const ra={class:"register-page"},ma={class:"register-form-content"},ua={class:"flex justify-center"},_a={key:0,class:"flex flex-row gap-x-6"},pa={class:"mt-10 flex-1"},ca={class:"payment-frequency"},ya={class:"payment-frequency-content flex flex-col gap-y-2"},fa={class:"mt-1"},ba={class:"form-item__content flex flex-col gap-y-2"},va={key:1,class:"ml-2"},Va={class:"form-item__content flex flex-col gap-y-2"},ga={key:1,class:"ml-2"},ha={class:"form-item__content flex flex-col gap-y-2"},xa={key:1,class:"ml-2"},ka={class:"form-item__content flex flex-col gap-y-2"},Ua={key:0,class:"text-sm text-gray-600 mt-2"},Aa={key:0},Ca={key:1},Na={class:"ml-2"},Ta={key:0,class:"text-sm text-gray-600 ml-2 mt-1"},La={key:0},wa={key:1},Da={class:"form-item"},Ma={class:"form-item__content"},Pa={class:"ml-2"},Fa={key:2,class:"form-item"},Sa={class:"form-item__content"},Ya={class:"ml-2"},qa={class:"form-item"},Ea={class:"form-item__label"},Oa={class:"ml-2"},Ra={key:0,class:"flex-1 pl-8"},$a={class:"form-item__content flex flex-col gap-y-2"},Ba={class:"flex flex-wrap gap-4"},Ha={class:"flex items-center gap-2"},Ia={class:"flex items-center gap-2"},Wa={class:"form-item__content flex flex-col gap-y-2"},Ga={class:"form-item__content flex flex-col gap-y-2"},Ka={class:"form-item__content flex flex-col gap-y-2"},Qa={class:"form-item__content flex flex-col gap-y-2"},za={class:"form-item__content flex flex-col gap-y-2"},ja={class:"form-item mb-4"},Ja={class:"form-item__content flex items-center"},Xa={class:"form-item__content flex flex-col gap-y-2"},Za={class:"form-item__content flex flex-col gap-y-2"},et={class:"form-item__content flex flex-col gap-y-2"},at={class:"billing-address-form mt-6"},tt={class:"field mb-4"},lt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},nt={class:"field"},ot={class:"field"},st={class:"field mb-4"},dt={class:"field mb-4"},it={class:"field mb-4"},rt={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"},mt={class:"field"},ut={class:"field"},_t={class:"field"},pt={class:"field mb-4"},ct={class:"field mb-4"},yt={class:"form-item__content flex flex-col gap-y-2 my-2"},ft={class:"flex items-center gap-x-2"},bt={class:"flex items-center gap-x-2"},vt={for:"terms",class:"text-sm"},Vt={class:"form-item_checkbox flex items-center gap-x-2"},gt={key:1,class:"flex flex-col items-center justify-center gap-y-4 py-12 mt-12"},ht={class:"register-form-actions"},xt=Se({__name:"registerView",setup(kt){const X=Re(),xe=$e(),Z=R(!1),ae=R([{label:"Calculated Installments",value:G.CALCULATED_INSTALLMENTS},{label:"Custom Installment Amount",value:G.CUSTOM_INSTALLMENT_AMOUNT},{label:"Pay Full Amount",value:G.PAY_FULL_AMOUNT_NOW}]),te=R([{label:"Weekly",value:k.WEEKLY},{label:"Fortnightly",value:k.FORTNIGHTLY},{label:"Monthly",value:k.MONTHLY},{label:"Quarterly",value:k.QUARTERLY}]),{registerAccountInfo:b,register:ke}=Ye(),K=sa(),{model:a,currentStep:T}=fe(K),B=qe({register:!1,submit:!1,getRegisterAccountInfo:!1}),O=R(""),z=R(""),Ue=oa(),{countries:le,isLoading:ne,isLoaded:Ae}=fe(Ue),ee=R([]),oe=(n,e=!1)=>{e||(a.value.card.billingAddress.state="");const f=le.value.find(c=>c.iso2===n);f&&f.state&&f.state.length>0?ee.value=f==null?void 0:f.state:ee.value=[]},L=R(),se=R(),Ce=Ve(ge({flexiRates:he().min(0,"Payment plan is required"),first_payment_date:aa().min(N().add(3,"day").startOf("day").toDate(),"First payment date must be 3 days from today or in the future").max(N(b==null?void 0:b.last_payment_date).toDate(),"First payment date must not be after the last payment date"),amount:he().min(0,"Amount is required")})),Ne=Ve(ge({paymentType:C().min(1,"Payment method is required"),bsb:C().optional().test("bsb-format","Please enter a valid BSB number (e.g. 123-456 or 123456)",n=>a.value.paymentMethod===1?!n||n.trim()===""?!1:/^\d{3}-\d{3}$|^\d{6}$/.test(n):!0),account_no:C().optional().test("account_no","Please enter a valid account number (6-10 digits)",n=>a.value.paymentMethod===1?!n||n.trim()===""?!1:/^\d{6,10}$/.test(n):!0),account_name:C().optional().test("account_name","Bank account name is required",n=>!(a.value.paymentMethod===1&&(!n||n.trim()===""))),nickname:C().optional(),card_number:C().optional().test("card-number","Please enter a valid 16-digit card number",n=>a.value.paymentMethod===2?!n||n.trim()===""?!1:/^\d{16}$/.test(n.replace(/\s/g,"")):!0),name_on_card:C().optional().test("name-on-card","Name on card is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),expiry_date:C().optional().test("expiry-date","Please enter a valid expiry date (format: MM/YY)",n=>{if(a.value.paymentMethod===2){if(!n||n.trim()==="")return!1;const[e,f]=n.split("/").map(Number);return new Date(2e3+f,e-1)>=new Date}return!0}),security_code:C().optional().test("security-code","Please enter a valid security code (3-4 digits)",n=>a.value.paymentMethod===2?!n||n.trim()===""?!1:/^\d{3,4}$/.test(n):!0),country:C().optional().test("country","Country is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingFirstName:C().optional().test("billing-first-name","First name is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingLastName:C().optional().test("billing-last-name","Last name is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingCompany:C().optional(),billingAddress:C().optional().test("billing-address","Address is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingAddressLine2:C().optional(),billingCity:C().optional().test("billing-city","City is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingState:C().optional().test("billing-state","State is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingPostcode:C().optional().test("billing-postcode","Postcode is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingPhone:C().optional().test("billing-phone","Phone is required",n=>!(a.value.paymentMethod===2&&(!n||n.trim()===""))),billingEmail:C().optional().test("billing-email","Please enter a valid email address",n=>a.value.paymentMethod===2?!n||n.trim()===""?!1:/^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/.test(n):!0)})),Te=async()=>{var n;if(T.value===1){const e=await((n=L.value)==null?void 0:n.validate());e!=null&&e.valid&&T.value++}window.scrollTo({top:0,behavior:"smooth"})},Le=async()=>{var e;const n=await((e=se.value)==null?void 0:e.validate());if(console.log("result",n),!!(n!=null&&n.valid)){if(n!=null&&n.valid&&!a.value.terms){window.$toast.add({severity:"error",summary:"Error",detail:"You must agree to the terms and conditions"});return}try{B.submit=!0;const f={return_url:window.location.href,type:a.value.paymentMethod===1?1:2,weight:a.value.is_default_payment_method?1:2};a.value.paymentMethod===1?f.bank={bsb:a.value.bank.bsb.replace(/-/g,""),account_no:a.value.bank.account_no,account_name:a.value.bank.account_name,nickname:a.value.bank.nickname}:a.value.paymentMethod===2&&(f.card={first_name:a.value.card.billingAddress.first_name,last_name:a.value.card.billingAddress.last_name,card_number:a.value.card.card_number,security_code:a.value.card.security_code,name_on_card:a.value.card.name_on_card,expiration_year:a.value.card.expiry_date.split("/")[1],expiration_month:a.value.card.expiry_date.split("/")[0],email:a.value.card.email,country_iso2:a.value.card.billingAddress.country,postcode:a.value.card.billingAddress.postal_code,state:a.value.card.billingAddress.state,city:a.value.card.billingAddress.city,line_1:a.value.card.billingAddress.address_line1,line_2:a.value.card.billingAddress.address_line2,phone:a.value.card.billingAddress.phone});const{code:c,data:d}=await K.createBanking(f);if(c===0&&(d.banking_id&&(a.value.banking_id=d==null?void 0:d.banking_id,de(d.banking_id)),d.html)){const w=document.createElement("div");w.innerHTML=d.html,document.body.appendChild(w),Ie(()=>{const F=document.querySelector("#step-up-form");F&&(F==null||F.submit())})}}finally{B.submit=!1}}},de=async n=>{let e=null,f=a.value.payment_plan_schedule;a.value.payment_plan===G.PAY_FULL_AMOUNT_NOW?(e=(b==null?void 0:b.total_amount_due)||0,f=k.FULL_AMOUNT):a.value.payment_plan===G.CALCULATED_INSTALLMENTS?e=a.value.regular_payment_amount:e=a.value.amount;try{B.submit=!0,await ke({bank_id:Number(n),first_name:a.value.first_name,last_name:a.value.last_name,email:a.value.email,mobile:a.value.mobile_phone,password:a.value.password,is_notice:a.value.notice?1:0,payment_frequency:{amount:Number(e),first_payment_date:N(a.value.first_payment_date).format("YYYY-MM-DD"),payment_plan:f},property:{property_number:a.value.property_number,verification_code:a.value.verification_code}})?(window.$toast.add({severity:"success",summary:"Success",detail:"Registration successful"}),K.reset(),xe.replace("/home")):K.setCurrentStep(2)}finally{B.submit=!1}},we=()=>{T.value--},De=(n,e,f)=>{if(!n||!e||f<=0)return"";const c=N(n);let d=c;switch(e){case k.WEEKLY:d=c.add((f-1)*7,"day");break;case k.FORTNIGHTLY:d=c.add((f-1)*14,"day");break;case k.MONTHLY:d=c.add(f-1,"month");break;case k.QUARTERLY:d=c.add((f-1)*3,"month");break;default:d=c;break}return d.format("YYYY-MM-DD")},Me=n=>{if(!a.value.first_payment_date||!a.value.payment_plan_schedule||!n||A(n).lte(0)){a.value.no_of_regular_payment=0,O.value="0",z.value="";return}const e=A((b==null?void 0:b.total_amount_due)||0),f=A(n),c=N(a.value.first_payment_date),d=N("2026-06-30");let w=0;switch(a.value.payment_plan_schedule){case k.QUARTERLY:w=Math.abs(d.diff(c,"quarter"))+1;break;case k.MONTHLY:w=Math.abs(d.diff(c,"month"))+1;break;case k.FORTNIGHTLY:{const q=d.diff(c,"day")+1;w=q<=14?1:Math.ceil(q/14);break}case k.WEEKLY:{const q=d.diff(c,"day")+1;w=q<=7?1:Math.ceil(q/7);break}default:w=1;break}const F=e.div(f).floor().toNumber(),P=Math.min(F,w),x=e.sub(f.mul(P)).toFixed(2,A.ROUND_HALF_UP);a.value.no_of_regular_payment=P,O.value=x,A(x).gt(0)&&P<w&&(a.value.no_of_regular_payment=P+1),z.value=De(a.value.first_payment_date,a.value.payment_plan_schedule,a.value.no_of_regular_payment)},Pe=()=>{var w,F,P,x,q,Q;if(!a.value.first_payment_date||!a.value.payment_plan_schedule||!(b!=null&&b.last_payment_date)){a.value.no_of_regular_payment=0,a.value.amount="0";return}if(L!=null&&L.value&&((F=(w=L==null?void 0:L.value)==null?void 0:w.values)==null?void 0:F.payment_plan)===G.CUSTOM_INSTALLMENT_AMOUNT){(x=(P=L==null?void 0:L.value)==null?void 0:P.values)!=null&&x.amount&&Me((Q=(q=L==null?void 0:L.value)==null?void 0:q.values)==null?void 0:Q.amount);return}const n=(b==null?void 0:b.total_amount_due)||1,e=N(a.value.first_payment_date),f=N(b==null?void 0:b.last_payment_date);let c=0,d="0";switch(a.value.payment_plan_schedule){case k.FULL_AMOUNT:c=1,d=A((b==null?void 0:b.total_amount_due)||1).toFixed(2,A.ROUND_HALF_UP);break;case k.QUARTERLY:c=Math.abs(f.diff(e,"quarter")||1),d=A(n).div(c).toFixed(2,A.ROUND_HALF_UP);break;case k.MONTHLY:c=Math.abs(f.diff(e,"month")||1),d=A(n).div(c).toFixed(2,A.ROUND_HALF_UP);break;case k.FORTNIGHTLY:{const H=f.diff(e,"day")+1;c=H<=14?1:Math.ceil(H/14),d=A(n).div(c).toFixed(2,A.ROUND_HALF_UP);break}case k.WEEKLY:{const H=f.diff(e,"day")+1;c=H<=7?1:Math.ceil(H/7),d=A(n).div(c).toFixed(2,A.ROUND_HALF_UP);break}}a.value.regular_payment_amount=d,a.value.no_of_regular_payment=Math.floor(c)},ie=(n,e,f)=>{if(!n||!e||f<=0)return N().year();const c=N(n);let d=c;switch(e){case k.WEEKLY:d=c.add((f-1)*7,"day");break;case k.FORTNIGHTLY:d=c.add((f-1)*14,"day");break;case k.MONTHLY:d=c.add(f-1,"month");break;case k.QUARTERLY:d=c.add((f-1)*3,"month");break;default:d=c;break}return d.year()},Fe=Ee(()=>{const n=N(),e=N("2025-09-27");return n.isAfter(e)?n.add(30,"day").toDate():N("2025-09-30").toDate()});return be([()=>a.value.amount,()=>a.value.first_payment_date,()=>a.value.payment_plan_schedule,()=>a.value.payment_plan,()=>b==null?void 0:b.last_payment_date],()=>{Pe()},{immediate:!0}),be(()=>Ae,n=>{n&&a.value.card.billingAddress.country&&oe(a.value.card.billingAddress.country,!0)},{immediate:!0}),Oe(()=>{var n;a.value.payment_plan_schedule===0&&(a.value.payment_plan_schedule=b==null?void 0:b.default_payment_plan,(n=L==null?void 0:L.value)==null||n.setFieldValue("payment_plan_schedule",a.value.payment_plan_schedule)),X.query.status==="0"&&X.query.banking_id&&(K.setCurrentStep(3),de(Number(X.query.banking_id)))}),(n,e)=>{var re,me,ue;const f=ea,c=Ze,d=Xe,w=Je,F=ze,P=je,x=Qe,q=Ke,Q=Ge,H=We,j=Be;return r(),g("div",ra,[o("div",{class:Y(["register-form",{"show-payment-method":l(T)===2||l(T)===3}])},[o("div",ma,[o("div",ua,[i(f,{src:l(la),width:"250px",alt:"Image"},null,8,["src"])]),l(T)===1||l(T)===2?(r(),g("div",_a,[o("div",pa,[o("div",ca,[e[34]||(e[34]=o("h2",{class:"text-2xl font-bold mb-4"}," Payment frequency ",-1)),o("div",ya,[o("p",null,[e[32]||(e[32]=o("strong",null,"Address:",-1)),y(" "+u((re=l(b))==null?void 0:re.address)+" "+u((me=l(b))==null?void 0:me.suburb),1)]),o("p",fa,[e[33]||(e[33]=o("strong",null,"Total Amount Due:",-1)),y(" "+u(l(W).formatAmount(new(l(A))(((ue=l(b))==null?void 0:ue.total_amount_due)||0).toFixed(2))),1)])])]),i(l(ve),{ref_key:"paymentPlanRef",ref:L,"validation-schema":l(Ce),class:"flex flex-col gap-x-8 gap-y-4 my-6"},{default:m(({values:t})=>{var s,v,_,_e,pe,ce,ye;return[i(l(h),{modelValue:l(a).payment_plan,"onUpdate:modelValue":e[0]||(e[0]=U=>l(a).payment_plan=U),name:"payment_plan",as:"div",class:"form-item"},{default:m(({errorMessage:U,field:S,handleChange:I})=>{var E;return[e[35]||(e[35]=o("label",{class:"form-item__label is-required"},"Payment Plan",-1)),o("div",ba,[l(T)===1?(r(),g(D,{key:0},[i(c,{modelValue:S.value,"onUpdate:modelValue":M=>S.value=M,options:ae.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Plan",class:"w-full",onValueChange:I},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"]),U?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(U),1)]),_:2},1024)):p("",!0)],64)):(r(),g("span",va,u((E=ae.value.find(M=>(M==null?void 0:M.value)===l(a).payment_plan))==null?void 0:E.label),1))])]}),_:1,__:[35]},8,["modelValue"]),[l($).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,l($).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(t==null?void 0:t.payment_plan)?(r(),V(l(h),{key:0,modelValue:l(a).payment_plan_schedule,"onUpdate:modelValue":e[1]||(e[1]=U=>l(a).payment_plan_schedule=U),name:"payment_plan_schedule",as:"div",class:"form-item"},{default:m(({errorMessage:U,field:S,handleChange:I})=>{var E;return[e[36]||(e[36]=o("label",{class:"form-item__label is-required"},"Payment Schedule",-1)),o("div",Va,[l(T)===1?(r(),g(D,{key:0},[i(c,{modelValue:S.value,"onUpdate:modelValue":M=>S.value=M,options:te.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Schedule",class:"w-full",onValueChange:I},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"]),U?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(U),1)]),_:2},1024)):p("",!0)],64)):(r(),g("span",ga,u((E=te.value.find(M=>(M==null?void 0:M.value)===l(a).payment_plan_schedule))==null?void 0:E.label),1))])]}),_:1,__:[36]},8,["modelValue"])):p("",!0),i(l(h),{modelValue:l(a).first_payment_date,"onUpdate:modelValue":e[2]||(e[2]=U=>l(a).first_payment_date=U),name:"first_payment_date",as:"div",class:"form-item"},{default:m(({errorMessage:U,field:S,handleChange:I})=>[e[37]||(e[37]=o("label",{class:"form-item__label is-required"},"First payment date",-1)),o("div",ha,[l(T)===1?(r(),g(D,{key:0},[i(w,{modelValue:S.value,"onUpdate:modelValue":E=>S.value=E,"min-date":l(N)().add(3,"day").toDate(),"max-date":Fe.value,"date-format":"dd/mm/yy","show-icon":"",class:Y(["w-full",{"p-invalid":U}]),onValueChange:I},null,8,["modelValue","onUpdate:modelValue","min-date","max-date","class","onValueChange"]),U?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(U),1)]),_:2},1024)):p("",!0)],64)):(r(),g("span",xa,u(l(N)(l(a).first_payment_date).format("DD/MM/YYYY")),1))])]),_:1,__:[37]},8,["modelValue"]),(t==null?void 0:t.payment_plan)===l($).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT?(r(),V(l(h),{key:1,modelValue:l(a).amount,"onUpdate:modelValue":e[3]||(e[3]=U=>l(a).amount=U),name:"amount",as:"div",class:"form-item"},{default:m(({errorMessage:U,field:S,handleChange:I})=>{var E;return[e[38]||(e[38]=o("label",{class:"form-item__label is-required"},"Amount",-1)),o("div",ka,[l(T)===1?(r(),g(D,{key:0},[i(F,{modelValue:S.value,"onUpdate:modelValue":M=>S.value=M,"min-fraction-digits":2,"max-fraction-digits":2,max:Number(((E=l(b))==null?void 0:E.total_amount_due)||0),class:"w-full",onValueChange:I},null,8,["modelValue","onUpdate:modelValue","max","onValueChange"]),t.amount>0?(r(),g("div",Ua,[Number(O.value)>0?(r(),g("p",Aa," The last payment amount will be: "+u(l(W).formatAmount(O.value)),1)):Number(O.value)===0?(r(),g("p",Ca," All payments will be exactly "+u(l(W).formatAmount(new(l(A))(t.amount).toFixed(2))),1)):p("",!0)])):p("",!0),U?(r(),V(d,{key:1,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(U),1)]),_:2},1024)):p("",!0)],64)):(r(),g(D,{key:1},[o("span",Na,u(l(a).amount?l(W).formatAmount(new(l(A))(l(a).amount).toFixed(2)):""),1),t.amount>0?(r(),g("div",Ta,[Number(O.value)>0?(r(),g("p",La," The last payment amount will be: "+u(l(W).formatAmount(O.value)),1)):Number(O.value)===0?(r(),g("p",wa," All payments will be exactly "+u(l(W).formatAmount(new(l(A))(t.amount).toFixed(2))),1)):p("",!0)])):p("",!0)],64))])]}),_:2,__:[38]},1032,["modelValue"])):p("",!0),o("div",Da,[e[39]||(e[39]=o("label",{class:"form-item__label"},"Last payment date :",-1)),o("div",Ma,[o("span",Pa,[(t==null?void 0:t.payment_plan)===l($).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT&&z.value?(r(),g(D,{key:0},[y(u(l(N)(z.value).format("DD/MM/YYYY")),1)],64)):(r(),g(D,{key:1},[y(u((s=l(b))!=null&&s.last_payment_date?l(N)((v=l(b))==null?void 0:v.last_payment_date).format("DD/MM/YYYY"):""),1)],64))])])]),(t==null?void 0:t.payment_plan)===l($).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS?(r(),g("div",Fa,[e[40]||(e[40]=o("label",{class:"form-item__label"},"Regular payment amount :",-1)),o("div",Sa,[o("span",Ya,u(l(a).regular_payment_amount?l(W).formatAmount(new(l(A))(l(a).regular_payment_amount).toFixed(2)):""),1)])])):p("",!0),o("div",qa,[o("label",Ea,[y("Number of payments during "+u(l(N)().year())+" ",1),(t==null?void 0:t.payment_plan)===l($).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT&&(t!=null&&t.first_payment_date)&&(t!=null&&t.payment_plan_schedule)&&((_=l(a))!=null&&_.no_of_regular_payment)?(r(),g(D,{key:0},[y(" - "+u(ie(new Date(t==null?void 0:t.first_payment_date),t==null?void 0:t.payment_plan_schedule,(_e=l(a))==null?void 0:_e.no_of_regular_payment)),1)],64)):(t==null?void 0:t.payment_plan)===l($).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS&&(t!=null&&t.first_payment_date)&&(t!=null&&t.payment_plan_schedule)&&((pe=l(a))!=null&&pe.no_of_regular_payment)?(r(),g(D,{key:1},[y(" - "+u(ie(new Date(t==null?void 0:t.first_payment_date),t==null?void 0:t.payment_plan_schedule,(ce=l(a))==null?void 0:ce.no_of_regular_payment)),1)],64)):(ye=l(b))!=null&&ye.last_payment_date?(r(),g(D,{key:2},[y(" - "+u(l(N)(l(b).last_payment_date).year()),1)],64)):p("",!0),e[41]||(e[41]=y(" :"))]),o("span",Oa,[(t==null?void 0:t.payment_plan)===l($).Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW?(r(),g(D,{key:0},[y(" 1 ")],64)):(r(),g(D,{key:1},[y(u(l(a).no_of_regular_payment?l(a).no_of_regular_payment:""),1)],64))])])]}),_:1},8,["validation-schema"]),e[42]||(e[42]=o("p",{class:"text-red-600 text-sm"}," Please Note that if your FlexiRates account accrues 3 dishonoured (declined) payments during the financial year, your FlexiRates registration will be cancelled. ",-1)),e[43]||(e[43]=o("p",{class:"text-red-600 text-sm"}," The regular payment amount may differ from the original setup amount if a payment is skipped or fails. ",-1))]),l(T)===2?(r(),g("div",Ra,[e[63]||(e[63]=o("div",{class:"text-2xl font-bold mb-6",style:{"margin-block":"24px 8px",color:"#545454"}}," Payment Method ",-1)),i(l(ve),{ref_key:"paymentMethodRef",ref:se,"validation-schema":l(Ne),class:"flex flex-col gap-x-8 gap-y-4 my-6"},{default:m(()=>[i(l(h),{modelValue:l(a).paymentMethod,"onUpdate:modelValue":e[4]||(e[4]=t=>l(a).paymentMethod=t),name:"paymentType",as:"div",class:"form-item lg:mb-8"},{default:m(({errorMessage:t,field:s,handleChange:v})=>[e[46]||(e[46]=o("label",{class:"form-item__label"},null,-1)),o("div",$a,[o("div",Ba,[o("div",Ha,[i(P,{modelValue:s.value,"onUpdate:modelValue":_=>s.value=_,"input-id":"card",name:"pizza",value:2,onValueChange:_=>{v(_),l(a).paymentMethod=_}},null,8,["modelValue","onUpdate:modelValue","value","onValueChange"]),e[44]||(e[44]=o("label",{for:"card",class:"cursor-pointer"},"Card Number",-1))]),o("div",Ia,[i(P,{modelValue:s.value,"onUpdate:modelValue":_=>s.value=_,"input-id":"bankAccount",name:"pizza",value:1,onValueChange:_=>{v(_),l(a).paymentMethod=_}},null,8,["modelValue","onUpdate:modelValue","value","onValueChange"]),e[45]||(e[45]=o("label",{for:"bankAccount",class:"cursor-pointer"},"Bank Account",-1))])]),t?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(t),1)]),_:2},1024)):p("",!0)])]),_:1,__:[46]},8,["modelValue"]),l(a).paymentMethod===1?(r(),g(D,{key:0},[i(l(h),{modelValue:l(a).bank.bsb,"onUpdate:modelValue":e[5]||(e[5]=t=>l(a).bank.bsb=t),name:"bsb",as:"div",class:"form-item is-required"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[e[47]||(e[47]=o("label",{class:"form-item__label is-required"},"BSB Number",-1)),o("div",Wa,[i(x,{modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:"w-full",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)])]),_:1,__:[47]},8,["modelValue"]),i(l(h),{modelValue:l(a).bank.account_name,"onUpdate:modelValue":e[6]||(e[6]=t=>l(a).bank.account_name=t),name:"account_name",as:"div",class:"form-item is-required"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[e[48]||(e[48]=o("label",{class:"form-item__label is-required"},"Account Name",-1)),o("div",Ga,[i(x,{modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:"w-full",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)])]),_:1,__:[48]},8,["modelValue"]),i(l(h),{modelValue:l(a).bank.account_no,"onUpdate:modelValue":e[7]||(e[7]=t=>l(a).bank.account_no=t),name:"account_no",as:"div",class:"form-item is-required"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[e[49]||(e[49]=o("label",{class:"form-item__label is-required"},"Account Number",-1)),o("div",Ka,[i(x,{modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:"w-full",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)])]),_:1,__:[49]},8,["modelValue"]),i(l(h),{modelValue:l(a).bank.nickname,"onUpdate:modelValue":e[8]||(e[8]=t=>l(a).bank.nickname=t),name:"nickname",as:"div",class:"form-item is-required"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[e[50]||(e[50]=o("label",{class:"form-item__label"},"Nickname",-1)),o("div",Qa,[i(x,{modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:"w-full",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)])]),_:1,__:[50]},8,["modelValue"])],64)):p("",!0),l(a).paymentMethod===2?(r(),g(D,{key:1},[i(l(h),{modelValue:l(a).card.card_number,"onUpdate:modelValue":e[9]||(e[9]=t=>l(a).card.card_number=t),name:"card_number",as:"div",class:"form-item"},{default:m(({errorMessage:t,field:s,handleChange:v})=>[e[51]||(e[51]=o("label",{class:"form-item__label is-required"},"Card Number",-1)),o("div",za,[i(x,{modelValue:s.value,"onUpdate:modelValue":_=>s.value=_,class:Y(["w-full",{"p-invalid":t}]),onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),t?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(t),1)]),_:2},1024)):p("",!0)])]),_:1,__:[51]},8,["modelValue"]),o("div",ja,[e[52]||(e[52]=o("label",{class:"form-item__label"},null,-1)),o("div",Ja,[i(f,{src:l(ta),width:"100px",alt:"Image"},null,8,["src"])])]),i(l(h),{modelValue:l(a).card.expiry_date,"onUpdate:modelValue":e[10]||(e[10]=t=>l(a).card.expiry_date=t),name:"expiry_date",as:"div",class:"form-item is-required"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[e[53]||(e[53]=o("label",{class:"form-item__label is-required"},"Expiry Date",-1)),o("div",Xa,[i(q,{"model-value":t.value,placeholder:"MM/YY",mask:"99/99",class:Y(["w-full",{"p-invalid":s}]),onValueChange:v},null,8,["model-value","class","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)])]),_:1,__:[53]},8,["modelValue"]),i(l(h),{modelValue:l(a).card.name_on_card,"onUpdate:modelValue":e[11]||(e[11]=t=>l(a).card.name_on_card=t),name:"name_on_card",as:"div",class:"form-item is-required"},{default:m(({errorMessage:t,field:s,handleChange:v})=>[e[54]||(e[54]=o("label",{class:"form-item__label is-required"},"Name on Card",-1)),o("div",Za,[i(x,{modelValue:s.value,"onUpdate:modelValue":_=>s.value=_,class:Y(["w-full",{"p-invalid":t}]),onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),t?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(t),1)]),_:2},1024)):p("",!0)])]),_:1,__:[54]},8,["modelValue"]),i(l(h),{modelValue:l(a).card.security_code,"onUpdate:modelValue":e[12]||(e[12]=t=>l(a).card.security_code=t),name:"security_code",as:"div",class:"form-item is-required"},{default:m(({errorMessage:t,field:s,handleChange:v})=>[e[55]||(e[55]=o("label",{class:"form-item__label is-required"},"CVV",-1)),o("div",et,[i(x,{modelValue:s.value,"onUpdate:modelValue":_=>s.value=_,class:Y(["w-full",{"p-invalid":t}]),onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),t?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(t),1)]),_:2},1024)):p("",!0)])]),_:1,__:[55]},8,["modelValue"]),o("div",at,[e[56]||(e[56]=o("h3",{class:"text-xl font-semibold mb-4"}," Billing address ",-1)),e[57]||(e[57]=o("p",{class:"text-gray-600 mb-4"}," Select the address that matches your card or payment method. ",-1)),o("div",tt,[i(l(h),{modelValue:l(a).card.billingAddress.country,"onUpdate:modelValue":e[13]||(e[13]=t=>l(a).card.billingAddress.country=t),name:"country"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[i(c,{id:"country",modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:"w-full",options:l(le),"option-label":"name","option-value":"iso2",placeholder:"Select country",loading:l(ne),filter:"","show-clear":"",onValueChange:_=>{v(_),oe(_)}},null,8,["modelValue","onUpdate:modelValue","options","loading","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),o("div",lt,[o("div",nt,[i(l(h),{modelValue:l(a).card.billingAddress.first_name,"onUpdate:modelValue":e[14]||(e[14]=t=>l(a).card.billingAddress.first_name=t),name:"billingFirstName"},{default:m(({field:t,errorMessage:s})=>[i(x,J(t,{id:"firstName",class:["w-full",{"p-invalid":s}],placeholder:"First name"}),null,16,["class"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),o("div",ot,[i(l(h),{modelValue:l(a).card.billingAddress.last_name,"onUpdate:modelValue":e[15]||(e[15]=t=>l(a).card.billingAddress.last_name=t),name:"billingLastName"},{default:m(({field:t,errorMessage:s})=>[i(x,J(t,{id:"lastName",class:["w-full",{"p-invalid":s}],placeholder:"Last name"}),null,16,["class"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])])]),o("div",st,[i(l(h),{modelValue:l(a).card.billingAddress.company,"onUpdate:modelValue":e[16]||(e[16]=t=>l(a).card.billingAddress.company=t),name:"billingCompany"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[i(x,{id:"company",modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:Y(["w-full",{"p-invalid":s}]),placeholder:"Company (optional)",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),o("div",dt,[i(l(h),{modelValue:l(a).card.billingAddress.address_line1,"onUpdate:modelValue":e[17]||(e[17]=t=>l(a).card.billingAddress.address_line1=t),name:"billingAddress"},{default:m(({field:t,errorMessage:s})=>[i(x,J(t,{id:"address",class:["w-full",{"p-invalid":s}],placeholder:"Address"}),null,16,["class"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),o("div",it,[i(l(h),{modelValue:l(a).card.billingAddress.address_line2,"onUpdate:modelValue":e[18]||(e[18]=t=>l(a).card.billingAddress.address_line2=t),name:"billingAddressLine2"},{default:m(({field:t})=>[i(x,J(t,{id:"addressLine2",class:"w-full",placeholder:"Address line 2 (optional)"}),null,16)]),_:1},8,["modelValue"])]),o("div",rt,[o("div",mt,[i(l(h),{modelValue:l(a).card.billingAddress.city,"onUpdate:modelValue":e[19]||(e[19]=t=>l(a).card.billingAddress.city=t),name:"billingCity"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[i(x,{id:"city",modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:Y(["w-full",{"p-invalid":s}]),placeholder:"City",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),o("div",ut,[i(l(h),{modelValue:l(a).card.billingAddress.state,"onUpdate:modelValue":e[20]||(e[20]=t=>l(a).card.billingAddress.state=t),name:"billingState"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[i(c,{id:"state",class:"w-full","model-value":t.value,options:ee.value,"option-label":"name","option-value":"name",placeholder:"Select state",loading:l(ne),disabled:!l(a).card.billingAddress.country,filter:"",onValueChange:v},null,8,["model-value","options","loading","disabled","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),o("div",_t,[i(l(h),{modelValue:l(a).card.billingAddress.postal_code,"onUpdate:modelValue":e[21]||(e[21]=t=>l(a).card.billingAddress.postal_code=t),name:"billingPostcode"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[i(x,{id:"postcode",modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:Y(["w-full",{"p-invalid":s}]),placeholder:"Postcode",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])])]),o("div",pt,[i(l(h),{modelValue:l(a).card.billingAddress.phone,"onUpdate:modelValue":e[22]||(e[22]=t=>l(a).card.billingAddress.phone=t),name:"billingPhone"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[i(x,{id:"phone",modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,class:Y(["w-full",{"p-invalid":s}]),placeholder:"Phone (required)",onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)]),_:1},8,["modelValue"])]),i(l(h),{modelValue:l(a).card.email,"onUpdate:modelValue":e[23]||(e[23]=t=>l(a).card.email=t),name:"billingEmail"},{default:m(({field:t,errorMessage:s,handleChange:v})=>[o("div",ct,[i(x,{modelValue:t.value,"onUpdate:modelValue":_=>t.value=_,placeholder:"Billing Email",class:Y(["w-full",{"p-invalid":s}]),onValueChange:v},null,8,["modelValue","onUpdate:modelValue","class","onValueChange"]),s?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(s),1)]),_:2},1024)):p("",!0)])]),_:1},8,["modelValue"])])],64)):p("",!0),i(l(h),{modelValue:l(a).is_default_payment_method,"onUpdate:modelValue":e[26]||(e[26]=t=>l(a).is_default_payment_method=t),name:"is_default_payment_method",as:"div",class:"form-item is-required"},{default:m(({errorMessage:t})=>[o("div",yt,[o("div",ft,[i(P,{modelValue:l(a).is_default_payment_method,"onUpdate:modelValue":e[24]||(e[24]=s=>l(a).is_default_payment_method=s),"input-id":"is_default_payment_method_primary",name:"is_default_payment_method",value:!0},null,8,["modelValue"]),e[58]||(e[58]=o("label",{for:"is_default_payment_method_primary",class:"cursor-pointer"},[y("Make Default "),o("strong",null,"Primary"),y(" Payment Method?")],-1)),i(P,{modelValue:l(a).is_default_payment_method,"onUpdate:modelValue":e[25]||(e[25]=s=>l(a).is_default_payment_method=s),class:"ml-4","input-id":"is_default_payment_method_secondary",name:"is_default_payment_method",value:!1},null,8,["modelValue"]),e[59]||(e[59]=o("label",{for:"is_default_payment_method_secondary",class:"cursor-pointer"},[y("Make Default "),o("strong",null,"Secondary"),y(" Payment Method?")],-1))]),t?(r(),V(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[y(u(t),1)]),_:2},1024)):p("",!0),e[60]||(e[60]=o("p",{class:"text-sm text-gray-500 my-2"}," This will not affect any current active schedules. Your selected preference will be used to autofill future registrations. ",-1))])]),_:1},8,["modelValue"]),o("div",bt,[i(Q,{modelValue:l(a).terms,"onUpdate:modelValue":e[27]||(e[27]=t=>l(a).terms=t),binary:"","input-id":"terms"},null,8,["modelValue"]),o("label",vt,[e[61]||(e[61]=y(" By ticking, you are confirming that you have read, understood andd agree to the ")),o("span",{class:"font-bold cursor-pointer",onClick:e[28]||(e[28]=He(t=>Z.value=!0,["stop"]))}," Terms and Conditions ")])]),o("div",Vt,[i(Q,{modelValue:l(a).notice,"onUpdate:modelValue":e[29]||(e[29]=t=>l(a).notice=t),"true-value":!0,"false-value":!1,binary:"","input-id":"notice"},null,8,["modelValue"]),e[62]||(e[62]=o("label",{for:"notice",class:"text-sm"}," Optional: Receive your future rates notices via email and store them electronically. ",-1))])]),_:1},8,["validation-schema"])])):p("",!0)])):p("",!0),l(T)===3?(r(),g("div",gt,[i(H,{"stroke-width":"4"}),e[64]||(e[64]=o("span",{class:"text-lg text-gray-700 font-medium"},"Registering, please wait...",-1))])):p("",!0)]),o("div",ht,[l(T)===1?(r(),V(j,{key:0,class:"btn",severity:"primary",label:"BACK",onClick:e[30]||(e[30]=t=>n.$router.back())})):p("",!0),l(T)===2?(r(),V(j,{key:1,loading:B.submit,class:"btn",label:"BACK",severity:"primary",onClick:we},null,8,["loading"])):p("",!0),l(T)===2?(r(),V(j,{key:2,loading:B.submit,class:"btn",label:"SUBMIT",severity:"warn",onClick:Le},null,8,["loading"])):p("",!0),l(T)===1?(r(),V(j,{key:3,class:"btn",loading:B.getRegisterAccountInfo,label:"NEXT",severity:"warn",onClick:Te},null,8,["loading"])):p("",!0)])],2),i(na,{modelValue:Z.value,"onUpdate:modelValue":e[31]||(e[31]=t=>Z.value=t)},null,8,["modelValue"]),i(da)])}}}),jt=ia(xt,[["__scopeId","data-v-4a26de5b"]]);export{jt as default};
