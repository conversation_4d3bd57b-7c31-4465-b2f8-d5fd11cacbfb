import{d as T,i as g,j as m,w as I,c as i,o,h as n,e as c,F as C,r as S,t as F,b as A,u as r,s as h,f as G,g as E,k as O}from"./index-Cr_XsrqN.js";import{S as s}from"./BaseRangeDatePicker.vue_vue_type_style_index_0_lang-jkTsn2Nm.js";import{_ as V}from"./seatchItem.vue_vue_type_script_setup_true_lang-C3LrCJS1.js";const U={class:"common-search-wrap"},$={class:"common-search"},D={class:"search-container bg-white p-4"},K={class:"search-form-container gap-15"},L={class:"search-form-label"},W={class:"search-container-actions"},X={key:0,class:"more-filters-container flex items-end"},j={class:"more-filters-container flex items-end"},z={class:"search-btn-container flex items-end"},q={class:"advanced-filters-content p-4"},J={key:0,class:"text-lg font-medium mb-2"},P={class:"search-btn-container w-full flex justify-end"},ee=T({__name:"search",props:{loading:{type:Boolean,default:!1},basicSearchFields:{default:()=>[{name:"multiSelect",label:"多选示例",type:s.SELECT,multiple:!0,placeholder:"请选择选项",options:[{label:"选项1",value:1},{label:"选项2",value:2},{label:"选项3",value:3},{label:"选项4",value:4}],defaultValue:[]}]},advancedSearchFields:{default:()=>[]},modelValue:{default:()=>({})}},emits:["search","update:modelValue"],setup(k,{expose:w,emit:R}){const u=k,p=R,v=g(!1),f=g({}),t=m({get(){return u.modelValue||{}},set(e){p("update:modelValue",e)}}),d=m(()=>[...u.basicSearchFields,...u.advancedSearchFields]),B=e=>e.defaultValue!==void 0?e.type===s.NUMBER_RANGE&&Array.isArray(e.defaultValue)?[...e.defaultValue]:e.defaultValue:e.type===s.NUMBER_RANGE?[null,null]:e.type===s.CHECKBOX||e.type===s.SWITCH?!1:e.type===s.SELECT||e.type===s.RADIO?"":e.type===s.DATE_RANGE?null:"",H=()=>{if(d.value&&d.value.length>0){const e={...t.value};let l=!1;d.value.forEach(a=>{f.value[a.name]=B(a),e[a.name]===void 0&&(e[a.name]=f.value[a.name],l=!0)}),l&&p("update:modelValue",e)}};I(()=>d.value,()=>{H()},{immediate:!0});const y=()=>{p("search"),v.value=!1},N=e=>{t.value[e]&&Array.isArray(t.value[e])&&(t.value[e]=[null,null])},b=()=>{if(!t.value||!u.advancedSearchFields)return!1;for(const e of u.advancedSearchFields){const l=t.value[e.name];if(l!=null&&(Array.isArray(l)&&l.some(a=>a!==null&&a!=="")||(e.type===s.CHECKBOX||e.type===s.SWITCH)&&l||l!==""))return!0}return!1},_=m(()=>b()),x=m(()=>b()?"pi-filter-fill":"pi-filter"),M=()=>{d.value.forEach(e=>{const l=f.value[e.name];l!==void 0&&(Array.isArray(l)?t.value[e.name]=[...l]:t.value[e.name]=l)}),y()};return w({hasAdvancedFilters:_}),(e,l)=>(o(),i("div",U,[n("div",$,[n("div",D,[n("div",K,[(o(!0),i(C,null,S(e.basicSearchFields,a=>(o(),i("div",{key:a.name,class:"search-field-container"},[n("div",L,F(a.label),1),c(V,{field:a,"model-value":t.value,"is-advanced":!1},null,8,["field","model-value"])]))),128))]),n("div",W,[e.advancedSearchFields.length>0?(o(),i("div",X,[c(r(h),{icon:`pi ${x.value}`,label:"More Filters",class:G(["more-filters-btn",{"has-filters":_.value}]),text:"",style:{"text-decoration":"underline",color:"#545454","font-weight":"500"},onClick:l[0]||(l[0]=a=>v.value=!0)},{default:E(()=>l[2]||(l[2]=[n("span",null,"More Filters",-1)])),_:1,__:[2]},8,["icon","class"])])):A("",!0),n("div",j,[c(r(h),{label:"Clear Filters",class:"more-filters-btn",text:"",loading:e.loading,severity:"warn",onClick:M},null,8,["loading"])]),n("div",z,[c(r(h),{label:"SEARCH",class:"search-btn",loading:e.loading,severity:"warn",onClick:y},null,8,["loading"])])])]),c(r(O),{visible:v.value,"onUpdate:visible":l[1]||(l[1]=a=>v.value=a),header:"Advanced Filters",modal:!0,style:{width:"650px"},closable:!0,draggable:"","dismissable-mask":!0},{default:E(()=>[n("div",q,[(o(!0),i(C,null,S(e.advancedSearchFields,a=>(o(),i("div",{key:a.name,class:"mb-6"},[a.type!==r(s).CHECKBOX&&a.type!==r(s).SWITCH?(o(),i("div",J,F(a.label),1)):A("",!0),c(V,{field:a,"model-value":t.value,"is-advanced":!0,"on-clear":()=>a.type===r(s).NUMBER_RANGE?N(a.name):null},null,8,["field","model-value","on-clear"])]))),128)),n("div",P,[c(r(h),{label:"SEARCH",icon:"pi pi-search",class:"search-btn",loading:e.loading,severity:"warn",onClick:y},null,8,["loading"])])])]),_:1},8,["visible"])])]))}});export{ee as _};
