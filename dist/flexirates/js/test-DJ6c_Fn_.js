import{_ as W}from"./BaseFileUpload-ptYtZfQ1.js";import{d as N,w as T,c as f,a as R,b as C,u as d,o as m,e as o,n as O,f as F,g as M,h as s,s as B,t as $,F as I,r as H,i as E,j as S,k as K,l as j,m as z}from"./index-Cr_XsrqN.js";import{S as i,_ as J,s as q}from"./BaseRangeDatePicker.vue_vue_type_style_index_0_lang-jkTsn2Nm.js";import{s as Q}from"./index-BwvvB-v3.js";import{s as U,a as Y}from"./index-fEC-lipk.js";import{s as Z}from"./index-DdGtf4Je.js";import{s as x}from"./index-BH-zSNAS.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index--SCaD3QG.js";import"./index-BquGHVpa.js";import"./index-BiHyKo52.js";import"./index-DhbL6JTo.js";import"./index-CQnajQNe.js";const _={key:0,class:"search-form-input-group"},ee={key:0,class:"pi pi-search search-form-input-icon"},le={key:4,class:"flex items-center gap-3"},ae={key:5,class:"flex items-center gap-2"},se=["for"],te={key:6,class:"flex flex-wrap gap-6"},ie=["for"],ne={key:7,class:"flex items-center gap-2"},oe=["for"],D=N({__name:"BaseSearchItem",props:{field:{},modelValue:{},isAdvanced:{type:Boolean},onClear:{type:Function}},emits:["update:modelValue"],setup(c,{emit:h}){const l=c,b=h,V=()=>{l.field.type===i.NUMBER_RANGE&&(!l.modelValue[l.field.name]||!Array.isArray(l.modelValue[l.field.name]))&&(l.modelValue[l.field.name]=[null,null])};T(()=>l.field,V,{immediate:!0}),T(()=>l.modelValue,V,{immediate:!0,deep:!0});const g=(e,t)=>typeof t=="boolean"?t:t==="true"||t===!0||!1,k=()=>{l.onClear?l.onClear():l.field.type===i.NUMBER_RANGE&&(l.modelValue[l.field.name]=[null,null],b("update:modelValue",l.modelValue))},v=e=>{if(l.field.type===i.NUMBER_RANGE&&Array.isArray(l.modelValue[l.field.name])){const t=l.modelValue[l.field.name][1];l.modelValue[l.field.name][0]=e,e!==null&&t!==null&&e>t&&(l.modelValue[l.field.name][1]=e),b("update:modelValue",l.modelValue)}},A=e=>{if(l.field.type===i.NUMBER_RANGE&&Array.isArray(l.modelValue[l.field.name])){const t=l.modelValue[l.field.name][0];l.modelValue[l.field.name][1]=e,e!==null&&t!==null&&t>e&&(l.modelValue[l.field.name][0]=e),b("update:modelValue",l.modelValue)}};return(e,t)=>e.field.type===d(i).TEXT?(m(),f("div",_,[e.isAdvanced?C("",!0):(m(),f("i",ee)),o(d(Z),{modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":t[0]||(t[0]=r=>e.modelValue[e.field.name]=r),placeholder:e.field.placeholder,class:F(e.isAdvanced?"w-full":"search-form-input"),style:O(e.field.width&&!e.isAdvanced?{width:e.field.width}:{}),maxlength:e.field.maxlength},null,8,["modelValue","placeholder","class","style","maxlength"])])):e.field.type===d(i).SELECT?(m(),f("div",{key:1,class:F(e.isAdvanced?"":"search-form-select-group")},[o(d(x),{modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":t[1]||(t[1]=r=>e.modelValue[e.field.name]=r),options:e.field.options,"option-label":"label","option-value":"value",placeholder:e.field.placeholder||"All",class:F(e.isAdvanced?"w-full":"search-form-select"),style:O(e.field.width&&!e.isAdvanced?{width:e.field.width}:{}),"show-clear":""},{dropdownicon:M(()=>t[7]||(t[7]=[s("span",{class:"search-form-select-arrow"},null,-1)])),_:1},8,["modelValue","options","placeholder","class","style"])],2)):e.field.type===d(i).DATE_RANGE?(m(),R(J,{key:2,modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":t[2]||(t[2]=r=>e.modelValue[e.field.name]=r),class:"search-form-date-range w-full","selection-mode":"range",placeholder:e.field.placeholder||"Select Date Range","date-format":"dd/mm/yy"},null,8,["modelValue","placeholder"])):e.field.type===d(i).NUMBER?(m(),R(d(U),{key:3,modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":t[3]||(t[3]=r=>e.modelValue[e.field.name]=r),placeholder:e.field.placeholder||"Enter Number",min:e.field.min,max:e.field.max,step:e.field.step||1,class:"w-full"},null,8,["modelValue","placeholder","min","max","step"])):e.field.type===d(i).NUMBER_RANGE?(m(),f("div",le,[Array.isArray(e.modelValue[e.field.name])?(m(),R(d(U),{key:0,"model-value":e.modelValue[e.field.name][0],placeholder:"Min",min:e.field.min||0,max:e.field.max||999999999,step:e.field.step||1,class:"flex-1","onUpdate:modelValue":v},null,8,["model-value","min","max","step"])):C("",!0),Array.isArray(e.modelValue[e.field.name])?(m(),R(d(U),{key:1,"model-value":e.modelValue[e.field.name][1],placeholder:"Max",min:e.field.min||0,max:e.field.max||999999999,step:e.field.step||1,class:"flex-1","onUpdate:modelValue":A},null,8,["model-value","min","max","step"])):C("",!0),Array.isArray(e.modelValue[e.field.name])&&(e.modelValue[e.field.name][0]!==null||e.modelValue[e.field.name][1]!==null)?(m(),R(d(B),{key:2,severity:"warn",icon:"pi pi-times",text:"",class:"clear-btn",onClick:k})):C("",!0)])):e.field.type===d(i).CHECKBOX?(m(),f("div",ae,[o(d(Q),{"model-value":g(e.field.name,e.modelValue[e.field.name]),binary:!0,"input-id":e.field.name,"onUpdate:modelValue":t[4]||(t[4]=r=>e.modelValue[e.field.name]=r)},null,8,["model-value","input-id"]),s("label",{for:e.field.name,class:"cursor-pointer"},$(e.field.label),9,se)])):e.field.type===d(i).RADIO?(m(),f("div",te,[(m(!0),f(I,null,H(e.field.options,r=>(m(),f("div",{key:r.value,class:"flex items-center gap-2"},[o(d(Y),{modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":t[5]||(t[5]=n=>e.modelValue[e.field.name]=n),value:r.value,"input-id":`${e.field.name}_${r.value}`},null,8,["modelValue","value","input-id"]),s("label",{for:`${e.field.name}_${r.value}`,class:"cursor-pointer"},$(r.label),9,ie)]))),128))])):e.field.type===d(i).SWITCH?(m(),f("div",ne,[o(d(q),{"model-value":g(e.field.name,e.modelValue[e.field.name]),"input-id":e.field.name,"onUpdate:modelValue":t[6]||(t[6]=r=>e.modelValue[e.field.name]=r)},null,8,["model-value","input-id"]),s("label",{for:e.field.name,class:"cursor-pointer"},$(e.field.label),9,oe)])):C("",!0)}}),de={class:"common-search-wrap"},re={class:"common-search"},ue={class:"search-container bg-white p-4"},me={class:"search-form-container gap-15"},pe={class:"search-form-label"},fe={class:"search-container-actions"},ce={key:0,class:"more-filters-container flex items-end"},ve={class:"more-filters-container flex items-end"},ye={class:"search-btn-container flex items-end"},be={class:"advanced-filters-content p-4"},he={key:0,class:"text-lg font-medium mb-2"},ge={class:"search-btn-container w-full flex justify-end"},Ve=N({__name:"BaseSearch",props:{loading:{type:Boolean,default:!1},basicSearchFields:{default:()=>[]},advancedSearchFields:{default:()=>[]},modelValue:{default:()=>({})}},emits:["search","update:modelValue"],setup(c,{expose:h,emit:l}){const b=c,V=l,g=E(!1),k=E({}),v=S({get(){return b.modelValue||{}},set(a){V("update:modelValue",a)}}),A=S(()=>[...b.basicSearchFields,...b.advancedSearchFields]),e=a=>a.defaultValue!==void 0?a.type===i.NUMBER_RANGE&&Array.isArray(a.defaultValue)?[...a.defaultValue]:a.defaultValue:a.type===i.NUMBER_RANGE?[null,null]:a.type===i.CHECKBOX||a.type===i.SWITCH?!1:a.type===i.SELECT||a.type===i.RADIO?"":a.type===i.DATE_RANGE?null:"",t=()=>{if(A.value&&A.value.length>0){const a={...v.value};let p=!1;A.value.forEach(u=>{k.value[u.name]=e(u),a[u.name]===void 0&&(a[u.name]=k.value[u.name],p=!0)}),p&&V("update:modelValue",a)}};T(()=>A.value,()=>{t()},{immediate:!0});const r=()=>{V("search"),g.value=!1},n=a=>{v.value[a]&&Array.isArray(v.value[a])&&(v.value[a]=[null,null])},w=()=>{if(!v.value||!b.advancedSearchFields)return!1;for(const a of b.advancedSearchFields){const p=v.value[a.name];if(p!=null&&(Array.isArray(p)&&p.some(u=>u!==null&&u!=="")||(a.type===i.CHECKBOX||a.type===i.SWITCH)&&p||p!==""))return!0}return!1},G=S(()=>w()),L=S(()=>w()?"pi-filter-fill":"pi-filter"),P=()=>{A.value.forEach(a=>{const p=k.value[a.name];p!==void 0&&(Array.isArray(p)?v.value[a.name]=[...p]:v.value[a.name]=p)}),r()};return h({hasAdvancedFilters:G}),(a,p)=>(m(),f("div",de,[s("div",re,[s("div",ue,[s("div",me,[(m(!0),f(I,null,H(a.basicSearchFields,u=>(m(),f("div",{key:u.name,class:"search-field-container"},[s("div",pe,$(u.label),1),o(D,{field:u,"model-value":v.value,"is-advanced":!1},null,8,["field","model-value"])]))),128))]),s("div",fe,[a.advancedSearchFields.length>0?(m(),f("div",ce,[o(d(B),{icon:`pi ${L.value}`,label:"More Filters",class:F(["more-filters-btn",{"has-filters":G.value}]),text:"",onClick:p[0]||(p[0]=u=>g.value=!0)},{default:M(()=>p[2]||(p[2]=[s("span",null,"More Filters",-1)])),_:1,__:[2]},8,["icon","class"])])):C("",!0),s("div",ve,[o(d(B),{label:"Clear Filters",class:"more-filters-btn",text:"",loading:a.loading,onClick:P},null,8,["loading"])]),s("div",ye,[o(d(B),{label:"SEARCH",icon:"pi pi-search",class:"search-btn",loading:a.loading,severity:"warn",onClick:r},null,8,["loading"])])])]),o(d(K),{visible:g.value,"onUpdate:visible":p[1]||(p[1]=u=>g.value=u),header:"Advanced Filters",modal:!0,style:{width:"650px"},closable:!0,draggable:"","dismissable-mask":!0},{default:M(()=>[s("div",be,[(m(!0),f(I,null,H(a.advancedSearchFields,u=>(m(),f("div",{key:u.name,class:"mb-6"},[u.type!==d(i).CHECKBOX&&u.type!==d(i).SWITCH?(m(),f("div",he,$(u.label),1)):C("",!0),o(D,{field:u,"model-value":v.value,"is-advanced":!0,"on-clear":()=>u.type===d(i).NUMBER_RANGE?n(u.name):null},null,8,["field","model-value","on-clear"])]))),128)),s("div",ge,[o(d(B),{label:"SEARCH",icon:"pi pi-search",class:"search-btn",loading:a.loading,severity:"warn",onClick:r},null,8,["loading"])])])]),_:1},8,["visible"])])]))}}),ke=N({name:"BaseTag",props:{text:{type:String,default:""},type:{type:String,default:"default",validator:c=>["default","paid","upcoming","failed"].includes(c)},clickable:{type:Boolean,default:!1}},emits:["click"],setup(c,{emit:h}){return{handleClick:()=>{c.clickable&&h("click")}}}});function Ae(c,h,l,b,V,g){return m(),f("div",{class:F(["base-tag",[`base-tag--${c.type}`,{"base-tag--clickable":c.clickable}]]),onClick:h[0]||(h[0]=(...k)=>c.handleClick&&c.handleClick(...k))},[j(c.$slots,"default",{},()=>[z($(c.text),1)],!0)],2)}const y=X(ke,[["render",Ae],["__scopeId","data-v-0fbe98e0"]]),Ce={class:"test p-4"},Ee={class:"mb-8"},Se={class:"mt-4 p-4 bg-gray-100 rounded-lg"},$e={class:"whitespace-pre-wrap"},we={class:"mb-8"},Re={class:"tag-container mb-8"},Be={class:"flex flex-wrap gap-2 mb-6"},Fe={class:"flex flex-wrap gap-2"},Ne={class:"status-list w-60 border border-gray-200 rounded bg-gray-50"},Ue={class:"status-item flex items-center p-2 border-b border-gray-200"},Te={class:"status-item flex items-center p-2 border-b border-gray-200"},Me={class:"status-item flex items-center p-2 border-b border-gray-200"},Ie={class:"status-item flex items-center p-2 border-b border-gray-200"},He={class:"status-item flex items-center p-2"},Ge=N({__name:"test",setup(c){const h=E([]),l=E({keyword:"",category:"",status:"",dateRange:null,price:null,priceRange:[null,null],inStock:!1,rating:"",featured:!1}),b=E([{label:"All",value:""},{label:"电子产品",value:"electronics"},{label:"服装",value:"clothing"},{label:"家居",value:"home"},{label:"书籍",value:"books"},{label:"食品1",value:"food"}]),V=E([{label:"All",value:""},{label:"Active",value:"active"},{label:"Inactive",value:"inactive"},{label:"Pending",value:"pending"}]),g=E([{label:"5星",value:5},{label:"4星及以上",value:4},{label:"3星及以上",value:3},{label:"2星及以上",value:2},{label:"1星及以上",value:1}]),k=S(()=>[{name:"keyword",label:"What are you looking for?",type:i.TEXT,placeholder:"Search for Customer name, email, etc.",maxlength:50,defaultValue:"",width:"420px"},{name:"category",label:"Category",type:i.SELECT,placeholder:"All",options:b.value,defaultValue:"",width:"200px"},{name:"status",label:"Status",type:i.SELECT,placeholder:"All",options:V.value,defaultValue:"",width:"200px"}]),v=S(()=>[{name:"dateRange",label:"上架日期",type:i.DATE_RANGE,placeholder:"选择日期范围",defaultValue:null},{name:"price",label:"价格",type:i.NUMBER,placeholder:"输入价格",min:0,max:1e4,step:10,defaultValue:null},{name:"priceRange",label:"价格区间",type:i.NUMBER_RANGE,min:0,max:1e4,step:10,defaultValue:[null,null]},{name:"inStock",label:"仅显示有库存商品",type:i.CHECKBOX,defaultValue:!1},{name:"rating",label:"商品评分",type:i.RADIO,options:g.value,defaultValue:""},{name:"featured",label:"精选商品",type:i.SWITCH,defaultValue:!1}]),A=()=>{console.log("搜索参数:",l.value)},e=S(()=>JSON.stringify(l.value,null,2)),t=r=>{console.log(`${r} tag clicked!`)};return(r,n)=>(m(),f("div",Ce,[n[11]||(n[11]=s("h2",{class:"text-2xl font-bold mb-6"}," BaseSearch 与 More Filters 示例 ",-1)),s("div",Ee,[o(Ve,{modelValue:l.value,"onUpdate:modelValue":n[0]||(n[0]=w=>l.value=w),"basic-search-fields":k.value,"advanced-search-fields":v.value,loading:!1,onSearch:A},null,8,["modelValue","basic-search-fields","advanced-search-fields"]),s("div",Se,[n[6]||(n[6]=s("h3",{class:"text-lg font-bold mb-2"}," 当前搜索内容: ",-1)),s("pre",$e,$(e.value),1)])]),n[12]||(n[12]=s("h2",{class:"text-2xl font-bold mb-6 mt-8"}," 文件上传组件示例 ",-1)),s("div",we,[o(W,{"model-value":h.value,"onUpdate:modelValue":n[1]||(n[1]=w=>h.value=w),multiple:!0,"max-files":2,accept:"image/*"},null,8,["model-value"])]),n[13]||(n[13]=s("h2",{class:"text-2xl font-bold mb-6 mt-8"}," 标签组件示例 ",-1)),s("div",Re,[n[8]||(n[8]=s("h3",{class:"text-lg font-bold mb-4"}," 基本标签类型 ",-1)),s("div",Be,[o(y,{text:"Default"}),o(y,{text:"Paid",type:"paid"}),o(y,{text:"Upcoming",type:"upcoming"}),o(y,{text:"Failed",type:"failed"})]),n[9]||(n[9]=s("h3",{class:"text-lg font-bold mb-4"}," 可点击标签示例 ",-1)),s("div",Fe,[o(y,{text:"Default Clickable",clickable:"",onClick:n[2]||(n[2]=()=>t("default"))}),o(y,{text:"Paid Clickable",type:"paid",clickable:"",onClick:n[3]||(n[3]=()=>t("paid"))}),o(y,{text:"Upcoming Clickable",type:"upcoming",clickable:"",onClick:n[4]||(n[4]=()=>t("upcoming"))}),o(y,{text:"Failed Clickable",type:"failed",clickable:"",onClick:n[5]||(n[5]=()=>t("failed"))})]),n[10]||(n[10]=s("h3",{class:"text-lg font-bold mb-4 mt-6"}," 模拟状态列表 ",-1)),s("div",Ne,[n[7]||(n[7]=s("div",{class:"border-b border-gray-200 px-4 py-2 font-bold"}," Status ",-1)),s("div",Ue,[o(y,{text:"Paid",type:"paid"})]),s("div",Te,[o(y,{text:"Upcoming",type:"upcoming"})]),s("div",Me,[o(y,{text:"Paid",type:"paid"})]),s("div",Ie,[o(y,{text:"Failed",type:"failed"})]),s("div",He,[o(y,{text:"Paid",type:"paid"})])])])]))}}),Ze=X(Ge,[["__scopeId","data-v-b98e2530"]]);export{Ze as default};
