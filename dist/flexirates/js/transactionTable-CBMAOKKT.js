import{_ as ee,B as te,a as ae,s as oe,b as le,c as se}from"./BaseDataTableSuperSearch.vue_vue_type_style_index_0_lang-NpWNwL72.js";import{s as re}from"./index-Dvu_aI8r.js";import{s as ne}from"./index-BH-zSNAS.js";import{d as ie,a9 as T,a8 as V,i as _,j,a as u,o as l,b1 as E,g as i,b as y,h as s,e as g,c as d,F as c,r as k,H as w,s as ue,u as H,t as m,l as C,m as b,f as P,U as N}from"./index-Cr_XsrqN.js";import{s as de}from"./index-BwvvB-v3.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CQnajQNe.js";import"./index-fEC-lipk.js";import"./index-BiHyKo52.js";import"./index-DdGtf4Je.js";import"./index-DhbL6JTo.js";import"./date-B2oFx4oA.js";import"./dayjs.min-BN044Q0-.js";const ce={class:"table-search"},fe={class:"flex gap-4 justify-end mb-2"},me={class:"flex flex-col gap-2"},ve={class:"empty-state-container"},ye={class:"empty-state-title"},be={class:"empty-state-message"},ge={class:"paginator-container"},he={class:"flex items-center gap-2"},ke={class:"paginator-button-container"},we=ie({__name:"transactionTable",props:T({value:{},columns:{},minWidth:{default:"49rem"},rows:{default:20},dataKey:{default:"id"},rowsPerPageOptions:{default(){return[20,50,100]}},stripedRows:{type:Boolean,default:!1},showMultipleColumn:{type:Boolean,default:!0},showEditColumn:{type:Boolean,default:!0},editColumnLabel:{default(){return N().t("actions")}},exportLabel:{default(){return N().t("export")}},paginator:{type:Boolean,default:!0},editColumnHeader:{},size:{default:void 0},scrollable:{type:Boolean,default:!1},failed:{type:Boolean},failureMessage:{},showSearchBar:{type:Boolean,default:!0},rowHover:{type:Boolean,default:!1}},{searchFields:{type:Object,default(){return{}}},searchFieldsModifiers:{},"v-model:selection":{type:Array,default(){return[]}},"v-model:selectionModifiers":{}}),emits:T(["sort","page","changeSearch","export","edit","editRows","rowClick"],["update:searchFields","update:v-model:selection"]),setup(S,{expose:z,emit:L}){const o=S,h=L,r=V(S,"searchFields"),$=_(),x=V(S,"v-model:selection"),U=()=>{const t={};for(const a in r.value)if(Object.prototype.hasOwnProperty.call(r.value,a)){const f=r.value[a].value;t[a]=f}return t},F=(t,a)=>{a.value=t,h("changeSearch",U())},v=_({}),O=_();o.columns.forEach(t=>{v.value[t.field]===void 0&&(v.value[t.field]=!0)});const I=j(()=>o.columns.filter(t=>v.value[t.field])),q=t=>{v.value[t]=!v.value[t]},A=j(()=>{let t=!1;for(const a of Object.keys(r.value))Object.prototype.hasOwnProperty.call(r.value,a)&&r.value[a].isSuperSearch&&(t=!0);return t}),D=()=>{for(const t of Object.keys(r.value))if(Object.prototype.hasOwnProperty.call(r.value,t)&&r.value[t].isSuperSearch)return t};return z({exportCSV:()=>{$.value.exportCSV()},getSelectedRowsData:()=>$.value.getSelectedRowsData()}),(t,a)=>{const f=ue,M=le,K=ne,W=re,X=se;return l(),u(X,{ref_key:"dataTableRef",ref:$,selection:x.value,"onUpdate:selection":a[3]||(a[3]=e=>x.value=e),value:t.value,"striped-rows":o.stripedRows,"table-style":{"min-width":o.minWidth},paginator:o.paginator&&t.value.length>0,rows:o.rows,"data-key":o.dataKey,size:o.size,"rows-per-page-options":o.rowsPerPageOptions,scrollable:o.scrollable,class:"base-data-table transactions-table","row-hover":o.rowHover,onSort:a[4]||(a[4]=e=>h("sort",e)),onPage:a[5]||(a[5]=e=>h("page",e)),onRowClick:a[6]||(a[6]=e=>h("rowClick",e))},E({empty:i(()=>[s("div",ve,[s("div",{class:P(["empty-state-card",{"error-state":o.failed}])},[s("div",{class:P(["empty-state-icon-container",{"error-icon":o.failed}])},[s("i",{class:P(o.failed?"pi pi-exclamation-triangle":"pi pi-inbox")},null,2)],2),s("h3",ye,[o.failed?(l(),d(c,{key:0},[b(m(t.$t("common.requestFailed")||"Request Failed"),1)],64)):(l(),d(c,{key:1},[b(m(t.$t("common.noDataFound")||"No data found"),1)],64))]),s("p",be,[o.failed&&o.failureMessage?(l(),d(c,{key:0},[b(m(o.failureMessage),1)],64)):o.failed?(l(),d(c,{key:1},[b(m(t.$t("common.requestFailedDescription")||"An error occurred while fetching data. Please try again later."),1)],64)):(l(),d(c,{key:2},[b(m(t.$t("common.noDataFoundDescription")||"Try adjusting your search or filter to find what you're looking for."),1)],64))]),C(t.$slots,"empty-action",{},void 0,!0)],2)])]),paginatorcontainer:i(({rows:e,page:n,pageCount:B=0,pageLinks:G=[],prevPageCallback:J,nextPageCallback:Q,rowChangeCallback:Y,changePageCallback:Z})=>{var R;return[s("div",ge,[s("div",he,[a[8]||(a[8]=s("span",{class:"hidden sm:block"},"Show",-1)),g(K,{"model-value":e,options:(R=o.rowsPerPageOptions)==null?void 0:R.map(p=>({label:p,value:p})),"option-label":"label","option-value":"value",onChange:p=>Y(p.value)},null,8,["model-value","options","onChange"])]),s("div",ke,[g(W,{align:"center",layout:"vertical"}),n!==0?(l(),u(f,{key:0,icon:"pi pi-chevron-left",class:"paginator-button",label:"PREV",rounded:"",text:"",onClick:J},null,8,["onClick"])):y("",!0),(l(!0),d(c,null,k(G,p=>(l(),u(f,{key:p,label:String(p),disabled:p===n+1,class:"paginator-button-page",onClick:Ce=>Z(p-1)},null,8,["label","disabled","onClick"]))),128)),n!==B-1?(l(),u(f,{key:1,label:"NEXT",class:"paginator-button",icon:"pi pi-chevron-right","icon-pos":"right",rounded:"",text:"",onClick:Q},null,8,["onClick"])):y("",!0)])])]}),default:i(()=>[t.showMultipleColumn?(l(),u(M,{key:0,"selection-mode":"multiple",exportable:!1})):y("",!0),(l(!0),d(c,null,k(I.value,e=>(l(),u(M,w({ref_for:!0},e,{key:e.field}),E({_:2},[e.field==="action"&&!e.isCustom?{name:"header",fn:i(()=>[a[7]||(a[7]=s("i",{class:"pi pi-cog m-auto !text-xl"},null,-1))]),key:"0"}:e.field==="action"&&e.isCustom?{name:"header",fn:i(()=>[C(t.$slots,"actionHeader",{},void 0,!0)]),key:"1"}:void 0,e.field==="action"?{name:"body",fn:i(n=>[C(t.$slots,e.template,w({ref_for:!0},n),void 0,!0)]),key:"2"}:e.template?{name:"body",fn:i(n=>[C(t.$slots,e.template,w({ref_for:!0},n),void 0,!0)]),key:"3"}:e!=null&&e.format?{name:"body",fn:i(n=>[b(m((e==null?void 0:e.format)&&e.format(n.data)),1)]),key:"4"}:void 0]),1040))),128))]),_:2},[o.showSearchBar?{name:"header",fn:i(()=>[A.value?(l(),u(ee,{key:0,"search-field":r.value[D()],"onUpdate:value":a[0]||(a[0]=e=>F(e,r.value[D()]))},null,8,["search-field"])):y("",!0),s("div",ce,[g(te,null,{default:i(()=>[(l(!0),d(c,null,k(r.value,(e,n)=>(l(),u(ae,w({key:n},{ref_for:!0},e,{"is-hide":!!(e!=null&&e.isHide),"onUpdate:value":B=>F(B,e)}),null,16,["is-hide","onUpdate:value"]))),128))]),_:1}),s("div",fe,[o.showEditColumn?(l(),u(f,{key:0,icon:"pi pi-cog",label:o.editColumnLabel,severity:"secondary",onClick:a[1]||(a[1]=e=>O.value.toggle(e))},null,8,["label"])):y("",!0),g(f,{icon:"pi pi-external-link",label:o.exportLabel,severity:"secondary",onClick:a[2]||(a[2]=e=>t.$emit("export"))},null,8,["label"]),o.showEditColumn?(l(),u(H(oe),{key:1,ref_key:"editEditColumnPopover",ref:O,class:"p-4"},{default:i(()=>[s("div",me,[(l(!0),d(c,null,k(o.columns,e=>(l(),d("div",{key:e.field,class:"flex items-center gap-2"},[g(H(de),{"model-value":v.value[e.field],binary:!0,severity:"secondary",onChange:n=>q(e.field)},null,8,["model-value","onChange"]),s("span",null,m(e.header),1)]))),128))])]),_:1},512)):y("",!0)])])]),key:"0"}:void 0]),1032,["selection","value","striped-rows","table-style","paginator","rows","data-key","size","rows-per-page-options","scrollable","row-hover"])}}}),je=pe(we,[["__scopeId","data-v-8829356d"]]);export{je as default};
