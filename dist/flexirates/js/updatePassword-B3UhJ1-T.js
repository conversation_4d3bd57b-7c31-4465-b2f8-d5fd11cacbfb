import{d as h,K as $,x as S,i as f,c as B,o as c,h as s,e as t,g as l,u as n,G as w,a as p,b as x,H as P,m as v,t as g,s as T,E as U,a4 as E}from"./index-Cr_XsrqN.js";import{s as A}from"./index--SCaD3QG.js";import{s as D}from"./index-CEK2t3I_.js";import{t as F,c as I,d as b,g as R}from"./index.esm-Qe0UyvbF.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CQnajQNe.js";import"./index-6Y5dfxc3.js";import"./index-DdGtf4Je.js";import"./index-BiHyKo52.js";const j={class:"flexirates-wrap"},G={class:"security-container"},H={class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10"},K={class:"relative"},L={class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10"},O={class:"flex flex-col xl:flex-row gap-2"},Z={class:"relative"},z={class:"flex flex-col lg:flex-row lg:items-center gap-2 min-h-10"},J={class:"relative"},Q={class:"flex justify-center items-center py-10 gap-10"},W=h({__name:"updatePassword",setup(X){const _=$(),k=S(),r=f({currentPassword:"",newPassword:"",confirmNewPassword:""}),V=f(),u=f(!1),N=F(I({currentPassword:b().required("Please enter your current password"),newPassword:b().min(8,"Password must be at least 8 characters").matches(/[A-Z]/,"Password must contain at least one uppercase letter").matches(/\d/,"Password must contain at least one number"),confirmNewPassword:b().oneOf([R("newPassword")],"Passwords do not match").required("Please confirm your new password")})),C=async d=>{try{u.value=!0;const e={password:d.currentPassword,new_password:d.newPassword};console.log(e),(await E(e)).code===0&&(_.add({severity:"success",summary:"Tips",detail:"Successfully update password .",life:3e3}),k.back())}catch(e){console.log(e)}finally{u.value=!1}};return(d,e)=>{const i=D,m=A,y=T;return c(),B("div",j,[e[9]||(e[9]=s("div",{class:"flexirates-title"},[s("div",{class:"flexirates-title-text"}," Security ")],-1)),s("div",G,[e[8]||(e[8]=s("div",{class:"border-b-1 border-[#545454] text-xl font-semibold py-8"}," Update Password ",-1)),t(n(U),{ref_key:"formRef",ref:V,class:"flex flex-col gap-16","validation-schema":n(N),onSubmit:C},{default:l(()=>[t(n(w),{modelValue:r.value.currentPassword,"onUpdate:modelValue":e[0]||(e[0]=a=>r.value.currentPassword=a),as:"div",class:"flex flex-col gap-4 mt-10",name:"currentPassword"},{default:l(({field:a,errorMessage:o})=>[s("div",H,[e[4]||(e[4]=s("label",{for:"currentPassword",class:"w-60"},"Current Password",-1)),s("div",K,[t(i,P(a,{id:"user-current-password","toggle-mask":"",fluid:"",feedback:!1,class:"form-input lg:w-120 xl:w-160",name:"securityCurrentPassword",type:"password",autocomplete:"change-current-password"}),null,16),o?(c(),p(m,{key:0,variant:"text",class:"text-red-500 absolute top-13 left-0 w-auto"},{default:l(()=>[v(g(o),1)]),_:2},1024)):x("",!0)])])]),_:1},8,["modelValue"]),t(n(w),{modelValue:r.value.newPassword,"onUpdate:modelValue":e[1]||(e[1]=a=>r.value.newPassword=a),as:"div",class:"flex flex-col gap-4",name:"newPassword"},{default:l(({field:a,errorMessage:o})=>[s("div",L,[e[6]||(e[6]=s("label",{for:"newPassword",class:"w-60"},"New Password",-1)),s("div",O,[s("div",Z,[t(i,P(a,{"toggle-mask":"",fluid:"",feedback:!1,class:"form-input lg:w-120 xl:w-160",name:"newPassword",type:"password"}),null,16),o?(c(),p(m,{key:0,variant:"text",class:"text-red-500 absolute top-13 left-0 w-auto"},{default:l(()=>[v(g(o),1)]),_:2},1024)):x("",!0)]),e[5]||(e[5]=s("div",{class:"xl:ml-8"}," Include at least one capital letter,one number,and at least 8 characters ",-1))])])]),_:1},8,["modelValue"]),t(n(w),{modelValue:r.value.confirmNewPassword,"onUpdate:modelValue":e[2]||(e[2]=a=>r.value.confirmNewPassword=a),as:"div",class:"flex flex-col gap-4",name:"confirmNewPassword"},{default:l(({field:a,errorMessage:o})=>[s("div",z,[e[7]||(e[7]=s("label",{for:"confirmNewPassword",class:"w-60"},"Confirm Password",-1)),s("div",J,[t(i,P(a,{"toggle-mask":"",fluid:"",feedback:!1,class:"form-input lg:w-120 xl:w-160",name:"confirmPassword",type:"password"}),null,16),o?(c(),p(m,{key:0,variant:"text",class:"text-red-500 absolute top-13 left-0 w-auto"},{default:l(()=>[v(g(o),1)]),_:2},1024)):x("",!0)])])]),_:1},8,["modelValue"]),s("div",Q,[t(y,{label:"CANCEL",severity:"secondary",class:"btn cancel-button",onClick:e[3]||(e[3]=a=>d.$router.back())}),t(y,{label:"UPDATE",severity:"warn",loading:u.value,class:"btn",type:"submit"},null,8,["loading"])])]),_:1},8,["validation-schema"])])])}}}),ne=q(W,[["__scopeId","data-v-12afaa23"]]);export{ne as default};
