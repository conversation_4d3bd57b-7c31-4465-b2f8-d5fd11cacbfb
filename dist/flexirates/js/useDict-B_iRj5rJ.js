import{a6 as D,Y as y,i as v,j as g}from"./index-Cr_XsrqN.js";const h=t=>D("/getDictDataConst",{params:{type:t},baseURL:"/api/merchant"}),T=y("dict",()=>{const t=v({}),n=v({});return{dictCache:t,loadingTypes:n,getDictByType:async e=>{if(t.value[e])return t.value[e];n.value[e]=!0;try{const a=await h(e);return a.data?(t.value[e]=a.data,a.data):[]}catch(a){return console.error(`Failed to fetch dictionary data for type: ${e}`,a),[]}finally{n.value[e]=!1}},clearDictCache:e=>{if(e){const a={...t.value};delete a[e],t.value=a}else t.value={}},isTypeLoading:e=>g(()=>!!n.value[e]),getDictLabel:(e,a)=>{if(!t.value[e])return"";const o=t.value[e].find(s=>s.value===a);return o?o.label:""}}},{persist:!1});function L(t,n){const i=T(),l=v([]),d=Reflect.get(i,"isTypeLoading"),f=Reflect.apply(d,i,[t]),e=async()=>{const c=Reflect.get(i,"getDictByType");return l.value=await Reflect.apply(c,i,[t]),n&&n(l.value),l.value},a=c=>{const r=l.value.find(u=>u.value===c);return r?r.label:""},o=c=>{const r=l.value.find(u=>u.label===c);return r?r.value:void 0},s=g(()=>l.value.map(c=>({label:c.label,value:c.value}))),p=()=>{const c=Reflect.get(i,"clearDictCache");Reflect.apply(c,i,[t])};return e(),{dictList:l,loading:f,loadDict:e,getLabel:a,getValue:o,options:s,clearCache:p}}export{L as u};
