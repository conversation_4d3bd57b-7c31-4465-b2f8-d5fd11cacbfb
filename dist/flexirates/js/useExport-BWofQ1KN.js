import{K as c,i as p}from"./index-Cr_XsrqN.js";function u(r){const o=c(),a=p(!1);return{isExporting:a,handleExport:async s=>{try{a.value=!0,r.onExportStart&&r.onExportStart();const l={...r.getParams(),file_type:s},t=await r.exportFn(l);if(t.data&&t.data.url){const n=t.data.url;window.open(n,"_blank"),o.add({severity:"success",summary:"Export Successful",detail:`Data has been exported as ${s.toUpperCase()}`,life:3e3}),r.onExportSuccess&&r.onExportSuccess(n)}else throw new Error("Export URL not found in response")}catch(e){console.error("Export failed:",e),o.add({severity:"error",summary:"Export Failed",detail:"Failed to export data. Please try again.",life:3e3}),r.onExportError&&r.onExportError(e)}finally{a.value=!1}}}}export{u};
