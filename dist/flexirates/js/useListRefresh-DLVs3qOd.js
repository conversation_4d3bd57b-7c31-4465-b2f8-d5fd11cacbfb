import{Y as u,i as h,x as R,a5 as c}from"./index-Cr_XsrqN.js";const i=u("list",()=>{const e=h(new Map);return{setNeedRefresh:(t,o)=>{e.value.set(t,o)},getNeedRefresh:t=>e.value.get(t)||!1,clearRefreshFlag:t=>{e.value.delete(t)}}},{persist:!0});function d(e,r){const s=i(),f=R();return c(()=>{s.getNeedRefresh(e)&&(r==null||r(),s.clearRefreshFlag(e))}),{backWithRefresh:(n=!0)=>{s.setNeedRefresh(e,!0),n&&f.back()},withRefresh:()=>{s.setNeedRefresh(e,!0)}}}export{d as u};
