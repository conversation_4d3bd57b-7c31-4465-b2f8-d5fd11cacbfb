import{d as D}from"./dayjs.min-BN044Q0-.js";import{i as n,D as x,p as C}from"./index-Cr_XsrqN.js";function R(d,Y){let c=null;return function(...h){c&&window.clearTimeout(c),c=window.setTimeout(()=>{d.apply(this,h)},Y)}}function G(d){const{requestFn:Y,defaultParams:c={},immediate:h=!0,page_size:j=50,debounce:_=!0,debounceTime:q=200}=d,f=n([]),y=n(!1),p=n(null),m=n(!1),v=n(""),g=n(0),b=n(null),l=n(1),o=x({...c}),w=t=>{const r={};for(const e in t)(Array.isArray(t[e])&&t[e].length>0||!Array.isArray(t[e])&&String(t[e]).length>0)&&(r[e]=t[e]);return r},i=async t=>{try{y.value=!0,p.value=null,m.value=!1,v.value="";const r={page_size:j,page:l.value,...w(o),...t},e=await Y(r);if(e.code!==0){m.value=!0,v.value=e.message||"Failed",f.value=[],g.value=0;return}const{data:a=[],total:s=0,...u}=e.data;f.value=a,g.value=s,b.value=u}catch(r){p.value=r,m.value=!0,v.value=r.message||"搜索出错，请稍后重试",f.value=[],g.value=0}finally{y.value=!1}},F=_?R(i,q):i,A=t=>{Object.assign(o,t),l.value=1},L=(t,r=[])=>{const e={...t};for(const a in e){if((a==="created_at[]"||a==="updated_at[]")&&Array.isArray(e[a])&&e[a].length===2){const s=e[a],u=D(s[0]).format("YYYY-MM-DD"),P=s!=null&&s[1]?D(s[1]).format("YYYY-MM-DD"):u;e[a]=[u,P]}if(r.includes(a)&&Array.isArray(e[a])&&e[a].length===2){const s=e[a],u=D(s[0]).format("YYYY-MM-DD"),P=s!=null&&s[1]?D(s[1]).format("YYYY-MM-DD"):u;e[a]=[u,P]}}return Object.assign(o,e),o},M=()=>(l.value=1,F()),O=t=>(A(t),M()),S=()=>i(),T=()=>(l.value=1,Object.assign(o,c),i()),k=t=>(l.value=t.page+1,i({page_size:t.rows})),z=()=>w(o);return C(()=>{h&&M()}),{list:f,loading:y,error:p,failed:m,failureMessage:v,total:g,currentPage:l,queryParams:o,other:b,refresh:S,reset:T,setParams:A,search:M,updateParams:O,onPageChange:k,getSearchParams:z,setSearchParams:L}}export{G as u};
