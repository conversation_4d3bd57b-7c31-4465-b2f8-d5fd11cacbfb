import{R as h,S as j}from"./index-Cr_XsrqN.js";import{Z as s,a as y,b as a}from"./types-C7gnwoCB.js";/**
  * vee-validate v4.15.0
  * (c) 2024 <PERSON><PERSON><PERSON><PERSON>
  * @license MIT
  */const f=t=>t!==null&&!!t&&typeof t=="object"&&!Array.isArray(t);function l(t){return Number(t)>=0}function m(t){return typeof t=="object"&&t!==null}function P(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}function c(t){if(!m(t)||P(t)!=="[object Object]")return!1;if(Object.getPrototypeOf(t)===null)return!0;let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function p(t,e){return Object.keys(e).forEach(n=>{if(c(e[n])&&c(t[n])){t[n]||(t[n]={}),p(t[n],e[n]);return}t[n]=e[n]}),t}function g(t){const e=t.split(".");if(!e.length)return"";let n=String(e[0]);for(let r=1;r<e.length;r++){if(l(e[r])){n+=`[${e[r]}]`;continue}n+=`.${e[r]}`}return n}function Z(t,e){return{__type:"VVTypedSchema",async parse(r){const i=await t.safeParseAsync(r,e);if(i.success)return{value:i.data,errors:[]};const o={};return d(i.error.issues,o),{errors:Object.values(o)}},cast(r){try{return t.parse(r)}catch{const o=O(t);return f(o)&&f(r)?p(o,r):r}},describe(r){try{if(!r)return{required:!t.isOptional(),exists:!0};const i=_(r,t);return i?{required:!i.isOptional(),exists:!0}:{required:!1,exists:!1}}catch{return{required:!1,exists:!1}}}}}function d(t,e){t.forEach(n=>{const r=g(n.path.join("."));n.code==="invalid_union"&&(d(n.unionErrors.flatMap(i=>i.issues),e),!r)||(e[r]||(e[r]={errors:[],path:r}),e[r].errors.push(n.message))})}function O(t){if(t instanceof s)return Object.fromEntries(Object.entries(t.shape).map(([e,n])=>n instanceof y?[e,n._def.defaultValue()]:n instanceof s?[e,O(n)]:[e,void 0]))}function _(t,e){if(!u(e))return null;if(h(t))return e.shape[j(t)];const n=(t||"").split(/\.|\[(\d+)\]/).filter(Boolean);let r=e;for(let i=0;i<=n.length;i++){const o=n[i];if(!o||!r)return r;if(u(r)){r=r.shape[o]||null;continue}l(o)&&N(r)&&(r=r._def.type)}return null}function b(t){return t._def.typeName}function N(t){return b(t)===a.ZodArray}function u(t){return b(t)===a.ZodObject}export{Z as t};
