import{g as t}from"./getAssetsFile-D2Insbdr.js";import{d as u,a as p,o as c,p as i,u as o,b as n,n as m,E as l,t as _}from"./index-DWWVg5mm.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const x=s=>{switch(s){case 1:return t("merchant/card-type/visa.png");case 2:return t("merchant/card-type/mastercard.png");case 3:return t("merchant/card-type/amex.png");case 4:return t("merchant/card-type/discover.png");case 5:return t("merchant/card-type/diners.png");case 0:case 6:return t("merchant/card-type/carte-blanche.png")}return""},g={class:"base-card-type"},b={key:0,class:"base-card-type__icon"},f=["src"],w={class:"base-card-type__text"},T=u({__name:"BaseCardType",props:{text:{default:""},cardType:{default:null},isShowCardNumber:{type:Boolean,default:!1}},setup(s){const e=s,d=a=>{var r;return(r=String(a))==null?void 0:r.slice(-4)},h=a=>{switch(a){case 1:return{width:"40px",maxWidth:"40px",height:"28px",borderRadius:"4px"};case 2:return{width:"28px",maxWidth:"28px",height:"28px",borderRadius:"4px"};case 3:return{width:"42px",maxWidth:"42px",height:"28px",borderRadius:"4px"};case 4:return{width:"34px",maxWidth:"34px",height:"32px",borderRadius:"4px"};case 5:return{width:"34px",maxWidth:"34px",height:"28px",borderRadius:"4px"};case 0:case 6:return{width:"28px",maxWidth:"28px",height:"28px"}}return{width:"24px",height:"24px",borderRadius:"4px"}};return(a,r)=>(c(),p("div",g,[e.cardType!==null&&o(x)(e.cardType)?(c(),p("div",b,[n("img",{style:m(h(e.cardType)),src:o(x)(e.cardType),alt:"icon"},null,12,f)])):i("",!0),e.isShowCardNumber&&e.text&&d(e.text)?(c(),p(l,{key:1},[r[0]||(r[0]=n("span",{class:"base-card-type__text",style:{"font-size":"50px"}}," •••• ",-1)),n("div",w,_(d(e.text)),1)],64)):i("",!0)]))}}),B=y(T,[["__scopeId","data-v-b0f814d8"]]);export{B as _};
