import{d as y,r as d,a as r,o as u,e as s,s as L,U as C,w as c,b as n,E,G as k,t as w}from"./index-DWWVg5mm.js";import{s as V}from"./index-T2UmNbVz.js";const h={class:"export-dialog"},$={class:"export-options"},D={class:"file-type-options"},F=["for"],N={class:"dialog-footer"},P=y({__name:"BaseExportDialog",props:{title:{default:"Export Options"},exportButtonLabel:{default:"EXPORT"},cancelButtonLabel:{default:"CANCEL"},loading:{type:Boolean,default:!1},exportLoading:{type:Boolean,default:!1}},emits:["export"],setup(p,{emit:v}){const l=p,m=v,a=d(!1),o=d("csv"),f=[{name:"CSV",value:"csv"}],g=()=>{a.value=!0,o.value="csv"},_=()=>{o.value&&(m("export",o.value),a.value=!1)};return(O,t)=>{const i=L,b=V,x=C;return u(),r("div",h,[s(i,{label:"EXPORT",class:"!px-12",loading:l.loading,onClick:g},null,8,["loading"]),s(x,{visible:a.value,"onUpdate:visible":t[2]||(t[2]=e=>a.value=e),header:l.title,modal:!0,closable:!0,style:{width:"300px"},class:"export-dialog"},{footer:c(()=>[n("div",N,[s(i,{label:l.cancelButtonLabel,severity:"secondary",loading:l.exportLoading,class:"dialog-cancel-button w-30",onClick:t[1]||(t[1]=e=>a.value=!1)},null,8,["label","loading"]),s(i,{label:l.exportButtonLabel,severity:"warn",disabled:!o.value,loading:l.exportLoading,class:"w-30",onClick:_},null,8,["label","disabled","loading"])])]),default:c(()=>[n("div",$,[n("div",D,[(u(),r(E,null,k(f,e=>n("div",{key:e.value,class:"format-option"},[s(b,{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=B=>o.value=B),value:e.value,"input-id":`format_${e.value}`},null,8,["modelValue","value","input-id"]),n("label",{for:`format_${e.value}`,class:"ml-2"},w(e.name),9,F)])),64))])])]),_:1},8,["visible","header"])])}}});export{P as _};
