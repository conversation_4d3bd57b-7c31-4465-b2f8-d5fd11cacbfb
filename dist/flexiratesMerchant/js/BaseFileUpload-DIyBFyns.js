import{aL as Pe,I as Ve,J as Me,aM as Be,a3 as Le,a as p,o as i,p as w,A as ee,M as V,j as de,t as S,d as Oe,r as A,c as T,g as Re,f as Te,m as We,q as N,b as c,E as ie,G as ue,z as P,e as _,u as k,s as C,n as xe,w as He,aN as Ue}from"./index-DWWVg5mm.js";import{s as je}from"./index-DNtGhG9S.js";import{_ as qe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ge=d=>Pe("/downloadCenter/upload",d,{headers:{"Content-Type":"multipart/form-data"}});var Je=Ve`
    .p-progressbar {
        position: relative;
        overflow: hidden;
        height: dt('progressbar.height');
        background: dt('progressbar.background');
        border-radius: dt('progressbar.border.radius');
    }

    .p-progressbar-value {
        margin: 0;
        background: dt('progressbar.value.background');
    }

    .p-progressbar-label {
        color: dt('progressbar.label.color');
        font-size: dt('progressbar.label.font.size');
        font-weight: dt('progressbar.label.font.weight');
    }

    .p-progressbar-determinate .p-progressbar-value {
        height: 100%;
        width: 0%;
        position: absolute;
        display: none;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        transition: width 1s ease-in-out;
    }

    .p-progressbar-determinate .p-progressbar-label {
        display: inline-flex;
    }

    .p-progressbar-indeterminate .p-progressbar-value::before {
        content: '';
        position: absolute;
        background: inherit;
        inset-block-start: 0;
        inset-inline-start: 0;
        inset-block-end: 0;
        will-change: inset-inline-start, inset-inline-end;
        animation: p-progressbar-indeterminate-anim 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
    }

    .p-progressbar-indeterminate .p-progressbar-value::after {
        content: '';
        position: absolute;
        background: inherit;
        inset-block-start: 0;
        inset-inline-start: 0;
        inset-block-end: 0;
        will-change: inset-inline-start, inset-inline-end;
        animation: p-progressbar-indeterminate-anim-short 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        animation-delay: 1.15s;
    }

    @keyframes p-progressbar-indeterminate-anim {
        0% {
            inset-inline-start: -35%;
            inset-inline-end: 100%;
        }
        60% {
            inset-inline-start: 100%;
            inset-inline-end: -90%;
        }
        100% {
            inset-inline-start: 100%;
            inset-inline-end: -90%;
        }
    }
    @-webkit-keyframes p-progressbar-indeterminate-anim {
        0% {
            inset-inline-start: -35%;
            inset-inline-end: 100%;
        }
        60% {
            inset-inline-start: 100%;
            inset-inline-end: -90%;
        }
        100% {
            inset-inline-start: 100%;
            inset-inline-end: -90%;
        }
    }

    @keyframes p-progressbar-indeterminate-anim-short {
        0% {
            inset-inline-start: -200%;
            inset-inline-end: 100%;
        }
        60% {
            inset-inline-start: 107%;
            inset-inline-end: -8%;
        }
        100% {
            inset-inline-start: 107%;
            inset-inline-end: -8%;
        }
    }
    @-webkit-keyframes p-progressbar-indeterminate-anim-short {
        0% {
            inset-inline-start: -200%;
            inset-inline-end: 100%;
        }
        60% {
            inset-inline-start: 107%;
            inset-inline-end: -8%;
        }
        100% {
            inset-inline-start: 107%;
            inset-inline-end: -8%;
        }
    }
`,Ye={root:function(X){var G=X.instance;return["p-progressbar p-component",{"p-progressbar-determinate":G.determinate,"p-progressbar-indeterminate":G.indeterminate}]},value:"p-progressbar-value",label:"p-progressbar-label"},Xe=Me.extend({name:"progressbar",style:Je,classes:Ye}),Ke={name:"BaseProgressBar",extends:Be,props:{value:{type:Number,default:null},mode:{type:String,default:"determinate"},showValue:{type:Boolean,default:!0}},style:Xe,provide:function(){return{$pcProgressBar:this,$parentInstance:this}}},q={name:"ProgressBar",extends:Ke,inheritAttrs:!1,computed:{progressStyle:function(){return{width:this.value+"%",display:"flex"}},indeterminate:function(){return this.mode==="indeterminate"},determinate:function(){return this.mode==="determinate"},dataP:function(){return Le({determinate:this.determinate,indeterminate:this.indeterminate})}}},Qe=["aria-valuenow","data-p"],Ze=["data-p"],Ie=["data-p"],er=["data-p"];function rr(d,X,G,s,f,m){return i(),p("div",ee({role:"progressbar",class:d.cx("root"),"aria-valuemin":"0","aria-valuenow":d.value,"aria-valuemax":"100","data-p":m.dataP},d.ptmi("root")),[m.determinate?(i(),p("div",ee({key:0,class:d.cx("value"),style:m.progressStyle,"data-p":m.dataP},d.ptm("value")),[d.value!=null&&d.value!==0&&d.showValue?(i(),p("div",ee({key:0,class:d.cx("label"),"data-p":m.dataP},d.ptm("label")),[V(d.$slots,"default",{},function(){return[de(S(d.value+"%"),1)]})],16,Ie)):w("",!0)],16,Ze)):m.indeterminate?(i(),p("div",ee({key:1,class:d.cx("value"),"data-p":m.dataP},d.ptm("value")),null,16,er)):w("",!0)],16,Qe)}q.render=rr;const tr={key:0,class:"p-fileupload-errors"},ar={key:1,class:"p-fileupload-avatar-content"},sr={key:0,class:"p-fileupload-avatar-placeholder"},lr={key:1,class:"p-fileupload-avatar-preview"},or=["src"],nr={key:0,class:"p-fileupload-avatar-actions"},ir={key:2,class:"p-fileupload-gallery-content"},ur={key:0,class:"p-fileupload-gallery-placeholder"},dr={class:"p-fileupload-gallery-grid"},pr={class:"p-fileupload-gallery-item-preview"},cr=["src","alt"],fr={class:"p-fileupload-gallery-item-overlay"},vr={class:"p-fileupload-gallery-item-info"},gr={class:"p-fileupload-gallery-item-name"},mr={class:"p-fileupload-gallery-item-size"},hr={class:"p-fileupload-gallery-item-actions"},yr={key:1,class:"p-fileupload-gallery-actions"},wr={key:3,class:"p-fileupload-content"},br={class:"p-fileupload-upload"},kr={key:0},Fr={key:1},xr={class:"p-fileupload-row-content"},Ur={class:"p-fileupload-file"},$r={class:"p-fileupload-file-thumbnail"},_r=["src","alt"],Cr={class:"p-fileupload-file-details"},zr={class:"p-fileupload-file-name"},Er={class:"p-fileupload-file-size"},Sr={key:0,class:"p-fileupload-file-error"},Dr={key:1,class:"p-fileupload-file-warning"},Ar={class:"p-fileupload-file-actions"},Nr={key:0,class:"p-fileupload-file-progress"},Pr={key:1,class:"p-fileupload-actions"},Vr={key:2,class:"p-fileupload-progress"},Mr={class:"p-fileupload-progress-label"},Br=["accept","multiple","disabled"],Lr=Oe({__name:"BaseFileUpload",props:{mode:{type:String,default:"normal",validator:d=>["normal","avatar","gallery"].includes(d)},multiple:{type:Boolean,default:!0},accept:{type:String,default:""},maxSize:{type:Number,default:5*1024*1024},maxFiles:{type:Number,default:0},autoUpload:{type:Boolean,default:!0},uploadFunction:{type:[Function,null],default:null},uploadApiFunction:{type:Function,default:null},uploadUrl:{type:String,default:""},headers:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},showFileList:{type:Boolean,default:!0},fileListMaxHeight:{type:String,default:"300px"},imageCompressionOptions:{type:Object,default:()=>({enabled:!1,maxWidth:1920,maxHeight:1080,quality:.8,mimeType:"image/jpeg"})},chunkedUpload:{type:Boolean,default:!1},chunkSize:{type:Number,default:2*1024*1024},previewBeforeUpload:{type:Boolean,default:!1},modelValue:{type:Array,default:()=>[]},checkUploadUrl:{type:Boolean,default:!0},formDataName:{type:String,default:"upload_files[]"},fetchRemoteFileInfo:{type:Function,default:null}},emits:["update:modelValue","select","beforeUpload","progress","success","error","remove","exceed","exceedSize"],setup(d,{expose:X,emit:G}){const s=d,f=G,m=new Map,pe=()=>{const e=Date.now(),r=60*60*1e3;for(const[t,a]of m.entries())e-a.timestamp>r&&m.delete(t)};pe();const M=(e,r)=>{const t=new FormData;t.append(s.formDataName,e),r!=null&&r.onProgress&&(setTimeout(()=>r.onProgress(50),500),setTimeout(()=>r.onProgress(100),1e3));const a=r!=null&&r.signal?new AbortController:void 0;return r!=null&&r.signal&&a&&r.signal.addEventListener("abort",()=>{a.abort()}),(s.uploadApiFunction||Ge)(t).then(n=>n)};M.supportsChunks=!1;const re=A(null),B=A(!1),L=A([]),o=A([]),z=A(!1),E=A({}),O=A(0),R=A([]),K=T(()=>s.mode==="avatar"),ce=T(()=>s.mode==="gallery"),$e=T(()=>s.accept.split(",").map(e=>e.trim())),te=T(()=>o.value.length>0),W=T(()=>te.value&&!z.value&&!s.disabled),fe=T(()=>({maxHeight:s.fileListMaxHeight,overflowY:"auto"})),Q=["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","image/bmp"],_e=T(()=>({images:o.value.filter(r=>Q.includes(r.file.type)),documents:o.value.filter(r=>!Q.includes(r.file.type))})),ve=()=>Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15),ae=()=>{s.disabled||re.value&&re.value.click()},Ce=e=>{if(s.disabled)return;let r=null;e instanceof Event&&e.target?r=e.target.files:e instanceof FileList&&(r=e),!(!r||r.length===0)&&ge(r)},se=e=>{s.disabled||(e.preventDefault(),B.value=!1,e.dataTransfer&&ge(e.dataTransfer.files))},le=e=>{s.disabled||(e.preventDefault(),B.value=!0)},oe=e=>{s.disabled||(e.preventDefault(),B.value=!1)},ge=async e=>{const r=[];if(pe(),s.maxFiles>0&&e.length+o.value.length>s.maxFiles){f("exceed",{files:e,maxFiles:s.maxFiles,currentCount:o.value.length}),window.$toast.add({severity:"error",summary:"Error",detail:`You have exceeded the maximum number of files (${s.maxFiles})`,life:3e3});return}K.value&&H();for(let t=0;t<e.length;t++){const a=e[t];if(s.maxSize>0&&a.size>s.maxSize){f("exceedSize",{file:a,maxSize:s.maxSize}),window.$toast.add({severity:"error",summary:"Error",detail:"You have exceeded the maximum file size",life:3e3});continue}s.multiple||L.value.length===0?r.push(a):(r.push(a),H())}r.length!==0&&(await ze(r),s.autoUpload&&(s.uploadUrl||s.uploadFunction||typeof M=="function")&&j())},me=e=>{var y;if(!s.accept)return!0;const r=$e.value,t=e.type,a=(y=e.name.split(".").pop())==null?void 0:y.toLowerCase();return!!r.some(n=>!!(n===t||n.endsWith("/*")&&t.startsWith(n.replace("/*","/"))||n.startsWith(".")&&`.${a}`===n))},ze=async e=>{const r=[];for(const a of e){const y=ve();let n="",u="pending",b;(s.accept?me(a):!0)||(u="error",b=`Unsupported file format: must be a file of type: ${s.accept}`),Q.includes(a.type)&&(n=URL.createObjectURL(a));const x=ne(a),g=m.get(x),h={file:a,id:y,progress:0,status:u,preview:n,error:b,warning:g?`Note: This file has failed ${g.attempts} upload attempt(s)`:void 0};o.value.push(h),u==="pending"&&r.push(a),g&&g.attempts>1&&u==="pending"&&window.$toast.add({severity:"warn",summary:"Previous Upload Failed",detail:`File "${a.name}" failed to upload previously, retrying`,life:3e3})}const t=e.filter(a=>s.accept?me(a):!0);L.value=[...L.value,...t],r.length>0&&f("select",r),D()},D=()=>{if(s.multiple){const e=o.value.map(r=>{if(r.status==="success"){let t={url:"",name:r.file.name,size:r.file.size,type:r.file.type,status:"success"};if(r.response){if(typeof r.response=="string")t.url=r.response;else if(typeof r.response=="object")if("url"in r.response)t.url=r.response.url;else if(Array.isArray(r.response.file_path)&&r.response.file_path.length>0)t.url=r.response.file_path[0];else{const a=["url","path","filePath","file_url","fileUrl","src","source"];for(const y of a)if(y in r.response&&typeof r.response[y]=="string"){t.url=r.response[y];break}t.url||(t={...t,...r.response})}}return!t.url&&r.preview&&(t.url=r.preview),t}else{if(r.status==="error")return{name:r.file.name,size:r.file.size,type:r.file.type,status:"error",error:r.error,preview:r.preview||null,url:""};if(r.status==="pending"||r.status==="uploading")return{name:r.file.name,size:r.file.size,type:r.file.type,status:r.status,preview:r.preview||null,url:""}}return null}).filter(r=>r!==null);f("update:modelValue",e)}else if(o.value.length>0){const e=o.value[o.value.length-1];if(e.status==="success"){let r={url:"",name:e.file.name,size:e.file.size,type:e.file.type,status:"success"};if(e.response){if(typeof e.response=="string")r.url=e.response;else if(typeof e.response=="object")if("url"in e.response)r.url=e.response.url;else if(Array.isArray(e.response.file_path)&&e.response.file_path.length>0)r.url=e.response.file_path[0];else{const t=["url","path","filePath","file_url","fileUrl","src","source"];for(const a of t)if(a in e.response&&typeof e.response[a]=="string"){r.url=e.response[a];break}r.url||(r={...r,...e.response})}}!r.url&&e.preview&&(r.url=e.preview),f("update:modelValue",[r])}else f("update:modelValue",[{name:e.file.name,size:e.file.size,type:e.file.type,status:e.status,error:e.error,preview:e.preview||null,url:""}])}else f("update:modelValue",[])},H=()=>{o.value.forEach(e=>{e.preview&&URL.revokeObjectURL(e.preview)}),L.value=[],o.value=[],D()},he=e=>{const r=o.value.findIndex(a=>a.id===e);if(r===-1)return;const t=o.value[r];t.status==="uploading"&&e in E.value&&(E.value[e].abort(),delete E.value[e]),t.preview&&URL.revokeObjectURL(t.preview),o.value.splice(r,1),L.value=L.value.filter(a=>a!==t.file),f("remove",t.file),D()},Ee=e=>new Promise((r,t)=>{if(!s.imageCompressionOptions.enabled||!e.type.startsWith("image/")){r(e);return}const a=new FileReader;a.readAsDataURL(e),a.onload=y=>{var u;const n=new Image;n.src=(u=y.target)==null?void 0:u.result,n.onload=()=>{const{maxWidth:b,maxHeight:v,quality:x,mimeType:g}=s.imageCompressionOptions,h=document.createElement("canvas");let F=n.width,l=n.height;F>b&&(l=Math.round(l*b/F),F=b),l>v&&(F=Math.round(F*v/l),l=v),h.width=F,h.height=l;const U=h.getContext("2d");if(!U){t(new Error("Could not get canvas context"));return}U.drawImage(n,0,0,F,l),h.toBlob($=>{if(!$){t(new Error("Image compression failed"));return}const Y=new File([$],e.name,{type:g||e.type,lastModified:Date.now()});r(Y)},g||e.type,x)},n.onerror=()=>{t(new Error("Failed to load image"))}},a.onerror=()=>{t(new Error("Failed to read file"))}}),ye=e=>e.startsWith("image/")?"pi pi-image":e.startsWith("video/")?"pi pi-video":e.startsWith("audio/")?"pi pi-volume-up":e.includes("pdf")?"pi pi-file-pdf":e.includes("word")||e.includes("document")?"pi pi-file-word":e.includes("excel")||e.includes("sheet")?"pi pi-file-excel":e.includes("powerpoint")||e.includes("presentation")?"pi pi-file-powerpoint":e.includes("zip")||e.includes("compressed")?"pi pi-folder":"pi pi-file",we=e=>e===0?"0 MB":`${(e/(1024*1024)).toFixed(2)} MB`,Z=e=>(Ne(e.id),new Promise((r,t)=>{const{file:a,id:y}=e,n=ne(a);if(s.uploadFunction||M){const v=s.uploadFunction||M;e.status="uploading",e.progress=0;const x=new AbortController;E.value[y]={xhr:null,abort:()=>x.abort()};const g=h=>{e.progress=h,I(),f("progress",{file:a,progress:h})};v(a,{headers:s.headers,onProgress:g,signal:x.signal}).then(h=>{const{code:F,data:l,message:U}=h;if(e.status="success",F===0)Array.isArray(l==null?void 0:l.file_path)&&(l==null?void 0:l.file_path.length)>0?e.response=l.file_path[0]:e.response=h,m.delete(n),D(),f("success",{file:a,response:h}),r();else{e.status="error",e.error=U||"Invalid server response format",f("error",{file:a,error:e.error});const $=m.get(n)||{attempts:0};m.set(n,{timestamp:Date.now(),attempts:$.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:U||"Invalid server response format",life:3e3}),e.status="error",e.error=U||"Invalid server response format",f("error",{file:a,error:e.error}),D(),t(new Error(U||"Invalid server response format"))}}).catch(h=>{if(h.name==="AbortError")return;e.status="error",e.error=h.message||"Upload failed",f("error",{file:a,error:e.error});const F=m.get(n)||{attempts:0};m.set(n,{timestamp:Date.now(),attempts:F.attempts+1}),t(h)});return}const u=new XMLHttpRequest,b=new FormData;b.append("file",a),u.open("POST",s.uploadUrl),Object.entries(s.headers).forEach(([v,x])=>{u.setRequestHeader(v,x.toString())}),u.upload.addEventListener("progress",v=>{if(v.lengthComputable){const x=Math.round(v.loaded*100/v.total);e.progress=x,I(),f("progress",{file:a,progress:x})}}),u.onload=()=>{if(u.status>=200&&u.status<300)try{const v=JSON.parse(u.responseText);e.status="success",e.response=v,m.delete(n),D(),f("success",{file:a,response:v}),r()}catch{e.status="error",e.error="Invalid server response format",f("error",{file:a,error:e.error});const v=m.get(n)||{attempts:0};m.set(n,{timestamp:Date.now(),attempts:v.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Invalid server response format",life:3e3}),t(new Error("Invalid server response format"))}else{e.status="error",e.error=`${u.status}: ${u.statusText}`,f("error",{file:a,error:e.error});const v=m.get(n)||{attempts:0};m.set(n,{timestamp:Date.now(),attempts:v.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:`${u.status}: ${u.statusText}`,life:3e3}),t(new Error(`Upload failed: ${u.status} ${u.statusText}`))}},u.onerror=()=>{e.status="error",e.error="Network error",console.log("Network error",e),f("error",{file:a,error:"Network error"});const v=m.get(n)||{attempts:0};m.set(n,{timestamp:Date.now(),attempts:v.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Network error",life:3e3}),t(new Error("Network error"))},u.onabort=()=>{e.status="error",e.error="Upload cancelled",e.progress=0,f("error",{file:a,error:"Upload cancelled"}),window.$toast&&window.$toast.add({severity:"info",summary:"Info",detail:"Upload cancelled",life:3e3}),t(new Error("Upload cancelled"))},E.value[y]={xhr:u,abort:()=>u.abort()},u.send(b)})),ne=e=>`${e.name}_${e.size}_${e.lastModified}`,Se=e=>new Promise((r,t)=>{const{file:a,id:y}=e,n=s.chunkSize,u=Math.ceil(a.size/n);let b=0,v=0;if((s.uploadFunction||M)&&a.size>n){const g=s.uploadFunction||M;if(!g.supportsChunks){Z(e).then(r).catch(t);return}e.status="uploading",e.progress=0;const h=new AbortController;E.value[y]={xhr:null,abort:()=>h.abort()};const F=l=>{e.progress=l,I(),f("progress",{file:a,progress:l})};g(a,{headers:s.headers,onProgress:F,signal:h.signal,chunked:!0,chunkSize:n}).then(l=>{e.status="success",e.response=l,D(),f("success",{file:a,response:l}),r()}).catch(l=>{l.name!=="AbortError"&&(e.status="error",e.error=l.message||"Upload failed",f("error",{file:a,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Upload Error",detail:l.message||"Chunked upload failed",life:3e3}),t(l))});return}const x=async()=>{if(b>=u){e.status="success",D(),r();return}const g=b*n,h=Math.min(a.size,g+n),F=a.slice(g,h),l=new XMLHttpRequest,U=new FormData;U.append("file",F,a.name),U.append("chunk",b.toString()),U.append("chunks",u.toString()),U.append("fileName",a.name),l.open("POST",s.uploadUrl),Object.entries(s.headers).forEach(([$,Y])=>{l.setRequestHeader($,Y.toString())}),l.upload.addEventListener("progress",$=>{if($.lengthComputable){const Y=Math.round($.loaded*100/$.total);v=Math.round((b+Y/100)/u*100),e.progress=v,I(),f("progress",{file:a,progress:v})}}),l.onload=()=>{if(l.status>=200&&l.status<300)try{if(b===u-1){const $=JSON.parse(l.responseText);e.response=$,f("success",{file:a,response:$})}b++,x()}catch{e.status="error",e.error="Invalid server response format",f("error",{file:a,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Invalid server response format",life:3e3}),t(new Error("Invalid server response format"))}else e.status="error",e.error=`${l.status}: ${l.statusText}`,f("error",{file:a,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:`Chunk upload failed: ${l.status} ${l.statusText}`,life:3e3}),t(new Error(`Upload failed: ${l.status} ${l.statusText}`))},l.onerror=()=>{e.status="error",e.error="Network error",f("error",{file:a,error:"Network error"}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Network error during chunk upload",life:3e3}),t(new Error("Network error"))},l.onabort=()=>{e.status="error",e.error="Upload cancelled",e.progress=0,f("error",{file:a,error:"Upload cancelled"}),window.$toast&&window.$toast.add({severity:"info",summary:"Info",detail:"Chunked upload cancelled",life:3e3}),t(new Error("Upload cancelled"))},E.value[y]={xhr:l,abort:()=>l.abort()},l.send(U)};x().catch(t)}),I=()=>{if(o.value.length===0){O.value=0;return}const e=o.value.reduce((r,t)=>r+t.progress,0);O.value=Math.round(e/o.value.length)},De=()=>{if(s.checkUploadUrl&&!s.uploadFunction&&!s.uploadUrl&&!M)return R.value.push("No upload function or URL provided"),!1;if(o.value.length===0)return R.value.push("No files selected"),!1;const e={files:L.value,cancel:!1};return f("beforeUpload",e),!e.cancel},j=async()=>{if(R.value=[],!De()){R.value.length>0&&window.$toast&&window.$toast.add({severity:"error",summary:"Validation Error",detail:R.value[0],life:3e3});return}z.value=!0;try{const e=o.value.filter(r=>r.status==="pending"&&r.error===void 0);for(const r of e){r.status="uploading",r.progress=0;try{let t=r.file;s.imageCompressionOptions.enabled&&r.file.type.startsWith("image/")&&(t=await Ee(r.file)),s.chunkedUpload&&t.size>s.chunkSize?await Se(r):await Z(r)}catch(t){console.error("File upload failed:",r.file.name,t)}}}catch(e){window.$toast&&window.$toast.add({severity:"error",summary:"Upload Error",detail:e instanceof Error?e.message:"An error occurred during upload",life:3e3})}finally{z.value=!1}},be=()=>{Object.values(E.value).forEach(e=>e.abort()),E.value={},o.value.forEach(e=>{e.status==="uploading"&&(e.status="error",e.error="Upload cancelled",e.progress=0)}),z.value=!1,O.value=0};X({upload:j,abort:be,clearFiles:H,submit:()=>{W.value&&j()}}),Re(()=>{Object.values(E.value).forEach(e=>e.abort()),o.value.forEach(e=>{e.preview&&URL.revokeObjectURL(e.preview)})});const ke=()=>{if(s.modelValue){if(Array.isArray(s.modelValue)&&s.modelValue.length>0)s.modelValue.forEach(e=>{if(typeof e=="string")J(e);else if(typeof e=="object"&&e!==null){const r=e;"status"in r&&r.status==="error"?J(r.url||"",r.name,r.size,"error",r.error):"url"in r&&J(r.url,r.name,r.size)}});else if(typeof s.modelValue=="string"&&s.modelValue)J(s.modelValue);else if(typeof s.modelValue=="object"&&s.modelValue!==null&&"url"in s.modelValue){const e=s.modelValue;J(e.url,e.name,e.size)}}},J=async(e,r,t,a,y)=>{let n=r||Fe(e),u=Ae(e);const b=ve();if(s.fetchRemoteFileInfo)try{const g=await s.fetchRemoteFileInfo(e);g.type&&(u=g.type),g.name&&(n=g.name)}catch(g){console.warn("Failed to fetch remote file info:",g)}let v="";if(Q.includes(u)&&(v=e),u.startsWith("image/")&&!t){const g=new Image;g.onload=()=>{const h=Math.round(g.width*g.height*.25),F=o.value.findIndex(l=>l.id===b);if(F!==-1){const l=new File([new ArrayBuffer(h)],n,{type:u});o.value[F].file=l,o.value=[...o.value]}},g.src=e}const x={file:new File([],n,{type:u}),id:b,progress:100,status:a||"success",response:{url:e},preview:v||""};a==="error"&&y&&(x.error=y),o.value.push(x)},Fe=e=>{const r=e.split("/");return r[r.length-1].split("?")[0]},Ae=e=>{var y;const t=((y=Fe(e).split(".").pop())==null?void 0:y.toLowerCase())||"";return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",svg:"image/svg+xml",webp:"image/webp",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",zip:"application/zip",mp3:"audio/mpeg",mp4:"video/mp4"}[t]||"application/octet-stream"};Te(()=>{ke()}),We(()=>s.modelValue,()=>{o.value.length===0&&ke()},{deep:!0});const Ne=e=>{const r=o.value.find(t=>t.id===e);if(r){const t=ne(r.file);m.delete(t),r.warning&&(delete r.warning,o.value=[...o.value])}};return(e,r)=>(i(),p("div",{class:N(["p-fileupload p-component",{"p-fileupload-advanced":!K.value,"p-fileupload-avatar":K.value,"p-fileupload-gallery":ce.value,"p-disabled":d.disabled}])},[R.value.length>0?(i(),p("div",tr,[(i(!0),p(ie,null,ue(R.value,(t,a)=>(i(),P(k(je),{key:a,severity:"error",closable:!0},{default:He(()=>[de(S(t),1)]),_:2},1024))),128))])):w("",!0),K.value?(i(),p("div",ar,[c("div",{class:N(["p-fileupload-avatar-dropzone",{"p-fileupload-dragover":B.value}]),onClick:ae,onDragover:le,onDragleave:oe,onDrop:se},[o.value.length===0||!o.value[0].preview?(i(),p("div",sr,[r[1]||(r[1]=c("i",{class:"pi pi-user p-fileupload-avatar-icon"},null,-1)),V(e.$slots,"upload-text",{},()=>[r[0]||(r[0]=c("div",{class:"p-fileupload-text"}," Drop or click to upload avatar ",-1))],!0)])):(i(),p("div",lr,[c("img",{src:o.value[0].preview,alt:"Avatar Preview"},null,8,or),r[2]||(r[2]=c("div",{class:"p-fileupload-avatar-overlay"},[c("div",{class:"p-fileupload-avatar-overlay-content"},[c("i",{class:"pi pi-camera"}),c("span",null,"Change avatar")])],-1))]))],34),o.value.length>0&&!d.autoUpload&&!z.value&&o.value[0].status==="pending"?(i(),p("div",nr,[_(k(C),{icon:"pi pi-upload",class:"p-button-rounded p-button-success",disabled:!W.value,onClick:j},null,8,["disabled"]),_(k(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger",onClick:H})])):w("",!0),z.value&&o.value.filter(t=>t.status==="uploading").length>1?(i(),P(k(q),{key:1,value:O.value},null,8,["value"])):w("",!0)])):ce.value?(i(),p("div",ir,[c("div",{class:N(["p-fileupload-gallery-dropzone",{"p-fileupload-dragover":B.value}]),onClick:ae,onDragover:le,onDragleave:oe,onDrop:se},[o.value.length===0?(i(),p("div",ur,[V(e.$slots,"upload-icon",{},()=>[r[3]||(r[3]=c("i",{class:"pi pi-images p-fileupload-gallery-icon"},null,-1))],!0),V(e.$slots,"upload-text",{},()=>[r[4]||(r[4]=c("span",null,"Drop or click to upload images",-1))],!0)])):w("",!0)],34),d.showFileList&&o.value.length>0?(i(),p("div",{key:0,class:"p-fileupload-gallery-files",style:xe(fe.value)},[c("div",dr,[(i(!0),p(ie,null,ue(_e.value.images,t=>(i(),p("div",{key:t.id,class:N(["p-fileupload-gallery-item",{"p-fileupload-gallery-item-uploading":t.status==="uploading","p-fileupload-gallery-item-success":t.status==="success","p-fileupload-gallery-item-error":t.status==="error"}])},[c("div",pr,[t.preview?(i(),p("img",{key:0,src:t.preview,alt:t.file.name},null,8,cr)):(i(),p("i",{key:1,class:N(ye(t.file.type))},null,2))]),c("div",fr,[c("div",vr,[c("span",gr,S(t.file.name),1),c("span",mr,S(we(t.file.size)),1)]),c("div",hr,[t.status==="pending"?(i(),P(k(C),{key:0,icon:"pi pi-upload",class:"p-button-rounded p-button-outlined p-button-sm p-button-success",onClick:()=>{W.value&&Z(t)}},null,8,["onClick"])):w("",!0),_(k(C),{icon:"pi pi-times",class:"p-button-rounded p-button-outlined p-button-sm p-button-danger",onClick:()=>he(t.id)},null,8,["onClick"])]),t.status==="uploading"?(i(),P(k(q),{key:0,value:t.progress,class:"p-fileupload-gallery-item-progress"},null,8,["value"])):w("",!0),t.status==="success"?(i(),P(k(Ue),{key:1,value:"Success",severity:"success"})):w("",!0),t.status==="error"?(i(),P(k(Ue),{key:2,value:"Error",severity:"danger"})):w("",!0)])],2))),128))])],4)):w("",!0),!d.autoUpload&&!z.value&&te.value?(i(),p("div",yr,[_(k(C),{icon:"pi pi-upload",label:"Upload All",disabled:!W.value,onClick:j},null,8,["disabled"]),_(k(C),{icon:"pi pi-times",label:"Clear",class:"p-button-outlined p-button-danger",onClick:H})])):w("",!0),z.value&&o.value.filter(t=>t.status==="uploading").length>1?(i(),P(k(q),{key:2,value:O.value},null,8,["value"])):w("",!0)])):(i(),p("div",wr,[c("div",{class:N(["p-fileupload-dropzone",{"p-fileupload-dragover":B.value}]),onClick:ae,onDragover:le,onDragleave:oe,onDrop:se},[V(e.$slots,"upload-area",{},()=>[c("div",br,[V(e.$slots,"upload-icon",{},()=>[r[5]||(r[5]=c("i",{class:"pi pi-upload p-fileupload-icon"},null,-1))],!0),V(e.$slots,"upload-text",{},()=>[B.value?(i(),p("span",Fr,"Release to upload")):(i(),p("span",kr,"Drop files here or click to upload"))],!0)])],!0)],34),d.showFileList&&o.value.length>0?(i(),p("div",{key:0,class:"p-fileupload-files",style:xe(fe.value)},[(i(!0),p(ie,null,ue(o.value,t=>(i(),p("div",{key:t.id,class:N(["p-fileupload-row",{"p-fileupload-row-uploading":t.status==="uploading","p-fileupload-row-success":t.status==="success","p-fileupload-row-error":t.status==="error","p-fileupload-row-warning":t.warning}])},[c("div",xr,[c("div",Ur,[V(e.$slots,"file-item",{file:t.file,preview:t.preview,status:t.status,warning:t.warning},()=>[c("div",$r,[t.preview?(i(),p("img",{key:0,src:t.preview,alt:t.file.name,class:"p-fileupload-file-preview"},null,8,_r)):(i(),p("i",{key:1,class:N(["p-fileupload-file-icon",[ye(t.file.type)]])},null,2))]),c("div",Cr,[c("div",zr,S(t.file.name),1),c("div",Er,S(we(t.file.size)),1),t.status==="error"?(i(),p("div",Sr,S(t.error),1)):w("",!0),t.warning?(i(),p("div",Dr,[r[6]||(r[6]=c("i",{class:"pi pi-exclamation-triangle",style:{"margin-right":"4px"}},null,-1)),de(" "+S(t.warning),1)])):w("",!0)])],!0)]),c("div",Ar,[t.status==="pending"?(i(),P(k(C),{key:0,icon:"pi pi-upload",class:"p-button-outlined p-button-rounded p-button-success",onClick:()=>{W.value&&Z(t)}},null,8,["onClick"])):w("",!0),_(k(C),{icon:"pi pi-times",class:"p-button-outlined p-button-rounded p-button-danger",onClick:()=>he(t.id)},null,8,["onClick"])])]),t.status==="uploading"?(i(),p("div",Nr,[_(k(q),{value:t.progress},null,8,["value"])])):w("",!0)],2))),128))],4)):w("",!0),!d.autoUpload&&!z.value&&te.value?(i(),p("div",Pr,[_(k(C),{icon:"pi pi-upload",label:"Upload All",disabled:!W.value,onClick:j},null,8,["disabled"]),_(k(C),{icon:"pi pi-ban",label:"Clear",class:"p-button-outlined p-button-danger",onClick:H})])):w("",!0),z.value&&o.value.filter(t=>t.status==="uploading").length>1?(i(),p("div",Vr,[c("div",Mr,S(O.value)+"% ",1),_(k(q),{value:O.value},null,8,["value"]),_(k(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger p-button-outlined p-fileupload-cancel",onClick:be})])):w("",!0)])),c("input",{ref_key:"fileInput",ref:re,type:"file",class:"p-hidden",accept:d.accept,multiple:d.multiple,disabled:d.disabled,onChange:Ce},null,40,Br)],2))}}),jr=qe(Lr,[["__scopeId","data-v-d421e6f1"]]);export{jr as _};
