import{I as K,J as W,a3 as j,a as f,o as c,b as p,A as E,M as D,d as O,aO as J,r as N,f as q,z as V,aI as Q,G as $,w as T,aJ as Y,aK as Z,m as M,p as k,u as d,e as b,n as G,q as B,s as C,t as R,E as F,c as S,U as _}from"./index-DWWVg5mm.js";import{s as x}from"./index-M77XcG3U.js";import{s as U}from"./index-IMPOxVZV.js";import{a as ee}from"./index-BEwkorqa.js";import{s as le}from"./index-W8aGMMs9.js";import{s as ae}from"./index-T2UmNbVz.js";import{s as te}from"./index-DDWh5zyb.js";import{s as ie}from"./index-DNL_tiqf.js";var n=(a=>(a.TEXT="TEXT",a.SELECT="SELECT",a.DATE="date",a.DATE_RANGE="DATE_RANGE",a.NUMBER="NUMBER",a.NUMBER_RANGE="NUMBER_RANGE",a.CHECKBOX="CHECKBOX",a.RADIO="RADIO",a.SWITCH="SWITCH",a))(n||{}),se=K`
    .p-toggleswitch {
        display: inline-block;
        width: dt('toggleswitch.width');
        height: dt('toggleswitch.height');
    }

    .p-toggleswitch-input {
        cursor: pointer;
        appearance: none;
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        opacity: 0;
        z-index: 1;
        outline: 0 none;
        border-radius: dt('toggleswitch.border.radius');
    }

    .p-toggleswitch-slider {
        cursor: pointer;
        width: 100%;
        height: 100%;
        border-width: dt('toggleswitch.border.width');
        border-style: solid;
        border-color: dt('toggleswitch.border.color');
        background: dt('toggleswitch.background');
        transition:
            background dt('toggleswitch.transition.duration'),
            color dt('toggleswitch.transition.duration'),
            border-color dt('toggleswitch.transition.duration'),
            outline-color dt('toggleswitch.transition.duration'),
            box-shadow dt('toggleswitch.transition.duration');
        border-radius: dt('toggleswitch.border.radius');
        outline-color: transparent;
        box-shadow: dt('toggleswitch.shadow');
    }

    .p-toggleswitch-handle {
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: dt('toggleswitch.handle.background');
        color: dt('toggleswitch.handle.color');
        width: dt('toggleswitch.handle.size');
        height: dt('toggleswitch.handle.size');
        inset-inline-start: dt('toggleswitch.gap');
        margin-block-start: calc(-1 * calc(dt('toggleswitch.handle.size') / 2));
        border-radius: dt('toggleswitch.handle.border.radius');
        transition:
            background dt('toggleswitch.transition.duration'),
            color dt('toggleswitch.transition.duration'),
            inset-inline-start dt('toggleswitch.slide.duration'),
            box-shadow dt('toggleswitch.slide.duration');
    }

    .p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-slider {
        background: dt('toggleswitch.checked.background');
        border-color: dt('toggleswitch.checked.border.color');
    }

    .p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.checked.background');
        color: dt('toggleswitch.handle.checked.color');
        inset-inline-start: calc(dt('toggleswitch.width') - calc(dt('toggleswitch.handle.size') + dt('toggleswitch.gap')));
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-slider {
        background: dt('toggleswitch.hover.background');
        border-color: dt('toggleswitch.hover.border.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.hover.background');
        color: dt('toggleswitch.handle.hover.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-slider {
        background: dt('toggleswitch.checked.hover.background');
        border-color: dt('toggleswitch.checked.hover.border.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.checked.hover.background');
        color: dt('toggleswitch.handle.checked.hover.color');
    }

    .p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible) .p-toggleswitch-slider {
        box-shadow: dt('toggleswitch.focus.ring.shadow');
        outline: dt('toggleswitch.focus.ring.width') dt('toggleswitch.focus.ring.style') dt('toggleswitch.focus.ring.color');
        outline-offset: dt('toggleswitch.focus.ring.offset');
    }

    .p-toggleswitch.p-invalid > .p-toggleswitch-slider {
        border-color: dt('toggleswitch.invalid.border.color');
    }

    .p-toggleswitch.p-disabled {
        opacity: 1;
    }

    .p-toggleswitch.p-disabled .p-toggleswitch-slider {
        background: dt('toggleswitch.disabled.background');
    }

    .p-toggleswitch.p-disabled .p-toggleswitch-handle {
        background: dt('toggleswitch.handle.disabled.background');
    }
`,oe={root:{position:"relative"}},de={root:function(o){var l=o.instance,m=o.props;return["p-toggleswitch p-component",{"p-toggleswitch-checked":l.checked,"p-disabled":m.disabled,"p-invalid":l.$invalid}]},input:"p-toggleswitch-input",slider:"p-toggleswitch-slider",handle:"p-toggleswitch-handle"},ne=W.extend({name:"toggleswitch",style:se,classes:de,inlineStyles:oe}),re={name:"BaseToggleSwitch",extends:ee,props:{trueValue:{type:null,default:!0},falseValue:{type:null,default:!1},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},inputId:{type:String,default:null},inputClass:{type:[String,Object],default:null},inputStyle:{type:Object,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:ne,provide:function(){return{$pcToggleSwitch:this,$parentInstance:this}}},z={name:"ToggleSwitch",extends:re,inheritAttrs:!1,emits:["change","focus","blur"],methods:{getPTOptions:function(o){var l=o==="root"?this.ptmi:this.ptm;return l(o,{context:{checked:this.checked,disabled:this.disabled}})},onChange:function(o){if(!this.disabled&&!this.readonly){var l=this.checked?this.falseValue:this.trueValue;this.writeValue(l,o),this.$emit("change",o)}},onFocus:function(o){this.$emit("focus",o)},onBlur:function(o){var l,m;this.$emit("blur",o),(l=(m=this.formField).onBlur)===null||l===void 0||l.call(m,o)}},computed:{checked:function(){return this.d_value===this.trueValue},dataP:function(){return j({checked:this.checked,disabled:this.disabled,invalid:this.$invalid})}}},ue=["data-p-checked","data-p-disabled","data-p"],ce=["id","checked","tabindex","disabled","readonly","aria-checked","aria-labelledby","aria-label","aria-invalid"],he=["data-p"],ge=["data-p"];function me(a,o,l,m,v,i){return c(),f("div",E({class:a.cx("root"),style:a.sx("root")},i.getPTOptions("root"),{"data-p-checked":i.checked,"data-p-disabled":a.disabled,"data-p":i.dataP}),[p("input",E({id:a.inputId,type:"checkbox",role:"switch",class:[a.cx("input"),a.inputClass],style:a.inputStyle,checked:i.checked,tabindex:a.tabindex,disabled:a.disabled,readonly:a.readonly,"aria-checked":i.checked,"aria-labelledby":a.ariaLabelledby,"aria-label":a.ariaLabel,"aria-invalid":a.invalid||void 0,onFocus:o[0]||(o[0]=function(){return i.onFocus&&i.onFocus.apply(i,arguments)}),onBlur:o[1]||(o[1]=function(){return i.onBlur&&i.onBlur.apply(i,arguments)}),onChange:o[2]||(o[2]=function(){return i.onChange&&i.onChange.apply(i,arguments)})},i.getPTOptions("input")),null,16,ce),p("div",E({class:a.cx("slider")},i.getPTOptions("slider"),{"data-p":i.dataP}),[p("div",E({class:a.cx("handle")},i.getPTOptions("handle"),{"data-p":i.dataP}),[D(a.$slots,"handle",{checked:i.checked})],16,ge)],16,he)],16,ue)}z.render=me;var pe={name:"InputSwitch",extends:z,mounted:function(){console.warn("Deprecated since v4. Use ToggleSwitch component instead.")}};const fe=O({__name:"BaseRangeDatePicker",emits:["valueChange"],setup(a,{emit:o}){const l=o,m=J(),v=N(),i=N(1),y=h=>{var w;m["selection-mode"]==="range"&&((w=v.value)!=null&&w.overlayVisible)&&Array.isArray(h)&&h.length===2&&h[0]&&h[1]&&(v.value.overlayVisible=!1),l("valueChange",h)};return q(()=>{window.innerWidth<768?i.value=1:i.value=2}),(h,w)=>{const e=ie;return c(),V(e,E(h.$attrs,{ref_key:"datePickerRef",ref:v,"number-of-months":i.value,class:"base-range-date-picker","panel-class":"base-range-date-picker-panel",onValueChange:y}),Q({_:2},[$(h.$slots,(s,r)=>({name:r,fn:T(A=>[D(h.$slots,r,Y(Z(A)))])}))]),1040,["number-of-months"])}}}),be={key:0,class:"search-form-input-group"},ve={key:0,class:"pi pi-search search-form-input-icon"},we={key:4,class:"flex items-center gap-3"},ye={key:5,class:"flex items-center gap-2"},ke=["for"],Ve={key:6,class:"flex flex-wrap gap-6"},Ae=["for"],Ee={key:7,class:"flex items-center gap-2"},Ce=["for"],H=O({__name:"BaseSearchItem",props:{field:{},modelValue:{},isAdvanced:{type:Boolean},onClear:{type:Function}},emits:["update:modelValue"],setup(a,{emit:o}){const l=a,m=o,v=()=>{l.field.type===n.NUMBER_RANGE&&(!l.modelValue[l.field.name]||!Array.isArray(l.modelValue[l.field.name]))&&(l.modelValue[l.field.name]=[null,null])};M(()=>l.field,v,{immediate:!0}),M(()=>l.modelValue,v,{immediate:!0,deep:!0});const i=(e,s)=>typeof s=="boolean"?s:s==="true"||s===!0||!1,y=()=>{l.onClear?l.onClear():l.field.type===n.NUMBER_RANGE&&(l.modelValue[l.field.name]=[null,null],m("update:modelValue",l.modelValue))},h=e=>{if(l.field.type===n.NUMBER_RANGE&&Array.isArray(l.modelValue[l.field.name])){const s=l.modelValue[l.field.name][1];l.modelValue[l.field.name][0]=e,e!==null&&s!==null&&e>s&&(l.modelValue[l.field.name][1]=e),m("update:modelValue",l.modelValue)}},w=e=>{if(l.field.type===n.NUMBER_RANGE&&Array.isArray(l.modelValue[l.field.name])){const s=l.modelValue[l.field.name][0];l.modelValue[l.field.name][1]=e,e!==null&&s!==null&&s>e&&(l.modelValue[l.field.name][0]=e),m("update:modelValue",l.modelValue)}};return(e,s)=>e.field.type===d(n).TEXT?(c(),f("div",be,[e.isAdvanced?k("",!0):(c(),f("i",ve)),b(d(le),{modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":s[0]||(s[0]=r=>e.modelValue[e.field.name]=r),placeholder:e.field.placeholder,class:B(e.isAdvanced?"w-full":"search-form-input"),style:G(e.field.width&&!e.isAdvanced?{width:e.field.width}:{}),maxlength:e.field.maxlength},null,8,["modelValue","placeholder","class","style","maxlength"])])):e.field.type===d(n).SELECT?(c(),f("div",{key:1,class:B(e.isAdvanced?"":"search-form-select-group")},[b(d(te),{modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":s[1]||(s[1]=r=>e.modelValue[e.field.name]=r),options:e.field.options,"option-label":"label","option-value":"value",placeholder:e.field.placeholder||"All",class:B(e.isAdvanced?"w-full":"search-form-select"),style:G(e.field.width&&!e.isAdvanced?{width:e.field.width}:{}),"show-clear":""},{dropdownicon:T(()=>s[7]||(s[7]=[p("span",{class:"search-form-select-arrow"},null,-1)])),_:1},8,["modelValue","options","placeholder","class","style"])],2)):e.field.type===d(n).DATE_RANGE?(c(),V(fe,{key:2,modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":s[2]||(s[2]=r=>e.modelValue[e.field.name]=r),class:"search-form-date-range w-full","selection-mode":"range",placeholder:e.field.placeholder||"Select Date Range","date-format":"dd/mm/yy"},null,8,["modelValue","placeholder"])):e.field.type===d(n).NUMBER?(c(),V(d(U),{key:3,modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":s[3]||(s[3]=r=>e.modelValue[e.field.name]=r),placeholder:e.field.placeholder||"Enter Number",min:e.field.min,max:e.field.max,step:e.field.step||1,class:"w-full"},null,8,["modelValue","placeholder","min","max","step"])):e.field.type===d(n).NUMBER_RANGE?(c(),f("div",we,[Array.isArray(e.modelValue[e.field.name])?(c(),V(d(U),{key:0,"model-value":e.modelValue[e.field.name][0],placeholder:"Min",min:e.field.min||0,max:e.field.max||999999999,step:e.field.step||1,class:"flex-1","onUpdate:modelValue":h},null,8,["model-value","min","max","step"])):k("",!0),Array.isArray(e.modelValue[e.field.name])?(c(),V(d(U),{key:1,"model-value":e.modelValue[e.field.name][1],placeholder:"Max",min:e.field.min||0,max:e.field.max||999999999,step:e.field.step||1,class:"flex-1","onUpdate:modelValue":w},null,8,["model-value","min","max","step"])):k("",!0),Array.isArray(e.modelValue[e.field.name])&&(e.modelValue[e.field.name][0]!==null||e.modelValue[e.field.name][1]!==null)?(c(),V(d(C),{key:2,severity:"warn",icon:"pi pi-times",text:"",class:"clear-btn",onClick:y})):k("",!0)])):e.field.type===d(n).CHECKBOX?(c(),f("div",ye,[b(d(x),{"model-value":i(e.field.name,e.modelValue[e.field.name]),binary:!0,"input-id":e.field.name,"onUpdate:modelValue":s[4]||(s[4]=r=>e.modelValue[e.field.name]=r)},null,8,["model-value","input-id"]),p("label",{for:e.field.name,class:"cursor-pointer"},R(e.field.label),9,ke)])):e.field.type===d(n).RADIO?(c(),f("div",Ve,[(c(!0),f(F,null,$(e.field.options,r=>(c(),f("div",{key:r.value,class:"flex items-center gap-2"},[b(d(ae),{modelValue:e.modelValue[e.field.name],"onUpdate:modelValue":s[5]||(s[5]=A=>e.modelValue[e.field.name]=A),value:r.value,"input-id":`${e.field.name}_${r.value}`},null,8,["modelValue","value","input-id"]),p("label",{for:`${e.field.name}_${r.value}`,class:"cursor-pointer"},R(r.label),9,Ae)]))),128))])):e.field.type===d(n).SWITCH?(c(),f("div",Ee,[b(d(pe),{"model-value":i(e.field.name,e.modelValue[e.field.name]),"input-id":e.field.name,"onUpdate:modelValue":s[6]||(s[6]=r=>e.modelValue[e.field.name]=r)},null,8,["model-value","input-id"]),p("label",{for:e.field.name,class:"cursor-pointer"},R(e.field.label),9,Ce)])):k("",!0)}}),Re={class:"common-search-wrap"},Se={class:"common-search"},Be={class:"search-container bg-white"},Ne={class:"search-form-container gap-15"},$e={class:"search-form-label"},Te={class:"search-container-actions"},Ue={key:0,class:"more-filters-container flex items-end"},Me={class:"more-filters-container flex items-end"},Fe={class:"search-btn-container flex items-end"},Oe={class:"advanced-filters-content p-4"},Ie={key:0,class:"text-lg font-medium mb-2"},Pe={class:"search-btn-container w-full flex justify-end"},je=O({__name:"BaseSearch",props:{loading:{type:Boolean,default:!1},basicSearchFields:{default:()=>[]},advancedSearchFields:{default:()=>[]},modelValue:{default:()=>({})}},emits:["search","update:modelValue"],setup(a,{expose:o,emit:l}){const m=a,v=l,i=N(!1),y=N({}),h=S({get(){return m.modelValue||{}},set(t){v("update:modelValue",t)}}),w=S(()=>[...m.basicSearchFields,...m.advancedSearchFields]),e=t=>t.defaultValue!==void 0?t.type===n.NUMBER_RANGE&&Array.isArray(t.defaultValue)?[...t.defaultValue]:t.defaultValue:t.type===n.NUMBER_RANGE?[null,null]:t.type===n.CHECKBOX||t.type===n.SWITCH?!1:t.type===n.SELECT||t.type===n.RADIO?"":t.type===n.DATE_RANGE?null:"",s=()=>{if(w.value&&w.value.length>0){const t={...h.value};let g=!1;w.value.forEach(u=>{y.value[u.name]=e(u),t[u.name]===void 0&&(t[u.name]=y.value[u.name],g=!0)}),g&&v("update:modelValue",t)}};M(()=>w.value,()=>{s()},{immediate:!0});const r=()=>{v("search"),i.value=!1},A=t=>{h.value[t]&&Array.isArray(h.value[t])&&(h.value[t]=[null,null])},I=()=>{if(!h.value||!m.advancedSearchFields)return!1;for(const t of m.advancedSearchFields){const g=h.value[t.name];if(g!=null&&(Array.isArray(g)&&g.some(u=>u!==null&&u!=="")||(t.type===n.CHECKBOX||t.type===n.SWITCH)&&g||g!==""))return!0}return!1},P=S(()=>I()),L=S(()=>I()?"pi-filter-fill":"pi-filter"),X=()=>{w.value.forEach(t=>{const g=y.value[t.name];g!==void 0&&(Array.isArray(g)?h.value[t.name]=[...g]:h.value[t.name]=g)}),r()};return o({hasAdvancedFilters:P}),(t,g)=>(c(),f("div",Re,[p("div",Se,[p("div",Be,[p("div",Ne,[(c(!0),f(F,null,$(t.basicSearchFields,u=>(c(),f("div",{key:u.name,class:"search-field-container"},[p("div",$e,R(u.label),1),b(H,{field:u,"model-value":h.value,"is-advanced":!1},null,8,["field","model-value"])]))),128))]),p("div",Te,[t.advancedSearchFields.length>0?(c(),f("div",Ue,[b(d(C),{icon:`pi ${L.value}`,label:"More Filters",class:B(["more-filters-btn",{"has-filters":P.value}]),text:"",onClick:g[0]||(g[0]=u=>i.value=!0)},{default:T(()=>g[2]||(g[2]=[p("span",null,"More Filters",-1)])),_:1,__:[2]},8,["icon","class"])])):k("",!0),p("div",Me,[b(d(C),{label:"Clear Filters",class:"more-filters-btn",text:"",loading:t.loading,onClick:X},null,8,["loading"])]),p("div",Fe,[b(d(C),{label:"SEARCH",class:"search-btn",loading:t.loading,severity:"info",onClick:r},null,8,["loading"])])])]),b(d(_),{visible:i.value,"onUpdate:visible":g[1]||(g[1]=u=>i.value=u),header:"Advanced Filters",modal:!0,style:{width:"650px"},closable:!0,draggable:"","dismissable-mask":!0},{default:T(()=>[p("div",Oe,[(c(!0),f(F,null,$(t.advancedSearchFields,u=>(c(),f("div",{key:u.name,class:"mb-6"},[u.type!==d(n).CHECKBOX&&u.type!==d(n).SWITCH?(c(),f("div",Ie,R(u.label),1)):k("",!0),b(H,{field:u,"model-value":h.value,"is-advanced":!0,"on-clear":()=>u.type===d(n).NUMBER_RANGE?A(u.name):null},null,8,["field","model-value","on-clear"])]))),128)),p("div",Pe,[b(d(C),{label:"SEARCH",class:"search-btn",loading:t.loading,severity:"info",onClick:r},null,8,["loading"])])])]),_:1},8,["visible"])])]))}});export{n as S,je as _};
