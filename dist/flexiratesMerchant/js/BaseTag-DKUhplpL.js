import{d as s,a as n,M as o,j as i,t as c,q as r,o as d}from"./index-DWWVg5mm.js";import{_ as p}from"./_plugin-vue_export-helper-DlAUqK2U.js";const f=s({name:"BaseTag",props:{text:{type:String,default:""},type:{type:String,default:"default",validator:e=>["default","paid","upcoming","failed","info"].includes(e)},clickable:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:a}){return{handleClick:()=>{e.clickable&&a("click")}}}});function u(e,a,t,k,m,g){return d(),n("div",{class:r(["base-tag",[`base-tag--${e.type}`,{"base-tag--clickable":e.clickable}]]),onClick:a[0]||(a[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[o(e.$slots,"default",{},()=>[i(c(e.text),1)],!0)],2)}const C=p(f,[["render",u],["__scopeId","data-v-9b54f484"]]);export{C as _};
