import{B as z}from"./BaseDataTable-75nyB6_U.js";import{d as A,B as R,r as i,c as T,a as h,o as x,e as c,p as V,u as s,w as u,s as I,b as N,q as Y,t as b,j}from"./index-DWWVg5mm.js";import{d as E}from"./dayjs.min-DUDfdLe0.js";import{u as S}from"./useRequestList-Bk9BBkCQ.js";import{b as w}from"./ratepayer-CzZHWazT.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";const F={key:0,class:"flex justify-center mt-4"},O=A({__name:"activityLogs",setup(W){var y;const m=R(),p=S({page_size:10,defaultParams:{customer_id:(y=m.query)==null?void 0:y.customerId},requestFn:w}),B=i([{field:"",header:"Date & Time",style:{width:"200px",padding:"0 8px"},template:"date"},{field:"activity_type_desc",header:"Activity Type",style:{width:"300px"}},{field:"activity_detail",header:"Details"},{field:"status",header:"Status",style:{width:"100px"},template:"status"}]),{list:L,loading:C,total:d,onPageChange:D,failed:M,failureMessage:q}=p,l=i(1),P=i(10),f=()=>{l.value=1,p.refresh()},r=i(!1),g=i([]),k=async()=>{var n,a;if(!r.value){r.value=!0;try{const o=l.value+1,t=await w({page:o,page_size:P.value,customer_id:(n=m.query)==null?void 0:n.customerId});Array.isArray((a=t==null?void 0:t.data)==null?void 0:a.data)&&t.data.data.length>0&&(g.value=t.data.data,l.value=o)}finally{r.value=!1}}};f();const v=T(()=>[...L.value,...g.value]);return(n,a)=>{var _;const o=I,t=z;return x(),h("div",null,[c(t,{value:v.value,columns:B.value,loading:s(C),"show-multiple-column":!1,paginator:!1,"show-search-bar":!1,rows:10,lazy:!0,"data-key":"id",failed:s(M),"failure-message":s(q),"row-hover":!1,"total-records":s(d),class:"activity-log-table",onRefresh:f,onPage:a[0]||(a[0]=e=>s(D)(e))},{date:u(({data:e})=>[j(b(s(E)(e==null?void 0:e.activity_at).format("DD MMM YYYY, HH:mm A")),1)]),status:u(({data:e})=>[N("span",{class:Y(["font-semibold",{"text-green-500":(e==null?void 0:e.activity_status)===1,"text-red-500":(e==null?void 0:e.activity_status)===0}])},b(e==null?void 0:e.activity_status_desc),3)]),"empty-action":u(()=>[c(o,{label:"Refresh"})]),_:1},8,["value","columns","loading","failed","failure-message","total-records"]),((_=v.value)==null?void 0:_.length)<s(d)?(x(),h("div",F,[c(o,{label:"VIEW MORE",class:"btn",loading:r.value,onClick:k},null,8,["loading"])])):V("",!0)])}}}),pe=H(O,[["__scopeId","data-v-771746e5"]]);export{pe as default};
