import{s as k}from"./index-DNtGhG9S.js";import{s as y}from"./index-W8aGMMs9.js";import{t as X,c as B,b as r}from"./index.esm-DoKGfgcR.js";import{d as A,r as q,z as l,o as n,u as c,F as w,w as a,e as t,y as u,b as o,p as d,A as _,j as f,t as p}from"./index-DWWVg5mm.js";const x={class:"field mb-4 mt-4"},S={class:"field mb-4"},$={class:"field mb-4"},V={class:"field mb-4"},D=A({__name:"bankAccount",setup(C,{expose:h}){const v=q(null),N=X(B({bsb_number:r().required("BSB Number is required").matches(/^\d{3}-?\d{3}$/,"BSB Number must be 6 digits (XXX-XXX or XXXXXX format)"),account_name:r().required("Account Name is required").min(2,"Account Name must be at least 2 characters"),account_number:r().required("Account Number is required").matches(/^\d{6,10}$/,"Account Number must be 6-10 digits"),nickname:r().min(2,"Nickname must be at least 2 characters")}));return h({validate:()=>{var b;return(b=v.value)==null?void 0:b.validate()}}),(b,F)=>{const i=y,m=k;return n(),l(c(w),{ref_key:"formRef",ref:v,"validation-schema":c(N)},{default:a(()=>[t(c(u),{name:"bsb_number"},{default:a(({field:s,errorMessage:e})=>[o("div",x,[t(i,_(s,{class:[{"p-invalid":e},"w-full"],placeholder:"BSB Number *"}),null,16,["class"]),e?(n(),l(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:a(()=>[f(p(e),1)]),_:2},1024)):d("",!0)])]),_:1}),t(c(u),{name:"account_name"},{default:a(({field:s,errorMessage:e})=>[o("div",S,[t(i,_(s,{class:[{"p-invalid":e},"w-full"],placeholder:"Account Name *"}),null,16,["class"]),e?(n(),l(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:a(()=>[f(p(e),1)]),_:2},1024)):d("",!0)])]),_:1}),t(c(u),{name:"account_number"},{default:a(({field:s,errorMessage:e})=>[o("div",$,[t(i,_(s,{class:[{"p-invalid":e},"w-full"],placeholder:"Account Number *"}),null,16,["class"]),e?(n(),l(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:a(()=>[f(p(e),1)]),_:2},1024)):d("",!0)])]),_:1}),t(c(u),{name:"nickname"},{default:a(({field:s,errorMessage:e})=>[o("div",V,[t(i,_(s,{class:[{"p-invalid":e},"w-full"],placeholder:"Nickname"}),null,16,["class"]),e?(n(),l(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:a(()=>[f(p(e),1)]),_:2},1024)):d("",!0)])]),_:1})]),_:1},8,["validation-schema"])}}});export{D as _};
