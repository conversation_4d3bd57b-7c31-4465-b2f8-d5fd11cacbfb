import{d as $,r as a,c as V,a as E,o as L,e as f,b as s,u as t,p as z,w as i,t as r,j as T,s as G}from"./index-DWWVg5mm.js";import{d as U}from"./dayjs.min-DUDfdLe0.js";import{B as X}from"./BaseDataTable-75nyB6_U.js";import{S as d,_ as H}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{u as J}from"./useDict-BBitGqOA.js";import{u as K}from"./useListRefresh-AzGE_VAq.js";import{u as Q}from"./useRequestList-Bk9BBkCQ.js";import{g as Z}from"./support-Dzr7PNkO.js";import{a as ee}from"./dict-gu74pdeV.js";import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";const oe={class:"customer-page"},le={class:"bg-white rounded-[16px]"},re={class:"underline"},te={class:"flex flex-col gap-2"},ne={class:"flex flex-col gap-2"},ie={class:"flex flex-col gap-2"},pe={key:0,class:"flex justify-center py-6"},me=$({name:"flexiratesMerchantCancelList",__name:"cancellation",setup(ae){const D=a([{field:"customer_property.property_number",header:"Property Number",style:{minWidth:"100px"},template:"property"},{field:"customer_property.street_address",header:"Address",style:{minWidth:"150px"},template:"address"},{field:"customer_user.name",header:"Customer",style:{minWidth:"150px"},template:"customer"},{field:"customer_property.current_remaining_amount",header:"Amount",style:{minWidth:"100px"},template:"amount"},{field:"customer_plan.account_no",header:"Account",style:{minWidth:"150px"},template:"account"},{field:"status",header:"Status",style:{minWidth:"100px"},template:"status"},{field:"remark",header:"Notes and Remarks",style:{minWidth:"200px"},template:"notes"}]),{list:M,loading:A,total:_,refresh:W,search:b,onPageChange:P,failed:R,failureMessage:B,loading:F,setSearchParams:y}=Q({requestFn:Z,immediate:!0});K("flexiratesMerchantRatepayersList",W);const Y=m=>{const{sortField:o,sortOrder:c}=m;y({sort_by:o,sort_order:c===1?"asc":"desc"}),b()},u=a({keyword:"",status:null,"created_at[]":[]}),j=a(),g=a([]),{getLabel:O}=J("property_cancellation_status",m=>{g.value=ee(m,{label:"All",value:null})}),q=V(()=>[{name:"keyword",label:"Search",type:d.TEXT,placeholder:"Property Number",maxlength:50,defaultValue:""},{name:"status",label:"Status",type:d.SELECT,placeholder:"All",options:g.value,defaultValue:null}]),I=V(()=>[{name:"created_at[]",label:"Date range",type:d.DATE_RANGE,placeholder:"Please select date range",defaultValue:[]}]),h=()=>{y(u.value),b()};return(m,o)=>{const c=G;return L(),E("div",oe,[f(H,{modelValue:u.value,"onUpdate:modelValue":o[0]||(o[0]=e=>u.value=e),loading:t(F),"basic-search-fields":q.value,"advanced-search-fields":I.value,onSearch:h},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),s("div",le,[f(X,{ref_key:"customerTable",ref:j,"show-search-bar":!1,value:t(M),columns:D.value,scrollable:!0,"show-multiple-column":!1,loading:t(A),paginator:!1,rows:50,"total-records":t(_),"data-key":"id",failed:t(R),"failure-message":t(B),"row-hover":!0,"striped-rows":!1,style:{"--frozen-column-border-bottom":"-8px"},"is-show-expander":!1,onChangeSearch:h,onPage:o[1]||(o[1]=e=>t(P)(e)),onSort:Y},{property:i(({data:e})=>{var l;return[s("span",re,r((l=e==null?void 0:e.customer_property)==null?void 0:l.property_number),1)]}),address:i(({data:e})=>{var l;return[s("span",null,r((l=e==null?void 0:e.customer_property)==null?void 0:l.street_address),1)]}),customer:i(({data:e})=>{var l,n,p;return[s("div",te,[s("span",null,[o[2]||(o[2]=s("span",{class:"font-semibold"},"Name: ",-1)),s("span",null,r((l=e==null?void 0:e.customer_user)==null?void 0:l.name),1)]),s("span",null,[o[3]||(o[3]=s("span",{class:"font-semibold"},"Email: ",-1)),s("span",null,r((n=e==null?void 0:e.customer_user)==null?void 0:n.email),1)]),s("span",null,[o[4]||(o[4]=s("span",{class:"font-semibold"},"Mobile: ",-1)),s("span",null,r((p=e==null?void 0:e.customer_user)==null?void 0:p.mobile),1)])])]}),amount:i(({data:e})=>{var l;return[o[5]||(o[5]=T(" $")),s("span",null,r((l=e==null?void 0:e.customer_property)==null?void 0:l.current_remaining_amount),1)]}),account:i(({data:e})=>{var l,n,p,x,v,S,k,w,C,N;return[s("div",ne,[s("span",null,[o[6]||(o[6]=s("span",{class:"font-semibold"},"Card No: ",-1)),s("span",null,r((n=(l=e==null?void 0:e.customer_plan)==null?void 0:l.customer_banking)==null?void 0:n.account_no),1)]),s("span",null,[o[7]||(o[7]=s("span",{class:"font-semibold"},"Expiry: ",-1)),s("span",null,r((x=(p=e==null?void 0:e.customer_plan)==null?void 0:p.customer_banking)==null?void 0:x.expiration_month)+"/"+r((S=(v=e==null?void 0:e.customer_plan)==null?void 0:v.customer_banking)==null?void 0:S.expiration_year),1)]),s("span",null,[o[8]||(o[8]=s("span",{class:"font-semibold"},"Name: ",-1)),s("span",null,r((w=(k=e==null?void 0:e.customer_plan)==null?void 0:k.customer_banking)==null?void 0:w.first_name)+" "+r((N=(C=e==null?void 0:e.customer_plan)==null?void 0:C.customer_banking)==null?void 0:N.last_name),1)])])]}),status:i(({data:e})=>[T(r(t(O)(e==null?void 0:e.status)),1)]),notes:i(({data:e})=>{var l,n;return[s("div",ie,[s("span",null,[o[9]||(o[9]=s("span",{class:"font-semibold"},"Stopped Date: ",-1)),s("span",null,r((l=e==null?void 0:e.customer_plan)!=null&&l.stopped_date?t(U)((n=e==null?void 0:e.customer_plan)==null?void 0:n.stopped_date).format("DD MMM YYYY"):""),1)]),s("span",null,[o[10]||(o[10]=s("span",{class:"font-semibold"},"Reason: ",-1)),s("span",null,r(e==null?void 0:e.remark),1)])])]}),_:1},8,["value","columns","loading","total-records","failed","failure-message"]),t(_)>10?(L(),E("div",pe,[f(c,{label:"VIEW MORE"})])):z("",!0)])])}}}),Pe=se(me,[["__scopeId","data-v-8430f701"]]);export{Pe as default};
