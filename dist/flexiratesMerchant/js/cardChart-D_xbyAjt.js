import{u as f,i as y,m as o,a as h,b as m,c as x,d as E,e as S,f as v}from"./installCanvasRenderer-CM5zVs_H.js";import{d as g,r as _,c as b,m as w,f as C,l as L,a as k,o as A,b as B}from"./index-DWWVg5mm.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";const T={class:"card-chart-wrapper"},$=g({__name:"cardChart",props:{chartData:{type:Object,required:!0,default:()=>({xAxis:[],series1:[],series2:[]})},chartType:{type:String,default:"default",validator:r=>["default","blue","green","red"].includes(r)},title:{type:String,default:""}},setup(r,{expose:c}){const t=r;f([h,m,x,E,S,v]);let e=null;const l=_(null),a=b(()=>{const s={default:["#9E9E9E","#FF7043"],blue:["#9E9E9E","#29B6F6"],green:["#9E9E9E","#66BB6A"],red:["#9E9E9E","#EF5350"]};return s[t.chartType]||s.default}),p=()=>{l.value&&(e=y(l.value),window.addEventListener("resize",()=>{e==null||e.resize()}),i())},i=()=>{if(!e)return;const{xAxis:s,series1:n,series2:d}=t.chartData,u={title:{text:t.title,left:"center",show:!!t.title},tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},legend:{data:["Series 1","Series 2"],top:0,left:"center",itemGap:40},grid:{left:"3%",right:"4%",bottom:"0%",top:"20%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:s||[],offset:8,axisLine:{lineStyle:{color:"#E0E0E0"}},axisLabel:{show:!0,interval:0,rotate:60,fontSize:12,align:"right",margin:8,color:"#000"}},yAxis:{type:"value",axisLine:{show:!1},splitLine:{lineStyle:{color:"#E0E0E0",type:"dashed"}}},series:[{name:"Series 1",type:"line",stack:"Total",data:n||[],showSymbol:!1,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:o(a.value[0],.5)},{offset:1,color:o(a.value[0],.1)}]}},itemStyle:{color:a.value[0]},lineStyle:{width:2},emphasis:{focus:"series"},smooth:!0},{name:"Series 2",type:"line",stack:"Total",data:d||[],showSymbol:!1,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:o(a.value[1],.5)},{offset:1,color:o(a.value[1],.1)}]}},itemStyle:{color:a.value[1]},lineStyle:{width:2},emphasis:{focus:"series"},smooth:!0}]};e.setOption(u)};return w(()=>[t.chartData,t.chartType],()=>{i()},{deep:!0}),C(()=>{p()}),L(()=>{e&&(e.dispose(),e=null),window.removeEventListener("resize",()=>{e==null||e.resize()})}),c({chartInstance:e}),(s,n)=>(A(),k("div",T,[B("div",{ref_key:"chartRef",ref:l,class:"chart-container",style:{height:"236px"}},null,512)]))}}),M=z($,[["__scopeId","data-v-41ae461e"]]);export{M as default};
