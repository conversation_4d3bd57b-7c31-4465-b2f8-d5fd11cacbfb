import b from"./ratepayerForm-B2GbB0wm.js";import{t as h,c as v,a as q,b as e}from"./index.esm-DoKGfgcR.js";import{O as g,i as F,r as p,W as V,d as x,a as S,o as R,b as C,e as L,u as a}from"./index-DWWVg5mm.js";import{u as k}from"./useListRefresh-AzGE_VAq.js";import{g as $,c as P}from"./ratepayer-CzZHWazT.js";import"./index-T2UmNbVz.js";import"./index-BEwkorqa.js";import"./index-DNtGhG9S.js";import"./index-W8aGMMs9.js";import"./dayjs.min-DUDfdLe0.js";import"./bankAccount.vue_vue_type_script_setup_true_lang-CWOoX_Ll.js";import"./creditCard-BfPJagTg.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./scheduleForm.vue_vue_type_script_setup_true_lang-D6vmpJOy.js";import"./index-IMPOxVZV.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";import"./index-D2QnTskR.js";import"./decimal-LZMqkRTm.js";function w(o="create"){const c=g(),l=F(),s=p(!1),t=p(!1),m=p({address:"",default_payment_plan:0,dict_payment_plan:[],last_payment_date:"",property_number:"",suburb:"",total_amount_due:""}),{backWithRefresh:u}=k("ratepayersList",()=>{}),d=()=>({first_name:"",last_name:"",mobile_number:"",email_address:"",street_address:"",city:"",state:"",postcode:"",property_number:"",verification_code:"",payment_method:"bank_account",payment_frequency:{payment_plan:0,first_payment_date:"",amount:0},payment_detail:{type:0,bank:{bsb_number:"",account_name:"",account_number:""},card:{card_number:"",expiry_month:"",expiry_year:"",name_on_card:"",cvv:""},payment_method_type:""},schedule:{plan:0,first_payment_date:"",amount:0},is_default_payment_method:!0}),i=V(d()),y=h(v({first_name:e().min(2,"First name must be at least 2 characters").max(50,"First name must be less than 50 characters").required("First name is required"),last_name:e().min(2,"Last name must be at least 2 characters").max(50,"Last name must be less than 50 characters").required("Last name is required"),mobile_number:e().matches(/^\+?[\d\s\-()]+$/,"Please enter a valid mobile number").min(10,"Mobile number must be at least 10 characters").required("Mobile number is required"),email_address:e().email("Please enter a valid email address").required("Email address is required"),street_address:e().min(5,"Street address must be at least 5 characters").max(200,"Street address must be less than 200 characters").required("Street address is required"),city:e().min(2,"City must be at least 2 characters").max(100,"City must be less than 100 characters").required("City is required"),state:e().min(2,"State must be at least 2 characters").max(50,"State must be less than 50 characters").required("State is required"),postcode:e().matches(/^\d{4,6}$/,"Please enter a valid postcode (4-6 digits)").required("Postcode is required"),property_number:e().required("Property number is required"),verification_code:e().required("Verification code is required"),is_default_payment_method:q().optional()}));return{initialValues:i,scheduleData:m,schema:y,loading:s,verifyLoading:t,resetForm:()=>{Object.assign(i,d())},setFormValues:r=>{Object.assign(i,r)},submitForm:async r=>{console.log("data",r),s.value=!0;try{const{code:n}=await P(r);n===0&&(c.add({severity:"success",summary:"Success",detail:o==="create"?"Ratepayer created successfully":"Ratepayer updated successfully",life:3e3}),u())}catch(n){console.error(`Failed to ${o} ratepayer:`,n),c.add({severity:"error",summary:"Error",detail:o==="create"?"Failed to create ratepayer":"Failed to update ratepayer",life:3e3})}finally{s.value=!1}},cancel:()=>{l.back()},handleVerify:async r=>{try{t.value=!0;const{data:n,code:f}=await $(r);f===0&&(m.value=n,t.value=!1)}finally{t.value=!1}}}}const B={class:"ratepayer-create-page"},E={class:"px-6 py-8 bg-white rounded-2xl"},oe=x({name:"flexiratesMerchantRatepayerCreate",__name:"create",setup(o){const{initialValues:c,schema:l,loading:s,verifyLoading:t,submitForm:m,cancel:u,scheduleData:d,handleVerify:i}=w("create");return(y,_)=>(R(),S("div",B,[C("div",E,[L(b,{"schedule-data":a(d),"initial-values":a(c),"verify-loading":a(t),"validation-schema":a(l),loading:a(s),onSubmit:a(m),onCancel:a(u),onVerify:a(i)},null,8,["schedule-data","initial-values","verify-loading","validation-schema","loading","onSubmit","onCancel","onVerify"])])]))}});export{oe as default};
