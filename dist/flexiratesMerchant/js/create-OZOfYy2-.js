import{d as P,k as T,O as j,i as z,r as V,W as D,f as M,a as O,o as u,b as l,t as c,u as o,e as r,w as n,y as f,z as _,p as h,A as R,j as v,s as W,F as Z,bM as G,bN as H}from"./index-DWWVg5mm.js";import{s as J}from"./index-BpKqttS3.js";import{s as K}from"./index-DDWh5zyb.js";import{s as Q}from"./index-CutZZiSb.js";import{s as X}from"./index-DNtGhG9S.js";import{s as Y}from"./index-W8aGMMs9.js";import{t as ee,c as C,f as ae,d as $,b as w}from"./index.esm-DoKGfgcR.js";import{u as se}from"./useListRefresh-AzGE_VAq.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";import"./index-CjzDW1dI.js";const le={class:"merchant-create-page"},te={class:"flex justify-between items-center mb-4"},re={class:"text-3xl"},me={class:"p-4 bg-white rounded-2xl"},ce={class:"field"},ne={for:"user_name",class:"mb-2 block"},ie={class:"field"},de={for:"email",class:"mb-2 block"},ue={class:"field"},pe={for:"password",class:"mb-2 block"},fe={class:"field"},_e={class:"mb-2 block"},he={class:"field mb-4"},ve={for:"roles",class:"mb-2 block"},be={class:"flex justify-end mt-6 gap-2"},we=P({name:"flexiratesMerchantUserCreate",__name:"create",setup(ke){const{t:e}=T(),x=j(),S=z(),U=V(),b=V(!1),{backWithRefresh:q}=se("flexiratesMerchantUserList",()=>{}),m=D({user_name:"",email:"",password:"",to_fa_check:0,roles:[]}),g=V([]),A=async()=>{var a;const p=await G({page:1,page_size:100});g.value=(((a=p.data)==null?void 0:a.data)||[]).map(i=>({name:i.name,id:i.id}))};M(()=>{A()});const L=ee(C({user_name:w().min(3,e("validation.minLength",{min:3})).max(50,e("validation.maxLength",{max:50})).required(),email:w().email(e("validation.emailInvalid")).required(e("validation.emailRequired")),password:w().min(8,e("validation.passwordMinLength")).matches(/[A-Z]/,e("validation.passwordUppercase")).matches(/[a-z]/,e("validation.passwordLowercase")).matches(/\d/,e("validation.passwordNumber")).required(),to_fa_check:$().min(0,"2FA is required").required(),roles:ae().of(C({name:w(),id:$()})).min(1,"Role is required")})),N=async()=>{var p;b.value=!0;try{const a=await((p=U.value)==null?void 0:p.validate());if(!a.valid){b.value=!1;return}const i={...a.values,mfa_check:a.values.to_fa_check,roles:a.values.roles.map(k=>k.id)},{code:d}=await H(i);d===0&&(x.add({severity:"success",summary:e("common.success"),detail:e("merchant.messages.createSuccess","Merchant created successfully"),life:3e3}),q())}catch(a){console.error("Failed to Create a user:",a),x.add({severity:"error",summary:e("common.error"),detail:e("merchant.messages.createFailed","Failed to Create a user"),life:3e3})}finally{b.value=!1}},B=()=>{S.back()};return(p,a)=>{const i=Y,d=X,k=Q,E=K,I=J,F=W;return u(),O("div",le,[l("div",te,[l("h1",re,c(o(e)("menu.userCreate","Create User")),1)]),l("div",me,[r(o(Z),{ref_key:"formRef",ref:U,"validation-schema":o(L),class:"merchant-form flex flex-col gap-4",onSubmit:N},{default:n(()=>[r(o(f),{modelValue:m.user_name,"onUpdate:modelValue":a[0]||(a[0]=s=>m.user_name=s),name:"user_name",class:"form-col"},{default:n(({field:s,errorMessage:t})=>[l("div",ce,[l("label",ne,c(o(e)("merchant.form.name","Username"))+"*",1),r(i,R({id:"user_name"},s,{placeholder:o(e)("merchant.form.name","Username"),class:"w-full","input-props":{autocomplete:"new-password"}}),null,16,["placeholder"]),t?(u(),_(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[v(c(t),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),r(o(f),{modelValue:m.email,"onUpdate:modelValue":a[1]||(a[1]=s=>m.email=s),name:"email",class:"form-col"},{default:n(({field:s,errorMessage:t})=>[l("div",ie,[l("label",de,c(o(e)("merchant.form.email","Email"))+"*",1),r(i,R({id:"email"},s,{placeholder:o(e)("merchant.form.email","Email"),class:"w-full",type:"email","input-props":{autocomplete:"new-password"},autocomplete:"new-password"}),null,16,["placeholder"]),t?(u(),_(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[v(c(t),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),r(o(f),{modelValue:m.password,"onUpdate:modelValue":a[2]||(a[2]=s=>m.password=s),name:"password",class:"form-col"},{default:n(({field:s,errorMessage:t,handleChange:y})=>[l("div",ue,[l("label",pe,c(o(e)("merchant.form.password","Password"))+"*",1),r(k,{id:"password","model-value":s.value,placeholder:o(e)("merchant.form.password","Password"),class:"w-full","toggle-mask":"",root:{autocomplete:"new-password"},"input-props":{autocomplete:"new-password"},"onUpdate:modelValue":y},null,8,["model-value","placeholder","onUpdate:modelValue"]),t?(u(),_(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[v(c(t),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),r(o(f),{modelValue:m.to_fa_check,"onUpdate:modelValue":a[4]||(a[4]=s=>m.to_fa_check=s),name:"to_fa_check",as:"div",class:"form-col"},{default:n(({errorMessage:s})=>[l("div",fe,[l("label",_e,c(o(e)("merchant.form.2fa","2FA Authentication")),1),r(E,{modelValue:m.to_fa_check,"onUpdate:modelValue":a[3]||(a[3]=t=>m.to_fa_check=t),options:[{label:"Enabled",value:1},{label:"Disabled",value:0}],"option-label":"label","option-value":"value",class:"w-full",name:"to_fa_check",placeholder:o(e)("merchant.form.select2FA","Select 2FA Option")},null,8,["modelValue","placeholder"])]),s?(u(),_(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[v(c(s),1)]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),r(o(f),{modelValue:m.roles,"onUpdate:modelValue":a[5]||(a[5]=s=>m.roles=s),name:"roles",class:"form-col"},{default:n(({field:s,errorMessage:t,handleChange:y})=>[l("div",he,[l("label",ve,c(o(e)("merchant.form.roles","Roles")),1),r(I,{"model-value":s.value,options:g.value,"option-label":"name",placeholder:o(e)("merchant.form.selectRoles","Select Roles"),display:"chip",class:"w-full",name:"roles","onUpdate:modelValue":y},null,8,["model-value","options","placeholder","onUpdate:modelValue"]),t?(u(),_(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[v(c(t),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"]),l("div",be,[r(F,{type:"button",label:o(e)("common.cancel"),icon:"pi pi-times",class:"p-button-text",onClick:B},null,8,["label"]),r(F,{type:"submit",label:o(e)("common.save"),icon:"pi pi-check",loading:b.value},null,8,["label","loading"])])]),_:1},8,["validation-schema"])])])}}}),Be=oe(we,[["__scopeId","data-v-405fe505"]]);export{Be as default};
