import{s as R}from"./index-B_CyaZAL.js";import{d as k,B as g,i as v,r,f as x,a as n,o,z as b,e as B,b as D}from"./index-DWWVg5mm.js";import{u as c}from"./useListRefresh-AzGE_VAq.js";import{a as M}from"./ratepayer-CzZHWazT.js";import w from"./ratepayerDetail-Bja6D8eI.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Br9OVcmc.js";import"./index-DNtGhG9S.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-D2QnTskR.js";import"./index-Cui9J1ai.js";import"./index.esm-DoKGfgcR.js";import"./dayjs.min-DUDfdLe0.js";import"./useExport-R4ZaBF9X.js";import"./usePermissions-DgTRxXRz.js";import"./schedules-DUq33TjC.js";import"./format-Cs3Vg3WT.js";import"./activityLogs-Cs6ODkNj.js";import"./BaseDataTable-75nyB6_U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";import"./useRequestList-Bk9BBkCQ.js";import"./ratepayerDetailNotes-C4VgngFy.js";import"./index-pLSAVumt.js";import"./index.vue_vue_type_style_index_0_lang-BQUf2NGU.js";const L={class:"ratepayer-detail-page"},W={key:0,class:"p-6 flex justify-center items-center bg-white rounded-lg shadow-sm"},I=k({name:"flexiratesMerchantRatepayerDetail",__name:"detail",setup(P){const a=g(),s=v(),t=r(!0),i=r(null),l=r(a.query.customerId),{backWithRefresh:d}=c("flexiratesMerchantRatepayersList",()=>{}),{backWithRefresh:u}=c("flexiratesMerchantPropertiesList",()=>{}),p=async()=>{t.value=!0;try{const{data:m,code:e}=await M({id:a.params.id});e===0&&(i.value=m)}finally{t.value=!1}},f=()=>{s.push({name:"flexiratesMerchantRatepayersEdit",params:{id:a.params.id}})},h=()=>{s.back()},y=()=>{d(!1),u(!1),p()};return x(()=>{p()}),(m,e)=>{const _=R;return o(),n("div",L,[t.value?(o(),n("div",W,[B(_,{class:"w-8 h-8"}),e[0]||(e[0]=D("span",{class:"ml-3 text-gray-600"},"Loading ratepayer details...",-1))])):(o(),b(w,{key:1,"customer-id":l.value,data:i.value,loading:t.value,onEdit:f,onBack:h,onUpdate:y},null,8,["customer-id","data","loading"]))])}}}),ht=E(I,[["__scopeId","data-v-d723583e"]]);export{ht as default};
