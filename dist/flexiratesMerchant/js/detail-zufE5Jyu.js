import{F as a}from"./index-Cui9J1ai.js";import"./index.vue_vue_type_style_index_0_lang-BQUf2NGU.js";import{C as f}from"./index-40_tgnh-.js";import{d as b,B as h,O as p,r,f as g,z as y,o as m,w as x,a as k,p as w,b as e,t as l,u as o}from"./index-DWWVg5mm.js";import{u as C}from"./useDict-BBitGqOA.js";import{g as S}from"./settlement-CxaYQjrm.js";import{_ as D}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Br9OVcmc.js";const A={key:0,class:"settlement-content"},F={class:"bg-white p-6 rounded-2xl my-3 text-gray-600"},B={class:"text-2xl font-bold"},I={class:"bg-white px-6 py-2 rounded-2xl mt-8 text-gray-600"},N={class:"info-section"},E={class:"form-row"},L={class:"form-col"},M={class:"field"},R={class:"detail-value"},T={class:"form-col"},V={class:"field"},z={class:"detail-value"},O={class:"form-row"},P={class:"form-col"},j={class:"field"},q={class:"detail-value amount-highlight"},G={class:"form-col"},H={class:"field"},J={class:"detail-value"},K={class:"form-row"},Q={class:"form-col"},U={class:"field"},W={class:"detail-value"},X={class:"form-col"},Y={class:"field"},Z={class:"detail-value"},$={class:"form-row"},ee={class:"form-col"},te={class:"field"},se={class:"detail-value"},le={class:"form-col"},oe={class:"field"},ae={class:"detail-value"},ie={class:"form-row"},de={class:"form-col"},ne={class:"field"},re={class:"detail-value"},ce=b({name:"SettlementDetail",__name:"detail",setup(me){const _=h(),u=p(),s=r(null),d=r(!0),n=r(null),{getLabel:v}=C("settlement_type"),c=async()=>{try{d.value=!0,n.value=null;const i=await S({id:Number(_.params.id)});s.value=i.data}catch(i){n.value=i.message||"Failed to load settlement details",u.add({severity:"error",summary:"Error",detail:"Failed to load settlement details",life:3e3})}finally{d.value=!1}};return g(()=>{c()}),(i,t)=>(m(),y(o(f),{loading:d.value,error:n.value,onRetry:c},{default:x(()=>[s.value?(m(),k("div",A,[e("div",F,[e("div",B," Settlement Details - "+l(s.value.business_id),1)]),e("div",I,[t[10]||(t[10]=e("div",{class:"page-header"},[e("h1",{class:"page-title"}," Settlement Information ")],-1)),e("div",N,[e("div",E,[e("div",L,[e("div",M,[t[0]||(t[0]=e("label",{class:"field-label"},"BID",-1)),e("div",R,l(s.value.business_id||"-"),1)])]),e("div",T,[e("div",V,[t[1]||(t[1]=e("label",{class:"field-label"},"Settlement Currency",-1)),e("div",z,l(s.value.settlement_currency||"-"),1)])])]),e("div",O,[e("div",P,[e("div",j,[t[2]||(t[2]=e("label",{class:"field-label"},"Current Settlement Amount",-1)),e("div",q,l(o(a).formatAmount(s.value.settlement_amount)),1)])]),e("div",G,[e("div",H,[t[3]||(t[3]=e("label",{class:"field-label"},"Current Fee",-1)),e("div",J,l(o(a).formatAmount(s.value.settle_fee)),1)])])]),e("div",K,[e("div",Q,[e("div",U,[t[4]||(t[4]=e("label",{class:"field-label"},"Chargeback Amount",-1)),e("div",W,l(s.value.chargeback_amount?o(a).formatAmount(s.value.chargeback_amount):"-"),1)])]),e("div",X,[e("div",Y,[t[5]||(t[5]=e("label",{class:"field-label"},"Chargeback Fee",-1)),e("div",Z,l(s.value.chargeback_fee?o(a).formatAmount(s.value.chargeback_fee):"-"),1)])])]),e("div",$,[e("div",ee,[e("div",te,[t[6]||(t[6]=e("label",{class:"field-label"},"Settlement Fee",-1)),e("div",se,l(s.value.settlement_fee?o(a).formatAmount(s.value.settlement_fee):"-"),1)])]),e("div",le,[e("div",oe,[t[7]||(t[7]=e("label",{class:"field-label"},"Settlement Method",-1)),e("div",ae,l(o(v)(s.value.settlement_type)),1)])])]),e("div",ie,[e("div",de,[e("div",ne,[t[8]||(t[8]=e("label",{class:"field-label"},"Settlement Date",-1)),e("div",re,l(s.value.settle_at||"-"),1)])]),t[9]||(t[9]=e("div",{class:"form-col"},null,-1))])])])])):w("",!0)]),_:1},8,["loading","error"]))}}),ye=D(ce,[["__scopeId","data-v-f41eb429"]]);export{ye as default};
