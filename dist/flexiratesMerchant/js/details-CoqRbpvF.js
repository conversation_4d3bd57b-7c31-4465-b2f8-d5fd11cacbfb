import{B}from"./BaseDataTable-75nyB6_U.js";import{_ as C}from"./BaseCardType-BCAS9_FJ.js";import{d as M,B as k,r as c,f as Y,a as E,o as F,b as t,t as o,u as a,e as i,s as P,w as l,j as p,q as T}from"./index-DWWVg5mm.js";import{F as x}from"./index-Cui9J1ai.js";import{d as y}from"./dayjs.min-DUDfdLe0.js";import{u as S}from"./useExport-R4ZaBF9X.js";import{g as q,a as A}from"./schedules-DUq33TjC.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";import"./getAssetsFile-D2Insbdr.js";import"./Phone-dOHlUqSP.js";import"./login-bg-D47k_GaL.js";import"./isUpdated-DXQm7BuR.js";const R={class:"schedule-detail"},j={class:"bg-white dark:bg-gray-800 p-6 px-8 rounded-2xl flex items-center"},V={class:"header-title"},$={class:"title line-clamp-1"},O={class:"text-(color:--colors-gray)"},z={class:"flex flex-col md:flex-row gap-2 md:gap-6"},I={class:"bg-white dark:bg-gray-800 rounded-2xl p-6 px-8 mt-4"},K={class:"flex items-center justify-between mb-4"},L={class:"flex flex-col md:flex-row gap-2 md:gap-6"},U={class:"underline"},X=M({__name:"details",setup(G){const r=k(),n=c(!1),{handleExport:g,isExporting:b}=S({exportFn:A,getParams:()=>({id:r.params.id})}),v=c([{field:"due_date",header:"Due Date",style:{width:"14%"},template:"dueDate"},{field:"amount",header:"Amount",template:"amount",style:{width:"14%"}},{field:"",header:"Payment Method",template:"paymentMethod",style:{width:"14%"}},{field:"frequency",header:"Frequency",style:{width:"14%"}},{field:"",header:"Status",template:"status",style:{width:"14%"}},{field:"",header:"Failed Payment",template:"failedPayment",style:{width:"14%"}},{field:"",header:"Edit Count",template:"editCount",style:{width:"14%"}}]),u=c([]),f=async()=>{n.value=!0;try{const{code:m,data:s}=await q(Number(r.params.id));m===0&&(u.value=s)}finally{n.value=!1}};return Y(()=>{f()}),(m,s)=>{var _,h;const d=P,w=C,D=B;return F(),E("div",R,[t("div",j,[t("div",V,[t("div",$,[s[2]||(s[2]=t("span",{class:"text-(color:--colors-blue)"}," Office: ",-1)),t("span",O,o((h=(_=a(r))==null?void 0:_.query)==null?void 0:h.address),1)]),t("div",z,[i(d,{class:"w-35",label:"BACK",severity:"warn",onClick:s[0]||(s[0]=e=>m.$router.back())})])])]),t("div",I,[t("div",K,[s[3]||(s[3]=t("div",{class:"flex items-center gap-2 flex-1"},[t("span",{class:"text-(color:--colors-blue) font-bold text-3xl"}," Schedules ")],-1)),t("div",L,[i(d,{label:"EXPORT",class:"w-35",severity:"warn",loading:a(b),onClick:s[1]||(s[1]=e=>a(g)("csv"))},null,8,["loading"])])]),i(D,{value:u.value,columns:v.value,loading:n.value,"show-multiple-column":!1,paginator:!1,"show-search-bar":!1,"data-key":"id",onRefresh:f},{amount:l(({data:e})=>[p(o(a(x).formatAmount(e==null?void 0:e.amount)),1)]),dueDate:l(({data:e})=>[t("span",U,o(a(y)(e==null?void 0:e.due_date).format("DD MMM YYYY")),1)]),paymentMethod:l(({data:e})=>[i(w,{"card-type":e==null?void 0:e.credit_brand,"is-show-card-number":!0,text:e==null?void 0:e.account_no},null,8,["card-type","text"])]),failTimes:l(({data:e})=>[p(o(a(x).formatAmount(e==null?void 0:e.fail_times)),1)]),status:l(({data:e})=>[t("span",{class:T(["font-medium",{"text-red-500":e.status_desc==="Failed","text-green-500":e.status_desc==="Success","text-orange-500":e.status_desc==="Upcoming"}])},o(e==null?void 0:e.status_desc),3)]),newDate:l(({data:e})=>[p(o(a(y)(e.next_process_date).format("DD MMM YYYY")),1)]),failedPayment:l(({data:e})=>[t("span",null,o(e.failed_times)+" of "+o(e.failable_times),1)]),editCount:l(({data:e})=>[t("span",null,o(e.editable_count)+" of "+o(e.editable_count),1)]),"empty-action":l(()=>[i(d,{label:"Refresh"})]),_:1},8,["value","columns","loading"])])])}}}),ve=N(X,[["__scopeId","data-v-0d402179"]]);export{ve as default};
