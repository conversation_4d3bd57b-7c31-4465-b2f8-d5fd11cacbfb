import{_ as ve}from"./BaseTag-DKUhplpL.js";import{F}from"./index-Cui9J1ai.js";import"./index.vue_vue_type_style_index_0_lang-BQUf2NGU.js";import{C as ye}from"./index-40_tgnh-.js";import{t as be,c as xe,b as g}from"./index.esm-DoKGfgcR.js";import{d as de}from"./dayjs.min-DUDfdLe0.js";import{d as ge,B as he,O as we,r as D,W as ke,c as Ee,m as Ve,f as Ae,z as v,o as c,w as m,a as q,p as f,b as e,t as d,j as y,E as De,u as s,s as j,F as Ne,e as u,y as h,A as w}from"./index-DWWVg5mm.js";import{s as Fe,a as re}from"./index-yGDtrfNA.js";import{s as k}from"./index-W8aGMMs9.js";import{s as E}from"./index-DNtGhG9S.js";import{u as qe}from"./useDict-BBitGqOA.js";import{u as Re,P as B}from"./usePermissions-DgTRxXRz.js";import{T as ne}from"./transaction-CJv5Yd1H.js";import{a as Se}from"./properties-DEWCA0Ig.js";import{u as Te}from"./ratepayer-CzZHWazT.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Br9OVcmc.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";const Le={key:0,class:"property-content"},Ye={class:"bg-white p-6 rounded-2xl my-3 text-gray-600"},je={class:"text-2xl font-bold"},Be={key:0},Ie={class:"bg-white p-8 py-6 rounded-2xl mt-8 text-gray-600"},Ue={class:"bg-white rounded-2xl"},$e={class:"ratepayer-header flex justify-between items-center mb-6"},He={class:"flex gap-2"},ze={class:"flex flex-col text-(--colors-gray) mr-4"},Oe={class:"text-sm"},We={class:"font-medium text-(--colors-primary)"},Ge={class:"font-medium text-(--colors-primary)"},Je={class:"text-sm"},Ke={class:"font-medium text-(--colors-primary)"},Qe={class:"form-row"},Xe={class:"field"},Ze={class:"field-input-container"},Me={class:"field"},Pe={class:"field-input-container"},et={class:"form-row"},tt={class:"field"},st={class:"field-input-container"},at={class:"field"},lt={class:"field-input-container"},ot={class:"form-row"},it={class:"field"},dt={class:"flex flex-col gap-2 w-3/5"},rt={class:"field-input-container"},nt={class:"field-input-container"},mt={class:"flex gap-4"},ut={class:"field-input-container flex-1"},ct={class:"field-input-container flex-1"},_t={key:1},pt={class:"form-row"},ft={class:"field"},vt={class:"detail-value"},yt={class:"field"},bt={class:"detail-value"},xt={class:"form-row"},gt={class:"field"},ht={class:"detail-value"},wt={class:"field"},kt={class:"detail-value"},Et={class:"form-row"},Vt={class:"field"},At={class:"flex flex-col gap-2 w-3/5"},Dt={class:"detail-value"},Nt={class:"detail-value"},Ft={class:"flex gap-4"},qt={class:"detail-value"},Rt={class:"detail-value"},St={class:"mt-8 bg-white py-6 rounded-2xl"},Tt={class:"flex flex-col lg:flex-row lg:gap-8 text-(--colors-gray) mt-8"},Ct={class:"flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1"},Lt={class:"!mb-0 font-bold text-4xl"},Yt={class:"flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1"},jt={class:"!mb-0 font-bold text-4xl"},Bt={class:"flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1"},It={class:"!mb-0 font-bold text-4xl"},Ut={class:"flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1"},$t={class:"!mb-0 font-bold text-4xl"},Ht={class:"info-section mt-4"},zt={class:"payment-history"},Ot=ge({name:"flexiratesMerchantPropertiesDetail",__name:"details",setup(Wt){const me=he(),N=we(),{hasPermission:R}=Re(),n=D(null),S=D(!0),T=D(null),b=D(!1),C=D(!1),a=ke({first_name:"",last_name:"",email:"",mobile:"",street_address:"",city:"",state:"",postcode:""}),ue=be(xe({first_name:g().required("First Name is required"),last_name:g().required("Last Name is required"),email:g().email("Invalid email format").required("Email is required"),mobile:g().required("Mobile number is required"),street_address:g().required("Street address is required"),city:g().required("City is required"),state:g().required("State is required"),postcode:g().required("Postcode is required")})),x=Ee(()=>{var i,_,V,A;if(b.value)return a;const l=(_=(i=n.value)==null?void 0:i.customer)==null?void 0:_.user_profile,t=(A=(V=n.value)==null?void 0:V.customer)==null?void 0:A.customer_user;return{first_name:(l==null?void 0:l.first_name)||"",last_name:(l==null?void 0:l.last_name)||"",email:(t==null?void 0:t.email)||"",mobile:(t==null?void 0:t.mobile)||"",street_address:(l==null?void 0:l.address_line_1)||"",city:(l==null?void 0:l.city)||"",state:(l==null?void 0:l.state)||"",postcode:(l==null?void 0:l.postcode)||""}}),L=async()=>{try{S.value=!0,T.value=null;const l=await Se({id:me.params.id});n.value=l.data}catch(l){T.value=l.message||"Failed to load property details",N.add({severity:"error",summary:"Error",detail:"Failed to load property details",life:3e3})}finally{S.value=!1}},Y=()=>{var l,t;if(n.value){const i=(l=n.value.customer)==null?void 0:l.user_profile,_=(t=n.value.customer)==null?void 0:t.customer_user;a.first_name=(i==null?void 0:i.first_name)||"",a.last_name=(i==null?void 0:i.last_name)||"",a.email=(_==null?void 0:_.email)||"",a.mobile=(_==null?void 0:_.mobile)||"",a.street_address=(i==null?void 0:i.address_line_1)||"",a.city=(i==null?void 0:i.city)||"",a.state=(i==null?void 0:i.state)||"",a.postcode=(i==null?void 0:i.postcode)||""}},ce=()=>{Y(),b.value=!0},_e=()=>{b.value=!1,Y()},pe=async()=>{var l,t,i;if(!((t=(l=n.value)==null?void 0:l.customer)!=null&&t.customer_id)){N.add({severity:"error",summary:"Error",detail:"Unable to get customer ID",life:3e3});return}C.value=!0;try{const _={first_name:a.first_name,last_name:a.last_name,email:a.email,mobile:a.mobile,street_address:a.street_address,city:a.city,state:a.state,postcode:a.postcode},{code:V}=await Te((i=n.value.customer)==null?void 0:i.customer_id,_);V===0&&(N.add({severity:"success",summary:"Success",detail:"Ratepayer information updated successfully",life:3e3}),b.value=!1,await L())}catch(_){console.error("Failed to update ratepayer information:",_),N.add({severity:"error",summary:"Error",detail:"Failed to update ratepayer information",life:3e3})}finally{C.value=!1}},{getLabel:fe}=qe("trans_status");return Ve(()=>n.value,l=>{l&&Y()},{immediate:!0}),Ae(()=>{L()}),(l,t)=>{const i=ve;return c(),v(s(ye),{loading:S.value,error:T.value,onRetry:L},{default:m(()=>{var _,V,A,I,U,$,H,z,O,W,G,J,K,Q,X,Z,M,P,ee,te,se,ae,le,oe,ie;return[n.value?(c(),q("div",Le,[e("div",Ye,[e("div",je,[(V=(_=n.value)==null?void 0:_.customer_property)!=null&&V.street_address?(c(),q("span",Be,d((I=(A=n.value)==null?void 0:A.customer_property)==null?void 0:I.street_address),1)):f("",!0)])]),e("div",Ie,[e("div",Ue,[e("div",$e,[t[11]||(t[11]=e("h1",{class:"text-2xl font-bold"}," Ratepayer Details ",-1)),e("div",He,[e("div",ze,[e("div",Oe,[t[9]||(t[9]=y(" Logged in ")),e("span",We,d((H=($=(U=n.value)==null?void 0:U.customer)==null?void 0:$.customer_user)==null?void 0:H.login_times)+" times",1),(W=(O=(z=n.value)==null?void 0:z.customer)==null?void 0:O.customer_user)!=null&&W.last_login_time?(c(),q(De,{key:0},[t[8]||(t[8]=y(" | Last logged in: ")),e("span",Ge,d(s(de)((K=(J=(G=n.value)==null?void 0:G.customer)==null?void 0:J.customer_user)==null?void 0:K.last_login_time).format("DD MMM YYYY HH:mm:ss")),1)],64)):f("",!0)]),e("div",Je,[t[10]||(t[10]=y(" Registered date: ")),e("span",Ke,d(s(de)((Z=(X=(Q=n.value)==null?void 0:Q.customer)==null?void 0:X.customer_user)==null?void 0:Z.created_at).format("DD MMM YYYY HH:mm:ss")),1)])]),!b.value&&s(R)(s(B).RATE_PAYER_UPDATE)?(c(),v(s(j),{key:0,type:"button",label:"EDIT DETAILS",class:"!px-6",onClick:ce})):f("",!0),b.value&&s(R)(s(B).RATE_PAYER_UPDATE)?(c(),v(s(j),{key:1,type:"button",label:"SAVE",severity:"success",loading:C.value,onClick:pe},null,8,["loading"])):f("",!0),b.value&&s(R)(s(B).RATE_PAYER_UPDATE)?(c(),v(s(j),{key:2,type:"button",label:"CANCEL",severity:"secondary",outlined:"",onClick:_e})):f("",!0)])]),b.value?(c(),v(s(Ne),{key:0,"initial-values":a,"validation-schema":s(ue),class:"edit-form"},{default:m(()=>[e("div",Qe,[e("div",Xe,[t[12]||(t[12]=e("label",{class:"mb-2 block font-medium text-gray-700"},"First Name",-1)),u(s(h),{name:"first_name"},{default:m(({field:r,errorMessage:o})=>[e("div",Ze,[u(s(k),w(r,{modelValue:a.first_name,"onUpdate:modelValue":t[0]||(t[0]=p=>a.first_name=p),placeholder:"First Name",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1})]),e("div",Me,[t[13]||(t[13]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Last Name",-1)),u(s(h),{name:"last_name"},{default:m(({field:r,errorMessage:o})=>[e("div",Pe,[u(s(k),w(r,{modelValue:a.last_name,"onUpdate:modelValue":t[1]||(t[1]=p=>a.last_name=p),placeholder:"Last Name",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1})])]),e("div",et,[e("div",tt,[t[14]||(t[14]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Email Address",-1)),u(s(h),{name:"email"},{default:m(({field:r,errorMessage:o})=>[e("div",st,[u(s(k),w(r,{modelValue:a.email,"onUpdate:modelValue":t[2]||(t[2]=p=>a.email=p),placeholder:"Email Address",type:"email",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1})]),e("div",at,[t[15]||(t[15]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Mobile Number",-1)),u(s(h),{name:"mobile"},{default:m(({field:r,errorMessage:o})=>[e("div",lt,[u(s(k),w(r,{modelValue:a.mobile,"onUpdate:modelValue":t[3]||(t[3]=p=>a.mobile=p),placeholder:"Mobile Number",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1})])]),e("div",ot,[e("div",it,[t[16]||(t[16]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Mailing Address",-1)),e("div",dt,[u(s(h),{name:"street_address"},{default:m(({field:r,errorMessage:o})=>[e("div",rt,[u(s(k),w(r,{modelValue:a.street_address,"onUpdate:modelValue":t[4]||(t[4]=p=>a.street_address=p),placeholder:"Street Address",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1}),u(s(h),{name:"city"},{default:m(({field:r,errorMessage:o})=>[e("div",nt,[u(s(k),w(r,{modelValue:a.city,"onUpdate:modelValue":t[5]||(t[5]=p=>a.city=p),placeholder:"City",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1}),e("div",mt,[u(s(h),{name:"state"},{default:m(({field:r,errorMessage:o})=>[e("div",ut,[u(s(k),w(r,{modelValue:a.state,"onUpdate:modelValue":t[6]||(t[6]=p=>a.state=p),placeholder:"State",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1}),u(s(h),{name:"postcode"},{default:m(({field:r,errorMessage:o})=>[e("div",ct,[u(s(k),w(r,{modelValue:a.postcode,"onUpdate:modelValue":t[7]||(t[7]=p=>a.postcode=p),placeholder:"Postcode",class:["w-full",{"p-invalid":o}]}),null,16,["modelValue","class"]),o?(c(),v(s(E),{key:0,class:"mt-1",severity:"error",variant:"simple"},{default:m(()=>[y(d(o),1)]),_:2},1024)):f("",!0)])]),_:1})])])])])]),_:1},8,["initial-values","validation-schema"])):(c(),q("div",_t,[e("div",pt,[e("div",ft,[t[17]||(t[17]=e("label",{class:"mb-2 block font-medium text-gray-700"},"First Name",-1)),e("div",vt,d(x.value.first_name||"-"),1)]),e("div",yt,[t[18]||(t[18]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Last Name",-1)),e("div",bt,d(x.value.last_name||"-"),1)])]),e("div",xt,[e("div",gt,[t[19]||(t[19]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Email Address",-1)),e("div",ht,d(x.value.email||"-"),1)]),e("div",wt,[t[20]||(t[20]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Mobile Number",-1)),e("div",kt,d(x.value.mobile||"-"),1)])]),e("div",Et,[e("div",Vt,[t[21]||(t[21]=e("label",{class:"mb-2 block font-medium text-gray-700"},"Mailing Address",-1)),e("div",At,[e("div",Dt,d(x.value.street_address||"-"),1),e("div",Nt,d(x.value.city||"-"),1),e("div",Ft,[e("div",qt,d(x.value.state||"-"),1),e("div",Rt,d(x.value.postcode||"-"),1)])])])])]))]),e("div",St,[t[25]||(t[25]=e("h3",{class:"sub-title"}," Rates Information ",-1)),e("div",Tt,[e("div",Ct,[e("div",null," Full Rate Amount for "+d((M=n.value)==null?void 0:M.fiscal_year),1),e("div",Lt,d(s(F).formatAmount((ee=(P=n.value)==null?void 0:P.statistics)==null?void 0:ee.full_rate_amount)),1)]),e("div",Yt,[t[22]||(t[22]=e("div",null," Amount Paid to Date ",-1)),e("div",jt,d(s(F).formatAmount((se=(te=n.value)==null?void 0:te.statistics)==null?void 0:se.paid_amount)),1)]),e("div",Bt,[t[23]||(t[23]=e("div",null," Remaining Balance ",-1)),e("div",It,d(s(F).formatAmount((le=(ae=n.value)==null?void 0:ae.statistics)==null?void 0:le.remaining_amount)),1)]),e("div",Ut,[t[24]||(t[24]=e("div",null," Number of Remaining Scheduled Payments ",-1)),e("div",$t,d((ie=(oe=n.value)==null?void 0:oe.statistics)==null?void 0:ie.remaining_schedule_number),1)])])]),e("div",Ht,[t[27]||(t[27]=e("h3",{class:"section-title"}," Payment History ",-1)),e("div",zt,[u(s(Fe),{value:n.value.transaction},{empty:m(()=>t[26]||(t[26]=[e("div",{class:"flex justify-center items-center h-10"},[e("p",null,"No payment history found")],-1)])),default:m(()=>[u(s(re),{field:"payment_amount",header:"Amount"},{body:m(({data:r})=>[y(d(s(F).formatAmount(r==null?void 0:r.payment_amount)),1)]),_:1}),u(s(re),{field:"status",header:"Status"},{body:m(({data:r})=>[u(i,{type:r.status===s(ne).SUCCEEDED?"paid":r.status===s(ne).FAILED?"failed":"upcoming",text:s(fe)(r.status),class:"w-30"},null,8,["type","text"])]),_:1})]),_:1},8,["value"])])])])])):f("",!0)]}),_:1},8,["loading","error"])}}}),bs=Ce(Ot,[["__scopeId","data-v-614535c1"]]);export{bs as default};
