import{I as A,J as C,a as u,o as c,z as D,p as b,M as N,A as f,as as U,b as t,t as l,aM as z,a3 as j,d as E,B as I,i as V,r as g,f as R,e as i,w as F,j as q,s as G,u as k,bP as J}from"./index-DWWVg5mm.js";import{s as K}from"./index-DNtGhG9S.js";import{s as O}from"./index-B_CyaZAL.js";import{f as w}from"./date-BWkRbs8K.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Br9OVcmc.js";import"./dayjs.min-DUDfdLe0.js";var L=A`
    .p-tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: dt('tag.primary.background');
        color: dt('tag.primary.color');
        font-size: dt('tag.font.size');
        font-weight: dt('tag.font.weight');
        padding: dt('tag.padding');
        border-radius: dt('tag.border.radius');
        gap: dt('tag.gap');
    }

    .p-tag-icon {
        font-size: dt('tag.icon.size');
        width: dt('tag.icon.size');
        height: dt('tag.icon.size');
    }

    .p-tag-rounded {
        border-radius: dt('tag.rounded.border.radius');
    }

    .p-tag-success {
        background: dt('tag.success.background');
        color: dt('tag.success.color');
    }

    .p-tag-info {
        background: dt('tag.info.background');
        color: dt('tag.info.color');
    }

    .p-tag-warn {
        background: dt('tag.warn.background');
        color: dt('tag.warn.color');
    }

    .p-tag-danger {
        background: dt('tag.danger.background');
        color: dt('tag.danger.color');
    }

    .p-tag-secondary {
        background: dt('tag.secondary.background');
        color: dt('tag.secondary.color');
    }

    .p-tag-contrast {
        background: dt('tag.contrast.background');
        color: dt('tag.contrast.color');
    }
`,Q={root:function(a){var o=a.props;return["p-tag p-component",{"p-tag-info":o.severity==="info","p-tag-success":o.severity==="success","p-tag-warn":o.severity==="warn","p-tag-danger":o.severity==="danger","p-tag-secondary":o.severity==="secondary","p-tag-contrast":o.severity==="contrast","p-tag-rounded":o.rounded}]},icon:"p-tag-icon",label:"p-tag-label"},W=C.extend({name:"tag",style:L,classes:Q}),X={name:"BaseTag",extends:z,props:{value:null,severity:null,rounded:Boolean,icon:String},style:W,provide:function(){return{$pcTag:this,$parentInstance:this}}};function v(e){"@babel/helpers - typeof";return v=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},v(e)}function Y(e,a,o){return(a=Z(a))in e?Object.defineProperty(e,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[a]=o,e}function Z(e){var a=ee(e,"string");return v(a)=="symbol"?a:a+""}function ee(e,a){if(v(e)!="object"||!e)return e;var o=e[Symbol.toPrimitive];if(o!==void 0){var n=o.call(e,a);if(v(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(e)}var S={name:"Tag",extends:X,inheritAttrs:!1,computed:{dataP:function(){return j(Y({rounded:this.rounded},this.severity,this.severity))}}},te=["data-p"];function ae(e,a,o,n,p,d){return c(),u("span",f({class:e.cx("root"),"data-p":d.dataP},e.ptmi("root")),[e.$slots.icon?(c(),D(U(e.$slots.icon),f({key:0,class:e.cx("icon")},e.ptm("icon")),null,16,["class"])):e.icon?(c(),u("span",f({key:1,class:[e.cx("icon"),e.icon]},e.ptm("icon")),null,16)):b("",!0),e.value!=null||e.$slots.default?N(e.$slots,"default",{key:2},function(){return[t("span",f({class:e.cx("label")},e.ptm("label")),l(e.value),17)]}):b("",!0)],16,te)}S.render=ae;const se={class:"merchant-details"},oe={key:0,class:"flex justify-content-center align-items-center min-h-screen"},ne={key:1,class:"flex flex-column align-items-center gap-3 p-4"},re={key:2,class:"p-4"},ie={class:"flex align-items-center mb-4"},le={class:"text-xl font-medium m-0"},ce={class:"card"},de={class:"section"},ue={class:"info-list"},pe={class:"info-item"},ve={class:"info-value"},fe={class:"info-item"},ge={class:"info-value"},me={class:"info-item"},ye={class:"info-value"},be={class:"info-item"},_e={class:"info-value"},he={class:"info-item"},ke={class:"info-value"},we={class:"info-item"},Se={class:"info-value"},$e=E({name:"flexiratesMerchantUserDetails",__name:"details",setup(e){const a=I(),o=V(),n=g(null),p=g(!0),d=g(!1),m=g(""),_=async()=>{const r=a.params.id;if(!r){d.value=!0,m.value="Merchant ID is required",p.value=!1;return}p.value=!0,d.value=!1;try{const s=await J(r);n.value=s.data}catch(s){d.value=!0,m.value=s.message||"Failed to load merchant details"}finally{p.value=!1}},$=r=>r===void 0?"Unknown":r===1?"Active":"Inactive",P=r=>r===void 0?"Unknown":{1:"Admin",2:"User",3:"Guest"}[String(r)]||"Unknown",T=()=>{o.back()},B=()=>{o.push({name:"flexiratesMerchantUserEdit",params:{id:a.params.id}})};return R(()=>{_()}),(r,s)=>{const M=O,x=K,y=G,h=S;return c(),u("div",se,[p.value?(c(),u("div",oe,[i(M,{"stroke-width":"3"})])):d.value?(c(),u("div",ne,[i(x,{severity:"error",closable:!1,class:"w-full md:w-6"},{default:F(()=>[q(l(m.value),1)]),_:1}),i(y,{label:"Retry",icon:"pi pi-refresh",onClick:_})])):n.value?(c(),u("div",re,[t("div",ie,[i(y,{icon:"pi pi-arrow-left",text:"",class:"mr-3",onClick:T}),t("h1",le,l(n.value.name),1),i(y,{icon:"pi pi-pencil",text:"",class:"ml-auto",onClick:B})]),t("div",ce,[t("div",de,[s[6]||(s[6]=t("h2",{class:"section-title"}," Basic Information ",-1)),t("div",ue,[t("div",pe,[s[0]||(s[0]=t("div",{class:"info-label"}," Status ",-1)),t("div",ve,[i(h,{value:$(n.value.status),severity:n.value.status===1?"success":"danger"},null,8,["value","severity"])])]),t("div",fe,[s[1]||(s[1]=t("div",{class:"info-label"}," Type ",-1)),t("div",ge,l(P(n.value.type)),1)]),t("div",me,[s[2]||(s[2]=t("div",{class:"info-label"}," Email ",-1)),t("div",ye,l(n.value.email||"N/A"),1)]),t("div",be,[s[3]||(s[3]=t("div",{class:"info-label"}," 2FA Status ",-1)),t("div",_e,[i(h,{value:n.value.mfa_check===1?"Enabled":"Disabled",severity:n.value.mfa_check===1?"success":"warning"},null,8,["value","severity"])])]),t("div",he,[s[4]||(s[4]=t("div",{class:"info-label"}," Created ",-1)),t("div",ke,l(n.value.created_at?k(w)(n.value.created_at):"N/A"),1)]),t("div",we,[s[5]||(s[5]=t("div",{class:"info-label"}," Updated ",-1)),t("div",Se,l(n.value.updated_at?k(w)(n.value.updated_at):"N/A"),1)])])])])])):b("",!0)])}}}),De=H($e,[["__scopeId","data-v-cb2e64a2"]]);export{De as default};
