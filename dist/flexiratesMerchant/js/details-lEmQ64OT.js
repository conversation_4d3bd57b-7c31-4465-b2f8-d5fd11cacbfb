import{_ as wt}from"./BaseCardType-BCAS9_FJ.js";import{s as xt}from"./index-DimVHvgA.js";import{F as ut,f as w}from"./index-Cui9J1ai.js";import{d as kt}from"./dayjs.min-DUDfdLe0.js";import{s as l}from"./index-_nys9rwo.js";import{d as Dt,B as Et,r as x,f as Ft,a as m,o,b as t,e as a,u as s,t as d,q as v,p as _,E as k,G as D,n as ht,j as Bt,z as vt}from"./index-DWWVg5mm.js";import{u as Lt}from"./useDict-BBitGqOA.js";import{T as pt}from"./transaction-CJv5Yd1H.js";import{a as $t}from"./transactions-Dqpj7ebB.js";import{_ as Ct}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./getAssetsFile-D2Insbdr.js";import"./Phone-dOHlUqSP.js";import"./login-bg-D47k_GaL.js";import"./isUpdated-DXQm7BuR.js";const Nt={class:"flex flex-col gap-y-6 lg:flex-row lg:gap-x-6 mt-4"},Tt={class:"transaction-content"},jt={key:0},At={class:"flex flex-col gap-y-4"},It={class:"mt-10 mb-16"},Pt={class:"mb-4"},St={class:"mt-16"},zt={key:1},Mt={class:"headLine flex justify-between items-center"},Vt={class:"flex flex-col gap-y-4"},Yt={class:"flex items-center"},Rt={class:"headLine-amount"},Ht={class:"headLine-currency"},qt={class:"flex items-center gap-x-2"},Gt={class:"headLine-content"},Ot={class:"transactions-detail-left col-span-2 mt-10"},Ut={class:"time-line mb-16"},Jt={class:"notes-timeline mt-6"},Kt={key:0,class:"timeline-dot"},Qt={key:1,class:"timeline-line"},Wt={class:"note-content flex-1 pb-6"},Xt={class:"note-header mb-2"},Zt={class:"text-sm text-gray-500 mt-2"},te={class:"note-body"},ee={class:"text-gray-600 leading-relaxed"},se={class:"payment-breakdown"},ie={class:"w-full border-t border-gray-200"},ae=["onClick"],le={key:0,class:"w-full bg-gray-50 px-2 py-3 border-b border-gray-200"},ne={class:"text-sm text-gray-600"},de={class:"text-sm"},oe={key:0,class:"payment-method"},me={class:"mt-16"},ce={class:"w-full flex flex-wrap"},re={class:"payment-detail lg:w-1/2 h-10 md:w-full"},_e={class:"content"},ue={class:"payment-detail lg:w-1/2 h-10 md:w-full"},he={class:"content"},ve={class:"payment-detail lg:w-1/2 h-10 md:w-full"},pe={class:"content"},ye={class:"payment-detail lg:w-1/2 h-10 md:w-full"},fe={class:"content"},ge={class:"payment-detail lg:w-1/2 h-10 md:w-full"},be={class:"content text-[#9400ff]"},we={class:"payment-detail lg:w-1/2 h-10 md:w-full"},xe={key:0,class:"content"},ke={key:1,class:"content"},De={class:"transactions-detail"},Ee={key:0},Fe={key:1},Be={class:"transaction-details"},Le={class:"transaction-details"},$e={class:"details-edit"},Ce={class:"details-edit-content"},Ne={class:"details-edit"},Te={class:"details-edit-content flex items-center"},je={class:"details-edit"},Ae={class:"details-edit-content"},Ie={key:0,class:"customer-info"},Pe={class:"details-edit"},Se={class:"details-edit-content"},ze={class:"details-edit"},Me={class:"details-edit-content"},Ve={class:"details-edit"},Ye={class:"details-edit-content"},Re=Dt({__name:"details",setup(He){const yt=Et(),i=x({status:0,timelines:[]}),p=x(!0),{getLabel:ft}=Lt("trans_status"),u=x([{id:1,field:"Payment Amount",isFieldBold:!1,amount:""},{id:2,field:"Fees",isFieldBold:!1,amount:"",isExpanded:!1,details:[]},{id:3,field:"Net amount",isFieldBold:!0,amount:""}]),gt=c=>{c.id===2&&c.isExpanded!==void 0&&(c.isExpanded=!c.isExpanded)},bt=()=>{var c;p.value=!0,$t(Number((c=yt.params)==null?void 0:c.id)).then(e=>{u.value[0].amount=`${w(e.data.payment_amount,e.data.payment_currency)} ${e.data.payment_currency}`,u.value[1].amount=`${w(e.data.fee_amount||0,e.data.payment_currency)} ${e.data.payment_currency}`,u.value[1].details=e.data.settlement_details,u.value[2].amount=`${w(e.data.payment_amount,e.data.payment_currency)} ${e.data.net_currency}`,i.value=e.data,p.value=!1}).catch(()=>{p.value=!1})};return Ft(()=>{Promise.all([bt()])}),(c,e)=>{var F,B,L,$,C,N,T,j,A,I,P,S,z,M,V,Y,R,H,q,G,O,U,J,K,Q,W,X,Z,tt,et,st,it,at,lt,nt,dt,ot,mt,ct;const b=xt,E=wt;return o(),m("div",Nt,[t("div",Tt,[p.value?(o(),m("div",jt,[t("div",At,[a(s(l),{width:"60%",height:"3rem"}),a(s(l),{width:"40%",height:"1.5rem"})]),t("div",It,[a(s(l),{width:"20%",height:"2rem",class:"mb-4"}),a(s(l),{width:"100%",height:"1rem",class:"mb-2"}),a(s(l),{width:"100%",height:"1rem",class:"mb-2"}),a(s(l),{width:"80%",height:"1rem"})]),t("div",Pt,[a(s(l),{width:"30%",height:"2rem",class:"mb-4"}),a(s(l),{width:"100%",height:"3.5rem",class:"mb-2"}),a(s(l),{width:"100%",height:"3.5rem",class:"mb-2"}),a(s(l),{width:"100%",height:"3.5rem"})]),t("div",St,[a(s(l),{width:"30%",height:"2rem",class:"mb-4"}),a(s(l),{width:"100%",height:"1.5rem",class:"mb-2"}),a(s(l),{width:"100%",height:"1.5rem",class:"mb-2"}),a(s(l),{width:"100%",height:"1.5rem",class:"mb-2"}),a(s(l),{width:"100%",height:"1.5rem",class:"mb-2"}),a(s(l),{width:"100%",height:"1.5rem",class:"mb-2"})])])):(o(),m("div",zt,[t("div",Mt,[t("div",Vt,[t("div",Yt,[t("span",Rt,d(s(ut).formatAmount((F=i.value)==null?void 0:F.payment_amount,(B=i.value)==null?void 0:B.payment_currency)),1),t("span",Ht,d((L=i.value)==null?void 0:L.payment_currency),1),t("div",{class:v(["headLine-tag",i.value.status===s(pt).SUCCEEDED?"success":i.value.status===s(pt).FAILED?"failed":"default"])},d(s(ft)(($=i.value)==null?void 0:$.status)),3)]),t("div",qt,[e[0]||(e[0]=t("span",{class:"headLine-title"},"Charged to ",-1)),t("span",Gt,d((N=(C=i.value)==null?void 0:C.customer)==null?void 0:N.name),1)])])]),t("div",Ot,[t("div",Ut,[e[1]||(e[1]=t("div",{class:"flex justify-between items-center"},[t("div",{class:"time-line-title"}," Timeline ")],-1)),t("div",Jt,[(o(!0),m(k,null,D((T=i.value)==null?void 0:T.timelines,(n,h)=>{var y,f,r,g,rt,_t;return o(),m("div",{key:n==null?void 0:n.id,class:v(["note-item flex gap-4",{first:h===0,last:h===(((f=(y=i.value)==null?void 0:y.timelines)==null?void 0:f.length)||1)-1}])},[(((g=(r=i.value)==null?void 0:r.timelines)==null?void 0:g.length)||1)>1?(o(),m("div",Kt)):_("",!0),(((_t=(rt=i.value)==null?void 0:rt.timelines)==null?void 0:_t.length)||1)>1?(o(),m("div",Qt)):_("",!0),t("div",Wt,[t("div",Xt,[t("h4",{class:"text-xl font-semibold text-gray-900",style:ht({color:h===0&&n.status===3?"#4ade80":h===0&&n.status===2?"#ff0000":"#545454"})},d(n.title),5),t("p",Zt,d(s(kt)(n.created_at).format("DD MMM YYYY, HH:mm")),1)]),t("div",te,[t("p",ee,d(n.remark),1)])])],2)}),128))])]),t("div",se,[e[2]||(e[2]=t("div",null,[t("div",{class:"payment-breakdown-title mb-6"}," Ratepayer ")],-1)),t("div",ie,[(o(!0),m(k,null,D(u.value,(n,h)=>{var y,f;return o(),m("div",{key:n.id,class:"w-full flex flex-wrap"},[t("div",{class:v(["payment-detail py-5 px-2 md:w-full border-gray-200 flex items-center justify-between hover:bg-gray-100",{"border-b":h!==u.value.length-1||n.id===2&&n.isExpanded}]),style:ht(n.id===2?"cursor: pointer":""),onClick:r=>n.id===2?gt(n):null},[t("div",{class:v(["payment-label w-60",{"font-semibold":n.isFieldBold}])},[n.id===2&&((y=n.details)!=null&&y.length)?(o(),m("i",{key:0,class:v(["pi mr-2",n.isExpanded?"pi-chevron-down":"pi-chevron-right"]),style:{"margin-left":"8px","font-size":"12px"}},null,2)):_("",!0),Bt(" "+d(n.field),1)],2),t("div",{class:v(["payment-content",{"font-semibold":n.isFieldBold}])},d(n.amount),3)],14,ae),n.id===2&&n.isExpanded&&((f=n.details)!=null&&f.length)?(o(),m("div",le,[(o(!0),m(k,null,D(n.details,(r,g)=>(o(),m("div",{key:g,class:"flex justify-between items-center py-2 pl-8"},[t("div",ne,d(r.amount_type_text),1),t("div",de,d(s(ut).formatAmount(r.settlement_amount,r.settlement_currency))+" "+d(r.settlement_currency),1)]))),128))])):_("",!0)])}),128))])]),(j=i.value)!=null&&j.customer_banking?(o(),m("div",oe,[t("div",me,[e[3]||(e[3]=t("div",{class:"payment-method-title"}," Payment method ",-1)),a(b)]),t("div",ce,[t("div",re,[e[4]||(e[4]=t("div",{class:"payment-label font-semibold w-60"}," ID ",-1)),t("div",_e,d((A=i.value)==null?void 0:A.trans_no),1)]),t("div",ue,[e[5]||(e[5]=t("div",{class:"payment-label font-semibold w-60"}," Owner ",-1)),t("div",he,d((P=(I=i.value)==null?void 0:I.customer_banking)==null?void 0:P.account_name),1)]),t("div",ve,[e[6]||(e[6]=t("div",{class:"payment-label font-semibold w-60"}," Number ",-1)),t("div",pe,[(S=i.value)!=null&&S.customer_banking?(o(),vt(E,{key:0,text:(M=(z=i.value)==null?void 0:z.customer_banking)==null?void 0:M.account_no,"is-show-card-number":!0},null,8,["text"])):_("",!0)])]),t("div",ye,[e[7]||(e[7]=t("div",{class:"payment-label font-semibold w-60"}," Address ",-1)),t("div",fe,d(((Y=(V=i.value)==null?void 0:V.customer_banking)==null?void 0:Y.bill_address)||"No address"),1)]),t("div",ge,[e[8]||(e[8]=t("div",{class:"payment-label font-semibold w-60"}," Fingerprint ",-1)),t("div",be,d((H=(R=i.value)==null?void 0:R.customer_banking)==null?void 0:H.account_no),1)]),t("div",we,[e[9]||(e[9]=t("div",{class:"payment-label font-semibold w-60"}," Expires ",-1)),(G=(q=i.value)==null?void 0:q.customer_banking)!=null&&G.expiration_month?(o(),m("div",xe,d(`${(U=(O=i.value)==null?void 0:O.customer_banking)==null?void 0:U.expiration_month}/${(K=(J=i.value)==null?void 0:J.customer_banking)==null?void 0:K.expiration_year}`),1)):(o(),m("div",ke," - "))])])])):_("",!0)])]))]),t("div",De,[p.value?(o(),m("div",Ee,[a(s(l),{width:"60%",height:"3rem",class:"mb-4"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-4"}),a(s(l),{width:"60%",height:"3rem",class:"mt-8 mb-4"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem",class:"mb-2"}),a(s(l),{width:"100%",height:"2.75rem"})])):(o(),m("div",Fe,[t("div",Be,[e[13]||(e[13]=t("div",{class:"title"}," Details ",-1)),a(b),t("div",Le,[t("div",$e,[e[10]||(e[10]=t("div",{class:"details-edit-title"}," Payment ID ",-1)),t("div",Ce,d((Q=i.value)==null?void 0:Q.trans_no),1)]),t("div",Ne,[e[11]||(e[11]=t("div",{class:"details-edit-title"}," Payment method ",-1)),t("div",Te,[(W=i.value)!=null&&W.customer_banking?(o(),vt(E,{key:0,"card-type":(Z=(X=i.value)==null?void 0:X.customer_banking)==null?void 0:Z.credit_brand,text:(et=(tt=i.value)==null?void 0:tt.customer_banking)==null?void 0:et.account_no,"is-show-card-number":!0},null,8,["card-type","text"])):_("",!0)])]),t("div",je,[e[12]||(e[12]=t("div",{class:"details-edit-title"}," Last updated ",-1)),t("div",Ae,[t("span",null,d(((it=(st=i.value)==null?void 0:st.plan)==null?void 0:it.updated_at)||"-"),1)])])])]),(at=i.value)!=null&&at.customer?(o(),m("div",Ie,[e[17]||(e[17]=t("div",{class:"title"}," Ratepayer ",-1)),a(b),t("div",null,[t("div",Pe,[e[14]||(e[14]=t("div",{class:"details-edit-title"}," ID ",-1)),t("div",Se,d((nt=(lt=i.value)==null?void 0:lt.customer)==null?void 0:nt.customer_id),1)]),t("div",ze,[e[15]||(e[15]=t("div",{class:"details-edit-title"}," Name ",-1)),t("div",Me,d((ot=(dt=i.value)==null?void 0:dt.customer)==null?void 0:ot.name),1)]),t("div",Ve,[e[16]||(e[16]=t("div",{class:"details-edit-title"}," Description ",-1)),t("div",Ye,d(((ct=(mt=i.value)==null?void 0:mt.customer)==null?void 0:ct.description)||"-"),1)])])])):_("",!0)]))])])}}}),as=Ct(Re,[["__scopeId","data-v-6bce3dab"]]);export{as as default};
