import{F as n}from"./index-Cui9J1ai.js";import"./index.vue_vue_type_style_index_0_lang-BQUf2NGU.js";import{C as f}from"./index-40_tgnh-.js";import{d as b,B as h,O as p,r,f as y,z as g,o as _,w,a as x,p as D,b as s,t as l,u as o}from"./index-DWWVg5mm.js";import{u as C}from"./useDict-BBitGqOA.js";import{g as A}from"./settlementDetailInfo-CD9ArrOp.js";import{_ as T}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Br9OVcmc.js";const I={key:0,class:"settlement-detail-info-content"},k={class:"bg-white p-6 rounded-2xl my-3 text-gray-600"},B={class:"text-2xl font-bold"},S={class:"bg-white px-6 py-2 rounded-2xl mt-8 text-gray-600"},F={class:"info-section"},N={class:"form-row"},R={class:"form-col"},E={class:"field"},L={class:"detail-value"},V={class:"form-col"},z={class:"field"},M={class:"detail-value"},O={class:"form-row"},P={class:"form-col"},j={class:"field"},q={class:"detail-value"},G={class:"form-col"},H={class:"field"},J={class:"detail-value"},K={class:"info-section"},Q={class:"form-row"},U={class:"form-col"},W={class:"field"},X={class:"detail-value"},Y={class:"form-col"},Z={class:"field"},$={class:"detail-value amount-highlight"},ss={class:"form-row"},ts={class:"form-col"},es={class:"field"},ls={class:"detail-value"},os={class:"form-col"},as={class:"field"},is={class:"detail-value"},ds={class:"form-row"},ns={class:"form-col"},rs={class:"field"},cs={class:"detail-value"},_s={class:"form-col"},vs={class:"field"},ms={class:"detail-value amount-highlight"},us={class:"form-row"},fs={class:"form-col"},bs={class:"field"},hs={class:"detail-value"},ps={class:"form-col"},ys={class:"field"},gs={class:"detail-value"},ws={class:"info-section"},xs={class:"form-row"},Ds={class:"form-col"},Cs={class:"field"},As={class:"detail-value"},Ts={class:"form-col"},Is={class:"field"},ks={class:"detail-value"},Bs=b({name:"settlementDetailInfoDetail",__name:"detial",setup(Ss){const v=h(),m=p(),e=r(null),i=r(!0),d=r(null),{getLabel:u}=C("settlement_detail_amount_type"),c=async()=>{try{i.value=!0,d.value=null;const a=await A({id:Number(v.params.id)});e.value=a.data}catch(a){d.value=a.message||"Failed to load settlement detail information",m.add({severity:"error",summary:"Error",detail:"Failed to load settlement detail information",life:3e3})}finally{i.value=!1}};return y(()=>{c()}),(a,t)=>(_(),g(o(f),{loading:i.value,error:d.value,onRetry:c},{default:w(()=>[e.value?(_(),x("div",I,[s("div",k,[s("div",B," Settlement Detail - "+l(e.value.trans_no),1)]),s("div",S,[t[17]||(t[17]=s("div",{class:"page-header"},[s("h1",{class:"page-title"}," Settlement Detail Information ")],-1)),s("div",F,[t[4]||(t[4]=s("h3",{class:"section-title"}," Transaction Information ",-1)),s("div",N,[s("div",R,[s("div",E,[t[0]||(t[0]=s("label",{class:"field-label"},"Transaction Number",-1)),s("div",L,l(e.value.trans_no||"-"),1)])]),s("div",V,[s("div",z,[t[1]||(t[1]=s("label",{class:"field-label"},"Customer ID",-1)),s("div",M,l(e.value.customer_id||"-"),1)])])]),s("div",O,[s("div",P,[s("div",j,[t[2]||(t[2]=s("label",{class:"field-label"},"Card Type",-1)),s("div",q,l(e.value.card_type),1)])]),s("div",G,[s("div",H,[t[3]||(t[3]=s("label",{class:"field-label"},"Credit Brand",-1)),s("div",J,l(e.value.credit_brand),1)])])])]),s("div",K,[t[13]||(t[13]=s("h3",{class:"section-title"}," Amount Information ",-1)),s("div",Q,[s("div",U,[s("div",W,[t[5]||(t[5]=s("label",{class:"field-label"},"Currency",-1)),s("div",X,l(e.value.currency||"-"),1)])]),s("div",Y,[s("div",Z,[t[6]||(t[6]=s("label",{class:"field-label"},"Amount",-1)),s("div",$,l(o(n).formatAmount(e.value.amount)),1)])])]),s("div",ss,[s("div",ts,[s("div",es,[t[7]||(t[7]=s("label",{class:"field-label"},"Transaction Currency",-1)),s("div",ls,l(e.value.trans_currency||"-"),1)])]),s("div",os,[s("div",as,[t[8]||(t[8]=s("label",{class:"field-label"},"Transaction Amount",-1)),s("div",is,l(o(n).formatAmount(e.value.trans_amount)),1)])])]),s("div",ds,[s("div",ns,[s("div",rs,[t[9]||(t[9]=s("label",{class:"field-label"},"Settlement Currency",-1)),s("div",cs,l(e.value.settlement_currency||"-"),1)])]),s("div",_s,[s("div",vs,[t[10]||(t[10]=s("label",{class:"field-label"},"Settlement Amount",-1)),s("div",ms,l(o(n).formatAmount(e.value.settlement_amount)),1)])])]),s("div",us,[s("div",fs,[s("div",bs,[t[11]||(t[11]=s("label",{class:"field-label"},"Exchange Rate",-1)),s("div",hs,l(e.value.rate||"-"),1)])]),s("div",ps,[s("div",ys,[t[12]||(t[12]=s("label",{class:"field-label"},"Amount Type",-1)),s("div",gs,l(o(u)(e.value.amount_type)),1)])])])]),s("div",ws,[t[16]||(t[16]=s("h3",{class:"section-title"}," Additional Information ",-1)),s("div",xs,[s("div",Ds,[s("div",Cs,[t[14]||(t[14]=s("label",{class:"field-label"},"Settlement Date",-1)),s("div",As,l(e.value.settle_at||"-"),1)])]),s("div",Ts,[s("div",Is,[t[15]||(t[15]=s("label",{class:"field-label"},"Remarks",-1)),s("div",ks,l(e.value.remarks||"-"),1)])])])])])])):D("",!0)]),_:1},8,["loading","error"]))}}),Os=T(Bs,[["__scopeId","data-v-0b1d41c8"]]);export{Os as default};
