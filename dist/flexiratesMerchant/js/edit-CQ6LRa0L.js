import{d as j,k as z,O,i as Q,B as W,r as v,f as Z,v as G,a as H,o as _,b as r,t as d,u as l,e as m,w as c,y,z as V,p as k,j as g,A as J,s as K,F as X,bM as Y,bP as ee,bQ as ae}from"./index-DWWVg5mm.js";import{s as se}from"./index-BpKqttS3.js";import{s as te}from"./index-DDWh5zyb.js";import{s as le}from"./index-CutZZiSb.js";import{s as oe}from"./index-DNtGhG9S.js";import{s as re}from"./index-W8aGMMs9.js";import{t as ne,c as L,f as ie,d as P,b as x}from"./index.esm-DoKGfgcR.js";import{u as me}from"./useListRefresh-AzGE_VAq.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";import"./index-CjzDW1dI.js";const ce={class:"merchant-edit-page"},ue={class:"flex justify-between items-center mb-4"},pe={class:"text-3xl"},fe={class:"p-4 bg-white rounded-2xl"},he={class:"field"},ve={for:"user_name",class:"mb-2 block"},_e={class:"field"},we={for:"email",class:"mb-2 block"},be={class:"field"},ye={for:"new-password",class:"mb-2 block"},Ve={class:"field"},ke={class:"mb-2 block"},ge={class:"field mb-4"},xe={for:"roles",class:"mb-2 block"},Ue={class:"flex justify-end mt-6 gap-2"},Fe=j({name:"flexiratesMerchantUserEdit",__name:"edit",setup(Re){const{t:e}=z(),U=O(),B=Q(),I=W(),R=v(),f=v(!1),$=v(I.params.id),{backWithRefresh:q}=me("flexiratesMerchantUserList",()=>{}),n=v({user_name:"",email:"",password:"",mfa_check:0,roles:[]}),A=v(!1),C=v([]),D=async()=>{var a;try{const s=await Y({page:1,page_size:100});C.value=(((a=s.data)==null?void 0:a.data)||[]).map(u=>({name:u.name,id:u.id||0}))}catch(s){console.error("Failed to fetch roles:",s),U.add({severity:"error",summary:e("common.error"),detail:e("merchant.role.messages.fetchRolesFailed","Failed to fetch roles"),life:3e3})}},E=async()=>{try{f.value=!0;const a=await ee($.value),{name:s,email:u,mfa_check:o,roles:p}=a.data;n.value={user_name:s,email:u,password:"",mfa_check:o,roles:[]},p&&Array.isArray(p)&&(n.value.roles=p.map(h=>({name:h.name,id:h.id||0})))}catch(a){console.error("Failed to fetch merchant detail:",a),U.add({severity:"error",summary:e("common.error"),detail:e("merchant.messages.updateFailed","Failed to fetch merchant detail"),life:3e3})}finally{f.value=!1}},M=ne(L({user_name:x().min(3,e("validation.minLength",{min:3})).max(50,e("validation.maxLength",{max:50})).required(),email:x().email(e("validation.emailInvalid")).required(e("validation.emailRequired")),password:x().test("password-validation",e("validation.passwordMinLength"),a=>!a||a===""?!0:a.length>=8).test("password-uppercase",e("validation.passwordUppercase"),a=>!a||a===""?!0:/[A-Z]/.test(a)).test("password-lowercase",e("validation.passwordLowercase"),a=>!a||a===""?!0:/[a-z]/.test(a)).test("password-number",e("validation.passwordNumber"),a=>!a||a===""?!0:/\d/.test(a)),mfa_check:P().required(),roles:ie().of(L({name:x(),id:P()})).min(1,"Role is required")})),N=async()=>{var a,s,u;f.value=!0;try{const o=await((a=R.value)==null?void 0:a.validate());if(!o.valid){f.value=!1;return}const p={...o.values,roles:((u=(s=o==null?void 0:o.values)==null?void 0:s.roles)==null?void 0:u.map(w=>w==null?void 0:w.id))||[]};p.password||delete p.password;const{code:h}=await ae($.value,p);h===0&&(U.add({severity:"success",summary:e("common.success"),detail:e("merchant.messages.updateSuccess","Merchant updated successfully")}),q(),E())}catch(o){console.error("Failed to update user:",o)}finally{f.value=!1}},T=()=>{B.back()};return Z(()=>{Promise.all([D(),E()]).then(()=>{G(()=>{A.value=!0})})}),(a,s)=>{const u=re,o=oe,p=le,h=te,w=se,S=K;return _(),H("div",ce,[r("div",ue,[r("h1",pe,d(l(e)("menu.merchantEdit","Edit Merchant")),1)]),r("div",fe,[m(l(X),{ref_key:"formRef",ref:R,"validation-schema":l(M),class:"merchant-form flex flex-col gap-4",onSubmit:N},{default:c(()=>[m(l(y),{modelValue:n.value.user_name,"onUpdate:modelValue":s[0]||(s[0]=t=>n.value.user_name=t),name:"user_name",class:"form-col"},{default:c(({field:t,errorMessage:i,handleChange:b})=>[r("div",he,[r("label",ve,d(l(e)("merchant.form.name","Username"))+"*",1),m(u,{id:"user_name",modelValue:t.value,"onUpdate:modelValue":F=>t.value=F,placeholder:l(e)("merchant.form.name","Username"),class:"w-full",autocomplete:"new-password",onValueChange:b},null,8,["modelValue","onUpdate:modelValue","placeholder","onValueChange"]),i?(_(),V(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[g(d(i),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),m(l(y),{modelValue:n.value.email,"onUpdate:modelValue":s[1]||(s[1]=t=>n.value.email=t),name:"email",class:"form-col"},{default:c(({field:t,errorMessage:i})=>[r("div",_e,[r("label",we,d(l(e)("merchant.form.email","Email"))+"*",1),m(u,J({id:"email"},t,{placeholder:l(e)("merchant.form.email","Email"),class:"w-full",type:"email",autocomplete:"new-password"}),null,16,["placeholder"]),i?(_(),V(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[g(d(i),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),m(l(y),{modelValue:n.value.password,"onUpdate:modelValue":s[2]||(s[2]=t=>n.value.password=t),name:"password",as:"div"},{default:c(({field:t,errorMessage:i,handleChange:b})=>[r("div",be,[r("label",ye,d(l(e)("merchant.form.password","Password")),1),m(p,{id:"new-password",modelValue:t.value,"onUpdate:modelValue":F=>t.value=F,placeholder:l(e)("merchant.form.password","Password"),class:"w-full","toggle-mask":"",feedback:!0,autocomplete:"new-password",disabled:!A.value,"input-props":{autocomplete:"new-password"},onValueChange:b},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","onValueChange"]),s[6]||(s[6]=r("div",{class:"text-sm text-gray-500 mt-2"}," If you do not fill in the password, the password will remain unchanged ",-1)),i?(_(),V(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[g(d(i),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),m(l(y),{modelValue:n.value.mfa_check,"onUpdate:modelValue":s[4]||(s[4]=t=>n.value.mfa_check=t),name:"mfa_check",as:"div",class:"form-col"},{default:c(({errorMessage:t})=>[r("div",Ve,[r("label",ke,d(l(e)("merchant.form.2fa","2FA Authentication")),1),m(h,{modelValue:n.value.mfa_check,"onUpdate:modelValue":s[3]||(s[3]=i=>n.value.mfa_check=i),options:[{label:"Enabled",value:1},{label:"Disabled",value:0}],"option-label":"label","option-value":"value",class:"w-full",name:"to_fa_check",placeholder:l(e)("merchant.form.select2FA","Select 2FA Option")},null,8,["modelValue","placeholder"])]),t?(_(),V(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[g(d(t),1)]),_:2},1024)):k("",!0)]),_:1},8,["modelValue"]),m(l(y),{modelValue:n.value.roles,"onUpdate:modelValue":s[5]||(s[5]=t=>n.value.roles=t),name:"roles",class:"form-col"},{default:c(({field:t,errorMessage:i,handleChange:b})=>[r("div",ge,[r("label",xe,d(l(e)("merchant.form.roles","Roles")),1),m(w,{"model-value":t.value,options:C.value,"option-label":"name",placeholder:l(e)("merchant.form.selectRoles","Select Roles"),display:"chip",class:"w-full",name:"roles","onUpdate:modelValue":b},null,8,["model-value","options","placeholder","onUpdate:modelValue"]),i?(_(),V(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[g(d(i),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),r("div",Ue,[m(S,{type:"button",label:l(e)("common.cancel"),icon:"pi pi-times",class:"p-button-text",onClick:T},null,8,["label"]),m(S,{type:"submit",label:l(e)("common.save"),icon:"pi pi-check",loading:f.value},null,8,["label","loading"])])]),_:1},8,["validation-schema"])])])}}}),je=de(Fe,[["__scopeId","data-v-7c8ecaee"]]);export{je as default};
