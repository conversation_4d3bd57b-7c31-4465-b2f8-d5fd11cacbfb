import{d as s,f as n,a as o,o as r,b as e}from"./index-DWWVg5mm.js";const d={class:"ratepayer-edit-page"},p=s({name:"flexiratesMerchantRatepayerEdit",__name:"edit",setup(i){const a=async()=>{};return n(()=>{a()}),(l,t)=>(r(),o("div",d,t[0]||(t[0]=[e("div",{class:"flex justify-between items-center mb-4"},[e("h1",{class:"text-3xl"}," Edit Ratepayer ")],-1),e("div",{class:"p-6 bg-white rounded-2xl"},null,-1)])))}});export{p as default};
