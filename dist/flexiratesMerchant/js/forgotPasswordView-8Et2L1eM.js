import{d as x,k as I,x as S,r as i,a as m,b as s,e as a,u as o,n as $,t as d,w as _,s as C,z as P,p as V,j as B,o as r}from"./index-DWWVg5mm.js";import{s as T}from"./index-DNtGhG9S.js";import{s as N}from"./index-W8aGMMs9.js";import{s as j}from"./index-DEgIf3z8.js";import{h as z,s as L}from"./index-BKi0p8Ug.js";import{f as q,_ as O}from"./login-bg-D47k_GaL.js";import{o as R,s as A}from"./types-C_HOBWe9.js";import{_ as D}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BEwkorqa.js";import"./index-CjzDW1dI.js";const E={class:"auth-container"},F={class:"logo-wrap"},G={class:"logo"},K={class:"auth-content-wrap"},M={class:"auth-content"},U={key:0,class:"forgot-password"},W={class:"title"},Y={class:"forgot-subtitle"},H={class:"flex flex-col gap-4"},J={class:"flex gap-4 justify-center"},Q={key:1,class:"is-submit"},X={class:"flex gap-4 justify-center"},Z=x({__name:"forgotPasswordView",setup(ss){const{t}=I(),v=S(),n=i(!1),g=i({email:""}),b=i(z(R({email:A({message:t("validation.emailRequired")}).min(1,{message:t("validation.emailRequired")}).email({message:t("validation.emailInvalid")})}))),p=i(!1),w=async({values:l,valid:e})=>{if(e)try{n.value=!0,await v.forgotPassword(l.email)}catch{}finally{n.value=!1,p.value=!0}};return(l,e)=>{const y=j,h=N,k=T,c=C;return r(),m("div",E,[s("div",F,[s("div",G,[a(y,{src:o(q),width:"230px",alt:"Image"},null,8,["src"])])]),s("div",{class:"auth-bg-wrap",style:$({backgroundImage:`url(${o(O)})`,backgroundSize:"100% 100%"})},null,4),s("div",K,[s("div",M,[p.value?(r(),m("div",Q,[e[2]||(e[2]=s("h1",{class:"title"}," Check Your Inbox ",-1)),e[3]||(e[3]=s("p",{class:"forgot-subtitle"}," We have sent password recovery instructions to your email. ",-1)),s("div",X,[a(c,{class:"!mt-6 w-full back-to-login",severity:"info",label:"BACK TO LOGIN",onClick:e[1]||(e[1]=u=>l.$router.back())})])])):(r(),m("div",U,[s("h1",W,d(o(t)("forgotPassword.title")),1),s("p",Y,d(o(t)("forgotPassword.subtitle")),1),a(o(L),{"initial-values":g.value,resolver:b.value,class:"flex flex-col gap-6 w-full",onSubmit:w},{default:_(u=>{var f;return[s("div",H,[a(h,{name:"email",type:"text",placeholder:o(t)("forgotPassword.email"),class:"form-input"},null,8,["placeholder"]),(f=u.email)!=null&&f.invalid?(r(),P(k,{key:0,severity:"error",size:"small",variant:"simple"},{default:_(()=>[B(d(u.email.error.message),1)]),_:2},1024)):V("",!0)]),a(c,{loading:n.value,class:"!mt-6 w-full login-submit",type:"submit",severity:"warn",label:o(t)("button.submit")},null,8,["loading","label"]),s("div",J,[a(c,{class:"!p-0",text:"",label:o(t)("forgotPassword.backToLogin"),onClick:e[0]||(e[0]=es=>l.$router.back())},null,8,["label"])])]}),_:1},8,["initial-values","resolver","onSubmit"])]))])])])}}}),ds=D(Z,[["__scopeId","data-v-d8a2e257"]]);export{ds as default};
