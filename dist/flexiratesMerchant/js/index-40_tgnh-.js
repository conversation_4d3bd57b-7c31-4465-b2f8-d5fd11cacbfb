import{B as c,s as m}from"./index.vue_vue_type_style_index_0_lang-BQUf2NGU.js";import{s as g}from"./index-Br9OVcmc.js";import{a as r,o as t,A as i,b as n,d as u,e as f,u as y,t as v,M as h}from"./index-DWWVg5mm.js";import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";var k={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},_=c.extend({name:"progressspinner",style:g,classes:k}),$={name:"BaseProgressSpinner",extends:m,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:_,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},d={name:"ProgressSpinner",extends:$,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},B=["fill","stroke-width"];function P(e,a,l,p,o,s){return t(),r("div",i({class:e.cx("root"),role:"progressbar"},e.ptmi("root")),[(t(),r("svg",i({class:e.cx("spin"),viewBox:"25 25 50 50",style:s.svgStyle},e.ptm("spin")),[n("circle",i({class:e.cx("circle"),cx:"50",cy:"50",r:"20",fill:e.fill,"stroke-width":e.strokeWidth,strokeMiterlimit:"10"},e.ptm("circle")),null,16,B)],16))],16)}d.render=P;const w={class:"common-page"},b={key:0,class:"loading-container"},x={key:1,class:"error-container"},C={class:"error-message"},D={key:2,class:"common-page-content"},A=u({__name:"index",props:{loading:{type:Boolean,default:!1},error:{default:null}},emits:["retry"],setup(e,{emit:a}){const l=a,p=()=>{l("retry")};return(o,s)=>(t(),r("div",w,[o.loading?(t(),r("div",b,[f(y(d),{style:{width:"50px",height:"50px"},"stroke-width":"4"}),s[0]||(s[0]=n("p",{class:"loading-text"}," Loading... ",-1))])):o.error?(t(),r("div",x,[s[1]||(s[1]=n("div",{class:"error-icon"}," ⚠️ ",-1)),n("p",C,v(o.error),1),n("button",{class:"retry-button",onClick:p}," Retry ")])):(t(),r("div",D,[h(o.$slots,"default",{},void 0,!0)]))]))}}),V=S(A,[["__scopeId","data-v-579f6d29"]]);export{V as C};
