import{s as te,B as se}from"./BaseDataTable-75nyB6_U.js";import{_ as oe}from"./BaseCardType-BCAS9_FJ.js";import{R as k,d as O,r,a as l,o as m,e as y,w as _,b as t,q as W,p as x,t as i,M as ne,x as ae,f as ie,v as re,E as P,G as $,u as c,j as F,s as le,T as me}from"./index-DWWVg5mm.js";import{d as v}from"./dayjs.min-DUDfdLe0.js";import{u as ce}from"./useRequestList-Bk9BBkCQ.js";import{g as ue}from"./transactions-Dqpj7ebB.js";import{f as pe}from"./date-BWkRbs8K.js";import{f as de}from"./format-Cs3Vg3WT.js";import{i as _e,a as fe}from"./install-B3TjOJg-.js";import{u as ye,i as he,a as ve,c as be,e as ge,d as xe,b as Me,f as Ye}from"./installCanvasRenderer-CM5zVs_H.js";import{_ as we}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./getAssetsFile-D2Insbdr.js";import"./Phone-dOHlUqSP.js";import"./login-bg-D47k_GaL.js";import"./isUpdated-DXQm7BuR.js";const Te=()=>k("/cardinia/home/<USER>"),Pe=h=>k("/cardinia/home/<USER>",{params:{time:h}}),$e=h=>k("/cardinia/home/<USER>",{params:h}),ke={key:0,class:"popover-header flex justify-between items-center border-b border-[#545454] pb-2"},Re={class:"font-semibold text-xl text-[#031f73]"},Ae={class:"popover-content"},Se=O({__name:"index",props:{title:{type:String,default:""},customized:{type:Boolean,default:!1}},setup(h,{expose:w}){const f=h,b=r(),T=u=>{b.value.toggle(u)},M=u=>{b.value.hide(u)};return w({toggle:T,hide:M}),(u,R)=>{const g=te;return m(),l("div",null,[y(g,{ref_key:"op",ref:b,class:"custom-flexirates-popover"},{default:_(()=>[t("div",{class:W(["popover-wrapper",{"px-2":!f.customized,"py-4":!f.customized}])},[f.title?(m(),l("div",ke,[t("div",Re,i(f.title),1),t("div",null,[t("i",{class:"pi-times pi cursor-pointer",style:{color:"#0073cf","font-weight":"700","font-size":"16px"},onClick:M})])])):x("",!0),t("div",Ae,[ne(u.$slots,"content")])],2)]),_:3},512)])}}}),Ne={class:"home"},De={class:"merchant-common-page"},Ee={class:"welcome"},Ve={class:"welcome-title"},Ce={class:"w-full max-w-8xl"},Le={class:"total"},je={class:"statistical-title"},Fe={class:"total-content border-style"},Oe={class:"total-amount"},We={class:"next flex justify-between items-start gap-[4rem]"},Be={class:"next-chart w-2/3"},ze={class:"home-subtitle"},qe={class:"next-sum w-1/3"},Ie={class:"statistical-title"},Ue={class:"item-content border-style"},He={class:"item-amount"},Ge={key:0},Je={class:"month"},Ke={class:"home-subtitle"},Qe={class:"month-content"},Xe={class:"item-title flex items-center justify-between statistical-title"},Ze={class:"item-content border-style"},et={key:0},tt={class:"pop"},st={class:"merchant-common-page mt-6"},ot={class:"payment-header flex justify-between items-center"},nt={class:"underline"},at={key:0},it={key:1},rt=O({__name:"index",setup(h){ye([ve,be,ge,xe,_e,Me,Ye,fe]);const{getUserInfo:w}=ae(),f=r(""),b=r(!1),T=r(!0),M=r(),u=r(),R=r(),g=r(),B=r([{template:"property_number",field:"customer_plan.customer_property.property_number",header:"Property Number",style:{minWidth:"120px"}},{template:"remit_date",field:"remit_date",header:"Remit Date",style:{minWidth:"120px"}},{template:"payment_amount",field:"payment_amount",header:"Amount",style:{minWidth:"200px"}},{template:"payment_method",field:"payment_method",header:"PaymentMethod",style:{minWidth:"100px"}},{field:"customer.name",header:"Customer Name",style:{minWidth:"160px"}},{template:"address",field:"address",header:"Address",style:{minWidth:"120px"}},{field:"customer.email_primary",header:"Email",style:{minWidth:"120px"}},{field:"customer.phone_mobile",header:"Mobile",style:{minWidth:"120px"}}]),{list:z,loading:q,total:I,failed:U,failureMessage:H}=ce({requestFn:ue,immediate:!0}),G=s=>{if(!g.value)return;const o=he(g.value),a=v(),p=v().subtract(1,"y"),Y=Math.max(...Object.values(s==null?void 0:s.current).map(Number),...Object.values(s==null?void 0:s.last).map(Number)),e=r({title:{text:`Payments on ${p.format("MMMM YYYY")} vs ${a.format("MMMM YYYY")}`,textStyle:{color:"#545454",fontSize:"14px"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"6%"},xAxis:{type:"category",data:Object.keys(s==null?void 0:s.current)},yAxis:{type:"value",axisLabel:{formatter:n=>`$${n}`},min:0,max:Math.floor(Y+Y*.2)},color:["#ebb700","#0073cf"],legend:{data:[`FY-${p.format("YYYY")}`,`FY-${a.format("YYYY")}`],left:"0",top:"22px",icon:"circle"},series:[{data:Object.values(s==null?void 0:s.last),type:"bar",tooltip:{valueFormatter(n){return`$${n}`}},itemStyle:{borderRadius:[3,3,0,0]},name:`FY-${p.format("YYYY")}`},{data:Object.values(s==null?void 0:s.current),type:"bar",tooltip:{valueFormatter(n){return`$${n}`}},itemStyle:{borderRadius:[3,3,0,0]},name:`FY-${a.format("YYYY")}`}]});o.setOption(e.value)};ie(()=>{setTimeout(()=>{b.value=!0},300),Promise.all([w().then(s=>{f.value=s.name}),Te().then(s=>{M.value=s.data,X(s.data)}),Pe(v().format("YYYY-MM-DD")).then(s=>{Z(s.data)}),$e({date:v().format("YYYY-MM")}).then(({code:s,data:o})=>{s===0&&re(()=>{G(o==null?void 0:o.weekly)})})]).catch(()=>{T.value=!1})});const A=r([{name:"Total Active Registrations",amount:"00",detail:"View all"},{name:"Total Active Email Registrations",amount:"0",detail:"View all"},{name:"Total Active SMS Registrations",amount:"0",detail:"View all"},{name:"Total Ratepayers",amount:"0",detail:"View all"}]),S=r([{name:"Number of Pending Payments",amount:"0",detail:"View all"},{name:"Amount of Pending Payments",amount:"0",detail:"View all"}]),N=r([{name:"Total Payments Received",amount:"0",detail:"View all"},{name:"Total Number of Failed Payments",amount:"0",detail:"View all"},{name:"Total Payments Received via OTHER SOURCES",amount:"0",detail:"View all"},{name:"Total Amount in Arrears",amount:"0",detail:"View all"}]),J={"Total Active Registrations":"total_active_count","Total Active Ratepayers":"total_rate_count","Total Active Email Registrations":"","Total Active SMS Registrations":""},K={"Number of Pending Payments":"pending_payment_count","Amount of Pending Payments":"pending_payment_amount"},Q={"Total Payments Received":"total_payment_received","Total Number of Failed Payments":"total_failed_payments","Total Payments Received via OTHER SOURCES":"other_sources_total","Total Amount in Arrears":"arrears_amount_total"},X=s=>{A.value.forEach(o=>{const a=J[o.name];a&&(o.amount=String(s[a]))}),S.value.forEach(o=>{const a=K[o.name];a&&(o.amount=String(s[a]))})},Z=s=>{N.value.forEach(o=>{var p;const a=Q[o.name];a&&(o.amount=(p=s[a])==null?void 0:p.toString())})},ee=s=>{u.value.toggle(s)};return(s,o)=>{const a=le,p=oe,Y=se;return m(),l("div",Ne,[t("div",De,[y(me,{name:"fade",appear:""},{default:_(()=>[t("div",null,[t("div",Ee,[t("div",Ve," Welcome, "+i(f.value)+"! ",1)]),t("div",Ce,[t("div",Le,[(m(!0),l(P,null,$(A.value,e=>(m(),l("div",{key:e.name,class:"total-item"},[t("div",je,i(e.name),1),t("div",Fe,[t("div",Oe,i(e.amount),1)])]))),128))]),t("div",We,[t("div",Be,[t("div",ze," Month of "+i(c(v)().format("MMMM")),1),t("div",{id:"chart",ref_key:"renderDom",ref:g,style:{width:"100%",height:"500px"}},null,512)]),t("div",qe,[(m(!0),l(P,null,$(S.value,e=>(m(),l("div",{key:e.name,class:"next-sum-item"},[t("div",Ie,i(e.name),1),t("div",Ue,[t("div",He,[e.name==="Amount of Pending Payments"?(m(),l("span",Ge,"$")):x("",!0),F(i(e.amount),1)])])]))),128))])]),t("div",Je,[t("div",Ke," This Month · "+i(c(v)().format("MMMM")),1),t("div",Qe,[(m(!0),l(P,null,$(N.value,e=>(m(),l("div",{key:e.name,class:"month-sum-item"},[t("div",Xe,[t("span",null,i(e.name),1),y(a,{icon:"pi pi-question-circle",variant:"text",style:{color:"#545454"},rounded:"",onClick:o[0]||(o[0]=n=>ee(n))})]),t("div",Ze,[t("div",{class:W(["item-amount",{"text-[#eb001b]":e.name==="Total Number of Failed Payments"}])},[e.name!=="Total Number of Failed Payments"?(m(),l("span",et,"$")):x("",!0),F(i(e.amount),1)],2)])]))),128))])])])])]),_:1}),t("div",tt,[y(Se,{ref_key:"popRef",ref:u},{content:_(()=>o[2]||(o[2]=[t("div",{class:"flex items-center gap-4 justify-center"},[t("i",{class:"pi pi-question-circle",style:{color:"#ebb700","font-size":"40px"}}),t("p",{class:"w-[400px]"}," Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer metus urna, convallis vel nulla vel, tempus vulputate. ")],-1)])),_:1},512)])]),t("div",st,[t("div",ot,[o[3]||(o[3]=t("div",{class:"home-subtitle"}," Payments List ",-1)),y(a,{label:"VIEW ALL PAYMENTS",severity:"info",onClick:o[1]||(o[1]=e=>s.$router.push({name:"flexiratesMerchantPayments"}))})]),t("div",null,[y(Y,{ref_key:"customerTable",ref:R,"show-search-bar":!1,value:c(z),columns:B.value,scrollable:!0,"show-multiple-column":!1,loading:c(q),paginator:!1,rows:6,"total-records":c(I),"data-key":"id",failed:c(U),"failure-message":c(H),"striped-rows":!1,style:{"--frozen-column-border-bottom":"-8px"},"is-show-expander":!1,"row-hover":!0},{property_number:_(({data:e})=>{var n,d;return[t("span",nt,i((d=(n=e==null?void 0:e.customer_plan)==null?void 0:n.customer_property)==null?void 0:d.property_number),1)]}),address:_(({data:e})=>{var n,d,D,E,V,C,L,j;return[(d=(n=e==null?void 0:e.customer_plan)==null?void 0:n.customer_property)!=null&&d.street_address?(m(),l("span",at,i((E=(D=e==null?void 0:e.customer_plan)==null?void 0:D.customer_property)==null?void 0:E.street_address)+" , ",1)):x("",!0),(C=(V=e==null?void 0:e.customer_plan)==null?void 0:V.customer_property)!=null&&C.suburb?(m(),l("span",it,i((j=(L=e==null?void 0:e.customer_plan)==null?void 0:L.customer_property)==null?void 0:j.suburb),1)):x("",!0)]}),payment_method:_(({data:e})=>{var n,d;return[y(p,{"card-type":(n=e==null?void 0:e.customer_banking)==null?void 0:n.credit_brand,"is-show-card-number":!0,text:(d=e==null?void 0:e.customer_banking)==null?void 0:d.account_no},null,8,["card-type","text"])]}),payment_amount:_(({data:e})=>[t("span",null,i(c(de)(e.payment_amount,e.payment_currency)),1)]),remit_date:_(({data:e})=>[t("span",null,i(c(pe)(e.created_at)),1)]),_:1},8,["value","columns","loading","total-records","failed","failure-message"])])])])}}}),Ct=we(rt,[["__scopeId","data-v-10c9dff7"]]);export{Ct as default};
