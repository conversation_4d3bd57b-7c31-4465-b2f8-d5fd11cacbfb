import{J as n,aM as o,a as s,o as t,A as r,b as a}from"./index-DWWVg5mm.js";import{s as p}from"./index-Br9OVcmc.js";var l={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},c=n.extend({name:"progressspinner",style:p,classes:l}),d={name:"BaseProgressSpinner",extends:o,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:c,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},g={name:"ProgressSpinner",extends:d,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},m=["fill","stroke-width"];function u(e,f,v,y,S,i){return t(),s("div",r({class:e.cx("root"),role:"progressbar"},e.ptmi("root")),[(t(),s("svg",r({class:e.cx("spin"),viewBox:"25 25 50 50",style:i.svgStyle},e.ptm("spin")),[a("circle",r({class:e.cx("circle"),cx:"50",cy:"50",r:"20",fill:e.fill,"stroke-width":e.strokeWidth,strokeMiterlimit:"10"},e.ptm("circle")),null,16,m)],16))],16)}g.render=u;export{g as s};
