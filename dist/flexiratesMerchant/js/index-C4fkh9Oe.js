import{b8 as p,a2 as f,a4 as d}from"./index-DWWVg5mm.js";function a(e){"@babel/helpers - typeof";return a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(e)}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,g(n.key),n)}}function m(e,t,r){return t&&h(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function g(e){var t=_(e,"string");return a(t)=="symbol"?t:t+""}function _(e,t){if(a(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(a(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var L=function(){function e(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){};b(this,e),this.element=t,this.listener=r}return m(e,[{key:"bindScrollListener",value:function(){this.scrollableParents=p(this.element);for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].addEventListener("scroll",this.listener)}},{key:"unbindScrollListener",value:function(){if(this.scrollableParents)for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].removeEventListener("scroll",this.listener)}},{key:"destroy",value:function(){this.unbindScrollListener(),this.element=null,this.listener=null,this.scrollableParents=null}}])}();function u(e){"@babel/helpers - typeof";return u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(e)}function S(e){return C(e)||k(e)||w(e)||P()}function P(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function w(e,t){if(e){if(typeof e=="string")return c(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(e,t):void 0}}function k(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function C(e){if(Array.isArray(e))return c(e)}function c(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function A(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function j(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,v(n.key),n)}}function O(e,t,r){return t&&j(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function y(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){var t=$(e,"string");return u(t)=="symbol"?t:t+""}function $(e,t){if(u(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(u(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var T=function(){function e(t){var r=t.init,n=t.type;A(this,e),y(this,"helpers",void 0),y(this,"type",void 0),this.helpers=new Set(r),this.type=n}return O(e,[{key:"add",value:function(r){this.helpers.add(r)}},{key:"update",value:function(){}},{key:"delete",value:function(r){this.helpers.delete(r)}},{key:"clear",value:function(){this.helpers.clear()}},{key:"get",value:function(r,n){var i=this._get(r,n),l=i?this._recursive(S(this.helpers),i):null;return f(l)?l:null}},{key:"_isMatched",value:function(r,n){var i,l=r==null?void 0:r.parent;return(l==null||(i=l.vnode)===null||i===void 0?void 0:i.key)===n||l&&this._isMatched(l,n)||!1}},{key:"_get",value:function(r,n){var i,l;return((i=n||(r==null?void 0:r.$slots))===null||i===void 0||(l=i.default)===null||l===void 0?void 0:l.call(i))||null}},{key:"_recursive",value:function(){var r=this,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],l=[];return i.forEach(function(o){o.children instanceof Array?l=l.concat(r._recursive(l,o.children)):o.type.name===r.type?l.push(o):f(o.key)&&(l=l.concat(n.filter(function(s){return r._isMatched(s,o.key)}).map(function(s){return s.vnode})))}),l}}])}();function M(e,t){if(e){var r=e.props;if(r){var n=t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),i=Object.prototype.hasOwnProperty.call(r,n)?n:t;return e.type.extends.props[t].type===Boolean&&r[i]===""?!0:r[i]}}return null}var B=d();export{L as C,B as O,T as _,M as g};
