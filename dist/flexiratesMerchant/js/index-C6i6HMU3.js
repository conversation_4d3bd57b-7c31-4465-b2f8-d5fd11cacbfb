import{F as Be}from"./index-Cui9J1ai.js";import{d as re}from"./dayjs.min-DUDfdLe0.js";import{D as fe}from"./decimal-LZMqkRTm.js";import{R as Oe,d as ot,r as Y,f as ct,l as vt,a as W,o as Z,b as p,t as j,u as P,e as $,s as _e,E as he,G as pe,q as me,p as ut,v as Le}from"./index-DWWVg5mm.js";import{s as Fe}from"./index-DNL_tiqf.js";import{s as dt}from"./index-DDWh5zyb.js";import{s as Q}from"./index-_nys9rwo.js";import{n as gt,g as Ze,l as ft,h as ht,j as Pe,p as J,P as R,D as Ge,r as $e,k as Je,o as pt,S as Ue,q as Xe,s as Me,t as ye,v as Ke,w as mt,_ as Ie,x as Qe,y as ee,z as yt,C as bt,Z as St,A as Te,B as je,E as wt,F as At,G as xt,H as Dt,I as _t,J as Lt,K as Mt,L as Tt,M as qe,N as et,O as Ct,Q as Nt,R as Pt,T as Ot,U as It,u as kt,i as ue,V as Rt,a as Et,b as Vt,c as Yt,f as zt,d as Bt,e as Ft}from"./installCanvasRenderer-CM5zVs_H.js";import{g as be,i as Gt,a as $t}from"./install-B3TjOJg-.js";import{_ as Ut}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-DcXu5dWS.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";const Xt=t=>Oe("/cardinia/report/overview",{params:t}),jt=t=>Oe("/cardinia/report/monthly",{params:t}),qt=t=>Oe("/cardinia/report/annual",{params:t});var He=Math.PI*2,Se=Math.PI/180;function tt(t,a){return ht(t.getBoxLayoutParams(),{width:a.getWidth(),height:a.getHeight()})}function at(t,a){var r=tt(t,a),e=t.get("center"),l=t.get("radius");Pe(l)||(l=[0,l]);var i=J(r.width,a.getWidth()),n=J(r.height,a.getHeight()),o=Math.min(i,n),g=J(l[0],o/2),h=J(l[1],o/2),c,m,f=t.coordinateSystem;if(f){var d=f.dataToPoint(e);c=d[0]||0,m=d[1]||0}else Pe(e)||(e=[e,e]),c=J(e[0],i)+r.x,m=J(e[1],n)+r.y;return{cx:c,cy:m,r0:g,r:h}}function Ht(t,a,r){a.eachSeriesByType(t,function(e){var l=e.getData(),i=l.mapDimension("value"),n=tt(e,r),o=at(e,r),g=o.cx,h=o.cy,c=o.r,m=o.r0,f=-e.get("startAngle")*Se,d=e.get("endAngle"),u=e.get("padAngle")*Se;d=d==="auto"?f-He:-d*Se;var y=e.get("minAngle")*Se,b=y+u,x=0;l.each(i,function(V){!isNaN(V)&&x++});var _=l.getSum(i),D=Math.PI/(_||x)*2,T=e.get("clockwise"),L=e.get("roseType"),C=e.get("stillShowZeroSum"),I=l.getDataExtent(i);I[0]=0;var A=T?1:-1,N=[f,d],z=A*u/2;gt(N,!T),f=N[0],d=N[1];var H=rt(e);H.startAngle=f,H.endAngle=d,H.clockwise=T;var F=Math.abs(d-f),G=F,K=0,E=f;if(l.setLayout({viewRect:n,r:c}),l.each(i,function(V,S){var v;if(isNaN(V)){l.setItemLayout(S,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:T,cx:g,cy:h,r0:m,r:L?NaN:c});return}L!=="area"?v=_===0&&C?D:V*D:v=F/x,v<b?(v=b,G-=b):K+=V;var s=E+A*v,w=0,M=0;u>v?(w=E+A*v/2,M=w):(w=E+z,M=s-z),l.setItemLayout(S,{angle:v,startAngle:w,endAngle:M,clockwise:T,cx:g,cy:h,r0:m,r:L?ft(V,I,[m,c]):c}),E=s}),G<He&&x)if(G<=.001){var U=F/x;l.each(i,function(V,S){if(!isNaN(V)){var v=l.getItemLayout(S);v.angle=U;var s=0,w=0;U<u?(s=f+A*(S+1/2)*U,w=s):(s=f+A*S*U+z,w=f+A*(S+1)*U-z),v.startAngle=s,v.endAngle=w}})}else D=G/K,E=f,l.each(i,function(V,S){if(!isNaN(V)){var v=l.getItemLayout(S),s=v.angle===b?b:V*D,w=0,M=0;s<u?(w=E+A*s/2,M=w):(w=E+z,M=E+A*s-z),v.startAngle=w,v.endAngle=M,E+=A*s}})})}var rt=Ze();function Wt(t){return{seriesType:t,reset:function(a,r){var e=r.findComponents({mainType:"legend"});if(!(!e||!e.length)){var l=a.getData();l.filterSelf(function(i){for(var n=l.getName(i),o=0;o<e.length;o++)if(!e[o].isSelected(n))return!1;return!0})}}}}function nt(t,a,r,e,l,i,n,o){var g=l-t,h=i-a,c=r-t,m=e-a,f=Math.sqrt(c*c+m*m);c/=f,m/=f;var d=g*c+h*m,u=d/f;u*=f;var y=n[0]=t+u*c,b=n[1]=a+u*m;return Math.sqrt((y-l)*(y-l)+(b-i)*(b-i))}var te=new R,O=new R,k=new R,ae=new R,q=new R,we=[],B=new R;function Zt(t,a){if(a<=180&&a>0){a=a/180*Math.PI,te.fromArray(t[0]),O.fromArray(t[1]),k.fromArray(t[2]),R.sub(ae,te,O),R.sub(q,k,O);var r=ae.len(),e=q.len();if(!(r<.001||e<.001)){ae.scale(1/r),q.scale(1/e);var l=ae.dot(q),i=Math.cos(a);if(i<l){var n=nt(O.x,O.y,k.x,k.y,te.x,te.y,we);B.fromArray(we),B.scaleAndAdd(q,n/Math.tan(Math.PI-a));var o=k.x!==O.x?(B.x-O.x)/(k.x-O.x):(B.y-O.y)/(k.y-O.y);if(isNaN(o))return;o<0?R.copy(B,O):o>1&&R.copy(B,k),B.toArray(t[1])}}}}function Jt(t,a,r){if(r<=180&&r>0){r=r/180*Math.PI,te.fromArray(t[0]),O.fromArray(t[1]),k.fromArray(t[2]),R.sub(ae,O,te),R.sub(q,k,O);var e=ae.len(),l=q.len();if(!(e<.001||l<.001)){ae.scale(1/e),q.scale(1/l);var i=ae.dot(a),n=Math.cos(r);if(i<n){var o=nt(O.x,O.y,k.x,k.y,te.x,te.y,we);B.fromArray(we);var g=Math.PI/2,h=Math.acos(q.dot(a)),c=g+h-r;if(c>=g)R.copy(B,k);else{B.scaleAndAdd(q,o/Math.tan(Math.PI/2-c));var m=k.x!==O.x?(B.x-O.x)/(k.x-O.x):(B.y-O.y)/(k.y-O.y);if(isNaN(m))return;m<0?R.copy(B,O):m>1&&R.copy(B,k)}B.toArray(t[1])}}}}function Ce(t,a,r,e){var l=r==="normal",i=l?t:t.ensureState(r);i.ignore=a;var n=e.get("smooth");n&&n===!0&&(n=.3),i.shape=i.shape||{},n>0&&(i.shape.smooth=n);var o=e.getModel("lineStyle").getLineStyle();l?t.useStyle(o):i.style=o}function Kt(t,a){var r=a.smooth,e=a.points;if(e)if(t.moveTo(e[0][0],e[0][1]),r>0&&e.length>=3){var l=Xe(e[0],e[1]),i=Xe(e[1],e[2]);if(!l||!i){t.lineTo(e[1][0],e[1][1]),t.lineTo(e[2][0],e[2][1]);return}var n=Math.min(l,i)*r,o=Me([],e[1],e[0],n/l),g=Me([],e[1],e[2],n/i),h=Me([],o,g,.5);t.bezierCurveTo(o[0],o[1],o[0],o[1],h[0],h[1]),t.bezierCurveTo(g[0],g[1],g[0],g[1],e[2][0],e[2][1])}else for(var c=1;c<e.length;c++)t.lineTo(e[c][0],e[c][1])}function Qt(t,a,r){var e=t.getTextGuideLine(),l=t.getTextContent();if(!l){e&&t.removeTextGuideLine();return}for(var i=a.normal,n=i.get("show"),o=l.ignore,g=0;g<Ge.length;g++){var h=Ge[g],c=a[h],m=h==="normal";if(c){var f=c.get("show"),d=m?o:$e(l.states[h]&&l.states[h].ignore,o);if(d||!$e(f,n)){var u=m?e:e&&e.states[h];u&&(u.ignore=!0),e&&Ce(e,!0,h,c);continue}e||(e=new Je,t.setTextGuideLine(e),!m&&(o||!n)&&Ce(e,!0,"normal",a.normal),t.stateProxy&&(e.stateProxy=t.stateProxy)),Ce(e,!1,h,c)}}if(e){pt(e.style,r),e.style.fill=null;var y=i.get("showAbove"),b=t.textGuideLineConfig=t.textGuideLineConfig||{};b.showAbove=y||!1,e.buildPath=Kt}}function ea(t,a){a=a||"labelLine";for(var r={normal:t.getModel(a)},e=0;e<Ue.length;e++){var l=Ue[e];r[l]=t.getModel([l,a])}return r}var ta=Math.PI/180;function We(t,a,r,e,l,i,n,o,g,h){if(t.length<2)return;function c(y){for(var b=y.rB,x=b*b,_=0;_<y.list.length;_++){var D=y.list[_],T=Math.abs(D.label.y-r),L=e+D.len,C=L*L,I=Math.sqrt(Math.abs((1-T*T/x)*C)),A=a+(I+D.len2)*l,N=A-D.label.x,z=D.targetTextWidth-N*l;lt(D,z,!0),D.label.x=A}}function m(y){for(var b={list:[],maxY:0},x={list:[],maxY:0},_=0;_<y.length;_++)if(y[_].labelAlignTo==="none"){var D=y[_],T=D.label.y>r?x:b,L=Math.abs(D.label.y-r);if(L>=T.maxY){var C=D.label.x-a-D.len2*l,I=e+D.len,A=Math.abs(C)<I?Math.sqrt(L*L/(1-C*C/I/I)):I;T.rB=A,T.maxY=L}T.list.push(D)}c(b),c(x)}for(var f=t.length,d=0;d<f;d++)if(t[d].position==="outer"&&t[d].labelAlignTo==="labelLine"){var u=t[d].label.x-h;t[d].linePoints[1][0]+=u,t[d].label.x=h}mt(t,g,g+n)&&m(t)}function aa(t,a,r,e,l,i,n,o){for(var g=[],h=[],c=Number.MAX_VALUE,m=-Number.MAX_VALUE,f=0;f<t.length;f++){var d=t[f].label;Ne(t[f])||(d.x<a?(c=Math.min(c,d.x),g.push(t[f])):(m=Math.max(m,d.x),h.push(t[f])))}for(var f=0;f<t.length;f++){var u=t[f];if(!Ne(u)&&u.linePoints){if(u.labelStyleWidth!=null)continue;var d=u.label,y=u.linePoints,b=void 0;u.labelAlignTo==="edge"?d.x<a?b=y[2][0]-u.labelDistance-n-u.edgeDistance:b=n+l-u.edgeDistance-y[2][0]-u.labelDistance:u.labelAlignTo==="labelLine"?d.x<a?b=c-n-u.bleedMargin:b=n+l-m-u.bleedMargin:d.x<a?b=d.x-n-u.bleedMargin:b=n+l-d.x-u.bleedMargin,u.targetTextWidth=b,lt(u,b)}}We(h,a,r,e,1,l,i,n,o,m),We(g,a,r,e,-1,l,i,n,o,c);for(var f=0;f<t.length;f++){var u=t[f];if(!Ne(u)&&u.linePoints){var d=u.label,y=u.linePoints,x=u.labelAlignTo==="edge",_=d.style.padding,D=_?_[1]+_[3]:0,T=d.style.backgroundColor?0:D,L=u.rect.width+T,C=y[1][0]-y[2][0];x?d.x<a?y[2][0]=n+u.edgeDistance+L+u.labelDistance:y[2][0]=n+l-u.edgeDistance-L-u.labelDistance:(d.x<a?y[2][0]=d.x+u.labelDistance:y[2][0]=d.x-u.labelDistance,y[1][0]=y[2][0]+C),y[1][1]=y[2][1]=d.y}}}function lt(t,a,r){if(r===void 0&&(r=!1),t.labelStyleWidth==null){var e=t.label,l=e.style,i=t.rect,n=l.backgroundColor,o=l.padding,g=o?o[1]+o[3]:0,h=l.overflow,c=i.width+(n?0:g);if(a<c||r){var m=i.height;if(h&&h.match("break")){e.setStyle("backgroundColor",null),e.setStyle("width",a-g);var f=e.getBoundingRect();e.setStyle("width",Math.ceil(f.width)),e.setStyle("backgroundColor",n)}else{var d=a-g,u=a<c?d:r?d>t.unconstrainedWidth?null:d:null;e.setStyle("width",u)}var y=e.getBoundingRect();i.width=y.width;var b=(e.style.margin||0)+2.1;i.height=y.height+b,i.y-=(i.height-m)/2}}}function Ne(t){return t.position==="center"}function ra(t){var a=t.getData(),r=[],e,l,i=!1,n=(t.get("minShowLabelAngle")||0)*ta,o=a.getLayout("viewRect"),g=a.getLayout("r"),h=o.width,c=o.x,m=o.y,f=o.height;function d(C){C.ignore=!0}function u(C){if(!C.ignore)return!0;for(var I in C.states)if(C.states[I].ignore===!1)return!0;return!1}a.each(function(C){var I=a.getItemGraphicEl(C),A=I.shape,N=I.getTextContent(),z=I.getTextGuideLine(),H=a.getItemModel(C),F=H.getModel("label"),G=F.get("position")||H.get(["emphasis","label","position"]),K=F.get("distanceToLabelLine"),E=F.get("alignTo"),U=J(F.get("edgeDistance"),h),V=F.get("bleedMargin"),S=H.getModel("labelLine"),v=S.get("length");v=J(v,h);var s=S.get("length2");if(s=J(s,h),Math.abs(A.endAngle-A.startAngle)<n){ye(N.states,d),N.ignore=!0,z&&(ye(z.states,d),z.ignore=!0);return}if(u(N)){var w=(A.startAngle+A.endAngle)/2,M=Math.cos(w),X=Math.sin(w),ie,de,ke,ge;e=A.cx,l=A.cy;var ne=G==="inside"||G==="inner";if(G==="center")ie=A.cx,de=A.cy,ge="center";else{var Ae=(ne?(A.r+A.r0)/2*M:A.r*M)+e,xe=(ne?(A.r+A.r0)/2*X:A.r*X)+l;if(ie=Ae+M*3,de=xe+X*3,!ne){var Re=Ae+M*(v+g-A.r),Ee=xe+X*(v+g-A.r),Ve=Re+(M<0?-1:1)*s,Ye=Ee;E==="edge"?ie=M<0?c+U:c+h-U:ie=Ve+(M<0?-K:K),de=Ye,ke=[[Ae,xe],[Re,Ee],[Ve,Ye]]}ge=ne?"center":E==="edge"?M>0?"right":"left":M>0?"left":"right"}var oe=Math.PI,le=0,ce=F.get("rotate");if(Ke(ce))le=ce*(oe/180);else if(G==="center")le=0;else if(ce==="radial"||ce===!0){var st=M<0?-w+oe:-w;le=st}else if(ce==="tangential"&&G!=="outside"&&G!=="outer"){var se=Math.atan2(M,X);se<0&&(se=oe*2+se);var it=X>0;it&&(se=oe+se),le=se-oe}if(i=!!le,N.x=ie,N.y=de,N.rotation=le,N.setStyle({verticalAlign:"middle"}),ne){N.setStyle({align:ge});var De=N.states.select;De&&(De.x+=N.x,De.y+=N.y)}else{var ve=N.getBoundingRect().clone();ve.applyTransform(N.getComputedTransform());var ze=(N.style.margin||0)+2.1;ve.y-=ze/2,ve.height+=ze,r.push({label:N,labelLine:z,position:G,len:v,len2:s,minTurnAngle:S.get("minTurnAngle"),maxSurfaceAngle:S.get("maxSurfaceAngle"),surfaceNormal:new R(M,X),linePoints:ke,textAlign:ge,labelDistance:K,labelAlignTo:E,edgeDistance:U,bleedMargin:V,rect:ve,unconstrainedWidth:ve.width,labelStyleWidth:N.style.width})}I.setTextConfig({inside:ne})}}),!i&&t.get("avoidLabelOverlap")&&aa(r,e,l,g,h,f,c,m);for(var y=0;y<r.length;y++){var b=r[y],x=b.label,_=b.labelLine,D=isNaN(x.x)||isNaN(x.y);if(x){x.setStyle({align:b.textAlign}),D&&(ye(x.states,d),x.ignore=!0);var T=x.states.select;T&&(T.x+=x.x,T.y+=x.y)}if(_){var L=b.linePoints;D||!L?(ye(_.states,d),_.ignore=!0):(Zt(L,b.minTurnAngle),Jt(L,b.surfaceNormal,b.maxSurfaceAngle),_.setShape({points:L}),x.__hostTarget.textGuideLineConfig={anchor:new R(L[0][0],L[0][1])})}}}var na=function(t){Ie(a,t);function a(r,e,l){var i=t.call(this)||this;i.z2=2;var n=new St;return i.setTextContent(n),i.updateData(r,e,l,!0),i}return a.prototype.updateData=function(r,e,l,i){var n=this,o=r.hostModel,g=r.getItemModel(e),h=g.getModel("emphasis"),c=r.getItemLayout(e),m=ee(be(g.getModel("itemStyle"),c,!0),c);if(isNaN(m.startAngle)){n.setShape(m);return}if(i){n.setShape(m);var f=o.getShallow("animationType");o.ecModel.ssr?(Te(n,{scaleX:0,scaleY:0},o,{dataIndex:e,isFrom:!0}),n.originX=m.cx,n.originY=m.cy):f==="scale"?(n.shape.r=c.r0,Te(n,{shape:{r:c.r}},o,e)):l!=null?(n.setShape({startAngle:l,endAngle:l}),Te(n,{shape:{startAngle:c.startAngle,endAngle:c.endAngle}},o,e)):(n.shape.endAngle=c.startAngle,je(n,{shape:{endAngle:c.endAngle}},o,e))}else wt(n),je(n,{shape:m},o,e);n.useStyle(r.getItemVisual(e,"style")),At(n,g);var d=(c.startAngle+c.endAngle)/2,u=o.get("selectedOffset"),y=Math.cos(d)*u,b=Math.sin(d)*u,x=g.getShallow("cursor");x&&n.attr("cursor",x),this._updateLabel(o,r,e),n.ensureState("emphasis").shape=ee({r:c.r+(h.get("scale")&&h.get("scaleSize")||0)},be(h.getModel("itemStyle"),c)),ee(n.ensureState("select"),{x:y,y:b,shape:be(g.getModel(["select","itemStyle"]),c)}),ee(n.ensureState("blur"),{shape:be(g.getModel(["blur","itemStyle"]),c)});var _=n.getTextGuideLine(),D=n.getTextContent();_&&ee(_.ensureState("select"),{x:y,y:b}),ee(D.ensureState("select"),{x:y,y:b}),xt(this,h.get("focus"),h.get("blurScope"),h.get("disabled"))},a.prototype._updateLabel=function(r,e,l){var i=this,n=e.getItemModel(l),o=n.getModel("labelLine"),g=e.getItemVisual(l,"style"),h=g&&g.fill,c=g&&g.opacity;Dt(i,_t(n),{labelFetcher:e.hostModel,labelDataIndex:l,inheritColor:h,defaultOpacity:c,defaultText:r.getFormattedLabel(l,"normal")||e.getName(l)});var m=i.getTextContent();i.setTextConfig({position:null,rotation:null}),m.attr({z2:10});var f=r.get(["label","position"]);if(f!=="outside"&&f!=="outer")i.removeTextGuideLine();else{var d=this.getTextGuideLine();d||(d=new Je,this.setTextGuideLine(d)),Qt(this,ea(n),{stroke:h,opacity:Lt(o.get(["lineStyle","opacity"]),c,1)})}},a}(Qe),la=function(t){Ie(a,t);function a(){var r=t!==null&&t.apply(this,arguments)||this;return r.ignoreLabelLineUpdate=!0,r}return a.prototype.render=function(r,e,l,i){var n=r.getData(),o=this._data,g=this.group,h;if(!o&&n.count()>0){for(var c=n.getItemLayout(0),m=1;isNaN(c&&c.startAngle)&&m<n.count();++m)c=n.getItemLayout(m);c&&(h=c.startAngle)}if(this._emptyCircleSector&&g.remove(this._emptyCircleSector),n.count()===0&&r.get("showEmptyCircle")){var f=rt(r),d=new Qe({shape:ee(at(r,l),f)});d.useStyle(r.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=d,g.add(d)}n.diff(o).add(function(u){var y=new na(n,u,h);n.setItemGraphicEl(u,y),g.add(y)}).update(function(u,y){var b=o.getItemGraphicEl(y);b.updateData(n,u,h),b.off("click"),g.add(b),n.setItemGraphicEl(u,b)}).remove(function(u){var y=o.getItemGraphicEl(u);yt(y,r,u)}).execute(),ra(r),r.get("animationTypeUpdate")!=="expansion"&&(this._data=n)},a.prototype.dispose=function(){},a.prototype.containPoint=function(r,e){var l=e.getData(),i=l.getItemLayout(0);if(i){var n=r[0]-i.cx,o=r[1]-i.cy,g=Math.sqrt(n*n+o*o);return g<=i.r&&g>=i.r0}},a.type="pie",a}(bt);function sa(t,a,r){a=Pe(a)&&{coordDimensions:a}||ee({encodeDefine:t.getEncode()},a);var e=t.getSource(),l=Mt(e,a).dimensions,i=new Tt(l,t);return i.initData(e,r),i}var ia=function(){function t(a,r){this._getDataWithEncodedVisual=a,this._getRawData=r}return t.prototype.getAllNames=function(){var a=this._getRawData();return a.mapArray(a.getName)},t.prototype.containName=function(a){var r=this._getRawData();return r.indexOfName(a)>=0},t.prototype.indexOfName=function(a){var r=this._getDataWithEncodedVisual();return r.indexOfName(a)},t.prototype.getItemVisual=function(a,r){var e=this._getDataWithEncodedVisual();return e.getItemVisual(a,r)},t}(),oa=Ze(),ca=function(t){Ie(a,t);function a(){return t!==null&&t.apply(this,arguments)||this}return a.prototype.init=function(r){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new ia(qe(this.getData,this),qe(this.getRawData,this)),this._defaultLabelLine(r)},a.prototype.mergeOption=function(){t.prototype.mergeOption.apply(this,arguments)},a.prototype.getInitialData=function(){return sa(this,{coordDimensions:["value"],encodeDefaulter:et(Ct,this)})},a.prototype.getDataParams=function(r){var e=this.getData(),l=oa(e),i=l.seats;if(!i){var n=[];e.each(e.mapDimension("value"),function(g){n.push(g)}),i=l.seats=Nt(n,e.hostModel.get("percentPrecision"))}var o=t.prototype.getDataParams.call(this,r);return o.percent=i[r]||0,o.$vars.push("percent"),o},a.prototype._defaultLabelLine=function(r){Pt(r,"labelLine",["show"]);var e=r.labelLine,l=r.emphasis.labelLine;e.show=e.show&&r.label.show,l.show=l.show&&r.emphasis.label.show},a.type="series.pie",a.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},a}(Ot);function va(t){return{seriesType:t,reset:function(a,r){var e=a.getData();e.filterSelf(function(l){var i=e.mapDimension("value"),n=e.get(i,l);return!(Ke(n)&&!isNaN(n)&&n<0)})}}}function ua(t){t.registerChartView(la),t.registerSeriesModel(ca),It("pie",t.registerAction),t.registerLayout(et(Ht,"pie")),t.registerProcessor(Wt("pie")),t.registerProcessor(va("pie"))}const da={class:"flex flex-col gap-4 bg-white mb-4 p-4 rounded-2xl"},ga={class:"report-header"},fa={class:"header-left"},ha={class:"date"},pa={class:"header-right"},ma={class:"overview-cards"},ya={class:"card-content"},ba={class:"change-info"},Sa={class:"card-content"},wa={class:"value"},Aa={class:"change-info"},xa={class:"date"},Da={class:"charts-section"},_a={class:"chart-row"},La={class:"chart-container"},Ma={class:"chart-header"},Ta={class:"chart-container"},Ca={class:"chart-header"},Na={class:"pie-charts-row"},Pa={class:"pie-chart-container"},Oa={key:0,class:"skeleton-overlay"},Ia={class:"pie-chart-container"},ka={class:"pie-chart-container"},Ra={class:"bottom-stats"},Ea={class:"flex justify-end mb-4"},Va={class:"stats-cards"},Ya={key:0,class:"grid grid-cols-4 gap-4"},za={class:"change-info"},Ba={class:"value"},Fa={class:"change-info"},Ga={class:"date"},$a=ot({__name:"index",setup(t){kt([Et,Vt,Yt,zt,Gt,Bt,ua,Ft,$t]);const a=Y([{title:"Total Payments Received",current:"0",last:"0"},{title:"Total Number of Failed Payments",current:"0",last:"0"},{title:"Total Number of Ratepayers",current:"0",last:"0"},{title:"Total Amount in Arrears",current:"0",last:"0"}]),r=Y([{title:"Total Active Registrations",current:"0",last:"0"},{title:"Total Active Email Registrations",current:"0",last:"0"},{title:"Total SMS Email Registrations",current:"0",last:"0"},{title:"Total Refunds",current:"0",last:"0"}]),e=Y(new Date),l=Y(new Date),i=Y(new Date),n=Y(new Date().getFullYear()),o=Y([]),g=Y(!0),h=Y(!0),c=Y(!0),m=Y(),f=Y(),d=Y(),u=Y(),y=Y(),b=Y();let x=null,_=null,D=null,T=null,L=null,C=null;const I=S=>{if(!f.value)return;const v=[],s=[];for(const M in S)v.push(re(M).format("D")),s.push(S[M]);x=ue(f.value);const w={grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},xAxis:{type:"category",data:v,axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:{type:"value",axisLine:{show:!1},splitLine:{lineStyle:{color:"#f0f0f0"}}},series:[{type:"line",data:s,smooth:!0,lineStyle:{color:"#1890ff"},itemStyle:{color:"#1890ff"},areaStyle:{color:new Rt(0,0,0,1,[{offset:0,color:"rgba(24, 144, 255, 0.2)"},{offset:1,color:"rgba(24, 144, 255, 0.05)"}])}}]};x.setOption(w)},A=S=>{if(!d.value)return;const v=[],s=[String(n.value-1),String(n.value)],w={current:[],last:[]};for(const X in S.current)v.push(re(X).format("MMM")),w.current.push(Number(S.current[X]));for(const X in S.last)w.last.push(Number(S.last[X]));_=ue(d.value);const M={grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},legend:{data:s,top:0,right:0},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:v},yAxis:{type:"value"},series:[{name:s[0],type:"bar",data:w.last,itemStyle:{color:"#1890ff"}},{name:s[1],type:"bar",data:w.current,itemStyle:{color:"#52c41a"}}]};_.setOption(M)},N=(S,v,s)=>{if(!u.value)return;D=ue(u.value);const w={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {d}%"},legend:{orient:"vertical",left:"left",top:"middle",itemGap:12,textStyle:{fontSize:12,color:"#666"},data:["Active","Inactive","Cancelled"]},series:[{name:"Registrations",type:"pie",radius:["0%","70%"],center:["65%","50%"],avoidLabelOverlap:!1,label:{show:!0,position:"outside",formatter:"{d}%",fontSize:12,color:"#666",fontWeight:"normal"},labelLine:{show:!0,length:8,length2:5},data:[{value:S,name:"Active",itemStyle:{color:"#B8D432"}},{value:v,name:"Inactive",itemStyle:{color:"#FF7043"}},{value:s,name:"Cancelled",itemStyle:{color:"#E53E3E"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.3)"}}}]};D.setOption(w)},z=()=>{if(!y.value)return;T=ue(y.value);const S={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {d}%"},legend:{orient:"vertical",left:"left",top:"middle",itemGap:12,textStyle:{fontSize:12,color:"#666"},data:["Type A","Type B","Type C","Type D"]},series:[{name:"Properties",type:"pie",radius:["0%","70%"],center:["65%","50%"],avoidLabelOverlap:!1,label:{show:!0,position:"outside",formatter:"{d}%",fontSize:12,color:"#666",fontWeight:"normal"},labelLine:{show:!0,length:8,length2:5},data:[{value:35,name:"Type A",itemStyle:{color:"#B8D432"}},{value:25,name:"Type B",itemStyle:{color:"#1E88E5"}},{value:25,name:"Type C",itemStyle:{color:"#FF7043"}},{value:15,name:"Type D",itemStyle:{color:"#E53E3E"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.3)"}}}]};T.setOption(S)},H=()=>{if(!b.value)return;L=ue(b.value);const S={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {d}%"},legend:{orient:"vertical",left:"left",top:"middle",itemGap:12,textStyle:{fontSize:12,color:"#666"},data:["Successful","Pending","Failed"]},series:[{name:"Payments",type:"pie",radius:["0%","70%"],center:["65%","50%"],avoidLabelOverlap:!1,label:{show:!0,position:"outside",formatter:"{d}%",fontSize:12,color:"#666",fontWeight:"normal"},labelLine:{show:!0,length:8,length2:5},data:[{value:53.1,name:"Successful",itemStyle:{color:"#B8D432"}},{value:27.6,name:"Pending",itemStyle:{color:"#1E88E5"}},{value:19.3,name:"Failed",itemStyle:{color:"#E53E3E"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.3)"}}}]};L.setOption(S)},F=()=>{x==null||x.resize(),_==null||_.resize(),D==null||D.resize(),T==null||T.resize(),L==null||L.resize()},G=()=>{m.value&&(C=new ResizeObserver(S=>{for(const v of S)v.target===m.value&&F()}),C.observe(m.value))},K=async()=>{l.value=new Date(e.value),await E()},E=async()=>{g.value=!0;try{const{code:S,data:v}=await Xt({month:re(l.value).format("YYYY-MM")}),s=(w,M)=>({title:M.title,current:w.current,last:w.last});S===0&&(a.value[0]=s(v.total_amount_received,a.value[0]),a.value[1]=s(v.total_number_failed,a.value[1]),a.value[2]=s(v.total_number_registered,a.value[2]),a.value[3]=s(v.total_amount_arrears,a.value[3]),r.value[0]=s(v.total_active_registered,r.value[0]),r.value[1]=s(v.total_self_registered,r.value[1]),r.value[2]=s(v.total_merchant_registered,r.value[2]),r.value[3]=s(v.total_number_registered,r.value[3]),Le(()=>{N(v.registered_count.active,v.registered_count.inactive,v.registered_count.cancelled)}))}finally{g.value=!1}},U=async()=>{h.value=!0;try{const{code:S,data:v}=await jt({month:re(i.value).format("YYYY-MM")});S===0&&Le(()=>{I(v)})}finally{h.value=!1}},V=async()=>{c.value=!0;try{const{code:S,data:v}=await qt({year:String(n.value)});S===0&&Le(()=>{A(v)})}finally{c.value=!1}};return ct(()=>{for(let S=2023;S<new Date().getFullYear();S++)o.value.push({label:`${S}-${S+1}`,value:S+1});E(),U(),V(),setTimeout(()=>{z(),H(),G()},100),window.addEventListener("resize",F)}),vt(()=>{window.removeEventListener("resize",F),C==null||C.disconnect()}),(S,v)=>(Z(),W("div",{ref_key:"reportOverviewRef",ref:m,class:"report-overview"},[p("div",da,[p("div",ga,[p("div",fa,[v[3]||(v[3]=p("h1",null,"Overview",-1)),p("span",ha,"as of "+j(P(re)(l.value).format("MMMM YYYY")),1)]),p("div",pa,[$(P(Fe),{modelValue:e.value,"onUpdate:modelValue":v[0]||(v[0]=s=>e.value=s),placeholder:"Select Date",class:"date-picker","date-format":"mm/yy",view:"month","max-date":new Date},null,8,["modelValue","max-date"]),$(P(_e),{label:"SEARCH",class:"w-40",severity:"info",onClick:K}),$(P(_e),{label:"EXPORT",class:"w-40"})])]),p("div",ma,[g.value?(Z(),W(he,{key:0},pe(4,s=>p("div",{key:`skeleton-${s}`,class:"overview-card"},[p("div",ya,[$(P(Q),{height:"1.2rem",width:"70%",class:"mb-3"}),$(P(Q),{height:"2.5rem",width:"60%",class:"mb-2"}),p("div",ba,[$(P(Q),{height:"1rem",width:"40%"}),$(P(Q),{height:"1rem",width:"30%"})])])])),64)):(Z(!0),W(he,{key:1},pe(a.value,(s,w)=>(Z(),W("div",{key:w,class:"overview-card"},[p("div",Sa,[p("h3",null,j(s.title),1),p("div",wa,j(typeof s.current=="string"?P(Be).formatAmount(P(fe)(Number(s.current)||0).toFixed(2)):s.current),1),p("div",Aa,[p("span",null,[p("span",{class:me(["arrow",[s.current>s.last?"positive":"negative"]])},j(s.current>s.last?"↗":"↘"),3),p("span",{class:me(["change ml-4",[s.current>s.last?"positive":"negative"]])},j(Number(s==null?void 0:s.last)===0?Number(s==null?void 0:s.current)>0?"100.00":"0.00":P(fe)(Number(s==null?void 0:s.current)||0).minus(Number(s==null?void 0:s.last)||0).div(Number(s==null?void 0:s.last)||0).mul(100).toFixed(2))+"% ",3)]),p("span",xa,"as of "+j(P(re)(l.value).format("MMMM YYYY")),1)])])]))),128))])]),p("div",Da,[p("div",_a,[p("div",La,[p("div",Ma,[v[4]||(v[4]=p("h3",null,"Monthly",-1)),$(P(Fe),{modelValue:i.value,"onUpdate:modelValue":v[1]||(v[1]=s=>i.value=s),placeholder:"Select Date",class:"date-picker","date-format":"mm/yy",view:"month","max-date":new Date,onValueChange:U},null,8,["modelValue","max-date"])]),p("div",{ref_key:"monthlyChartRef",ref:f,class:"chart"},null,512)]),p("div",Ta,[p("div",Ca,[v[5]||(v[5]=p("h3",null,"Annual",-1)),$(P(dt),{modelValue:n.value,"onUpdate:modelValue":v[2]||(v[2]=s=>n.value=s),options:o.value,"option-label":"label","option-value":"value",class:"chart-dropdown",onValueChange:V},null,8,["modelValue","options"])]),p("div",{ref_key:"annualChartRef",ref:d,class:"chart"},null,512)])]),p("div",Na,[p("div",Pa,[v[6]||(v[6]=p("h3",null,"Registrations",-1)),p("div",{ref_key:"registrationsChartRef",ref:u,class:"pie-chart"},null,512),g.value?(Z(),W("div",Oa)):ut("",!0)]),p("div",Ia,[v[7]||(v[7]=p("h3",null,"Properties (Coming Soon)",-1)),p("div",{ref_key:"propertiesChartRef",ref:y,class:"pie-chart"},null,512)]),p("div",ka,[v[8]||(v[8]=p("h3",null,"Payments (Coming Soon)",-1)),p("div",{ref_key:"paymentsChartRef",ref:b,class:"pie-chart"},null,512),v[9]||(v[9]=p("div",{class:"pie-total"}," Total: $0.00 ",-1))])])]),p("div",Ra,[p("div",Ea,[$(P(_e),{label:"DOWNLOAD",severity:"info",class:"w-40"})]),p("div",Va,[g.value?(Z(),W("div",Ya,[(Z(),W(he,null,pe(4,s=>p("div",{key:`bottom-skeleton-${s}`,class:"stat-card"},[$(P(Q),{height:"1.2rem",width:"70%",class:"mb-3"}),$(P(Q),{height:"2.5rem",width:"60%",class:"mb-2"}),p("div",za,[$(P(Q),{height:"1rem",width:"40%"}),$(P(Q),{height:"1rem",width:"30%"})])])),64))])):(Z(!0),W(he,{key:1},pe(r.value,(s,w)=>(Z(),W("div",{key:w,class:"stat-card"},[p("h3",null,j(s.title),1),p("div",Ba,j(P(Be).formatAmount(P(fe)(Number(s.current)||0).toFixed(2))),1),p("div",Fa,[p("span",null,[p("span",{class:me(["arrow",[s.current>s.last?"positive":"negative"]])},j(s.current>s.last?"↗":"↘"),3),p("span",{class:me(["change ml-4",[s.current>s.last?"positive":"negative"]])},j(Number(s==null?void 0:s.last)===0?Number(s==null?void 0:s.current)>0?"100.00":"0.00":P(fe)(Number(s==null?void 0:s.current)||0).minus(Number(s==null?void 0:s.last)||0).div(Number(s==null?void 0:s.last)||0).mul(100).toFixed(2))+"% ",3)]),p("span",Ga,"as of "+j(P(re)(l.value).format("MMMM YYYY")),1)])]))),128))])])],512))}}),lr=Ut($a,[["__scopeId","data-v-a11aa24a"]]);export{lr as default};
