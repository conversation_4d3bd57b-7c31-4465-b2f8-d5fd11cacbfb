import{d as D,r as S,f as j,a as x,o as P,b as r,t as I,p as $,u as O,e as L,s as N}from"./index-DWWVg5mm.js";import{_ as U,b as F,a as M}from"./Phone-dOHlUqSP.js";import{u as T,P as q}from"./usePermissions-DgTRxXRz.js";import{a as B}from"./support-Dzr7PNkO.js";import{_ as K}from"./_plugin-vue_export-helper-DlAUqK2U.js";function G(n,e,t,o){function a(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function c(l){try{h(o.next(l))}catch(p){s(p)}}function d(l){try{h(o.throw(l))}catch(p){s(p)}}function h(l){l.done?i(l.value):a(l.value).then(c,d)}h((o=o.apply(n,[])).next())})}function V(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Z=function n(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var o,a,i;if(Array.isArray(e)){if(o=e.length,o!=t.length)return!1;for(a=o;a--!==0;)if(!n(e[a],t[a]))return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(i=Object.keys(e),o=i.length,o!==Object.keys(t).length)return!1;for(a=o;a--!==0;)if(!Object.prototype.hasOwnProperty.call(t,i[a]))return!1;for(a=o;a--!==0;){var s=i[a];if(!n(e[s],t[s]))return!1}return!0}return e!==e&&t!==t},z=V(Z);const A="__googleMapsScriptId";var f;(function(n){n[n.INITIALIZED=0]="INITIALIZED",n[n.LOADING=1]="LOADING",n[n.SUCCESS=2]="SUCCESS",n[n.FAILURE=3]="FAILURE"})(f||(f={}));class u{constructor({apiKey:e,authReferrerPolicy:t,channel:o,client:a,id:i=A,language:s,libraries:c=[],mapIds:d,nonce:h,region:l,retries:p=3,url:y="https://maps.googleapis.com/maps/api/js",version:m}){if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=e,this.authReferrerPolicy=t,this.channel=o,this.client=a,this.id=i||A,this.language=s,this.libraries=c,this.mapIds=d,this.nonce=h,this.region=l,this.retries=p,this.url=y,this.version=m,u.instance){if(!z(this.options,u.instance.options))throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(u.instance.options)}`);return u.instance}u.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?f.FAILURE:this.done?f.SUCCESS:this.loading?f.LOADING:f.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){let e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+=`&key=${this.apiKey}`),this.channel&&(e+=`&channel=${this.channel}`),this.client&&(e+=`&client=${this.client}`),this.libraries.length>0&&(e+=`&libraries=${this.libraries.join(",")}`),this.language&&(e+=`&language=${this.language}`),this.region&&(e+=`&region=${this.region}`),this.version&&(e+=`&v=${this.version}`),this.mapIds&&(e+=`&map_ids=${this.mapIds.join(",")}`),this.authReferrerPolicy&&(e+=`&auth_referrer_policy=${this.authReferrerPolicy}`),e}deleteScript(){const e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise((e,t)=>{this.loadCallback(o=>{o?t(o.error):e(window.google)})})}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){var e,t;if(document.getElementById(this.id)){this.callback();return}const o={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(o).forEach(i=>!o[i]&&delete o[i]),!((t=(e=window==null?void 0:window.google)===null||e===void 0?void 0:e.maps)===null||t===void 0)&&t.importLibrary||(i=>{let s,c,d,h="The Google Maps JavaScript API",l="google",p="importLibrary",y="__ib__",m=document,g=window;g=g[l]||(g[l]={});const v=g.maps||(g.maps={}),k=new Set,b=new URLSearchParams,C=()=>s||(s=new Promise((_,w)=>G(this,void 0,void 0,function*(){var E;yield c=m.createElement("script"),c.id=this.id,b.set("libraries",[...k]+"");for(d in i)b.set(d.replace(/[A-Z]/g,R=>"_"+R[0].toLowerCase()),i[d]);b.set("callback",l+".maps."+y),c.src=this.url+"?"+b,v[y]=_,c.onerror=()=>s=w(Error(h+" could not load.")),c.nonce=this.nonce||((E=m.querySelector("script[nonce]"))===null||E===void 0?void 0:E.nonce)||"",m.head.append(c)})));v[p]?console.warn(h+" only loads once. Ignoring:",i):v[p]=(_,...w)=>k.add(_)&&C().then(()=>v[p](_,...w))})(o);const a=this.libraries.map(i=>this.importLibrary(i));a.length||a.push(this.importLibrary("core")),Promise.all(a).then(()=>this.callback(),i=>{const s=new ErrorEvent("error",{error:i});this.loadErrorCallback(s)})}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){const t=this.errors.length*Math.pow(2,this.errors.length);console.error(`Failed to load Google Maps script, retrying in ${t} ms.`),setTimeout(()=>{this.deleteScript(),this.setScript()},t)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach(e=>{e(this.onerrorEvent)}),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version){console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),this.callback();return}this.loading=!0,this.setScript()}}}const J={class:"flexirates-wrap"},W={class:"mt-4"},X={class:"message flex flex-col gap-2"},H={class:"flex items-center"},Y={class:"message-content"},Q={class:"flex items-center"},ee={class:"message-content"},te={class:"flex items-center"},se={class:"message-content"},ie={class:"cancel"},re={key:0},oe=D({__name:"index",setup(n){const e=S(null),t=S(),{hasPermission:o}=T(),a=async()=>{const i=new u({apiKey:"AIzaSyDaX67ixHeBCcXYyEeKUy8qEzWahD85qDk",version:"weekly",libraries:["places","marker"]});try{await i.importLibrary("maps").then(({Map:s})=>{e.value=new s(document.getElementById("map"),{center:{lat:-37.8005294,lng:144.9932422},zoom:15})})}catch(s){console.error("加载Google Maps失败:",s)}};return j(()=>{Promise.all([B().then(i=>{t.value=i.data}),a()])}),(i,s)=>{var d,h,l;const c=N;return P(),x("div",J,[s[5]||(s[5]=r("div",{class:"flexirates-title"},[r("div",{class:"flexirates-title-text"}," Need help? ")],-1)),r("div",W,[r("div",X,[r("div",H,[s[1]||(s[1]=r("div",{class:"flex items-center gap-12"},[r("img",{src:U,alt:""}),r("div",{class:"message-label"}," Phone: ")],-1)),r("div",Y,I((d=t.value)==null?void 0:d.phone),1)]),r("div",Q,[s[2]||(s[2]=r("div",{class:"flex items-center gap-12"},[r("img",{src:F,alt:""}),r("div",{class:"message-label"}," Email: ")],-1)),r("div",ee,I((h=t.value)==null?void 0:h.email),1)]),r("div",te,[s[3]||(s[3]=r("div",{class:"flex items-center gap-12"},[r("img",{src:M,alt:""}),r("div",{class:"message-label"}," Office Address: ")],-1)),r("div",se,I((l=t.value)==null?void 0:l.office_address),1)])])]),s[6]||(s[6]=r("div",{class:"mt-6"},[r("div",{id:"map",class:"map"})],-1)),r("div",ie,[s[4]||(s[4]=r("div",{class:"w-2/3"},[r("div",{class:"text-xl font-bold"}," I'd like to cancel my payment arrangement. "),r("div",null," The below button will take you to a page to cancel your existing FlexiRates arrangment online. After a cancellation, you will need to resume payments by the quarterly instalments, unless you set up a new arrangement (on FlexiRates or by calling us). ")],-1)),O(o)(O(q).SUPPORT_DETAIL)?(P(),x("div",re,[L(c,{label:"VIEW ALL",class:"support-btn",onClick:s[0]||(s[0]=p=>i.$router.push({name:"supportCancellation"}))})])):$("",!0)])])}}}),de=K(oe,[["__scopeId","data-v-02e67bde"]]);export{de as default};
