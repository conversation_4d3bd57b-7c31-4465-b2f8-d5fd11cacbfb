import{d as _,r as m,f as g,g as f,a as b,b as s,h,t as e,e as l,w as d,u as c,s as p,n as k,i as B,j as v,o as N}from"./index-DWWVg5mm.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const w={class:"not-found"},y={class:"content"},C={class:"error-container"},I={class:"error-message"},V={class:"error-description"},x={class:"actions"},H={class:"progress-container"},S={class:"progress-text"},T={class:"progress-bar"},D=_({__name:"index",setup(F){const i=B(),r=()=>{i.push({path:"/"})},u=()=>{i.back()},n=m(0);let t=null;return g(()=>{t=setInterval(()=>{n.value+=1,n.value>=100&&(t&&(clearInterval(t),t=null),r())},50)}),f(()=>{t&&(clearInterval(t),t=null)}),(o,a)=>(N(),b("div",w,[s("div",y,[s("div",C,[a[2]||(a[2]=h('<div class="error-code" data-v-33026264><span class="digit" data-v-33026264>4</span><div class="planet-container" data-v-33026264><div class="planet" data-v-33026264></div><div class="orbit" data-v-33026264><div class="satellite" data-v-33026264></div></div></div><span class="digit" data-v-33026264>4</span></div>',1)),s("h2",I,e(o.$t("message.pageNotFound")),1),s("p",V,e(o.$t("message.pageNotFoundDescription")),1),s("div",x,[l(c(p),{class:"p-button-primary go-back-btn",onClick:u},{default:d(()=>[a[0]||(a[0]=s("i",{class:"pi pi-arrow-left"},null,-1)),v(" "+e(o.$t("button.goBack")),1)]),_:1,__:[0]}),l(c(p),{class:"p-button-outlined home-btn",onClick:r},{default:d(()=>[a[1]||(a[1]=s("i",{class:"pi pi-home"},null,-1)),v(" "+e(o.$t("button.backToHome")),1)]),_:1,__:[1]})]),s("div",H,[s("div",S,e(o.$t("message.redirectingToHome"))+" "+e(n.value)+"% ",1),s("div",T,[s("div",{class:"progress-fill",style:k({width:`${n.value}%`})},null,4)])])])])]))}}),E=$(D,[["__scopeId","data-v-33026264"]]);export{E as default};
