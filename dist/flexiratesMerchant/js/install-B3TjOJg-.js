import{_ as F,W as We,t as E,T as Fe,X as tt,Y as le,$ as ge,v as rt,j as Q,a0 as Ge,a1 as at,a2 as nt,A as q,B as Y,E as it,a3 as st,z as pe,a4 as Ye,a5 as ot,a6 as N,C as ut,a7 as ze,x as Ue,a8 as Ne,y as V,I as ft,H as lt,a9 as vt,aa as ht,G as dt,F as ct,ab as gt,ac as pt,N as mt,ad as yt,ae as xt,af as At,ag as p,g as ve,ah as U,ai as X,aj as St,ak as he,al as ee,am as Xe,an as He,M as bt,ao as _t,ap as Ct,aq as Ze,ar as wt,as as Tt,at as Dt,au as Lt,av as Rt,aw as me,ax as H,Z as se,ay as Pt,p as ye,az as Bt,aA as Et,aB as Mt,r as It,aC as kt,aD as Vt,aE as Wt}from"./installCanvasRenderer-CM5zVs_H.js";var oe=function(t){F(e,t);function e(){var r=t!==null&&t.apply(this,arguments)||this;return r.type=e.type,r}return e.prototype.getInitialData=function(r,a){return We(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(r,a,n){var i=this.coordinateSystem;if(i&&i.clampData){var s=i.clampData(r),f=i.dataToPoint(s);if(n)E(i.getAxes(),function(h,d){if(h.type==="category"&&a!=null){var c=h.getTicksCoords(),g=h.getTickModel().get("alignWithLabel"),m=s[d],y=a[d]==="x1"||a[d]==="y1";if(y&&!g&&(m+=1),c.length<2)return;if(c.length===2){f[d]=h.toGlobalCoord(h.getExtent()[y?1:0]);return}for(var A=void 0,S=void 0,w=1,_=0;_<c.length;_++){var C=c[_].coord,b=_===c.length-1?c[_-1].tickValue+w:c[_].tickValue;if(b===m){S=C;break}else if(b<m)A=C;else if(A!=null&&b>m){S=(C+A)/2;break}_===1&&(w=b-c[0].tickValue)}S==null&&(A?A&&(S=c[c.length-1].coord):S=c[0].coord),f[d]=h.toGlobalCoord(S)}});else{var o=this.getData(),l=o.getLayout("offset"),u=o.getLayout("size"),v=i.getBaseAxis().isHorizontal()?0:1;f[v]+=l+u/2}return f}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(Fe);Fe.registerClass(oe);var Ft=function(t){F(e,t);function e(){var r=t!==null&&t.apply(this,arguments)||this;return r.type=e.type,r}return e.prototype.getInitialData=function(){return We(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},e.prototype.getProgressiveThreshold=function(){var r=this.get("progressiveThreshold"),a=this.get("largeThreshold");return a>r&&(r=a),r},e.prototype.brushSelector=function(r,a,n){return n.rect(a.getItemLayout(r))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=tt(oe.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),e}(oe),Gt=function(){function t(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return t}(),xe=function(t){F(e,t);function e(r){var a=t.call(this,r)||this;return a.type="sausage",a}return e.prototype.getDefaultShape=function(){return new Gt},e.prototype.buildPath=function(r,a){var n=a.cx,i=a.cy,s=Math.max(a.r0||0,0),f=Math.max(a.r,0),o=(f-s)*.5,l=s+o,u=a.startAngle,v=a.endAngle,h=a.clockwise,d=Math.PI*2,c=h?v-u<d:u-v<d;c||(u=v-(h?d:-d));var g=Math.cos(u),m=Math.sin(u),y=Math.cos(v),A=Math.sin(v);c?(r.moveTo(g*s+n,m*s+i),r.arc(g*l+n,m*l+i,o,-Math.PI+u,u,!h)):r.moveTo(g*f+n,m*f+i),r.arc(n,i,f,u,v,!h),r.arc(y*l+n,A*l+i,o,v-Math.PI*2,v-Math.PI,!h),s!==0&&r.arc(n,i,s,v,u,h)},e}(le);function Yt(t,e){e=e||{};var r=e.isRoundCap;return function(a,n,i){var s=n.position;if(!s||s instanceof Array)return ge(a,n,i);var f=t(s),o=n.distance!=null?n.distance:5,l=this.shape,u=l.cx,v=l.cy,h=l.r,d=l.r0,c=(h+d)/2,g=l.startAngle,m=l.endAngle,y=(g+m)/2,A=r?Math.abs(h-d)/2:0,S=Math.cos,w=Math.sin,_=u+h*S(g),C=v+h*w(g),b="left",T="top";switch(f){case"startArc":_=u+(d-o)*S(y),C=v+(d-o)*w(y),b="center",T="top";break;case"insideStartArc":_=u+(d+o)*S(y),C=v+(d+o)*w(y),b="center",T="bottom";break;case"startAngle":_=u+c*S(g)+Z(g,o+A,!1),C=v+c*w(g)+$(g,o+A,!1),b="right",T="middle";break;case"insideStartAngle":_=u+c*S(g)+Z(g,-o+A,!1),C=v+c*w(g)+$(g,-o+A,!1),b="left",T="middle";break;case"middle":_=u+c*S(y),C=v+c*w(y),b="center",T="middle";break;case"endArc":_=u+(h+o)*S(y),C=v+(h+o)*w(y),b="center",T="bottom";break;case"insideEndArc":_=u+(h-o)*S(y),C=v+(h-o)*w(y),b="center",T="top";break;case"endAngle":_=u+c*S(m)+Z(m,o+A,!0),C=v+c*w(m)+$(m,o+A,!0),b="left",T="middle";break;case"insideEndAngle":_=u+c*S(m)+Z(m,-o+A,!0),C=v+c*w(m)+$(m,-o+A,!0),b="right",T="middle";break;default:return ge(a,n,i)}return a=a||{},a.x=_,a.y=C,a.align=b,a.verticalAlign=T,a}}function zt(t,e,r,a){if(rt(a)){t.setTextConfig({rotation:a});return}else if(Q(e)){t.setTextConfig({rotation:0});return}var n=t.shape,i=n.clockwise?n.startAngle:n.endAngle,s=n.clockwise?n.endAngle:n.startAngle,f=(i+s)/2,o,l=r(e);switch(l){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":o=f;break;case"startAngle":case"insideStartAngle":o=i;break;case"endAngle":case"insideEndAngle":o=s;break;default:t.setTextConfig({rotation:0});return}var u=Math.PI*1.5-o;l==="middle"&&u>Math.PI/2&&u<Math.PI*1.5&&(u-=Math.PI),t.setTextConfig({rotation:u})}function Z(t,e,r){return e*Math.sin(t)*(r?-1:1)}function $(t,e,r){return e*Math.cos(t)*(r?1:-1)}function Ut(t,e,r){var a=t.get("borderRadius");if(a==null)return r?{cornerRadius:0}:null;Q(a)||(a=[a,a,a,a]);var n=Math.abs(e.r||0-e.r0||0);return{cornerRadius:Ge(a,function(i){return at(i,n)})}}var ae=Math.max,ne=Math.min;function Nt(t,e){var r=t.getArea&&t.getArea();if(ze(t,"cartesian2d")){var a=t.getBaseAxis();if(a.type!=="category"||!a.onBand){var n=e.getLayout("bandWidth");a.isHorizontal()?(r.x-=n,r.width+=n*2):(r.y-=n,r.height+=n*2)}}return r}var Xt=function(t){F(e,t);function e(){var r=t.call(this)||this;return r.type=e.type,r._isFirstFrame=!0,r}return e.prototype.render=function(r,a,n,i){this._model=r,this._removeOnRenderedListener(n),this._updateDrawMode(r);var s=r.get("coordinateSystem");(s==="cartesian2d"||s==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(r,a,n):this._renderNormal(r,a,n,i))},e.prototype.incrementalPrepareRender=function(r){this._clear(),this._updateDrawMode(r),this._updateLargeClip(r)},e.prototype.incrementalRender=function(r,a){this._progressiveEls=[],this._incrementalRenderLarge(r,a)},e.prototype.eachRendered=function(r){nt(this._progressiveEls||this.group,r)},e.prototype._updateDrawMode=function(r){var a=r.pipelineContext.large;(this._isLargeDraw==null||a!==this._isLargeDraw)&&(this._isLargeDraw=a,this._clear())},e.prototype._renderNormal=function(r,a,n,i){var s=this.group,f=r.getData(),o=this._data,l=r.coordinateSystem,u=l.getBaseAxis(),v;l.type==="cartesian2d"?v=u.isHorizontal():l.type==="polar"&&(v=u.dim==="angle");var h=r.isAnimationEnabled()?r:null,d=Ht(r,l);d&&this._enableRealtimeSort(d,f,n);var c=r.get("clip",!0)||d,g=Nt(l,f);s.removeClipPath();var m=r.get("roundCap",!0),y=r.get("showBackground",!0),A=r.getModel("backgroundStyle"),S=A.get("borderRadius")||0,w=[],_=this._backgroundEls,C=i&&i.isInitSort,b=i&&i.type==="changeAxisOrder";function T(x){var P=O[l.type](f,x),D=Jt(l,v,P);return D.useStyle(A.getItemStyle()),l.type==="cartesian2d"?D.setShape("r",S):D.setShape("cornerRadius",S),w[x]=D,D}f.diff(o).add(function(x){var P=f.getItemModel(x),D=O[l.type](f,x,P);if(y&&T(x),!(!f.hasValue(x)||!Ce[l.type](D))){var M=!1;c&&(M=Ae[l.type](g,D));var R=Se[l.type](r,f,x,D,v,h,u.model,!1,m);d&&(R.forceLabelAnimation=!0),we(R,f,x,P,D,r,v,l.type==="polar"),C?R.attr({shape:D}):d?be(d,h,R,D,x,v,!1,!1):q(R,{shape:D},r,x),f.setItemGraphicEl(x,R),s.add(R),R.ignore=M}}).update(function(x,P){var D=f.getItemModel(x),M=O[l.type](f,x,D);if(y){var R=void 0;_.length===0?R=T(P):(R=_[P],R.useStyle(A.getItemStyle()),l.type==="cartesian2d"?R.setShape("r",S):R.setShape("cornerRadius",S),w[x]=R);var Qe=O[l.type](f,x),et=Oe(v,Qe,l);Y(R,{shape:et},h,x)}var B=o.getItemGraphicEl(P);if(!f.hasValue(x)||!Ce[l.type](M)){s.remove(B);return}var te=!1;if(c&&(te=Ae[l.type](g,M),te&&s.remove(B)),B?it(B):B=Se[l.type](r,f,x,M,v,h,u.model,!!B,m),d&&(B.forceLabelAnimation=!0),b){var ce=B.getTextContent();if(ce){var re=st(ce);re.prevValue!=null&&(re.prevValue=re.value)}}else we(B,f,x,D,M,r,v,l.type==="polar");C?B.attr({shape:M}):d?be(d,h,B,M,x,v,!0,b):Y(B,{shape:M},r,x,null),f.setItemGraphicEl(x,B),B.ignore=te,s.add(B)}).remove(function(x){var P=o.getItemGraphicEl(x);P&&pe(P,r,x)}).execute();var L=this._backgroundGroup||(this._backgroundGroup=new Ye);L.removeAll();for(var G=0;G<w.length;++G)L.add(w[G]);s.add(L),this._backgroundEls=w,this._data=f},e.prototype._renderLarge=function(r,a,n){this._clear(),De(r,this.group),this._updateLargeClip(r)},e.prototype._incrementalRenderLarge=function(r,a){this._removeBackground(),De(a,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(r){var a=r.get("clip",!0)&&ot(r.coordinateSystem,!1,r),n=this.group;a?n.setClipPath(a):n.removeClipPath()},e.prototype._enableRealtimeSort=function(r,a,n){var i=this;if(a.count()){var s=r.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(a,r,n),this._isFirstFrame=!1;else{var f=function(o){var l=a.getItemGraphicEl(o),u=l&&l.shape;return u&&Math.abs(s.isHorizontal()?u.height:u.width)||0};this._onRendered=function(){i._updateSortWithinSameData(a,f,s,n)},n.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(r,a,n){var i=[];return r.each(r.mapDimension(a.dim),function(s,f){var o=n(f);o=o??NaN,i.push({dataIndex:f,mappedValue:o,ordinalNumber:s})}),i.sort(function(s,f){return f.mappedValue-s.mappedValue}),{ordinalNumbers:Ge(i,function(s){return s.ordinalNumber})}},e.prototype._isOrderChangedWithinSameData=function(r,a,n){for(var i=n.scale,s=r.mapDimension(n.dim),f=Number.MAX_VALUE,o=0,l=i.getOrdinalMeta().categories.length;o<l;++o){var u=r.rawIndexOf(s,i.getRawOrdinalNumber(o)),v=u<0?Number.MIN_VALUE:a(r.indexOfRawIndex(u));if(v>f)return!0;f=v}return!1},e.prototype._isOrderDifferentInView=function(r,a){for(var n=a.scale,i=n.getExtent(),s=Math.max(0,i[0]),f=Math.min(i[1],n.getOrdinalMeta().categories.length-1);s<=f;++s)if(r.ordinalNumbers[s]!==n.getRawOrdinalNumber(s))return!0},e.prototype._updateSortWithinSameData=function(r,a,n,i){if(this._isOrderChangedWithinSameData(r,a,n)){var s=this._dataSort(r,n,a);this._isOrderDifferentInView(s,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:s}))}},e.prototype._dispatchInitSort=function(r,a,n){var i=a.baseAxis,s=this._dataSort(r,i,function(f){return r.get(r.mapDimension(a.otherAxis.dim),f)});n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:s})},e.prototype.remove=function(r,a){this._clear(this._model),this._removeOnRenderedListener(a)},e.prototype.dispose=function(r,a){this._removeOnRenderedListener(a)},e.prototype._removeOnRenderedListener=function(r){this._onRendered&&(r.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(r){var a=this.group,n=this._data;r&&r.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(i){pe(i,r,N(i).dataIndex)})):a.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(ut),Ae={cartesian2d:function(t,e){var r=e.width<0?-1:1,a=e.height<0?-1:1;r<0&&(e.x+=e.width,e.width=-e.width),a<0&&(e.y+=e.height,e.height=-e.height);var n=t.x+t.width,i=t.y+t.height,s=ae(e.x,t.x),f=ne(e.x+e.width,n),o=ae(e.y,t.y),l=ne(e.y+e.height,i),u=f<s,v=l<o;return e.x=u&&s>n?f:s,e.y=v&&o>i?l:o,e.width=u?0:f-s,e.height=v?0:l-o,r<0&&(e.x+=e.width,e.width=-e.width),a<0&&(e.y+=e.height,e.height=-e.height),u||v},polar:function(t,e){var r=e.r0<=e.r?1:-1;if(r<0){var a=e.r;e.r=e.r0,e.r0=a}var n=ne(e.r,t.r),i=ae(e.r0,t.r0);e.r=n,e.r0=i;var s=n-i<0;if(r<0){var a=e.r;e.r=e.r0,e.r0=a}return s}},Se={cartesian2d:function(t,e,r,a,n,i,s,f,o){var l=new Ne({shape:V({},a),z2:1});if(l.__dataIndex=r,l.name="item",i){var u=l.shape,v=n?"height":"width";u[v]=0}return l},polar:function(t,e,r,a,n,i,s,f,o){var l=!n&&o?xe:Ue,u=new l({shape:a,z2:1});u.name="item";var v=$e(n);if(u.calculateTextPosition=Yt(v,{isRoundCap:l===xe}),i){var h=u.shape,d=n?"r":"endAngle",c={};h[d]=n?a.r0:a.startAngle,c[d]=a[d],(f?Y:q)(u,{shape:c},i)}return u}};function Ht(t,e){var r=t.get("realtimeSort",!0),a=e.getBaseAxis();if(r&&a.type==="category"&&e.type==="cartesian2d")return{baseAxis:a,otherAxis:e.getOtherAxis(a)}}function be(t,e,r,a,n,i,s,f){var o,l;i?(l={x:a.x,width:a.width},o={y:a.y,height:a.height}):(l={y:a.y,height:a.height},o={x:a.x,width:a.width}),f||(s?Y:q)(r,{shape:o},e,n,null);var u=e?t.baseAxis.model:null;(s?Y:q)(r,{shape:l},u,n)}function _e(t,e){for(var r=0;r<e.length;r++)if(!isFinite(t[e[r]]))return!0;return!1}var Zt=["x","y","width","height"],$t=["cx","cy","r","startAngle","endAngle"],Ce={cartesian2d:function(t){return!_e(t,Zt)},polar:function(t){return!_e(t,$t)}},O={cartesian2d:function(t,e,r){var a=t.getItemLayout(e),n=r?Kt(r,a):0,i=a.width>0?1:-1,s=a.height>0?1:-1;return{x:a.x+i*n/2,y:a.y+s*n/2,width:a.width-i*n,height:a.height-s*n}},polar:function(t,e,r){var a=t.getItemLayout(e);return{cx:a.cx,cy:a.cy,r0:a.r0,r:a.r,startAngle:a.startAngle,endAngle:a.endAngle,clockwise:a.clockwise}}};function Ot(t){return t.startAngle!=null&&t.endAngle!=null&&t.startAngle===t.endAngle}function $e(t){return function(e){var r=e?"Arc":"Angle";return function(a){switch(a){case"start":case"insideStart":case"end":case"insideEnd":return a+r;default:return a}}}(t)}function we(t,e,r,a,n,i,s,f){var o=e.getItemVisual(r,"style");if(f){if(!i.get("roundCap")){var u=t.shape,v=Ut(a.getModel("itemStyle"),u,!0);V(u,v),t.setShape(u)}}else{var l=a.get(["itemStyle","borderRadius"])||0;t.setShape("r",l)}t.useStyle(o);var h=a.getShallow("cursor");h&&t.attr("cursor",h);var d=f?s?n.r>=n.r0?"endArc":"startArc":n.endAngle>=n.startAngle?"endAngle":"startAngle":s?n.height>=0?"bottom":"top":n.width>=0?"right":"left",c=ft(a);lt(t,c,{labelFetcher:i,labelDataIndex:r,defaultText:vt(i.getData(),r),inheritColor:o.fill,defaultOpacity:o.opacity,defaultOutsidePosition:d});var g=t.getTextContent();if(f&&g){var m=a.get(["label","position"]);t.textConfig.inside=m==="middle"?!0:null,zt(t,m==="outside"?d:m,$e(s),a.get(["label","rotate"]))}ht(g,c,i.getRawValue(r),function(A){return pt(e,A)});var y=a.getModel(["emphasis"]);dt(t,y.get("focus"),y.get("blurScope"),y.get("disabled")),ct(t,a),Ot(n)&&(t.style.fill="none",t.style.stroke="none",E(t.states,function(A){A.style&&(A.style.fill=A.style.stroke="none")}))}function Kt(t,e){var r=t.get(["itemStyle","borderColor"]);if(!r||r==="none")return 0;var a=t.get(["itemStyle","borderWidth"])||0,n=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),i=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(a,n,i)}var jt=function(){function t(){}return t}(),Te=function(t){F(e,t);function e(r){var a=t.call(this,r)||this;return a.type="largeBar",a}return e.prototype.getDefaultShape=function(){return new jt},e.prototype.buildPath=function(r,a){for(var n=a.points,i=this.baseDimIdx,s=1-this.baseDimIdx,f=[],o=[],l=this.barWidth,u=0;u<n.length;u+=3)o[i]=l,o[s]=n[u+2],f[i]=n[u+i],f[s]=n[u+s],r.rect(f[0],f[1],o[0],o[1])},e}(le);function De(t,e,r,a){var n=t.getData(),i=n.getLayout("valueAxisHorizontal")?1:0,s=n.getLayout("largeDataIndices"),f=n.getLayout("size"),o=t.getModel("backgroundStyle"),l=n.getLayout("largeBackgroundPoints");if(l){var u=new Te({shape:{points:l},incremental:!!a,silent:!0,z2:0});u.baseDimIdx=i,u.largeDataIndices=s,u.barWidth=f,u.useStyle(o.getItemStyle()),e.add(u),r&&r.push(u)}var v=new Te({shape:{points:n.getLayout("largePoints")},incremental:!!a,ignoreCoarsePointer:!0,z2:1});v.baseDimIdx=i,v.largeDataIndices=s,v.barWidth=f,e.add(v),v.useStyle(n.getVisual("style")),v.style.stroke=null,N(v).seriesIndex=t.seriesIndex,t.get("silent")||(v.on("mousedown",Le),v.on("mousemove",Le)),r&&r.push(v)}var Le=gt(function(t){var e=this,r=qt(e,t.offsetX,t.offsetY);N(e).dataIndex=r>=0?r:null},30,!1);function qt(t,e,r){for(var a=t.baseDimIdx,n=1-a,i=t.shape.points,s=t.largeDataIndices,f=[],o=[],l=t.barWidth,u=0,v=i.length/3;u<v;u++){var h=u*3;if(o[a]=l,o[n]=i[h+2],f[a]=i[h+a],f[n]=i[h+n],o[n]<0&&(f[n]+=o[n],o[n]=-o[n]),e>=f[0]&&e<=f[0]+o[0]&&r>=f[1]&&r<=f[1]+o[1])return s[u]}return-1}function Oe(t,e,r){if(ze(r,"cartesian2d")){var a=e,n=r.getArea();return{x:t?a.x:n.x,y:t?n.y:a.y,width:t?a.width:n.width,height:t?n.height:a.height}}else{var n=r.getArea(),i=e;return{cx:n.cx,cy:n.cy,r0:t?n.r0:i.r0,r:t?n.r:i.r,startAngle:t?i.startAngle:0,endAngle:t?i.endAngle:Math.PI*2}}}function Jt(t,e,r){var a=t.type==="polar"?Ue:Ne;return new a({shape:Oe(e,r,t),silent:!0,z2:0})}function br(t){t.registerChartView(Xt),t.registerSeriesModel(Ft),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,mt(At,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,yt("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,xt("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,r){var a=e.componentType||"series";r.eachComponent({mainType:a,query:e},function(n){e.sortInfo&&n.axis.setCategorySortInfo(e.sortInfo)})})}function Qt(t,e,r,a){return t&&(t.legacy||t.legacy!==!1&&!r&&!a&&e!=="tspan"&&(e==="text"||p(t,"text")))}function er(t,e,r){var a=t,n,i,s;if(e==="text")s=a;else{s={},p(a,"text")&&(s.text=a.text),p(a,"rich")&&(s.rich=a.rich),p(a,"textFill")&&(s.fill=a.textFill),p(a,"textStroke")&&(s.stroke=a.textStroke),p(a,"fontFamily")&&(s.fontFamily=a.fontFamily),p(a,"fontSize")&&(s.fontSize=a.fontSize),p(a,"fontStyle")&&(s.fontStyle=a.fontStyle),p(a,"fontWeight")&&(s.fontWeight=a.fontWeight),i={type:"text",style:s,silent:!0},n={};var f=p(a,"textPosition");n.position=f?a.textPosition:"inside",p(a,"textPosition")&&(n.position=a.textPosition),p(a,"textOffset")&&(n.offset=a.textOffset),p(a,"textRotation")&&(n.rotation=a.textRotation),p(a,"textDistance")&&(n.distance=a.textDistance)}return Re(s,t),E(s.rich,function(o){Re(o,o)}),{textConfig:n,textContent:i}}function Re(t,e){e&&(e.font=e.textFont||e.font,p(e,"textStrokeWidth")&&(t.lineWidth=e.textStrokeWidth),p(e,"textAlign")&&(t.align=e.textAlign),p(e,"textVerticalAlign")&&(t.verticalAlign=e.textVerticalAlign),p(e,"textLineHeight")&&(t.lineHeight=e.textLineHeight),p(e,"textWidth")&&(t.width=e.textWidth),p(e,"textHeight")&&(t.height=e.textHeight),p(e,"textBackgroundColor")&&(t.backgroundColor=e.textBackgroundColor),p(e,"textPadding")&&(t.padding=e.textPadding),p(e,"textBorderColor")&&(t.borderColor=e.textBorderColor),p(e,"textBorderWidth")&&(t.borderWidth=e.textBorderWidth),p(e,"textBorderRadius")&&(t.borderRadius=e.textBorderRadius),p(e,"textBoxShadowColor")&&(t.shadowColor=e.textBoxShadowColor),p(e,"textBoxShadowBlur")&&(t.shadowBlur=e.textBoxShadowBlur),p(e,"textBoxShadowOffsetX")&&(t.shadowOffsetX=e.textBoxShadowOffsetX),p(e,"textBoxShadowOffsetY")&&(t.shadowOffsetY=e.textBoxShadowOffsetY))}var Ke={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},Pe=U(Ke);Ct(X,function(t,e){return t[e]=1,t},{});X.join(", ");var J=["","style","shape","extra"],z=ve();function de(t,e,r,a,n){var i=t+"Animation",s=He(t,a,n)||{},f=z(e).userDuring;return s.duration>0&&(s.during=f?bt(sr,{el:e,userDuring:f}):null,s.setToFinal=!0,s.scope=t),V(s,r[i]),s}function ie(t,e,r,a){a=a||{};var n=a.dataIndex,i=a.isInit,s=a.clearStyle,f=r.isAnimationEnabled(),o=z(t),l=e.style;o.userDuring=e.during;var u={},v={};if(ur(t,e,v),Ee("shape",e,v),Ee("extra",e,v),!i&&f&&(or(t,e,u),Be("shape",t,e,u),Be("extra",t,e,u),fr(t,e,l,u)),v.style=l,rr(t,v,s),nr(t,e),f)if(i){var h={};E(J,function(c){var g=c?e[c]:e;g&&g.enterFrom&&(c&&(h[c]=h[c]||{}),V(c?h[c]:h,g.enterFrom))});var d=de("enter",t,e,r,n);d.duration>0&&t.animateFrom(h,d)}else ar(t,e,n||0,r,u);je(t,e),l?t.dirty():t.markRedraw()}function je(t,e){for(var r=z(t).leaveToProps,a=0;a<J.length;a++){var n=J[a],i=n?e[n]:e;i&&i.leaveTo&&(r||(r=z(t).leaveToProps={}),n&&(r[n]=r[n]||{}),V(n?r[n]:r,i.leaveTo))}}function tr(t,e,r,a){if(t){var n=t.parent,i=z(t).leaveToProps;if(i){var s=de("update",t,e,r,0);s.done=function(){n.remove(t)},t.animateTo(i,s)}else n.remove(t)}}function W(t){return t==="all"}function rr(t,e,r){var a=e.style;if(!t.isGroup&&a){if(r){t.useStyle({});for(var n=t.animators,i=0;i<n.length;i++){var s=n[i];s.targetName==="style"&&s.changeTarget(t.style)}}t.setStyle(a)}e&&(e.style=null,e&&t.attr(e),e.style=a)}function ar(t,e,r,a,n){if(n){var i=de("update",t,e,a,r);i.duration>0&&t.animateFrom(n,i)}}function nr(t,e){p(e,"silent")&&(t.silent=e.silent),p(e,"ignore")&&(t.ignore=e.ignore),t instanceof Xe&&p(e,"invisible")&&(t.invisible=e.invisible),t instanceof le&&p(e,"autoBatch")&&(t.autoBatch=e.autoBatch)}var k={},ir={setTransform:function(t,e){return k.el[t]=e,this},getTransform:function(t){return k.el[t]},setShape:function(t,e){var r=k.el,a=r.shape||(r.shape={});return a[t]=e,r.dirtyShape&&r.dirtyShape(),this},getShape:function(t){var e=k.el.shape;if(e)return e[t]},setStyle:function(t,e){var r=k.el,a=r.style;return a&&(a[t]=e,r.dirtyStyle&&r.dirtyStyle()),this},getStyle:function(t){var e=k.el.style;if(e)return e[t]},setExtra:function(t,e){var r=k.el.extra||(k.el.extra={});return r[t]=e,this},getExtra:function(t){var e=k.el.extra;if(e)return e[t]}};function sr(){var t=this,e=t.el;if(e){var r=z(e).userDuring,a=t.userDuring;if(r!==a){t.el=t.userDuring=null;return}k.el=e,a(ir)}}function Be(t,e,r,a){var n=r[t];if(n){var i=e[t],s;if(i){var f=r.transition,o=n.transition;if(o)if(!s&&(s=a[t]={}),W(o))V(s,i);else for(var l=he(o),u=0;u<l.length;u++){var v=l[u],h=i[v];s[v]=h}else if(W(f)||ee(f,t)>=0){!s&&(s=a[t]={});for(var d=U(i),u=0;u<d.length;u++){var v=d[u],h=i[v];lr(n[v],h)&&(s[v]=h)}}}}}function Ee(t,e,r){var a=e[t];if(a)for(var n=r[t]={},i=U(a),s=0;s<i.length;s++){var f=i[s];n[f]=St(a[f])}}function or(t,e,r){for(var a=e.transition,n=W(a)?X:he(a||[]),i=0;i<n.length;i++){var s=n[i];if(!(s==="style"||s==="shape"||s==="extra")){var f=t[s];r[s]=f}}}function ur(t,e,r){for(var a=0;a<Pe.length;a++){var n=Pe[a],i=Ke[n],s=e[n];s&&(r[i[0]]=s[0],r[i[1]]=s[1])}for(var a=0;a<X.length;a++){var f=X[a];e[f]!=null&&(r[f]=e[f])}}function fr(t,e,r,a){if(r){var n=t.style,i;if(n){var s=r.transition,f=e.transition;if(s&&!W(s)){var o=he(s);!i&&(i=a.style={});for(var l=0;l<o.length;l++){var u=o[l],v=n[u];i[u]=v}}else if(t.getAnimationStyleProps&&(W(f)||W(s)||ee(f,"style")>=0)){var h=t.getAnimationStyleProps(),d=h?h.style:null;if(d){!i&&(i=a.style={});for(var c=U(r),l=0;l<c.length;l++){var u=c[l];if(d[u]){var v=n[u];i[u]=v}}}}}}}function lr(t,e){return _t(t)?t!==e:t!=null&&isFinite(t)}var qe=ve(),vr=["percent","easing","shape","style","extra"];function hr(t){t.stopAnimation("keyframe"),t.attr(qe(t))}function ue(t,e,r){if(!(!r.isAnimationEnabled()||!e)){if(Q(e)){E(e,function(f){ue(t,f,r)});return}var a=e.keyframes,n=e.duration;if(r&&n==null){var i=He("enter",r,0);n=i&&i.duration}if(!(!a||!n)){var s=qe(t);E(J,function(f){if(!(f&&!t[f])){var o;a.sort(function(l,u){return l.percent-u.percent}),E(a,function(l){var u=t.animators,v=f?l[f]:l;if(v){var h=U(v);if(f||(h=Ze(h,function(g){return ee(vr,g)<0})),!!h.length){o||(o=t.animate(f,e.loop,!0),o.scope="keyframe");for(var d=0;d<u.length;d++)u[d]!==o&&u[d].targetName===o.targetName&&u[d].stopTracks(h);f&&(s[f]=s[f]||{});var c=f?s[f]:s;E(h,function(g){c[g]=((f?t[f]:t)||{})[g]}),o.whenWithKeys(n*l.percent,v,h,l.easing)}}}),o&&o.delay(e.delay||0).duration(n).start(e.easing)}})}}}function dr(t,e){var r=t.existing;if(e.id=t.keyInfo.id,!e.type&&r&&(e.type=r.type),e.parentId==null){var a=e.parentOption;a?e.parentId=a.id:r&&(e.parentId=r.parentId)}e.parentOption=null}function Me(t,e){var r;return E(e,function(a){t[a]!=null&&t[a]!=="auto"&&(r=!0)}),r}function cr(t,e,r){var a=V({},r),n=t[e],i=r.$action||"merge";i==="merge"?n?(Dt(n,a,!0),Lt(n,a,{ignoreSize:!0}),Rt(r,n),K(r,n),K(r,n,"shape"),K(r,n,"style"),K(r,n,"extra"),r.clipPath=n.clipPath):t[e]=a:i==="replace"?t[e]=a:i==="remove"&&n&&(t[e]=null)}var Je=["transition","enterFrom","leaveTo"],gr=Je.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function K(t,e,r){if(r&&(!t[r]&&e[r]&&(t[r]={}),t=t[r],e=e[r]),!(!t||!e))for(var a=r?Je:gr,n=0;n<a.length;n++){var i=a[n];t[i]==null&&e[i]!=null&&(t[i]=e[i])}}function pr(t,e){if(t&&(t.hv=e.hv=[Me(e,["left","right"]),Me(e,["top","bottom"])],t.type==="group")){var r=t,a=e;r.width==null&&(r.width=a.width=0),r.height==null&&(r.height=a.height=0)}}var mr=function(t){F(e,t);function e(){var r=t!==null&&t.apply(this,arguments)||this;return r.type=e.type,r.preventAutoZ=!0,r}return e.prototype.mergeOption=function(r,a){var n=this.option.elements;this.option.elements=null,t.prototype.mergeOption.call(this,r,a),this.option.elements=n},e.prototype.optionUpdated=function(r,a){var n=this.option,i=(a?n:r).elements,s=n.elements=a?[]:n.elements,f=[];this._flatten(i,f,null);var o=wt(s,f,"normalMerge"),l=this._elOptionsToUpdate=[];E(o,function(u,v){var h=u.newOption;h&&(l.push(h),dr(u,h),cr(s,v,h),pr(s[v],h))},this),n.elements=Ze(s,function(u){return u&&delete u.$action,u!=null})},e.prototype._flatten=function(r,a,n){E(r,function(i){if(i){n&&(i.parentOption=n),a.push(i);var s=i.children;s&&s.length&&this._flatten(s,a,i),delete i.children}},this)},e.prototype.useElOptionsToUpdate=function(){var r=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,r},e.type="graphic",e.defaultOption={elements:[]},e}(Tt),Ie={path:null,compoundPath:null,group:Ye,image:Vt,text:se},I=ve(),yr=function(t){F(e,t);function e(){var r=t!==null&&t.apply(this,arguments)||this;return r.type=e.type,r}return e.prototype.init=function(){this._elMap=me()},e.prototype.render=function(r,a,n){r!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=r,this._updateElements(r),this._relocate(r,n)},e.prototype._updateElements=function(r){var a=r.useElOptionsToUpdate();if(a){var n=this._elMap,i=this.group,s=r.get("z"),f=r.get("zlevel");E(a,function(o){var l=H(o.id,null),u=l!=null?n.get(l):null,v=H(o.parentId,null),h=v!=null?n.get(v):i,d=o.type,c=o.style;d==="text"&&c&&o.hv&&o.hv[1]&&(c.textVerticalAlign=c.textBaseline=c.verticalAlign=c.align=null);var g=o.textContent,m=o.textConfig;if(c&&Qt(c,d,!!m,!!g)){var y=er(c,d);!m&&y.textConfig&&(m=o.textConfig=y.textConfig),!g&&y.textContent&&(g=y.textContent)}var A=xr(o),S=o.$action||"merge",w=S==="merge",_=S==="replace";if(w){var C=!u,b=u;C?b=ke(l,h,o.type,n):(b&&(I(b).isNew=!1),hr(b)),b&&(ie(b,A,r,{isInit:C}),Ve(b,o,s,f))}else if(_){j(u,o,n,r);var T=ke(l,h,o.type,n);T&&(ie(T,A,r,{isInit:!0}),Ve(T,o,s,f))}else S==="remove"&&(je(u,o),j(u,o,n,r));var L=n.get(l);if(L&&g)if(w){var G=L.getTextContent();G?G.attr(g):L.setTextContent(new se(g))}else _&&L.setTextContent(new se(g));if(L){var x=o.clipPath;if(x){var P=x.type,D=void 0,C=!1;if(w){var M=L.getClipPath();C=!M||I(M).type!==P,D=C?fe(P):M}else _&&(C=!0,D=fe(P));L.setClipPath(D),ie(D,x,r,{isInit:C}),ue(D,x.keyframeAnimation,r)}var R=I(L);L.setTextConfig(m),R.option=o,Ar(L,r,o),Pt({el:L,componentModel:r,itemName:L.name,itemTooltipOption:o.tooltip}),ue(L,o.keyframeAnimation,r)}})}},e.prototype._relocate=function(r,a){for(var n=r.option.elements,i=this.group,s=this._elMap,f=a.getWidth(),o=a.getHeight(),l=["x","y"],u=0;u<n.length;u++){var v=n[u],h=H(v.id,null),d=h!=null?s.get(h):null;if(!(!d||!d.isGroup)){var c=d.parent,g=c===i,m=I(d),y=I(c);m.width=ye(m.option.width,g?f:y.width)||0,m.height=ye(m.option.height,g?o:y.height)||0}}for(var u=n.length-1;u>=0;u--){var v=n[u],h=H(v.id,null),d=h!=null?s.get(h):null;if(d){var c=d.parent,y=I(c),A=c===i?{width:f,height:o}:{width:y.width,height:y.height},S={},w=Bt(d,v,A,null,{hv:v.hv,boundingMode:v.bounding},S);if(!I(d).isNew&&w){for(var _=v.transition,C={},b=0;b<l.length;b++){var T=l[b],L=S[T];_&&(W(_)||ee(_,T)>=0)?C[T]=L:d[T]=L}Y(d,C,r,0)}else d.attr(S)}}},e.prototype._clear=function(){var r=this,a=this._elMap;a.each(function(n){j(n,I(n).option,a,r._lastGraphicModel)}),this._elMap=me()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(Et);function fe(t){var e=p(Ie,t)?Ie[t]:Wt(t),r=new e({});return I(r).type=t,r}function ke(t,e,r,a){var n=fe(r);return e.add(n),a.set(t,n),I(n).id=t,I(n).isNew=!0,n}function j(t,e,r,a){var n=t&&t.parent;n&&(t.type==="group"&&t.traverse(function(i){j(i,e,r,a)}),tr(t,e,a),r.removeKey(I(t).id))}function Ve(t,e,r,a){t.isGroup||E([["cursor",Xe.prototype.cursor],["zlevel",a||0],["z",r||0],["z2",0]],function(n){var i=n[0];p(e,i)?t[i]=It(e[i],n[1]):t[i]==null&&(t[i]=n[1])}),E(U(e),function(n){if(n.indexOf("on")===0){var i=e[n];t[n]=kt(i)?i:null}}),p(e,"draggable")&&(t.draggable=e.draggable),e.name!=null&&(t.name=e.name),e.id!=null&&(t.id=e.id)}function xr(t){return t=V({},t),E(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(Mt),function(e){delete t[e]}),t}function Ar(t,e,r){var a=N(t).eventData;!t.silent&&!t.ignore&&!a&&(a=N(t).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name}),a&&(a.info=r.info)}function _r(t){t.registerComponentModel(mr),t.registerComponentView(yr),t.registerPreprocessor(function(e){var r=e.graphic;Q(r)?!r[0]||!r[0].elements?e.graphic=[{elements:r}]:e.graphic=[e.graphic[0]]:r&&!r.elements&&(e.graphic=[{elements:[r]}])})}export{_r as a,Ut as g,br as i};
