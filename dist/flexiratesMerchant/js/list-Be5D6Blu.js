import{s as he}from"./index-pLSAVumt.js";import{s as Ee}from"./index-DNtGhG9S.js";import{s as Ce}from"./index-W8aGMMs9.js";import{_ as ke}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-ir_p9YfA.js";import{_ as Ae}from"./BaseCardType-BCAS9_FJ.js";import{t as we,c as Re,b as J}from"./index.esm-DoKGfgcR.js";import{d as Te}from"./dayjs.min-DUDfdLe0.js";import{d as se,z as h,o as d,w as a,b as o,a as T,p,t as m,M as K,U as le,k as Ve,B as Pe,i as $e,r as n,c as Q,V as Se,e as i,u as s,s as u,j as _,q as De,F as Ne,y as Z}from"./index-DWWVg5mm.js";import{B as <PERSON>}from"./BaseDataTable-75nyB6_U.js";import{_ as Me}from"./BaseExportDialog.vue_vue_type_script_setup_true_lang-BNy0kbNl.js";import{S as F,_ as Ue}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{u as Ye}from"./useDict-BBitGqOA.js";import{u as Fe}from"./useExport-R4ZaBF9X.js";import{u as Be}from"./useListRefresh-AzGE_VAq.js";import{u as We,P as g}from"./usePermissions-DgTRxXRz.js";import{u as qe}from"./useRequestList-Bk9BBkCQ.js";import{f as ze,h as Ie,i as Oe}from"./ratepayer-CzZHWazT.js";import{c as je}from"./support-Dzr7PNkO.js";import{f as ee}from"./date-BWkRbs8K.js";import{a as Xe}from"./dict-gu74pdeV.js";import{_ as He}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BEwkorqa.js";import"./getAssetsFile-D2Insbdr.js";import"./Phone-dOHlUqSP.js";import"./login-bg-D47k_GaL.js";import"./isUpdated-DXQm7BuR.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";const Ge={class:"dialog-container"},Je={key:0,class:"dialog-header"},Ke={class:"dialog-title"},Qe={class:"dialog-controls"},Ze={class:"dialog-content"},te=se({__name:"index",props:{visible:{type:Boolean,default:!1},title:{default:""}},emits:["update:visible","close"],setup(B,{expose:b,emit:S}){const E=B,y=S,x=()=>{y("update:visible",!1),y("close")};return b({handleClose:x}),(C,V)=>{const D=le;return d(),h(D,{visible:C.visible,modal:!0,closable:!0,class:"custom-flexirates-dialog","onUpdate:visible":V[0]||(V[0]=N=>y("update:visible",N)),onHide:x},{container:a(()=>[o("div",Ge,[E.title?(d(),T("header",Je,[o("h2",Ke,m(E.title),1),o("div",Qe,[K(C.$slots,"header-status"),o("i",{class:"pi pi-times close-icon",onClick:x})])])):p("",!0),o("div",Ze,[K(C.$slots,"content")])])]),_:3},8,["visible"])}}}),et={class:"customer-page"},tt={key:0,class:"flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},st={class:"underline"},lt={class:"flex items-center"},ot={key:0,class:"flex flex-col gap-2"},at={class:"w-58"},it={class:"mt-8 flex flex-col gap-3"},rt={class:"flex items-center"},nt={class:"relative flex items-center"},ut={class:"flex justify-end gap-4"},dt={class:"confirmation-content"},mt={key:0},ct=se({name:"flexiratesMerchantRatepayersList",__name:"list",setup(B){const{t:b}=Ve(),S=Pe(),E=$e(),{hasPermission:y,hasAnyPermission:x}=We(),C=n([{field:"property.property_number",header:"Property Number",template:"property_number",style:{minWidth:"140px"}},{field:"status",header:"Status",template:"status",style:{minWidth:"90px"}},{field:"customer.user_profile.first_name",header:"First Name",style:{minWidth:"120px"}},{field:"customer.user_profile.last_name",header:"Last Name",style:{minWidth:"120px"}},{field:"property.street_address",header:"Address",style:{minWidth:"200px"}},{field:"property.postcode",header:"Postcode",style:{minWidth:"100px"}},{field:"customer.customer_user.email",header:"Email",style:{minWidth:"120px"}},{field:"customer.customer_user.mobile",header:"Mobile",style:{minWidth:"120px"}},{field:"action",header:"",template:"action",alignFrozen:"right",frozen:!0,style:{width:"50px"}}]),{list:V,loading:D,total:N,refresh:L,search:W,onPageChange:oe,failed:ae,failureMessage:ie,loading:q,setSearchParams:k}=qe({requestFn:Ie,immediate:!1}),{isExporting:z,handleExport:re}=Fe({exportFn:Oe,getParams:()=>k(f.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file..."})}});Be("flexiratesMerchantRatepayersList",L);const ne=l=>{const{sortField:t,sortOrder:c}=l;k({sort_by:t,sort_order:c===1?"asc":"desc"}),W()},f=n({keyword:"",status:null,"created_at[]":[]}),I=n([]),ue=n(),A=n(!1),O=n([]),{loading:de}=Ye("subscription_status_filter",l=>{O.value=Xe(l,{label:"All",value:null})}),me=Q(()=>[{name:"keyword",label:"What are you looking for?",type:F.TEXT,placeholder:"Search for Property Number",maxlength:50,defaultValue:""},{name:"status",label:"Status",type:F.SELECT,placeholder:"All",options:O.value,loading:de,defaultValue:null}]),ce=Q(()=>[{name:"created_at[]",label:"Created At",type:F.DATE_RANGE,placeholder:"Please select date range",defaultValue:[]}]),P=n(!1),M=n({id:"",name:"",email_primary:""}),j=l=>{var t,c;l.originalEvent.target instanceof HTMLTableCellElement&&E.push({name:"flexiratesMerchantRatepayersDetail",params:{id:(t=l.data)==null?void 0:t.id},query:{customerId:(c=l.data)==null?void 0:c.customer_id}})},pe=()=>{E.push({name:"flexiratesMerchantRatepayersCreate"})},fe=()=>{P.value=!1,M.value={id:"",name:"",email_primary:""}},$=n(),X=n(!1),ve=async()=>{if($.value){X.value=!0,console.log($.value);try{(await je({property_id:$.value.id})).code===0&&(A.value=!1,window.$toast.add({severity:"success",summary:"Success",detail:"Cancel registration successful"}))}catch(l){console.log(l)}finally{X.value=!1}}},w=n(!1),R=n({customer_id:"",content:"",title:""}),_e=we(Re({content:J().required("Please enter Details"),title:J().required("Please enter Title")})),U=n(!1),ye=async l=>{U.value=!0;const t={customer_id:$.value.customer_id,content:l.content,title:l.title};try{(await ze(t)).code===0&&window.$toast.add({severity:"success",summary:"Success",detail:"Add note successful"}),w.value=!1}catch(c){console.log(c)}finally{U.value=!1}},H=()=>{k(f.value),W()};return Se(()=>{const l=S.query;l&&Object.keys(l).length>0?(f.value={keyword:typeof l.keyword=="string"?l.keyword:"",status:l.status?Number(l.status):null,"created_at[]":Array.isArray(l["created_at[]"])?l["created_at[]"]:[]},k(f.value),L()):(f.value={keyword:"",status:null,"created_at[]":[]},k(f.value),L())}),(l,t)=>{const c=Ae,ge=ke,be=Ce,G=Ee,xe=he;return d(),T("div",et,[i(Ue,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=e=>f.value=e),loading:s(q),"basic-search-fields":me.value,"advanced-search-fields":ce.value,onSearch:H},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),s(x)([s(g).RATE_PAYER_CREATE,s(g).RATE_PAYER_EXPORT])?(d(),T("div",tt,[s(y)(s(g).RATE_PAYER_EXPORT)?(d(),h(Me,{key:0,loading:s(q),"export-loading":s(z),onExport:s(re)},null,8,["loading","export-loading","onExport"])):p("",!0),s(y)(s(g).RATE_PAYER_CREATE)?(d(),h(s(u),{key:1,label:"ADD NEW CUSTOMER",severity:"warn",class:"!px-8",onClick:pe})):p("",!0)])):p("",!0),i(Le,{ref_key:"customerTable",ref:ue,selection:I.value,"onUpdate:selection":t[3]||(t[3]=e=>I.value=e),"show-search-bar":!1,value:s(V),columns:C.value,scrollable:!0,"show-multiple-column":!1,loading:s(D),paginator:!0,rows:50,"total-records":s(N),"data-key":"id",failed:s(ae),"failure-message":s(ie),"striped-rows":!1,style:{"--frozen-column-border-bottom":"-8px"},"is-show-expander":!1,"row-hover":!0,onChangeSearch:H,onPage:t[4]||(t[4]=e=>s(oe)(e)),onRowClick:j,onSort:ne},{created_at:a(({data:e})=>[_(m(s(ee)(e.created_at)),1)]),property_number:a(({data:e})=>{var r;return[o("span",st,m((r=e==null?void 0:e.property)==null?void 0:r.property_number),1)]}),status:a(({data:e})=>[o("span",{class:De(["font-medium",{"text-green-500":(e==null?void 0:e.status)===1,"text-red-500":[2,4].includes(e==null?void 0:e.status),"text-blue-500":(e==null?void 0:e.status)===3}])},m(e==null?void 0:e.status_desc),3)]),last_payment:a(({data:e})=>[_(m(s(ee)(e.last_payment)),1)]),name:a(({data:e})=>[o("span",null,m(e==null?void 0:e.name),1)]),default_payment_method:a(({data:e})=>{var r,v;return[o("div",lt,[i(c,{"card-type":(r=e==null?void 0:e.customer_banking[0])==null?void 0:r.credit_brand,text:(v=e==null?void 0:e.customer_banking[0])==null?void 0:v.account_no,"is-show-card-number":!0},null,8,["card-type","text"])])]}),action:a(({data:e})=>[i(ge,{"content-width":"150px","is-show-detail":!1,loading:e.__loading},{default:a(()=>[s(x)([s(g).RATE_PAYER_DETAIL,s(g).RATE_PAYER_UPDATE])?(d(),T("div",ot,[s(y)(s(g).RATE_PAYER_DETAIL)?(d(),h(s(u),{key:0,severity:"secondary",onClick:r=>j(e)},{default:a(()=>t[13]||(t[13]=[_(" Details ")])),_:2,__:[13]},1032,["onClick"])):p("",!0),i(s(u),{severity:"secondary",onClick:r=>l.$router.push({name:"flexiratesMerchantPropertiesDetail",params:{id:e.id}})},{default:a(()=>t[14]||(t[14]=[_(" Edit Account ")])),_:2,__:[14]},1032,["onClick"]),i(s(u),{severity:"secondary",onClick:t[1]||(t[1]=r=>A.value=!0)},{default:a(()=>t[15]||(t[15]=[_(" Cancel Registration ")])),_:1,__:[15]}),i(s(u),{severity:"secondary",onClick:t[2]||(t[2]=r=>w.value=!0)},{default:a(()=>t[16]||(t[16]=[_(" Add Notes ")])),_:1,__:[16]})])):p("",!0)]),_:2},1032,["loading"])]),_:1},8,["selection","value","columns","loading","total-records","failed","failure-message"]),i(te,{visible:A.value,"onUpdate:visible":t[6]||(t[6]=e=>A.value=e)},{content:a(()=>[o("div",at,[t[17]||(t[17]=o("div",{class:"text-center text-[#031f73] text-2xl font-semibold"}," Are you sure you want to cancel Registration? ",-1)),o("div",it,[i(s(u),{label:"YES",severity:"warn",class:"w-full",loading:s(z),onClick:ve},null,8,["loading"]),i(s(u),{label:"CANCEL",class:"w-full",onClick:t[5]||(t[5]=e=>A.value=!1)})])])]),_:1},8,["visible"]),i(te,{visible:w.value,title:"Add Notes and Remarks","onUpdate:visible":t[10]||(t[10]=e=>w.value=e)},{content:a(()=>[o("div",null,[i(s(Ne),{"initial-values":R.value,"validation-schema":s(_e),class:"pt-4 text-xl flex flex-col gap-y-6",onSubmit:ye},{default:a(()=>[o("div",rt,[t[18]||(t[18]=o("div",{class:"w-[150px]"}," Date and Time ",-1)),o("div",null,m(s(Te)().format("DD MMM YYYY hh:mm")),1)]),i(s(Z),{modelValue:R.value.title,"onUpdate:modelValue":t[7]||(t[7]=e=>R.value.title=e),as:"div",name:"title"},{default:a(({field:e,handleChange:r,errorMessage:v})=>[o("div",nt,[t[19]||(t[19]=o("div",{class:"min-w-[150px]"}," Title ",-1)),i(be,{modelValue:e.value,"onUpdate:modelValue":Y=>e.value=Y,placeholder:"Account Notes",type:"text",class:"w-full",onValueChange:r},null,8,["modelValue","onUpdate:modelValue","onValueChange"]),v?(d(),h(G,{key:0,severity:"error",variant:"simple",class:"absolute left-[150px] top-12"},{default:a(()=>[_(m(v),1)]),_:2},1024)):p("",!0)])]),_:1},8,["modelValue"]),i(s(Z),{modelValue:R.value.content,"onUpdate:modelValue":t[8]||(t[8]=e=>R.value.content=e),as:"div",name:"content"},{default:a(({field:e,handleChange:r,errorMessage:v})=>[o("div",null,[t[20]||(t[20]=o("div",{class:"mb-2"}," Details: ",-1)),i(xe,{modelValue:e.value,"onUpdate:modelValue":Y=>e.value=Y,"auto-resize":"",rows:"6",cols:"50",placeholder:"Reg Cancelled due to 3 dishonours/declines",onValue:r},null,8,["modelValue","onUpdate:modelValue","onValue"]),v?(d(),h(G,{key:0,severity:"error",variant:"simple",class:"mt-2"},{default:a(()=>[_(m(v),1)]),_:2},1024)):p("",!0)])]),_:1},8,["modelValue"]),o("div",ut,[i(s(u),{label:"CANCEL",class:"btn",onClick:t[9]||(t[9]=e=>w.value=!1)}),i(s(u),{label:"SAVE",class:"btn",severity:"warn",type:"submit",loading:U.value},null,8,["loading"])])]),_:1},8,["initial-values","validation-schema"])])]),_:1},8,["visible"]),i(s(le),{visible:P.value,"onUpdate:visible":t[12]||(t[12]=e=>P.value=e),style:{width:"450px"},header:s(b)("customersPage.dialogs.confirmDelete"),modal:!0},{footer:a(()=>[i(s(u),{label:s(b)("common.no"),icon:"pi pi-times",text:"",onClick:t[11]||(t[11]=e=>P.value=!1)},null,8,["label"]),i(s(u),{label:s(b)("common.yes"),icon:"pi pi-check",text:"",onClick:fe},null,8,["label"])]),default:a(()=>[o("div",dt,[t[21]||(t[21]=o("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),M.value?(d(),T("span",mt,m(s(b)("customersPage.dialogs.deleteConfirmMessage",{name:M.value.name})),1)):p("",!0)])]),_:1},8,["visible","header"])])}}}),Jt=He(ct,[["__scopeId","data-v-511c757e"]]);export{Jt as default};
