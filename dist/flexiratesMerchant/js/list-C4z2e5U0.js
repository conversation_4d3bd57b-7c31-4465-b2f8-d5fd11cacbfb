import{_ as K}from"./BaseCardType-BCAS9_FJ.js";import{_ as Q}from"./BaseExportDialog.vue_vue_type_script_setup_true_lang-BNy0kbNl.js";import{S as f,_ as Z}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{F as ee}from"./index-Cui9J1ai.js";import{d as re,i as te,B as oe,r as p,c as T,f as se,a as u,o as d,e as c,p as g,u as t,w as m,b as x,t as l}from"./index-DWWVg5mm.js";import{B as ae}from"./BaseDataTable-75nyB6_U.js";import{u as ne}from"./useDict-BBitGqOA.js";import{u as me}from"./useExport-R4ZaBF9X.js";import{u as le,P as A}from"./usePermissions-DgTRxXRz.js";import{u as ie}from"./useRequestList-Bk9BBkCQ.js";import{g as pe,e as ue}from"./transactions-Dqpj7ebB.js";import{f as de}from"./date-BWkRbs8K.js";import{a as ce}from"./dict-gu74pdeV.js";import{_ as _e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./getAssetsFile-D2Insbdr.js";import"./Phone-dOHlUqSP.js";import"./login-bg-D47k_GaL.js";import"./isUpdated-DXQm7BuR.js";import"./index-T2UmNbVz.js";import"./index-BEwkorqa.js";import"./index-M77XcG3U.js";import"./index-IMPOxVZV.js";import"./index-W8aGMMs9.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";import"./index-yGDtrfNA.js";import"./index-BVQM_gwe.js";import"./index-DimVHvgA.js";import"./dayjs.min-DUDfdLe0.js";const he={key:0,class:"flex justify-end items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},ye={class:"underline"},fe={key:0},ge={key:1},xe=re({name:"flexiratesMerchantPaymentsList",__name:"list",setup(be){const M=ie({requestFn:pe}),{hasPermission:b}=le(),B=te(),C=oe(),{list:F,loading:_,total:N,refresh:h,search:v,onPageChange:V,failed:W,failureMessage:q,setSearchParams:n}=M,L=p([{template:"property_number",field:"customer_plan.customer_property.property_number",header:"Property Number",style:{minWidth:"140px"}},{template:"remit_date",field:"remit_date",header:"Remit Date",style:{minWidth:"140px"}},{template:"payment_amount",field:"payment_amount",header:"Amount",style:{minWidth:"140px"}},{template:"payment_method",field:"payment_method",header:"Payment Method",style:{minWidth:"140px"}},{field:"customer.name",header:"Customer Name",style:{minWidth:"140px"}},{template:"address",field:"address",header:"Address",style:{minWidth:"140px"}},{field:"customer.email_primary",header:"Email",style:{minWidth:"140px"}},{field:"customer.phone_mobile",header:"Mobile",style:{minWidth:"140px"}}]),o=p({payment_method:null,keyword:"","remit_date[]":[]}),P=p([]),$=p([]),O=T(()=>[{name:"keyword",label:"Search Payments",type:f.TEXT,placeholder:"Search for a payment",defaultValue:""},{name:"payment_method",label:"Payment Method",type:f.SELECT,placeholder:"All",options:P.value,defaultValue:""}]),j=T(()=>[{name:"remit_date[]",label:"Remit Date Range",type:f.DATE_RANGE,placeholder:"Select Remit Date Range",defaultValue:$.value}]),I=()=>{n(o.value,["refunded_date[]"]),v()},X=()=>{h()},Y=r=>{const{sortField:i,sortOrder:y}=r;n({sort_by:i,sort_order:y===1?"asc":"desc"}),v()},{isExporting:z,handleExport:G}=me({exportFn:ue,getParams:()=>n(o.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file...",life:3e3})}}),U=({data:r})=>{b(A.PAYMENTS_DETAIL)&&B.push({name:"flexiratesMerchantPaymentsDetail",params:{id:r.id}})};return ne("credit_brand",r=>{P.value=ce(r)}),se(()=>{const r=C.query;r&&Object.keys(r).length>0?(o.value={payment_method:r.payment_method?Number(r.payment_method):null,keyword:r.keyword||"","remit_date[]":Array.isArray(r["remit_date[]"])?r["remit_date[]"]:[]},n(o.value),h()):(o.value={payment_method:null,keyword:"","remit_date[]":[]},n(o.value),h())}),(r,i)=>{const y=Z,H=Q,J=K;return d(),u("div",null,[c(y,{modelValue:o.value,"onUpdate:modelValue":i[0]||(i[0]=e=>o.value=e),loading:t(_),"basic-search-fields":O.value,"advanced-search-fields":j.value,onSearch:I},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),t(b)(t(A).PAYMENTS_EXPORT)?(d(),u("div",he,[c(H,{loading:t(_),"export-loading":t(z),onExport:t(G)},null,8,["loading","export-loading","onExport"])])):g("",!0),c(ae,{value:t(F),columns:L.value,"show-edit-column":!1,"show-search-bar":!1,scrollable:!0,"show-multiple-column":!1,loading:t(_),paginator:!0,rows:50,"total-records":t(N),lazy:!0,"data-key":"id","sort-mode":"single","row-hover":!0,"sort-field":r.$route.query.sort_by,"sort-order":r.$route.query.sort_order==="desc"?-1:1,"search-placeholder":"Search","type-placeholder":"Filter By",failed:t(W),"failure-message":t(q),"striped-rows":!1,onPage:t(V),onSort:Y,onRefresh:X,onRowClick:U},{property_number:m(({data:e})=>{var s,a;return[x("span",ye,l((a=(s=e==null?void 0:e.customer_plan)==null?void 0:s.customer_property)==null?void 0:a.property_number),1)]}),address:m(({data:e})=>{var s,a,E,S,w,k,D,R;return[(a=(s=e==null?void 0:e.customer_plan)==null?void 0:s.customer_property)!=null&&a.street_address?(d(),u("span",fe,l((S=(E=e==null?void 0:e.customer_plan)==null?void 0:E.customer_property)==null?void 0:S.street_address)+" , ",1)):g("",!0),(k=(w=e==null?void 0:e.customer_plan)==null?void 0:w.customer_property)!=null&&k.suburb?(d(),u("span",ge,l((R=(D=e==null?void 0:e.customer_plan)==null?void 0:D.customer_property)==null?void 0:R.suburb),1)):g("",!0)]}),payment_method:m(({data:e})=>{var s,a;return[c(J,{"card-type":(s=e==null?void 0:e.customer_banking)==null?void 0:s.credit_brand,"is-show-card-number":!0,text:(a=e==null?void 0:e.customer_banking)==null?void 0:a.account_no},null,8,["card-type","text"])]}),payment_amount:m(({data:e})=>[x("span",null,l(t(ee).formatAmount(e==null?void 0:e.payment_amount,e==null?void 0:e.payment_currency)),1)]),remit_date:m(({data:e})=>[x("span",null,l(t(de)(e.created_at)),1)]),_:1},8,["value","columns","loading","total-records","sort-field","sort-order","failed","failure-message","onPage"])])}}}),Ze=_e(xe,[["__scopeId","data-v-a1f2d50e"]]);export{Ze as default};
