import{F as f}from"./index-Cui9J1ai.js";import{B as _}from"./BaseDataTable-75nyB6_U.js";import{u as c}from"./useListRefresh-AzGE_VAq.js";import{u as h}from"./useRequestList-Bk9BBkCQ.js";import{d as g,r as y,a as o,o as s,e as w,w as x,p as b,t as W,u as r}from"./index-DWWVg5mm.js";import{g as v}from"./properties-DEWCA0Ig.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";import"./dayjs.min-DUDfdLe0.js";const N={class:"payout-list-page"},C={key:0},P=g({name:"arrearList",__name:"list",setup(k){const i=y([{field:"property_number",header:"Property Number",style:{minWidth:"100px"}},{field:"street_address",header:"Address",style:{minWidth:"120px"}},{field:"customer_profile.first_name",header:"First Name",style:{minWidth:"120px"}},{field:"customer_profile.last_name",header:"Last Name",style:{minWidth:"120px"}},{field:"customer_user.email",header:"Email",style:{minWidth:"120px"}},{field:"customer_user.mobile",header:"Phone Number",style:{minWidth:"120px"}},{field:"amount_owing",template:"amount_owing",header:"Arrears Amount",style:{minWidth:"120px"}}]),{list:a,loading:m,total:l,refresh:n,onPageChange:p,failed:u,failureMessage:d}=h({requestFn:v});return c("arrearList",n),(A,t)=>(s(),o("div",N,[w(_,{value:r(a),columns:i.value,scrollable:!0,"show-multiple-column":!1,loading:r(m),paginator:!0,rows:50,"total-records":r(l),lazy:!0,"data-key":"id","show-search-bar":!1,failed:r(u),"failure-message":r(d),"striped-rows":!0,"row-hover":!0,onPage:t[0]||(t[0]=e=>r(p)(e))},{amount_owing:x(({data:e})=>[e!=null&&e.amount_owing?(s(),o("span",C,W(r(f).formatAmount(e==null?void 0:e.amount_owing)),1)):b("",!0)]),_:1},8,["value","columns","loading","total-records","failed","failure-message"])]))}}),Z=L(P,[["__scopeId","data-v-c8a2ae36"]]);export{Z as default};
