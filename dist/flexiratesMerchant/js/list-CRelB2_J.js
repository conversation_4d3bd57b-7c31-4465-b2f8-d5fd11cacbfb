import{d as q,B as I,i as O,r as n,c as v,V as $,a as b,o as d,e as c,p as P,u as s,b as l,w as i,z as Y,s as X,X as U,q as G,t as u,j as H}from"./index-DWWVg5mm.js";import{F as J}from"./index-Cui9J1ai.js";import{d as K}from"./dayjs.min-DUDfdLe0.js";import{B as Q}from"./BaseDataTable-75nyB6_U.js";import{_ as Z}from"./BaseExportDialog.vue_vue_type_script_setup_true_lang-BNy0kbNl.js";import{S as f,_ as ee}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{u as te}from"./useExport-R4ZaBF9X.js";import{u as se}from"./useListRefresh-AzGE_VAq.js";import{u as re,P as S}from"./usePermissions-DgTRxXRz.js";import{u as oe}from"./useRequestList-Bk9BBkCQ.js";import{g as ae,e as le}from"./properties-DEWCA0Ig.js";import{_ as ie}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";const ne={class:"customer-page"},ue={key:0,class:"flex justify-end items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},me={class:"underline"},pe={class:"underline"},de={class:"flex items-center justify-center min-h-10"},ce=q({name:"flexiratesMerchantPropertiesList",__name:"list",setup(fe){const w=I(),E=O(),k=n([{field:"property_number",template:"property_number",header:"Property Number",style:{minWidth:"140px"}},{field:"street_address",header:"Address",style:{minWidth:"200px"}},{field:"full_amount",header:"Full Rate Amount",template:"full_amount",style:{minWidth:"120px"}},{field:"due_date",header:"Due Date",style:{minWidth:"120px"}},{field:"status",header:"Status",template:"status",style:{minWidth:"100px"}},{field:"",template:"action",header:"",frozen:!0,alignFrozen:"right",style:{width:"160px"}}]),{hasPermission:h}=re(),{list:R,loading:T,total:C,refresh:m,search:_,onPageChange:F,failed:A,failureMessage:V,loading:y,setSearchParams:a}=oe({requestFn:ae,immediate:!1}),{isExporting:D,handleExport:L}=te({exportFn:le,getParams:()=>a(o.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file..."})}});se("flexiratesMerchantPropertiesList",m);const M=t=>{const{sortField:r,sortOrder:p}=t;a({sort_by:r,sort_order:p===1?"asc":"desc"}),_()},B=t=>{console.log(t)},o=n({keyword:"","created_at[]":[],status:""}),g=n([]),N=n(),z=v(()=>[{name:"keyword",label:"What are you looking for?",type:f.TEXT,placeholder:"Search for Property Number",maxlength:50,defaultValue:""},{name:"status",label:"Status",type:f.SELECT,placeholder:"Select Status",options:[{label:"All",value:""},{label:"Registered",value:1},{label:"Not Registered",value:2}],defaultValue:[]}]),W=v(()=>[{name:"created_at[]",label:"Created At",type:f.DATE_RANGE,placeholder:"Please select date range",defaultValue:[]}]),j=({data:t})=>{if(h(S.PROPERTIES_DETAIL)){if((t==null?void 0:t.status)!==1){window.$toast.add({severity:"info",summary:"Info",detail:"This property is not registered yet"});return}E.push({name:"flexiratesMerchantPropertiesDetail",params:{id:t.id}})}},x=()=>{a(o.value),_()};return $(()=>{const t=w.query;t&&Object.keys(t).length>0?(o.value={keyword:typeof t.keyword=="string"?t.keyword:"","created_at[]":Array.isArray(t["created_at[]"])?t["created_at[]"]:[]},a(o.value),m()):(o.value={keyword:"","created_at[]":[]},a(o.value),m())}),(t,r)=>{const p=X;return d(),b("div",ne,[c(ee,{modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=e=>o.value=e),loading:s(y),"basic-search-fields":z.value,"advanced-search-fields":W.value,onSearch:x},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),s(h)(s(S).PROPERTIES_EXPORT)?(d(),b("div",ue,[r[3]||(r[3]=l("div",{class:"flex-1"},null,-1)),c(Z,{loading:s(y),"export-loading":s(D),onExport:s(L)},null,8,["loading","export-loading","onExport"])])):P("",!0),c(Q,{ref_key:"customerTable",ref:N,selection:g.value,"onUpdate:selection":r[1]||(r[1]=e=>g.value=e),"show-search-bar":!1,value:s(R),columns:k.value,scrollable:!0,"show-multiple-column":!1,loading:s(T),paginator:!0,rows:50,"total-records":s(C),lazy:!0,"data-key":"id",failed:s(A),"failure-message":s(V),"striped-rows":!1,style:{"--frozen-column-border-bottom":"-8px"},"row-hover":!0,onChangeSearch:x,onPage:r[2]||(r[2]=e=>s(F)(e)),onRowClick:j,onSort:M},{full_amount:i(({data:e})=>[H(u(s(J).formatAmount(e==null?void 0:e.full_amount)),1)]),due_date:i(({data:e})=>[l("span",me,u(s(K)(e==null?void 0:e.due_date).format("DD MMM YYYY")),1)]),property_number:i(({data:e})=>[l("span",pe,u(e==null?void 0:e.property_number),1)]),status:i(({data:e})=>[l("span",{class:G(["font-bold",{"text-green-500":(e==null?void 0:e.status)===1,"text-red-500":(e==null?void 0:e.status)===2}])},u(e==null?void 0:e.status_desc),3)]),action:i(({data:e})=>[l("div",de,[(e==null?void 0:e.status)===2?(d(),Y(p,{key:0,label:"Setup FlexiRates",severity:"warn",onClick:U(he=>B(e),["stop"])},null,8,["onClick"])):P("",!0)])]),_:1},8,["selection","value","columns","loading","total-records","failed","failure-message"])])}}}),Ie=ie(ce,[["__scopeId","data-v-92081f7a"]]);export{Ie as default};
