import{_ as N}from"./BaseTag-DKUhplpL.js";import{F as D}from"./index-Cui9J1ai.js";import{R as P,d as I,B as R,i as w,r as u,V as A,a as B,o as G,e as m,w as p,j as T,t as W,u as s}from"./index-DWWVg5mm.js";import{B as F}from"./BaseDataTable-75nyB6_U.js";import{u as O}from"./useListRefresh-AzGE_VAq.js";import{u as S}from"./useRequestList-Bk9BBkCQ.js";import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./date-BWkRbs8K.js";import"./dayjs.min-DUDfdLe0.js";const q=e=>P("/cardinia/payout/list",{params:e});var r=(e=>(e[e.PENDING=0]="PENDING",e[e.COMPLETED=1]="COMPLETED",e[e.FAILED=2]="FAILED",e[e.PROCESSING=3]="PROCESSING",e))(r||{});const M=e=>({[r.PENDING]:"upcoming",[r.COMPLETED]:"paid",[r.FAILED]:"failed",[r.PROCESSING]:"upcoming"})[e]||"upcoming",V={class:"payout-list-page"},j=I({name:"payoutList",__name:"list",setup(e){const c=R(),d=w(),f=u([{field:"currency",header:"Currency",style:{minWidth:"100px"}},{field:"amount",header:"Gross",style:{minWidth:"120px"},template:"amount"},{field:"payment_amount",header:"Payout",style:{minWidth:"120px"}},{field:"account_name",header:"Account name",style:{minWidth:"150px"}},{field:"bsb",header:"BSB",style:{minWidth:"100px"}},{field:"account_no",header:"Account",style:{minWidth:"150px"}},{field:"arrive_by",header:"Arrive by",style:{minWidth:"150px"}},{field:"status",header:"Status",template:"status",style:{minWidth:"120px"}}]),{list:h,loading:_,total:g,refresh:y,onPageChange:v,failed:b,failureMessage:E,search:l,setSearchParams:n}=S({requestFn:q,immediate:!1});O("payoutList",y);const x=u([{label:"All",value:""},{label:"Pending",value:0},{label:"Success",value:1},{label:"Fail",value:2},{label:"Processing",value:3}]),C=a=>{var o;return((o=x.value.find(i=>i.value===a))==null?void 0:o.label)||""},L=a=>{d.push({name:"transactionsList",query:{"created_at[]":[a.data.arrive_by,a.data.arrive_by]}})};return A(()=>{const a=c.query;a&&a.status!==void 0?(n({status:Number(a.status)}),l()):(n({status:void 0}),l())}),(a,o)=>{const i=N;return G(),B("div",V,[m(F,{value:s(h),columns:f.value,scrollable:!0,"show-multiple-column":!1,loading:s(_),paginator:!0,rows:50,"total-records":s(g),lazy:!0,"data-key":"id","show-search-bar":!1,failed:s(b),"failure-message":s(E),"striped-rows":!0,"row-hover":!0,onPage:o[0]||(o[0]=t=>s(v)(t)),onRowClick:o[1]||(o[1]=t=>L(t))},{status:p(({data:t})=>[m(i,{text:C(t.status),type:s(M)(t.status)},null,8,["text","type"])]),amount:p(({data:t})=>[T(W(s(D).formatAmount(t==null?void 0:t.amount)),1)]),_:1},8,["value","columns","loading","total-records","failed","failure-message"])])}}}),ce=k(j,[["__scopeId","data-v-3eb1c256"]]);export{ce as default};
