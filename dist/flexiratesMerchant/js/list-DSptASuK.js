import{F as h}from"./index-Cui9J1ai.js";import{d as q,B as M,i as O,r as l,c as y,V as j,a as u,o as p,e as v,u as o,w as r,j as g,t as i,p as b}from"./index-DWWVg5mm.js";import{B as z}from"./BaseDataTable-75nyB6_U.js";import{S as c,_ as U}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{u as G}from"./useDict-BBitGqOA.js";import{u as X}from"./useListRefresh-AzGE_VAq.js";import{u as $,P as H}from"./usePermissions-DgTRxXRz.js";import{u as J}from"./useRequestList-Bk9BBkCQ.js";import{a as K}from"./settlementDetailInfo-CD9ArrOp.js";import{f as Q}from"./date-BWkRbs8K.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./dayjs.min-DUDfdLe0.js";const Z={class:"settlement-detail-info-page"},ee={key:0},te={key:0},oe=q({name:"settlementDetailInfoList",__name:"list",setup(se){const T=M(),S=O(),D=l([{field:"trans_no",header:"Transaction Number",style:{minWidth:"160px"}},{field:"customer_id",header:"Customer ID",style:{minWidth:"160px"}},{field:"currency",header:"Currency",style:{minWidth:"100px"}},{field:"amount",template:"amount",header:"Amount",style:{minWidth:"120px"}},{field:"settlement_currency",header:"Settlement Currency",style:{minWidth:"140px"}},{field:"settlement_amount",template:"settlement_amount",header:"Settlement Amount",style:{minWidth:"140px"}},{field:"amount_type",template:"amount_type",header:"Amount Type",style:{minWidth:"120px"}},{field:"settle_at",template:"settle_at",header:"Settlement Date",style:{minWidth:"140px"}}]),{hasPermission:x}=$(),{list:A,loading:L,total:E,refresh:m,search:d,onPageChange:C,failed:N,failureMessage:V,loading:k,setSearchParams:n}=J({requestFn:K});X("flexiratesMerchantSettlementDetailInfoList",m);const w=t=>{const{sortField:s,sortOrder:e}=t;n({sort_by:s,sort_order:e===1?"asc":"desc"}),d()},a=l({trans_no:"",amount_type:void 0,settle_at:""}),_=l([]),F=l(),{getLabel:I,options:P}=G("settlement_detail_amount_type"),R=y(()=>[{name:"trans_no",label:"Transaction Number",type:c.TEXT,placeholder:"Enter Transaction Number",defaultValue:""}]),W=y(()=>[{name:"amount_type",label:"Amount Type",type:c.SELECT,placeholder:"Select Amount Type",options:[{label:"All",value:void 0},...P.value],defaultValue:void 0},{name:"settle_at[]",label:"Settlement Date",type:c.DATE_RANGE,placeholder:"Select Remit Date Range",defaultValue:""}]),B=({data:t})=>{x(H.SETTLEMENT_DETAIL_INFO_DETAIL)&&S.push({name:"settlementDetailInfoDetail",params:{id:t.id}})},f=()=>{n(a.value),d()};return j(()=>{const t=T.query;t&&Object.keys(t).length>0?(a.value={trans_no:typeof t.trans_no=="string"?t.trans_no:"",amount_type:t.amount_type?Number(t.amount_type):void 0,settle_at:typeof t.settle_at=="string"?t.settle_at:""},n(a.value),m()):(a.value={trans_no:"",amount_type:void 0,settle_at:""},n(a.value),m())}),(t,s)=>(p(),u("div",Z,[v(U,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=e=>a.value=e),loading:o(k),"basic-search-fields":R.value,"advanced-search-fields":W.value,onSearch:f},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),v(z,{ref_key:"settlementDetailTable",ref:F,selection:_.value,"onUpdate:selection":s[1]||(s[1]=e=>_.value=e),"show-search-bar":!1,value:o(A),columns:D.value,scrollable:!0,"show-multiple-column":!1,loading:o(L),paginator:!0,rows:50,"total-records":o(E),lazy:!0,"data-key":"id",failed:o(N),"row-hover":!0,"failure-message":o(V),"striped-rows":!1,style:{"--frozen-column-border-bottom":"-8px"},onChangeSearch:f,onPage:s[2]||(s[2]=e=>o(C)(e)),onRowClick:B,onSort:w},{amount:r(({data:e})=>[e!=null&&e.amount?(p(),u("span",ee,i(o(h).formatAmount(e==null?void 0:e.amount)),1)):b("",!0)]),settlement_amount:r(({data:e})=>[e!=null&&e.settlement_amount?(p(),u("span",te,i(o(h).formatAmount(e==null?void 0:e.settlement_amount)),1)):b("",!0)]),amount_type:r(({data:e})=>[g(i(o(I)(e==null?void 0:e.amount_type)),1)]),settle_at:r(({data:e})=>[g(i(o(Q)(e.settle_at)),1)]),_:1},8,["selection","value","columns","loading","total-records","failed","failure-message"])]))}}),Ne=Y(oe,[["__scopeId","data-v-728245bc"]]);export{Ne as default};
