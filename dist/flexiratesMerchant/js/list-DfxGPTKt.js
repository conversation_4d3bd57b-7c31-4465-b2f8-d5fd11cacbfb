import{_ as j}from"./BaseTag-DKUhplpL.js";import{_ as z}from"./BaseExportDialog.vue_vue_type_script_setup_true_lang-BNy0kbNl.js";import{S as d,_ as G}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{F as v}from"./index-Cui9J1ai.js";import{d as J,i as K,r as i,c as Q,a as n,o as l,e as r,p as _,u as t,w as p,t as P}from"./index-DWWVg5mm.js";import{B as Y}from"./BaseDataTable-75nyB6_U.js";import{u as f}from"./useDict-BBitGqOA.js";import{u as Z}from"./useExport-R4ZaBF9X.js";import{u as ee}from"./useListRefresh-AzGE_VAq.js";import{u as te,P as w}from"./usePermissions-DgTRxXRz.js";import{u as se}from"./useRequestList-Bk9BBkCQ.js";import{b as oe,c as re}from"./schedules-DUq33TjC.js";import{a as L}from"./dict-gu74pdeV.js";import{_ as ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-T2UmNbVz.js";import"./index-BEwkorqa.js";import"./index-M77XcG3U.js";import"./index-IMPOxVZV.js";import"./index-W8aGMMs9.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";import"./index-yGDtrfNA.js";import"./index-BVQM_gwe.js";import"./index-DimVHvgA.js";import"./date-BWkRbs8K.js";import"./dayjs.min-DUDfdLe0.js";const ie={class:"merchant-list-page"},ne={key:0,class:"flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},le={key:0},pe={key:0},me=J({name:"flexiratesMerchantScheduleList",__name:"list",setup(ue){const m=se({requestFn:oe}),T=K(),{hasPermission:h}=te(),{list:C,loading:u,total:D,refresh:A,setSearchParams:y,search:F,onPageChange:k,failed:V,failureMessage:B}=m;ee("flexiratesMerchantScheduleList",A);const{isExporting:W,handleExport:R}=Z({exportFn:re,getParams:()=>y(a.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file...",life:3e3})}}),q=i([{field:"property.property_number",header:"Property Number",style:{minWidth:"80px"}},{template:"status",field:"status",header:"Status",style:{minWidth:"80px"}},{template:"process_type",field:"process_type",header:"Frequency",style:{minWidth:"80px"}},{field:"customer.name",header:"Customer Name",style:{minWidth:"80px"}},{field:"property.street_address",header:"Address",style:{minWidth:"80px"}},{template:"total_rate_amount",field:"property.initial_remaining_amount",header:"Total Rate Amount",style:{minWidth:"80px"}},{template:"amount_owing",field:"amount_owing",header:"Amount Owing",style:{minWidth:"80px"}}]),g=i([]),x=i([]),a=i({}),M=Q(()=>[{name:"keyword",label:"Search by Property Number",type:d.TEXT,placeholder:"Enter property number",maxlength:100,defaultValue:""},{name:"status",label:"Status",type:d.SELECT,placeholder:"All",options:x.value,defaultValue:""},{name:"process_type",label:"Process Type",type:d.SELECT,placeholder:"All",options:g.value,defaultValue:""}]),N=s=>{const{sortField:o,sortOrder:c}=s;m.setParams({sort_by:o,sort_order:c===1?"asc":"desc"}),m.search()},O=({data:s})=>{var o;h(w.SCHEDULE_DETAIL)&&T.push({name:"flexiratesMerchantSchedulesDetail",params:{id:s.id},query:{address:(o=s.property)==null?void 0:o.street_address,status:s.status}})},U=()=>{y(a.value),F()},$=s=>s===1?"paid":s===2?"upcoming":"default",{getLabel:H}=f("payment_plan_show",s=>{g.value=L(s)});f("subscription_status_filter",s=>{x.value=L(s)});const{getLabel:I}=f("subscription_status");return(s,o)=>{const c=G,X=z,S=j;return l(),n("div",ie,[r(c,{modelValue:a.value,"onUpdate:modelValue":o[0]||(o[0]=e=>a.value=e),loading:t(u),"basic-search-fields":M.value,onSearch:U},null,8,["modelValue","loading","basic-search-fields"]),t(h)(t(w).SCHEDULE_EXPORT)?(l(),n("div",ne,[r(X,{loading:t(u),"export-loading":t(W),onExport:t(R)},null,8,["loading","export-loading","onExport"])])):_("",!0),r(Y,{"show-multiple-column":!1,columns:q.value,value:t(C),loading:t(u),"total-records":t(D),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,scrollable:!0,"search-placeholder":"Search merchants...",failed:t(V),"failure-message":t(B),"striped-rows":!1,"row-hover":!0,onSort:N,onPage:o[1]||(o[1]=e=>t(k)(e)),onRowClick:O},{total_rate_amount:p(({data:e})=>{var b,E;return[(b=e==null?void 0:e.property)!=null&&b.initial_remaining_amount?(l(),n("span",le,P(t(v).formatAmount((E=e==null?void 0:e.property)==null?void 0:E.initial_remaining_amount)),1)):_("",!0)]}),amount_owing:p(({data:e})=>[e.amount_owing?(l(),n("span",pe,P(t(v).formatAmount(e==null?void 0:e.amount_owing)),1)):_("",!0)]),status:p(({data:e})=>[r(S,{text:t(I)(e.status),type:$(e.status)},null,8,["text","type"])]),process_type:p(({data:e})=>[r(S,{text:t(H)(e.process_type)},null,8,["text"])]),_:1},8,["columns","value","loading","total-records","failed","failure-message"])])}}}),Ue=ae(me,[["__scopeId","data-v-ddde4272"]]);export{Ue as default};
