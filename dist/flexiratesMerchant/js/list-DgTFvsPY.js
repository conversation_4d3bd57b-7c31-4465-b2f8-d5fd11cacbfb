import{d as ae,k as se,O as oe,i as re,x as le,r as c,c as ne,a as E,o as p,e as l,p as S,u as t,s as ie,w as n,z as me,b as u,t as f,U as ce,bR as de,bS as ue}from"./index-DWWVg5mm.js";import{_ as pe}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-ir_p9YfA.js";import{_ as fe}from"./BaseTag-DKUhplpL.js";import{S as x,_ as he}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{B as _e}from"./BaseDataTable-75nyB6_U.js";import{u as R}from"./useDict-BBitGqOA.js";import{u as ye}from"./useListRefresh-AzGE_VAq.js";import{u as ve,P as r}from"./usePermissions-DgTRxXRz.js";import{u as ge}from"./useRequestList-Bk9BBkCQ.js";import{f as Ee}from"./date-BWkRbs8K.js";import{a as C}from"./dict-gu74pdeV.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-W8aGMMs9.js";import"./index-T2UmNbVz.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";import"./index-yGDtrfNA.js";import"./index-BVQM_gwe.js";import"./index-DimVHvgA.js";import"./dayjs.min-DUDfdLe0.js";const xe={class:"merchant-list-page"},Ue={key:0,class:"flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},be={class:"confirmation-content"},Te={key:0},De=ae({name:"flexiratesMerchantUserList",__name:"list",setup(we){const{t:o}=se(),U=oe(),h=ge({requestFn:ue}),{hasPermission:i,hasAnyPermission:M}=ve(),_=re(),{user:L}=le(),{list:P,loading:b,total:F,refresh:T,setSearchParams:B,search:V,onPageChange:W,failed:$,failureMessage:z}=h;ye("flexiratesMerchantUserList",T);const I=c([{field:"name",header:o("merchant.columns.name","Username"),style:{minWidth:"150px"},template:"name",sortable:!0,sortField:"name"},{field:"email",header:o("merchant.columns.email","Email"),style:{minWidth:"200px"}},{field:"status",header:o("merchant.columns.status","Status"),style:{minWidth:"100px"},template:"status"},{field:"type",header:o("merchant.columns.type","Type"),style:{minWidth:"120px"},template:"type"},{field:"mfa_check",header:o("merchant.columns.2fa","2FA Status"),style:{minWidth:"120px"},template:"2fa"},{field:"created_at",header:o("common.created","Created Date"),style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"created_at"},{field:"action",header:"",style:{width:"50px"},template:"action",alignFrozen:"right",frozen:!0}]),D=c([]),w=c([]),y=c({name:"",email:"",status:"",type:"","created_at[]":[]}),O=ne(()=>[{name:"name",label:"Username",type:x.TEXT,placeholder:"Search for Username",maxlength:50,defaultValue:""},{name:"status",label:"Status",type:x.SELECT,placeholder:"All",options:D.value,defaultValue:""},{name:"type",label:"Type",type:x.SELECT,placeholder:"All",options:w.value,defaultValue:""}]),q=a=>{const{sortField:s,sortOrder:v}=a;h.setParams({sort_by:s,sort_order:v===1?"asc":"desc"}),h.search()},N=a=>a===void 0?"upcoming":{1:"paid",0:"failed"}[String(a)]||"upcoming",j=a=>a===void 0?"Unknown":{1:"Enabled",0:"Disabled"}[String(a)]||"Unknown",G=a=>a===void 0?"Unknown":{1:"Admin",2:"User",3:"Guest"}[String(a)]||"Unknown",X=({data:a})=>{i(r.USER_DETAIL)&&_.push({name:"flexiratesMerchantUserDetail",params:{id:a.id}})},H=a=>{i(r.USER_UPDATE)&&_.push({name:"flexiratesMerchantUserEdit",params:{id:a.id}})},J=()=>{i(r.USER_CREATE)&&_.push({name:"flexiratesMerchantUserCreate"})},d=c(!1),m=c(null),K=a=>{m.value=a,d.value=!0},Q=()=>{m.value&&(d.value=!1,de(String(m.value.id)).then(a=>{U.add({severity:"success",summary:o("common.success","Success"),detail:o("merchant.messages.deleteSuccess","Merchant deleted successfully"),life:3e3}),T()}).catch(a=>{console.error("Failed to delete user:",a),U.add({severity:"error",summary:o("common.error","Error"),detail:o("merchant.messages.deleteFailed","Failed to delete merchant"),life:3e3})}),m.value=null)},Y=()=>{B(y.value),V()};return R("user_status",a=>{D.value=C(a,{label:"All",value:""})}),R("user_type",a=>{w.value=C(a,{label:"All",value:""})}),(a,s)=>{const v=he,g=ie,k=fe,Z=pe,ee=ce;return p(),E("div",xe,[l(v,{modelValue:y.value,"onUpdate:modelValue":s[0]||(s[0]=e=>y.value=e),loading:t(b),"basic-search-fields":O.value,onSearch:Y},null,8,["modelValue","loading","basic-search-fields"]),t(i)(t(r).USER_CREATE)?(p(),E("div",Ue,[l(g,{label:"CREATE A USER",severity:"warn",class:"!px-8",onClick:J})])):S("",!0),l(_e,{columns:I.value,value:t(P),loading:t(b),"total-records":t(F),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,scrollable:!0,"show-multiple-column":!1,"search-placeholder":"Search merchants...",failed:t($),"failure-message":t(z),"striped-rows":!1,"row-hover":!0,onSort:q,onPage:s[1]||(s[1]=e=>t(W)(e)),onRowClick:X},{name:n(({data:e})=>[u("div",null,f(e.name),1)]),status:n(({data:e})=>[l(k,{text:j(e.status),type:N(e.status)},null,8,["text","type"])]),type:n(({data:e})=>[u("div",null,f(G(e.type)),1)]),"2fa":n(({data:e})=>[l(k,{text:e.mfa_check===1?"Enabled":"Disabled",type:e.mfa_check===1?"paid":"failed"},null,8,["text","type"])]),date:n(({data:e})=>[u("div",null,f(t(Ee)(e.created_at)),1)]),action:n(({data:e})=>{var A;return[t(M)([t(r).USER_UPDATE,t(r).USER_DETAIL,t(r).USER_DELETE])&&e.type!==0?(p(),me(Z,{key:0,"is-show-detail":e.type!==0&&t(i)(t(r).USER_DETAIL),"is-show-delete":e.type!==0&&e.merchant_id!==((A=t(L))==null?void 0:A.merchant_id)&&t(i)(t(r).USER_DELETE),"is-show-edit":e.type!==0&&t(i)(t(r).USER_UPDATE),loading:e.__loading,onEdit:te=>H(e),onDelete:te=>K(e)},null,8,["is-show-detail","is-show-delete","is-show-edit","loading","onEdit","onDelete"])):S("",!0)]}),_:1},8,["columns","value","loading","total-records","failed","failure-message"]),l(ee,{visible:d.value,"onUpdate:visible":s[3]||(s[3]=e=>d.value=e),style:{width:"450px"},header:t(o)("merchant.dialogs.confirmDelete"),modal:!0},{footer:n(()=>[l(g,{label:t(o)("common.no"),icon:"pi pi-times",text:"",onClick:s[2]||(s[2]=e=>d.value=!1)},null,8,["label"]),l(g,{label:t(o)("common.yes"),icon:"pi pi-check",text:"",onClick:Q},null,8,["label"])]),default:n(()=>[u("div",be,[s[4]||(s[4]=u("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),m.value?(p(),E("span",Te,f(t(o)("merchant.dialogs.deleteConfirmMessage",{name:m.value.name})),1)):S("",!0)])]),_:1},8,["visible","header"])])}}}),et=Se(De,[["__scopeId","data-v-dd00adf2"]]);export{et as default};
