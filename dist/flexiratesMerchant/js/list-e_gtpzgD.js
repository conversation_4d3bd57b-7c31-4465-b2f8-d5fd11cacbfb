import{F as _}from"./index-Cui9J1ai.js";import{d as q,B as I,i as O,r,c as h,V as j,a as p,o as u,e as y,u as s,w as n,j as v,t as i,p as g}from"./index-DWWVg5mm.js";import{B as z}from"./BaseDataTable-75nyB6_U.js";import{S,_ as U}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{u as G}from"./useDict-BBitGqOA.js";import{u as $}from"./useListRefresh-AzGE_VAq.js";import{u as H,P as J}from"./usePermissions-DgTRxXRz.js";import{u as K}from"./useRequestList-Bk9BBkCQ.js";import{a as Q}from"./settlement-CxaYQjrm.js";import{f as X}from"./date-BWkRbs8K.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-yGDtrfNA.js";import"./index-C4fkh9Oe.js";import"./index-DDWh5zyb.js";import"./index-7nJYUDnq.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-BVQM_gwe.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-T2UmNbVz.js";import"./index-DimVHvgA.js";import"./index-DNL_tiqf.js";import"./dayjs.min-DUDfdLe0.js";const Z={class:"settlement-page"},ee={key:0},te={key:0},se=q({name:"settlementDetailList",__name:"list",setup(le){const b=I(),T=O(),x=r([{field:"business_id",header:"BID",style:{minWidth:"160px"}},{field:"settlement_currency",header:"Settlement Currency",style:{minWidth:"140px"}},{field:"settlement_amount",template:"settlement_amount",header:"Current Settlement Amount",style:{minWidth:"180px"}},{field:"settle_fee",template:"settle_fee",header:"Current Fee",style:{minWidth:"120px"}},{field:"settlement_type",template:"settlement_type",header:"Settlement Method",style:{minWidth:"140px"}},{field:"settle_at",template:"settle_at",header:"Settlement Date",style:{minWidth:"140px"}}]),{hasPermission:D}=H(),{list:L,loading:C,total:E,refresh:m,search:c,onPageChange:V,failed:k,failureMessage:w,loading:A,setSearchParams:a}=K({requestFn:Q});$("flexiratesMerchantSettlementList",m);const F=t=>{const{sortField:l,sortOrder:e}=t;a({sort_by:l,sort_order:e===1?"asc":"desc"}),c()},o=r({settlement_type:void 0,settle_at:""}),d=r([]),P=r(),{getLabel:R,options:B}=G("settlement_type"),N=h(()=>[{name:"settlement_type",label:"Settlement Type",type:S.SELECT,placeholder:"Select Settlement Type",options:[{label:"All",value:void 0},...B.value],defaultValue:void 0}]),W=h(()=>[{name:"settle_at[]",label:"Settlement Date",type:S.DATE_RANGE,placeholder:"Select Remit Date Range",defaultValue:""}]),M=({data:t})=>{D(J.SETTLEMENT_DETAIL)&&T.push({name:"settlementDetail",params:{id:t.id}})},f=()=>{a(o.value),c()};return j(()=>{const t=b.query;t&&Object.keys(t).length>0?(o.value={settlement_type:t.settlement_type?Number(t.settlement_type):void 0,settle_at:typeof t.settle_at=="string"?t.settle_at:""},a(o.value),m()):(o.value={settlement_type:void 0,settle_at:""},a(o.value),m())}),(t,l)=>(u(),p("div",Z,[y(U,{modelValue:o.value,"onUpdate:modelValue":l[0]||(l[0]=e=>o.value=e),loading:s(A),"basic-search-fields":N.value,"advanced-search-fields":W.value,onSearch:f},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),y(z,{ref_key:"settlementTable",ref:P,selection:d.value,"onUpdate:selection":l[1]||(l[1]=e=>d.value=e),"show-search-bar":!1,value:s(L),columns:x.value,scrollable:!0,"show-multiple-column":!1,loading:s(C),paginator:!0,rows:50,"total-records":s(E),lazy:!0,"data-key":"id",failed:s(k),"row-hover":!0,"failure-message":s(w),"striped-rows":!1,style:{"--frozen-column-border-bottom":"-8px"},onChangeSearch:f,onPage:l[2]||(l[2]=e=>s(V)(e)),onRowClick:M,onSort:F},{settlement_amount:n(({data:e})=>[e!=null&&e.settlement_amount?(u(),p("span",ee,i(s(_).formatAmount(e==null?void 0:e.settlement_amount)),1)):g("",!0)]),settle_fee:n(({data:e})=>[e!=null&&e.settle_fee?(u(),p("span",te,i(s(_).formatAmount(e==null?void 0:e.settle_fee)),1)):g("",!0)]),settlement_type:n(({data:e})=>[v(i(s(R)(e==null?void 0:e.settlement_type)),1)]),settle_at:n(({data:e})=>[v(i(s(X)(e.settle_at)),1)]),_:1},8,["selection","value","columns","loading","total-records","failed","failure-message"])]))}}),ke=Y(se,[["__scopeId","data-v-6a4f3b90"]]);export{ke as default};
