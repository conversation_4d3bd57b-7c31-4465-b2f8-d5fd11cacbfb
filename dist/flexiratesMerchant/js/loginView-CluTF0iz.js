import{d as G,k as z,r as w,f as U,l as B,m as j,a as A,p as F,b as o,q as K,v as S,o as y,x as O,e as c,u,n as D,j as I,w as b,F as Q,y as H,z as M,A as E,t as P,s as Y,i as J,B as W}from"./index-DWWVg5mm.js";import{s as X}from"./index-M77XcG3U.js";import{s as Z}from"./index-CutZZiSb.js";import{s as ee}from"./index-DNtGhG9S.js";import{s as ae}from"./index-W8aGMMs9.js";import{s as te}from"./index-DEgIf3z8.js";import{t as oe,c as le,a as re,b as L}from"./index.esm-DoKGfgcR.js";import{f as se,_ as ne}from"./login-bg-D47k_GaL.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BEwkorqa.js";import"./index-C4fkh9Oe.js";import"./index-CjzDW1dI.js";const ie={class:"recaptcha-wrap"},ce={key:0,class:"recaptcha-loading"},ue={key:1,class:"recaptcha-timeout"},de={key:2,class:"recaptcha-error"},me=G({__name:"index",props:{siteKey:{default:"6Lc7EhQrAAAAAIsN6MfROAMUqehxNI27h2gPHcG8"},theme:{default:"light"},size:{default:"normal"},tabindex:{default:0}},emits:["verify","expired","error"],setup(q,{expose:d,emit:R}){const k=q,r=R,{locale:$}=z(),m=w(null),n=w(null),e=w(!1),s=w(!0),x=w(!1);let _=null;const T=()=>new Promise((a,f)=>{if(window.grecaptcha){a();return}const g=document.createElement("script");g.src=`https://www.google.com/recaptcha/api.js?hl=${$.value}&render=explicit`,g.async=!0,g.defer=!0,g.onload=()=>a(),g.onerror=l=>f(l),document.head.appendChild(g)}),C=async()=>{if(await S(),!(!window.grecaptcha||!m.value)){if(n.value!==null){window.grecaptcha.reset(n.value);return}n.value=window.grecaptcha.render(m.value,{sitekey:k.siteKey,theme:k.theme,size:k.size,tabindex:k.tabindex,callback:a=>r("verify",a),"expired-callback":()=>r("expired"),"error-callback":()=>r("error")})}},p=()=>{window.grecaptcha&&n.value!==null&&window.grecaptcha.reset(n.value)},t=()=>window.grecaptcha&&n.value!==null?window.grecaptcha.getResponse(n.value):"",h=()=>{_!==null&&window.clearTimeout(_),_=window.setTimeout(()=>{s.value&&(s.value=!1,x.value=!0)},3e4)},i=()=>{_!==null&&(window.clearTimeout(_),_=null)},v=async()=>{try{s.value=!0,x.value=!1,e.value=!1,h(),await T();const a=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(a),C(),s.value=!1,i())},100)}catch(a){console.error("Failed to load reCAPTCHA on retry:",a),s.value=!1,e.value=!0,i(),r("error")}};return U(async()=>{try{s.value=!0,h(),await T();const a=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(a),C(),s.value=!1,i())},100)}catch(a){console.error("Failed to load reCAPTCHA:",a),s.value=!1,e.value=!0,i(),r("error")}}),B(()=>{if(window.grecaptcha&&n.value!==null){const a=m.value;a&&(a.innerHTML=""),n.value=null}i()}),j($,()=>{n.value!==null&&(n.value=null,m.value&&(m.value.innerHTML=""),e.value=!1,x.value=!1,s.value=!0,h(),T().then(async()=>{await S(),C(),s.value=!1,i()}).catch(a=>{console.error("Failed to load reCAPTCHA after locale change:",a),s.value=!1,e.value=!0,i(),r("error")}))}),d({reset:p,execute:t}),(a,f)=>(y(),A("div",ie,[s.value?(y(),A("div",ce,f[0]||(f[0]=[o("div",{class:"loading-spinner"},null,-1),o("p",null,"Loading Google reCAPTCHA...",-1)]))):x.value?(y(),A("div",ue,[f[1]||(f[1]=o("p",null,"Loading Google reCAPTCHA timed out after 30 seconds.",-1)),o("button",{class:"retry-button",onClick:v}," Retry ")])):e.value?(y(),A("div",de,[f[2]||(f[2]=o("p",null,"Failed to load Google reCAPTCHA. Please check your internet connection and try again.",-1)),o("button",{class:"retry-button",onClick:v}," Retry ")])):F("",!0),o("div",{ref_key:"recaptchaContainer",ref:m,class:K(a.$attrs.class)},null,2)]))}}),pe=N(me,[["__scopeId","data-v-7523f593"]]),fe={class:"auth-container"},ve={class:"logo-wrap"},ge={class:"logo"},we={class:"auth-content-wrap"},_e={class:"auth-content"},he={class:"flex gap-4 justify-between tools"},ye={class:"flex items-center gap-2"},ke={for:"rememberMe"},be=G({__name:"loginView",setup(q){const{t:d}=z(),R=w(!1),k=w(null),r=w(),$=J(),m=W(),n=O(),e=w({email:"",password:"",rememberMe:!1,google_token:""}),s=oe(le({email:L().required(d("validation.emailRequired")).email(d("validation.emailInvalid")),password:L().required(d("validation.passwordRequired")),google_token:L().required(d("validation.googleTokenRequired")),rememberMe:re()})),x=p=>{p&&(e.value.google_token=p,r.value&&r.value.setFieldValue("google_token",p))},_=()=>{e.value.google_token="",r.value&&r.value.setFieldValue("google_token","")},T=()=>{e.value.google_token="",r.value&&r.value.setFieldValue("google_token","")},C=async()=>{var h,i,v;const{valid:p,errors:t}=await((h=r.value)==null?void 0:h.validate());if(p){console.log(p,t);try{R.value=!0,await n.login(e.value.email,e.value.password,e.value.google_token,e.value.rememberMe),$.replace(((i=m==null?void 0:m.query)==null?void 0:i.redirect)||"/")}catch{e.value.google_token="",(v=k.value)==null||v.reset()}finally{R.value=!1}}};return document.title="Cardinia",(p,t)=>{const h=te,i=ae,v=ee,a=Z,f=X,g=Y;return y(),A("div",fe,[o("div",ve,[o("div",ge,[c(h,{src:u(se),width:"250px",alt:"Image"},null,8,["src"])])]),o("div",{class:"auth-bg-wrap",style:D({backgroundImage:`url(${u(ne)})`,backgroundSize:"100% 100%"})},null,4),o("div",we,[o("div",_e,[t[6]||(t[6]=o("h1",{class:"title"}," Hello! ",-1)),t[7]||(t[7]=o("p",{class:"subtitle"},[I(" Login "),o("span",{class:"subtitle-account"},"Your Account")],-1)),c(u(Q),{ref_key:"formRef",ref:r,"validation-schema":u(s),class:"login-form",onSubmit:C},{default:b(()=>[c(u(H),{modelValue:e.value.email,"onUpdate:modelValue":t[0]||(t[0]=l=>e.value.email=l),as:"div",class:"flex flex-col gap-4",name:"email"},{default:b(({field:l,errorMessage:V})=>[c(i,E(l,{class:"form-input",name:"email",type:"text",placeholder:u(d)("login.email")}),null,16,["placeholder"]),V?(y(),M(v,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:b(()=>[I(P(V),1)]),_:2},1024)):F("",!0)]),_:1},8,["modelValue"]),c(u(H),{modelValue:e.value.password,"onUpdate:modelValue":t[1]||(t[1]=l=>e.value.password=l),as:"div",class:"flex flex-col gap-4",name:"password"},{default:b(({field:l,errorMessage:V})=>[c(a,E(l,{type:"text",placeholder:u(d)("login.password"),"toggle-mask":"",fluid:"",feedback:!1}),null,16,["placeholder"]),V?(y(),M(v,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:b(()=>[I(P(V),1)]),_:2},1024)):F("",!0)]),_:1},8,["modelValue"]),o("div",he,[o("div",ye,[c(f,{modelValue:e.value.rememberMe,"onUpdate:modelValue":t[2]||(t[2]=l=>e.value.rememberMe=l),name:"rememberMe",binary:""},null,8,["modelValue"]),o("label",ke,P(u(d)("login.rememberMe")),1)]),c(g,{text:"",label:u(d)("login.forgotPassword"),class:"!p-0",onClick:t[3]||(t[3]=l=>p.$router.push("/forgot-password"))},null,8,["label"])]),c(u(H),{modelValue:e.value.google_token,"onUpdate:modelValue":t[4]||(t[4]=l=>e.value.google_token=l),as:"div",class:"flex flex-col",name:"google_token"},{default:b(({errorMessage:l})=>[t[5]||(t[5]=o("p",{class:"text-lg !m-0",style:{"font-weight":"600"}}," *CAPTCHA ",-1)),c(pe,{ref_key:"recaptchaRef",ref:k,name:"google_token",class:"mb-2 mt-2",onVerify:x,onExpired:_,onError:T},null,512),l?(y(),M(v,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:b(()=>[I(P(l),1)]),_:2},1024)):F("",!0)]),_:1,__:[5]},8,["modelValue"]),c(g,{class:"login-submit",loading:R.value,severity:"warn",label:"LOGIN",onClick:C},null,8,["loading"])]),_:1},8,["validation-schema"])])])])}}}),Le=N(be,[["__scopeId","data-v-36f35bc7"]]);export{Le as default};
