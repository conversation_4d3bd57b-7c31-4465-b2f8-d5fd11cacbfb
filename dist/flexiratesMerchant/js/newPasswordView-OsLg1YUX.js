import{C as M,D as Y,d as J,r as b,a as p,b as n,e as f,u as m,n as x,w as _,F as Q,y as V,p as P,z as A,j as h,A as F,E as W,G as X,q as ee,t as O,s as te,B as se,i as re,o as d,H as oe}from"./index-DWWVg5mm.js";import{s as ne}from"./index-DNtGhG9S.js";import{s as ae}from"./index-CutZZiSb.js";import{s as le}from"./index-DEgIf3z8.js";import{_ as ie}from"./isUpdated-DXQm7BuR.js";import{Z as S,a as ce,c as L,o as ue,s as k}from"./types-C_HOBWe9.js";import{f as de,_ as fe}from"./login-bg-D47k_GaL.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C4fkh9Oe.js";import"./index-CjzDW1dI.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";/**
  * vee-validate v4.15.0
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */const U=e=>e!==null&&!!e&&typeof e=="object"&&!Array.isArray(e);function Z(e){return Number(e)>=0}function me(e){return typeof e=="object"&&e!==null}function ge(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}function T(e){if(!me(e)||ge(e)!=="[object Object]")return!1;if(Object.getPrototypeOf(e)===null)return!0;let s=e;for(;Object.getPrototypeOf(s)!==null;)s=Object.getPrototypeOf(s);return Object.getPrototypeOf(e)===s}function C(e,s){return Object.keys(s).forEach(o=>{if(T(s[o])&&T(e[o])){e[o]||(e[o]={}),C(e[o],s[o]);return}e[o]=s[o]}),e}function we(e){const s=e.split(".");if(!s.length)return"";let o=String(s[0]);for(let t=1;t<s.length;t++){if(Z(s[t])){o+=`[${s[t]}]`;continue}o+=`.${s[t]}`}return o}function _e(e,s){return{__type:"VVTypedSchema",async parse(t){const a=await e.safeParseAsync(t,s);if(a.success)return{value:a.data,errors:[]};const i={};return D(a.error.issues,i),{errors:Object.values(i)}},cast(t){try{return e.parse(t)}catch{const i=I(e);return U(i)&&U(t)?C(i,t):t}},describe(t){try{if(!t)return{required:!e.isOptional(),exists:!0};const a=he(t,e);return a?{required:!a.isOptional(),exists:!0}:{required:!1,exists:!1}}catch{return{required:!1,exists:!1}}}}}function D(e,s){e.forEach(o=>{const t=we(o.path.join("."));o.code==="invalid_union"&&(D(o.unionErrors.flatMap(a=>a.issues),s),!t)||(s[t]||(s[t]={errors:[],path:t}),s[t].errors.push(o.message))})}function I(e){if(e instanceof S)return Object.fromEntries(Object.entries(e.shape).map(([s,o])=>o instanceof ce?[s,o._def.defaultValue()]:o instanceof S?[s,I(o)]:[s,void 0]))}function he(e,s){if(!B(s))return null;if(M(e))return s.shape[Y(e)];const o=(e||"").split(/\.|\[(\d+)\]/).filter(Boolean);let t=s;for(let a=0;a<=o.length;a++){const i=o[a];if(!i||!t)return t;if(B(t)){t=t.shape[i]||null;continue}Z(i)&&be(t)&&(t=t._def.type)}return null}function R(e){return e._def.typeName}function be(e){return R(e)===L.ZodArray}function B(e){return R(e)===L.ZodObject}const ve={class:"auth-container"},ye={class:"logo-wrap"},xe={class:"logo"},Pe={class:"auth-content-wrap"},Oe={class:"auth-content"},je={key:0},Ee={class:"flex flex-col gap-4"},Ne={key:0,class:"strength-container"},Ve={class:"strength-bars"},Ae={class:"strength-indicator"},Fe={key:1,class:"password-updated flex flex-col justify-center items-center gap-4"},Se=J({__name:"newPasswordView",setup(e){const s=se(),o=re(),t=b(!1),a=b(!1),i=b(),g=b({password:"",reEnterPassword:""}),$=_e(ue({password:k().min(8,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}).regex(/\d/,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}).regex(/[a-z]/,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}).regex(/[A-Z]/,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}),reEnterPassword:k().refine(l=>l===g.value.password,{message:"Password do not match"})})),q=async l=>{var r;try{t.value=!0;const c=(r=s.params)==null?void 0:r.token;(await oe({new_password:l.password,token:c})).code===0&&(a.value=!0,setTimeout(()=>{o.push("/merchant/login")},2e3))}catch{}finally{t.value=!1}},v=l=>{const r={hasLower:/[a-z]/.test(l),hasUpper:/[A-Z]/.test(l),hasNumber:/\d/.test(l),isLongEnough:l.length>=12},c=Object.values(r).filter(Boolean).length;return c<=1?"Low":c<=3?"Average":"Excellent"},z=l=>{const r=v(l);return{Low:"#eb001b",Average:"#FFD700",Excellent:"#7ed956"}[r]},G=l=>{const r=v(l),c={Low:"#eb001b",Average:"#FFD700",Excellent:"#7ed956"};return[{active:r!=="None",color:c[r]},{active:["Average","Excellent"].includes(r),color:["Average","Excellent"].includes(r)?c[r]:"#fff"},{active:r==="Excellent",color:r==="Excellent"?c[r]:"#fff"}]};return(l,r)=>{const c=le,y=ae,j=ne,H=te;return d(),p("div",ve,[n("div",ye,[n("div",xe,[f(c,{src:m(de),width:"230px",alt:"Image"},null,8,["src"])])]),n("div",{class:"auth-bg-wrap",style:x({backgroundImage:`url(${m(fe)})`,backgroundSize:"100% 100%"})},null,4),n("div",Pe,[n("div",Oe,[a.value?(d(),p("div",Fe,r[7]||(r[7]=[n("div",{class:"img"},[n("img",{src:ie,alt:"",style:{width:"136px"}})],-1),n("div",{class:"title"}," Password Updated! ",-1),n("div",{class:"forgot-subtitle",style:{"margin-top":"0"}}," Please wait.You will be redirected to the Login Page. ",-1)]))):(d(),p("div",je,[r[5]||(r[5]=n("h1",{class:"title"}," New Password ",-1)),r[6]||(r[6]=n("p",{class:"forgot-subtitle"}," Enter a new password. ",-1)),f(m(Q),{ref_key:"newPasswordFormRef",ref:i,"validation-schema":m($),class:"flex flex-col gap-6 w-full new-password-form",onSubmit:q},{default:_(()=>[n("div",Ee,[f(m(V),{modelValue:g.value.password,"onUpdate:modelValue":r[0]||(r[0]=u=>g.value.password=u),as:"div",class:"flex flex-col gap-4",name:"password"},{default:_(({field:u,errorMessage:w})=>{var E;return[r[3]||(r[3]=n("label",{class:"form-label"},[h("New Password"),n("span",{class:"text-[#ff3131]"},"*")],-1)),f(y,F(u,{type:"text",placeholder:"Password","toggle-mask":"",fluid:"",feedback:!1}),null,16),((E=u.value)==null?void 0:E.length)>0?(d(),p("div",Ne,[n("div",Ve,[(d(!0),p(W,null,X(G(u.value),(N,K)=>(d(),p("div",{key:K,class:ee(["strength-segment",{active:N.active}]),style:x({backgroundColor:N.color})},null,6))),128))]),n("small",Ae,[r[2]||(r[2]=h(" Password strength: ")),n("span",{style:x({color:z(u.value)})},O(v(u.value)),5)])])):P("",!0),w?(d(),A(j,{key:1,class:"px-[1rem]",severity:"error",variant:"simple"},{default:_(()=>[h(O(w),1)]),_:2},1024)):P("",!0)]}),_:1,__:[3]},8,["modelValue"]),f(m(V),{modelValue:g.value.reEnterPassword,"onUpdate:modelValue":r[1]||(r[1]=u=>g.value.reEnterPassword=u),as:"div",class:"flex flex-col gap-4 mt-4",name:"reEnterPassword"},{default:_(({field:u,errorMessage:w})=>[r[4]||(r[4]=n("label",{class:"form-label"},[h("Re-enter Password"),n("span",{class:"text-[#ff3131]"},"*")],-1)),f(y,F(u,{type:"text",placeholder:"Re-enter Password","toggle-mask":"",fluid:"",feedback:!1}),null,16),w?(d(),A(j,{key:0,class:"px-[1rem]",severity:"error",variant:"simple"},{default:_(()=>[h(O(w),1)]),_:2},1024)):P("",!0)]),_:1,__:[4]},8,["modelValue"])]),f(H,{loading:t.value,class:"!mt-6 w-full login-submit",type:"submit",severity:"warn",label:"SUBMIT"},null,8,["loading"])]),_:1},8,["validation-schema"])]))])])])}}}),ze=pe(Se,[["__scopeId","data-v-b2d23a62"]]);export{ze as default};
