import{R as i,aL as s,Z as r,r as l}from"./index-DWWVg5mm.js";const g=t=>i("/cardinia/notify/list",{params:t}),y=t=>s("/cardinia/notify/delete",t),p=t=>s("/cardinia/notify/updateRead",t),c=()=>i("/cardinia/notify/unreadCount"),v=r("notifications",()=>{const t=l(0);let n=null;const a=async()=>{var e;try{const o=await c();t.value=((e=o==null?void 0:o.data)==null?void 0:e.count)||0}catch{t.value=0}};return{unreadCount:t,getUnreadCount:a,startPolling:()=>{a(),n&&clearInterval(n),n=window.setInterval(a,5e3)},stopPolling:()=>{n&&clearInterval(n),n=null}}},{persist:!0});export{p as a,y as d,g,v as u};
