import{d as z,O as A,x as q,k as D,r as p,f as P,a as V,o as d,b as t,e as o,t as i,z as S,w as c,p as T,u as n,j as U,s as M}from"./index-DWWVg5mm.js";import{s as L}from"./index-M77XcG3U.js";import{s as O}from"./index-DNtGhG9S.js";import{h as R,a as G,s as H}from"./index-BKi0p8Ug.js";import{s as J}from"./index-W8aGMMs9.js";import{s as K}from"./index-B_CyaZAL.js";import{_ as Q}from"./BaseFileUpload-DIyBFyns.js";import{o as W,b as X,s as b}from"./types-C_HOBWe9.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BEwkorqa.js";import"./index-Br9OVcmc.js";const Z={class:"profile-page"},$={class:"profile-header"},ee={class:"profile-info"},ae={class:"avatar-wrapper"},se={class:"profile-name"},te={class:"profile-form"},le={key:0,class:"loading-container"},oe={for:"email"},re={class:"flex align-items-center"},ne={for:"mfa_check",class:"ml-2"},ie={class:"text-gray-500"},ce={class:"flex gap-4 flex-2"},me=z({__name:"profile",setup(ue){const h=A(),y=q(),{t:a}=D(),x=p(!1),m=p(!1),B=p(R(W({name:b().min(1,a("user.profileSettings.nameRequired")),nickname:b().optional(),email:b().email(a("user.profileSettings.emailInvalid")),mfa_check:X().optional()}))),s=p({avatar:"",name:"",email:"",merchant_id:"",mfa_check:!1}),f=p([]),_=async()=>{x.value=!0;try{const l=await y.getUserInfo();l&&(s.value.name=l.name,s.value.email=l.email,s.value.merchant_id=l.merchant_id,s.value.mfa_check=!!l.mfa_check,s.value.avatar=l.avatar||""),l.avatar?f.value=[{url:l.avatar}]:f.value=[]}finally{x.value=!1}},C=async({valid:l,values:e})=>{if(l){m.value=!0;try{await y.updateUserInfo({user_name:e.name,email:e.email,avatar:e.avatar,mfa_check:e.mfa_check?1:0}),_(),h.add({severity:"success",summary:a("common.success"),detail:a("user.profileSettings.updateSuccess"),life:3e3})}finally{m.value=!1}}},F=async l=>{var v;const e=((v=l.response.data)==null?void 0:v.file_path[0])||"";if(!e){h.add({severity:"error",summary:a("common.error"),detail:a("user.profileSettings.uploadError"),life:3e3});return}s.value.avatar=e,m.value=!0;try{await y.updateUserInfo({user_name:s.value.name,email:s.value.email,mfa_check:s.value.mfa_check?1:0,avatar:e}),await _(),s.value.avatar=e,h.add({severity:"success",summary:a("common.success"),detail:a("user.profileSettings.updateSuccess")})}finally{m.value=!1}},j=()=>{_()};return P(async()=>{_()}),(l,e)=>{const v=Q,E=K,k=J,g=G,w=O,N=L,I=M;return d(),V("div",Z,[t("div",$,[t("div",ee,[t("div",ae,[o(v,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=r=>f.value=r),mode:"avatar",multiple:!1,"auto-upload":!0,accept:"image/jpeg,image/png,.jpg,.jpeg,.png","max-size":1024*1024*10,class:"avatar-uploader",onSuccess:F},null,8,["modelValue"]),t("div",se,[t("h2",null,i(s.value.name),1),t("p",null,i(s.value.email),1)])])])]),t("div",te,[x.value?(d(),V("div",le,[o(E)])):(d(),S(n(H),{key:1,"initial-values":s.value,resolver:B.value,class:"max-w-200 space-y-6",onSubmit:C},{default:c(()=>[o(g,{name:"merchant_id",class:"flex flex-col gap-3"},{default:c(()=>[e[1]||(e[1]=t("label",{for:"merchant_id"},"merchant id",-1)),o(k,{"model-value":s.value.merchant_id,disabled:!0,maxlength:"50"},null,8,["model-value"])]),_:1,__:[1]}),o(g,{name:"name",required:"",class:"flex flex-col gap-3"},{default:c(r=>[e[2]||(e[2]=t("label",{for:"name"},"Name",-1)),o(k,{placeholder:n(a)("user.profileSettings.namePlaceholder"),maxlength:"50"},null,8,["placeholder"]),r!=null&&r.invalid?(d(),S(w,{key:0,severity:"error",size:"small",variant:"simple"},{default:c(()=>{var u;return[U(i((u=r.error)==null?void 0:u.message),1)]}),_:2},1024)):T("",!0)]),_:1,__:[2]}),o(g,{name:"email",required:"",class:"flex flex-col gap-3"},{default:c(r=>[t("label",oe,i(n(a)("user.profileSettings.email")),1),o(k,{placeholder:n(a)("user.profileSettings.emailPlaceholder"),maxlength:"50"},null,8,["placeholder"]),r!=null&&r.invalid?(d(),S(w,{key:0,severity:"error",size:"small",variant:"simple"},{default:c(()=>{var u;return[U(i((u=r.error)==null?void 0:u.message),1)]}),_:2},1024)):T("",!0)]),_:1}),o(g,{name:"mfa_check",class:"flex flex-col gap-3 col-span-2"},{default:c(()=>[t("div",re,[o(N,{"input-id":"mfa_check",name:"mfa_check",binary:""}),t("label",ne,i(n(a)("user.profileSettings.enable2FA")||"Enable Two-Factor Authentication"),1)]),t("small",ie,i(n(a)("user.profileSettings.2FADescription")||"Enable two-factor authentication to add an extra layer of security to your account."),1)]),_:1}),t("div",ce,[o(I,{label:n(a)("user.profileSettings.saveChanges"),severity:"warn",loading:m.value,type:"submit"},null,8,["label","loading"]),o(I,{label:n(a)("user.profileSettings.cancel"),severity:"secondary",type:"button",onClick:j},null,8,["label"])])]),_:1},8,["initial-values","resolver"]))])])}}}),be=Y(me,[["__scopeId","data-v-9a45ee91"]]);export{be as default};
