import{aL as e,R as t}from"./index-DWWVg5mm.js";const c=a=>t("/cardinia/ratePayer/list",{params:a}),s=a=>t("/cardinia/ratePayer/detail",{params:a}),o=a=>t("/cardinia/property/baseInfo",{params:a}),n=a=>t("/cardinia/ratePayer/export",{params:a}),d=a=>e("/cardinia/ratePayer/create",a),y=(a,r)=>e("/cardinia/ratePayer/update",{...r,customer_id:a}),p=a=>t("/cardinia/ratePayer/noteList",{params:a}),g=a=>e("/cardinia/ratePayer/addNote",a),P=a=>t("/cardinia/activityLog/list",{params:a}),L=a=>t("/cardinia/activityLog/export",{params:a});export{s as a,P as b,d as c,p as d,L as e,g as f,o as g,c as h,n as i,y as u};
