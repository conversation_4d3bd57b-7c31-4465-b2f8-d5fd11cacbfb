import{s as O}from"./index-T2UmNbVz.js";import{d as Q,r as D,z as c,o as r,w as d,b as l,e as i,a as W,p as _,u as f,y as p,A as k,j as u,t as v,s as X,F as Z}from"./index-DWWVg5mm.js";import{s as C}from"./index-DNtGhG9S.js";import{s as M}from"./index-W8aGMMs9.js";import{d as V}from"./dayjs.min-DUDfdLe0.js";import{_ as ee}from"./bankAccount.vue_vue_type_script_setup_true_lang-CWOoX_Ll.js";import te from"./creditCard-BfPJagTg.js";import{_ as ae}from"./scheduleForm.vue_vue_type_script_setup_true_lang-D6vmpJOy.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-BEwkorqa.js";import"./index.esm-DoKGfgcR.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-IMPOxVZV.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";import"./index-D2QnTskR.js";import"./decimal-LZMqkRTm.js";const se={class:"form-row"},oe={class:"field"},ie={class:"field"},ne={class:"form-row"},de={class:"field"},me={class:"field"},re={class:"field"},ce={class:"field"},_e={class:"form-row"},ue={class:"field"},fe={class:"field"},pe={class:"form-row"},ve={class:"field"},ye={class:"field"},be={class:"form-row justify-end"},ke={key:0,class:"text-2xl font-bold"},he={class:"form-row"},we={class:"field flex flex-col gap-2"},xe={class:"flex items-center gap-2"},ge={class:"form-item__content flex flex-col gap-y-2 my-2"},Se={class:"flex items-center gap-x-2"},De=["onChange"],Ne={class:"flex justify-end mt-6 gap-2"},Pe=Q({__name:"ratepayerForm",props:{initialValues:{},validationSchema:{},loading:{type:Boolean},verifyLoading:{type:Boolean},scheduleData:{}},emits:["submit","cancel","verify"],setup(H,{emit:J}){const S=H,R=J,x=D(),N=D(),B=D(),U=D(),$=async()=>{var h,y,w,s,t,b,g,F,Y,j,E,L,A,T,q,I,z,G;const o=await((h=x.value)==null?void 0:h.validate());if(!(o!=null&&o.valid)){console.log("formValues",o==null?void 0:o.errors);return}if(!N.value)return window.$toast.add({severity:"error",summary:"Error",detail:"Please get the schedule first"});const{valid:a,values:n}=await((y=N.value)==null?void 0:y.validate());if(!a)return;const m={first_name:(w=o.values)==null?void 0:w.first_name,last_name:(s=o.values)==null?void 0:s.last_name,mobile:(t=o.values)==null?void 0:t.mobile_number,email:(b=o.values)==null?void 0:b.email_address,street_address:(g=o.values)==null?void 0:g.street_address,city:(F=o.values)==null?void 0:F.city,state:(Y=o.values)==null?void 0:Y.state,postcode:(j=o.values)==null?void 0:j.postcode,property:{property_number:(E=o.values)==null?void 0:E.property_number,verification_code:(L=o.values)==null?void 0:L.verification_code},payment_frequency:{payment_plan:n==null?void 0:n.plan,first_payment_date:n!=null&&n.first_payment_date?V(n==null?void 0:n.first_payment_date).format("YYYY-MM-DD"):null,amount:n==null?void 0:n.amount},banking:{type:((A=o.values)==null?void 0:A.payment_method)==="credit_card"?2:1,weight:(T=o.values)!=null&&T.is_default_payment_method?1:0}};if(((q=o.values)==null?void 0:q.payment_method)==="credit_card"){const{valid:P,values:e}=await((I=B.value)==null?void 0:I.validate());if(!P)return;m.banking.card={card_number:e==null?void 0:e.card_number,city:e==null?void 0:e.city,country_iso2:e==null?void 0:e.country_iso2,email:e==null?void 0:e.email,expiration_month:e==null?void 0:e.expiry_month,expiration_year:e==null?void 0:e.expiry_year,name_on_card:e==null?void 0:e.name_on_card,security_code:e==null?void 0:e.security_code,state:e==null?void 0:e.state,first_name:e==null?void 0:e.first_name,last_name:e==null?void 0:e.last_name,line_1:e==null?void 0:e.street_address,line_2:e==null?void 0:e.street_address_2,postcode:e==null?void 0:e.postcode,phone:e==null?void 0:e.phone,nickname:e==null?void 0:e.nickname}}if(((z=o.values)==null?void 0:z.payment_method)==="bank_account"){const{valid:P,values:e}=await((G=U.value)==null?void 0:G.validate());if(!P)return;m.banking.bank={bsb:e==null?void 0:e.bsb_number,account_name:e==null?void 0:e.account_name,account_no:e==null?void 0:e.account_number,nickname:e==null?void 0:e.nickname}}R("submit",m)},K=async()=>{var n,m,h,y;const o=await((n=x.value)==null?void 0:n.validateField("property_number")),a=await((m=x.value)==null?void 0:m.validateField("verification_code"));o!=null&&o.valid&&(a!=null&&a.valid)&&R("verify",{property_number:(h=x.value)==null?void 0:h.values.property_number,verification_code:(y=x.value)==null?void 0:y.values.verification_code})};return(o,a)=>{const n=M,m=C,h=X,y=O;return r(),c(f(Z),{ref_key:"formRef",ref:x,"validation-schema":S.validationSchema,class:"ratepayer-form flex flex-col gap-4","initial-values":o.initialValues,onSubmit:$},{default:d(({values:w})=>[a[17]||(a[17]=l("div",{class:"text-2xl font-bold"}," Ratepayer Details ",-1)),l("div",se,[i(f(p),{name:"first_name",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",oe,[a[2]||(a[2]=l("label",{for:"first_name",class:"mb-2 block"},"First Name",-1)),i(n,k({id:"first_name"},s,{placeholder:"First Name",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1}),i(f(p),{name:"last_name",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",ie,[a[3]||(a[3]=l("label",{for:"last_name",class:"mb-2 block"},"Last Name",-1)),i(n,k({id:"last_name"},s,{placeholder:"Last Name",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1})]),l("div",ne,[i(f(p),{name:"mobile_number",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",de,[a[4]||(a[4]=l("label",{for:"mobile_number",class:"mb-2 block"},"Mobile Number",-1)),i(n,k({id:"mobile_number"},s,{placeholder:"Mobile Number",class:["w-full",{"p-invalid":t}],type:"tel"}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1}),i(f(p),{name:"email_address",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",me,[a[5]||(a[5]=l("label",{for:"email_address",class:"mb-2 block"},"Email Address",-1)),i(n,k({id:"email_address"},s,{placeholder:"Email Address",class:["w-full",{"p-invalid":t}],type:"email"}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1})]),a[18]||(a[18]=l("div",{class:"mt-4"},[l("h3",{class:"text-lg font-medium mb-2"}," Mailing Address ")],-1)),i(f(p),{name:"street_address",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",re,[a[6]||(a[6]=l("label",{for:"street_address",class:"mb-2 block"},"Street Address",-1)),i(n,k({id:"street_address"},s,{placeholder:"Street Address",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1}),i(f(p),{name:"city",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",ce,[a[7]||(a[7]=l("label",{for:"city",class:"mb-2 block"},"City",-1)),i(n,k({id:"city"},s,{placeholder:"city",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1}),l("div",_e,[i(f(p),{name:"state",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",ue,[a[8]||(a[8]=l("label",{for:"state",class:"mb-2 block"},"State",-1)),i(n,k({id:"state"},s,{placeholder:"State",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1}),i(f(p),{name:"postcode",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",fe,[a[9]||(a[9]=l("label",{for:"postcode",class:"mb-2 block"},"Postcode",-1)),i(n,k({id:"postcode"},s,{placeholder:"Postcode",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1})]),a[19]||(a[19]=l("div",{class:"text-2xl font-bold"}," Property Details ",-1)),l("div",pe,[i(f(p),{name:"property_number",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",ve,[a[10]||(a[10]=l("label",{for:"property_number",class:"mb-2 block"},"Property Number",-1)),i(n,k({id:"property_number"},s,{placeholder:"Property Number",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1}),i(f(p),{name:"verification_code",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",ye,[a[11]||(a[11]=l("label",{for:"verification_code",class:"mb-2 block"},"Verification Code",-1)),i(n,k({id:"verification_code"},s,{placeholder:"Verification Code",class:["w-full",{"p-invalid":t}]}),null,16,["class"]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1})]),l("div",be,[i(h,{type:"button",label:"Get Schedule",disabled:w.property_number===""||w.verification_code==="",loading:o.verifyLoading,onClick:K},null,8,["disabled","loading"])]),S.scheduleData.dict_payment_plan.length>0?(r(),W("div",ke," Schedule ")):_("",!0),S.scheduleData.dict_payment_plan.length>0?(r(),c(ae,{key:1,ref_key:"scheduleFormRef",ref:N,"schedule-data":S.scheduleData},null,8,["schedule-data"])):_("",!0),a[20]||(a[20]=l("div",{class:"text-2xl font-bold"}," Payment Methods ",-1)),l("div",he,[i(f(p),{name:"payment_method",as:"div",class:"form-col"},{default:d(({field:s,errorMessage:t})=>[l("div",we,[l("div",xe,[i(y,{modelValue:s.value,"onUpdate:modelValue":b=>s.value=b,"input-id":"credit_card",name:"payment_method",value:"credit_card"},null,8,["modelValue","onUpdate:modelValue"]),a[12]||(a[12]=l("label",{for:"credit_card",class:"cursor-pointer"},"Credit Card",-1)),i(y,{modelValue:s.value,"onUpdate:modelValue":b=>s.value=b,class:"ml-4","input-id":"bank_account",name:"payment_method",value:"bank_account"},null,8,["modelValue","onUpdate:modelValue"]),a[13]||(a[13]=l("label",{for:"bank_account",class:"cursor-pointer"},"Bank Account",-1))]),t?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(t),1)]),_:2},1024)):_("",!0)])]),_:1})]),w.payment_method==="credit_card"?(r(),c(te,{key:2,ref_key:"creditCardRef",ref:B},null,512)):_("",!0),w.payment_method==="bank_account"?(r(),c(ee,{key:3,ref_key:"bankAccountRef",ref:U},null,512)):_("",!0),i(f(p),{modelValue:o.initialValues.is_default_payment_method,"onUpdate:modelValue":a[0]||(a[0]=s=>o.initialValues.is_default_payment_method=s),name:"is_default_payment_method",as:"div",class:"form-item is-required"},{default:d(({errorMessage:s,field:t,handleChange:b})=>[l("div",ge,[l("div",Se,[i(y,{modelValue:t.value,"onUpdate:modelValue":g=>t.value=g,"input-id":"is_default_payment_method_primary",name:"is_default_payment_method",value:!0,onChange:b},null,8,["modelValue","onUpdate:modelValue","onChange"]),l("label",{for:"is_default_payment_method_primary",class:"cursor-pointer",onChange:b},a[14]||(a[14]=[u("Make Default "),l("strong",null,"Primary",-1),u(" Payment Method?")]),40,De),i(y,{modelValue:t.value,"onUpdate:modelValue":g=>t.value=g,class:"ml-4","input-id":"is_default_payment_method_secondary",name:"is_default_payment_method",value:!1,onChange:b},null,8,["modelValue","onUpdate:modelValue","onChange"]),a[15]||(a[15]=l("label",{for:"is_default_payment_method_secondary",class:"cursor-pointer"},[u("Make Default "),l("strong",null,"Secondary"),u(" Payment Method?")],-1))]),s?(r(),c(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[u(v(s),1)]),_:2},1024)):_("",!0),a[16]||(a[16]=l("p",{class:"text-sm text-gray-500 my-2"}," This will not affect any current active schedules. Your selected preference will be used to autofill future registrations. ",-1))])]),_:1},8,["modelValue"]),l("div",Ne,[i(h,{type:"button",label:"Cancel",icon:"pi pi-times",class:"p-button-text",onClick:a[1]||(a[1]=s=>o.$emit("cancel"))}),i(h,{type:"button",label:"Save",icon:"pi pi-check",loading:o.loading,onClick:$},null,8,["loading"])])]),_:1,__:[17,18,19,20]},8,["validation-schema","initial-values"])}}}),Qe=le(Pe,[["__scopeId","data-v-35610b60"]]);export{Qe as default};
