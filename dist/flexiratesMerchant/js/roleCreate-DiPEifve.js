import{d as U,k as B,O as j,i as I,r as b,W as O,f as q,a as z,o as h,b as n,t as i,u as a,e as r,w as m,y,z as g,p as k,A as V,j as _,s as D,F as E,bT as K,bU as W}from"./index-DWWVg5mm.js";import{s as A}from"./index-DDFpWte-.js";import{s as M}from"./index-DNtGhG9S.js";import{s as G}from"./index-W8aGMMs9.js";import{t as H,c as J,b as C}from"./index.esm-DoKGfgcR.js";import{u as Q}from"./useListRefresh-AzGE_VAq.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-7nJYUDnq.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";const Y={class:"merchant-role-create-page"},Z={class:"flex justify-between items-center mb-4"},ee={class:"text-3xl"},se={class:"p-4 bg-white rounded-2xl"},ae={class:"field"},te={for:"name",class:"mb-2 block"},oe={class:"field"},le={for:"slug",class:"mb-2 block"},ne={for:"permissions",class:"mb-2 block"},re={class:"flex justify-content-end mt-6"},ce=U({name:"flexiratesMerchantUserRoleCreate",__name:"roleCreate",setup(ie){const{t:s}=B(),S=j(),L=I(),x=b(),f=b(!1),{backWithRefresh:$}=Q("flexiratesMerchantUserRoleList",()=>{}),o=O({name:"",slug:"",permissions:[]}),w=b([]),R=u=>u.map(e=>{var c;return{key:String(e.id),label:e.name,data:e,children:(c=e==null?void 0:e.children)!=null&&c.length?R(e.children):[]}}),F=async()=>{const u=await K();w.value=R(u.data)},N=H(J({name:C().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})).required(),slug:C().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})).matches(/^[a-z0-9-]+$/,"Slug must contain only lowercase letters, numbers, and hyphens").required()})),P=async()=>{var u;f.value=!0;try{const e=await((u=x.value)==null?void 0:u.validate()),c=[];for(const d in o.permissions)Object.prototype.hasOwnProperty.call(o.permissions,d)&&o.permissions[d].checked&&c.push(d);if(!e.valid){f.value=!1;return}const p={role_name:e.values.name,role_mark:e.values.slug,permissions:c},{code:v}=await W(p);if(v!==0)throw new Error("Failed to create role");S.add({severity:"success",summary:s("common.success"),detail:s("merchant.role.messages.createSuccess","Role created successfully"),life:3e3}),$()}catch(e){console.error("Failed to create role:",e)}finally{f.value=!1}},T=()=>{L.back()};return q(()=>{F()}),(u,e)=>{const c=G,p=M,v=A,d=D;return h(),z("div",Y,[n("div",Z,[n("h1",ee,i(a(s)("menu.userRoleCreate","Create Role")),1)]),n("div",se,[r(a(E),{ref_key:"formRef",ref:x,"validation-schema":a(N),class:"merchant-role-form flex flex-col gap-4",onSubmit:P},{default:m(()=>[r(a(y),{modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=t=>o.name=t),name:"name",class:"form-col"},{default:m(({field:t,errorMessage:l})=>[n("div",ae,[n("label",te,i(a(s)("merchant.role.columns.name","Role Name"))+"*",1),r(c,V({id:"name"},t,{placeholder:a(s)("merchant.role.columns.name","Role Name"),class:"w-full",autofocus:""}),null,16,["placeholder"]),l?(h(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[_(i(l),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),r(a(y),{modelValue:o.slug,"onUpdate:modelValue":e[1]||(e[1]=t=>o.slug=t),name:"slug",class:"form-col"},{default:m(({field:t,errorMessage:l})=>[n("div",oe,[n("label",le,i(a(s)("merchant.role.columns.slug","Slug"))+"*",1),r(c,V({id:"slug"},t,{placeholder:a(s)("merchant.role.columns.slug","Slug"),class:"w-full"}),null,16,["placeholder"]),l?(h(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[_(i(l),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),r(a(y),{name:"permissions",class:"form-col"},{default:m(({errorMessage:t})=>[n("label",ne,i(a(s)("merchant.role.columns.permissions","Permissions")),1),r(v,{"selection-keys":o.permissions,"onUpdate:selectionKeys":e[2]||(e[2]=l=>o.permissions=l),value:w.value,"selection-mode":"checkbox",class:"w-full md:w-[30rem] !p-0",placeholder:"Select permissions"},{content:m(({node:l})=>[_(i(l),1)]),_:1},8,["selection-keys","value"]),t?(h(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[_(i(t),1)]),_:2},1024)):k("",!0)]),_:1}),n("div",re,[r(d,{type:"button",label:a(s)("common.cancel"),icon:"pi pi-times",class:"p-button-text mr-2",onClick:T},null,8,["label"]),r(d,{type:"submit",label:a(s)("common.save"),icon:"pi pi-check",loading:f.value},null,8,["label","loading"])])]),_:1},8,["validation-schema"])])])}}}),ke=X(ce,[["__scopeId","data-v-7f999d8c"]]);export{ke as default};
