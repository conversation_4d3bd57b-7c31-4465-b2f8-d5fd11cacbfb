import{s as D}from"./index-DDFpWte-.js";import{d as L,O as I,i as O,B as W,r as h,W as K,f as G,a as H,o as y,b as i,e as n,w as m,u as f,y as b,z as k,p as _,A as V,j as x,t as v,s as J,F as Q,bT as X,bV as Y,bW as Z}from"./index-DWWVg5mm.js";import{s as ee}from"./index-DNtGhG9S.js";import{s as se}from"./index-W8aGMMs9.js";import{t as le,c as te,b as A}from"./index.esm-DoKGfgcR.js";import{u as ae}from"./useListRefresh-AzGE_VAq.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-7nJYUDnq.js";import"./index-DcXu5dWS.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";const ie={class:"merchant-role-edit-page"},re={class:"p-4 bg-white rounded-2xl"},ne={class:"field"},ce={class:"field"},de={class:"field"},me={class:"permission-bulk-actions"},ue={class:"flex flex-col gap-2"},fe={class:"flex align-items-center"},pe={class:"flex justify-end mt-6 gap-2"},he=L({name:"flexiratesMerchantUserRoleEdit",__name:"roleEdit",setup(ye){const{backWithRefresh:T}=ae("flexiratesMerchantUserRoleList",()=>{}),P=I(),E=O(),F=W(),S=h(),u=h(!1),R=h(F.params.id),o=K({name:"",slug:"",permissions:[],localPermissions:{}}),p=h([]),w=new Map,g=l=>l.map(e=>{var t,s;return w.set(String(e.id),{key:String(e.id),label:e.name,data:e,children:(t=e==null?void 0:e.children)!=null&&t.length?g(e.children):[]}),{key:String(e.id),label:e.name,data:e,children:(s=e==null?void 0:e.children)!=null&&s.length?g(e.children):[]}}),M=async()=>{const l=await X();p.value=g(l.data)},C=l=>{var t,s,r;const e={};for(const d of l){let a=!1;if(d.children&&d.children.length===((s=(t=w.get(String(d.id)))==null?void 0:t.children)==null?void 0:s.length)&&(a=!0),e[d.id]={checked:!0,partialChecked:a},(r=d.children)!=null&&r.length){const c=C(d.children);Object.assign(e,c)}}return e},N=async()=>{try{u.value=!0;const l=await Y(R.value),{name:e,slug:t,permissions:s}=l.data;o.name=e,o.slug=t,s&&Array.isArray(s)&&(o.localPermissions=C(s))}catch(l){console.error("Failed to fetch role detail:",l),P.add({severity:"error",summary:"Error",detail:"Failed to fetch role detail",life:3e3})}finally{u.value=!1}},B=le(te({name:A().min(3,"Minimum length is 3 characters").max(50,"Maximum length is 50 characters").required(),slug:A().min(3,"Minimum length is 3 characters").max(50,"Maximum length is 50 characters").required()})),$=async()=>{var l;u.value=!0;try{const e=await((l=S.value)==null?void 0:l.validate());if(!e.valid){u.value=!1;return}const t={role_name:e.values.name,role_mark:e.values.slug,permissions:Object.keys(o.localPermissions).map(r=>Number(r))},{code:s}=await Z(R.value,t);s===0&&(P.add({severity:"success",summary:"Success",detail:"Role updated successfully",life:3e3}),T())}catch(e){console.error("Failed to update role:",e)}finally{u.value=!1}},j=()=>{const l={},e=t=>{t.forEach(s=>{l[s.key]={checked:!0,partialChecked:!1},s.children&&s.children.length>0&&e(s.children)})};e(p.value),o.localPermissions=l},U=()=>{o.localPermissions={}},q=()=>{const l={},e=t=>{t.forEach(s=>{var a;const r=s.key;((a=o.localPermissions[r])==null?void 0:a.checked)||(l[r]={checked:!0,partialChecked:!1}),s.children&&s.children.length>0&&e(s.children)})};e(p.value),o.localPermissions=l},z=()=>{E.back()};return G(()=>{Promise.all([N()]).then(()=>{M()})}),(l,e)=>{const t=se,s=ee,r=J,d=D;return y(),H("div",ie,[e[6]||(e[6]=i("div",{class:"flex justify-between items-center mb-4"},[i("h1",{class:"text-3xl"}," Edit Role ")],-1)),i("div",re,[n(f(Q),{ref_key:"formRef",ref:S,"validation-schema":f(B),class:"merchant-role-form flex flex-col gap-4",onSubmit:$},{default:m(()=>[n(f(b),{modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=a=>o.name=a),name:"name",class:"form-col"},{default:m(({field:a,errorMessage:c})=>[i("div",ne,[e[3]||(e[3]=i("label",{for:"name",class:"mb-2 block"},"Role Name*",-1)),n(t,V({id:"name"},a,{placeholder:"Role Name",class:"w-full",autofocus:""}),null,16),c?(y(),k(s,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[x(v(c),1)]),_:2},1024)):_("",!0)])]),_:1},8,["modelValue"]),n(f(b),{modelValue:o.slug,"onUpdate:modelValue":e[1]||(e[1]=a=>o.slug=a),name:"slug",class:"form-col"},{default:m(({field:a,errorMessage:c})=>[i("div",ce,[e[4]||(e[4]=i("label",{for:"slug",class:"mb-2 block"},"Slug*",-1)),n(t,V({id:"slug"},a,{placeholder:"Slug",class:"w-full"}),null,16),c?(y(),k(s,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[x(v(c),1)]),_:2},1024)):_("",!0)])]),_:1},8,["modelValue"]),n(f(b),{name:"permissions",class:"form-col"},{default:m(({errorMessage:a})=>[i("div",de,[e[5]||(e[5]=i("label",{for:"permissions",class:"mb-2 block"},"Permissions",-1)),i("div",me,[n(r,{type:"button",label:"Select All",icon:"pi pi-check-square",size:"small",severity:"secondary",outlined:"",onClick:j}),n(r,{type:"button",label:"Toggle All",icon:"pi pi-refresh",size:"small",severity:"secondary",outlined:"",onClick:q}),n(r,{type:"button",label:"Deselect All",icon:"pi pi-square",size:"small",severity:"secondary",outlined:"",onClick:U})]),i("div",ue,[n(d,{"selection-keys":o.localPermissions,"onUpdate:selectionKeys":e[2]||(e[2]=c=>o.localPermissions=c),value:p.value,"selection-mode":"checkbox","meta-key-selection":!1,class:"w-full md:w-[30rem] !p-0",placeholder:"Select permissions"},{default:m(({node:c})=>[i("div",fe,[i("span",null,v(c.label),1)])]),_:1},8,["selection-keys","value"])]),a?(y(),k(s,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[x(v(a),1)]),_:2},1024)):_("",!0)])]),_:1}),i("div",pe,[n(r,{type:"button",label:"Cancel",icon:"pi pi-times",class:"p-button-text mr-2",onClick:z}),n(r,{type:"submit",label:"Save",icon:"pi pi-check",loading:u.value},null,8,["loading"])])]),_:1},8,["validation-schema"])])])}}}),Ve=oe(he,[["__scopeId","data-v-8c9acda8"]]);export{Ve as default};
