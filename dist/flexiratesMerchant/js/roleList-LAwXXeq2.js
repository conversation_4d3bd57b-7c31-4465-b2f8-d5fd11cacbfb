import{d as j,k as G,O as H,i as J,r as c,c as K,a as _,o as d,e as n,p as g,u as s,s as Q,w as u,z as Y,b as E,t as L,U as Z,bX as ee,bM as se}from"./index-DWWVg5mm.js";import{_ as te}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-ir_p9YfA.js";import{S as x,_ as oe}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{B as ae}from"./BaseDataTable-75nyB6_U.js";import{u as re}from"./useListRefresh-AzGE_VAq.js";import{u as le,P as r}from"./usePermissions-DgTRxXRz.js";import{u as ie}from"./useRequestList-Bk9BBkCQ.js";import{f as ne}from"./date-BWkRbs8K.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-W8aGMMs9.js";import"./index-T2UmNbVz.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";import"./index-yGDtrfNA.js";import"./index-BVQM_gwe.js";import"./index-DimVHvgA.js";import"./dayjs.min-DUDfdLe0.js";const ce={class:"merchant-role-list-page"},de={key:0,class:"flex justify-end gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},ue={class:"confirmation-content"},pe={key:0},he=j({name:"flexiratesMerchantRoleList",__name:"roleList",setup(fe){const{hasPermission:l,hasAnyPermission:D}=le(),{t}=G(),v=H(),R=J(),p=ie({requestFn:se}),{list:T,loading:y,total:w,refresh:C,reset:S,setSearchParams:P,search:A,onPageChange:O,failed:k,failureMessage:F}=p;re("flexiratesMerchantUserRoleList",S);const B=c([{field:"name",header:t("merchant.role.columns.name","Role Name"),style:{minWidth:"150px"},sortable:!0,sortField:"name"},{field:"slug",header:t("merchant.role.columns.slug","Slug"),style:{minWidth:"150px"}},{field:"created_at",header:t("common.created","Created Date"),style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"created_at"},{field:"action",header:"",style:{width:"60px"},template:"action",alignFrozen:"right",frozen:!0}]),b=c({name:"","created_at[]":[]}),U=K(()=>[{name:"name",label:t("merchant.role.columns.name","Role Name"),type:x.TEXT,placeholder:t("merchant.role.search.namePlaceholder","Search by role name"),maxlength:50,defaultValue:""},{name:"created_at[]",label:t("common.created","Created Date"),type:x.DATE_RANGE,placeholder:t("merchant.search.datePlaceholder","Select Date Range"),defaultValue:[]}]),M=a=>{P(a),A()},V=a=>{const{sortField:o,sortOrder:h}=a;p.setParams({sort_by:o,sort_order:h===1?"asc":"desc"}),p.search()},$=()=>{l(r.ROLE_CREATE)&&R.push({name:"flexiratesMerchantUserRoleCreate"})},N=a=>{l(r.ROLE_UPDATE)&&R.push({name:"flexiratesMerchantUserRoleEdit",params:{id:a.id}})},m=c(!1),i=c(null),z=a=>{l(r.ROLE_DELETE)&&(i.value=a,m.value=!0)},I=()=>{var a;(a=i.value)!=null&&a.id&&(m.value=!1,ee(String(i.value.id)).then(o=>{v.add({severity:"success",summary:t("common.success","Success"),detail:t("merchant.role.messages.deleteSuccess","Role deleted successfully"),life:3e3}),C()}).catch(o=>{console.error("Failed to delete role:",o),v.add({severity:"error",summary:t("common.error","Error"),detail:t("merchant.role.messages.deleteFailed","Failed to delete role"),life:3e3})}),i.value=null)};return(a,o)=>{const h=oe,f=Q,q=te,W=Z;return d(),_("div",ce,[n(h,{modelValue:b.value,"onUpdate:modelValue":o[0]||(o[0]=e=>b.value=e),loading:s(y),"basic-search-fields":U.value,onSearch:M},null,8,["modelValue","loading","basic-search-fields"]),s(l)(s(r).ROLE_CREATE)?(d(),_("div",de,[n(f,{label:"CREATE A ROLE",severity:"warn",class:"!px-8",onClick:$})])):g("",!0),n(ae,{columns:B.value,value:s(T),"row-hover":!0,loading:s(y),"total-records":s(w),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,"show-multiple-column":!1,scrollable:!0,"search-placeholder":"Search roles...",failed:s(k),"striped-rows":!1,"failure-message":s(F),onSort:V,onPage:o[1]||(o[1]=e=>s(O)(e))},{date:u(({data:e})=>[E("div",null,L(s(ne)(e.created_at)),1)]),action:u(({data:e})=>[s(D)([s(r).ROLE_UPDATE,s(r).ROLE_DELETE,s(r).ROLE_DETAIL])&&(e==null?void 0:e.slug)!=="administrator"?(d(),Y(q,{key:0,loading:e.__loading,"is-show-detail":(e==null?void 0:e.slug)!=="administrator"&&s(l)(s(r).ROLE_DETAIL),"is-show-delete":(e==null?void 0:e.slug)!=="administrator"&&s(l)(s(r).ROLE_DELETE),"is-show-edit":(e==null?void 0:e.slug)!=="administrator"&&s(l)(s(r).ROLE_UPDATE),onEdit:X=>N(e),onDelete:X=>z(e)},null,8,["loading","is-show-detail","is-show-delete","is-show-edit","onEdit","onDelete"])):g("",!0)]),_:1},8,["columns","value","loading","total-records","failed","failure-message"]),n(W,{visible:m.value,"onUpdate:visible":o[3]||(o[3]=e=>m.value=e),style:{width:"450px"},header:s(t)("merchant.role.dialogs.confirmDelete","确认删除"),modal:!0},{footer:u(()=>[n(f,{label:s(t)("common.no","取消"),icon:"pi pi-times",text:"",onClick:o[2]||(o[2]=e=>m.value=!1)},null,8,["label"]),n(f,{label:s(t)("common.yes","确定"),icon:"pi pi-check",text:"",onClick:I},null,8,["label"])]),default:u(()=>{var e;return[E("div",ue,[o[4]||(o[4]=E("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),i.value?(d(),_("span",pe,L(s(t)("merchant.role.dialogs.deleteConfirmMessage",{name:i.value.name},`确定要删除角色 "${(e=i.value)==null?void 0:e.name}" 吗？该操作不可恢复。`)),1)):g("",!0)])]}),_:1},8,["visible","header"])])}}}),$e=me(he,[["__scopeId","data-v-64c2b91c"]]);export{$e as default};
