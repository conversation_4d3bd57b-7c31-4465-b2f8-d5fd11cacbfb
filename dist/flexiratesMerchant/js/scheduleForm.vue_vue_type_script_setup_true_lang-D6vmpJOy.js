import{s as G}from"./index-IMPOxVZV.js";import{s as K}from"./index-DNL_tiqf.js";import{s as J}from"./index-DNtGhG9S.js";import{s as X}from"./index-DDWh5zyb.js";import{d as Z,r as F,f as ee,m as ae,z as N,o as p,u as s,F as te,w as v,b as t,e as x,p as h,_ as le,a as E,t as _,y as k,q as w,j as P,E as q,$ as ne}from"./index-DWWVg5mm.js";import{i as se,P as D,a as b}from"./index-D2QnTskR.js";import{t as oe,c as me,d as I,e as ie}from"./index.esm-DoKGfgcR.js";import{d as U}from"./dayjs.min-DUDfdLe0.js";import{D as d}from"./decimal-LZMqkRTm.js";const V=Object.freeze(Object.defineProperty({__proto__:null,Flexirates:se},Symbol.toStringTag,{value:"Module"})),ue={class:"flex flex-col gap-y-2"},de={class:"flex gap-x-2"},_e={class:"form-item__content"},re={class:"flex gap-x-2"},ce={class:"form-item__content"},pe={class:"form-item__content flex flex-col gap-y-2 mt-2"},ye={class:"form-item__content flex flex-col gap-y-2 mt-2"},fe={class:"form-item__content flex flex-col gap-y-2 mt-2"},ve={class:"form-item__content flex flex-col gap-y-2 mt-2"},he={class:"form-item__content flex flex-col gap-y-2 mt-2"},Pe={key:0,class:"text-sm text-gray-600 mt-2"},Le={class:"form-item flex"},Te={class:"form-item__content"},be={class:"ml-2"},Ne={key:2,class:"form-item flex"},xe={class:"form-item__content"},Ae={class:"ml-2"},De={class:"form-item flex"},Ue={class:"form-item__label"},Se={class:"ml-2"},Ie=Z({__name:"scheduleForm",props:{scheduleData:{}},setup(H,{expose:R}){const m=H,l=F(),W=F([{label:"Calculated Installments",value:D.CALCULATED_INSTALLMENTS},{label:"Custom Installment Amount",value:D.CUSTOM_INSTALLMENT_AMOUNT},{label:"Pay Full Amount",value:D.PAY_FULL_AMOUNT_NOW}]),j=F([{label:"Weekly",value:b.WEEKLY},{label:"Fortnightly",value:b.FORTNIGHTLY},{label:"Monthly",value:b.MONTHLY},{label:"Quarterly",value:b.QUARTERLY}]),Y=F(null),C=F(null),$=F(""),B=oe(me({paymentPlan:I().min(1,"Payment plan is required"),paymentPlanSchedule:I().when("paymentPlan",{is:e=>[D.CALCULATED_INSTALLMENTS,D.CUSTOM_INSTALLMENT_AMOUNT].includes(e),then:e=>e.min(1,"Payment schedule is required"),otherwise:e=>e.optional()}),first_payment_date:ie().min(U().startOf("day").toDate(),"First payment date must be today or in the future"),amount:I().when("paymentPlan",{is:D.CUSTOM_INSTALLMENT_AMOUNT,then:e=>e.min(.01,"Amount is required"),otherwise:e=>e.optional()}),flexiRates:I().optional(),plan:I().optional()})),z=({amount:e,count:a,totalAmountDue:f})=>{const i=d(f).sub(d(e).mul(a)).toFixed(2,d.ROUND_HALF_UP);$.value=i},Q=({first_payment_date:e,plan:a,last_payment_date:f})=>{var u,O,M,g,n,o,y,c;if(!e||!a||!f){C.value=0,Y.value="0";return}const i=((u=m.scheduleData)==null?void 0:u.total_amount_due)||1,L=U(e),S=U(f);let r=0,T="0";switch(a){case b.FULL_AMOUNT:r=1,T=d(i).toFixed(2,d.ROUND_HALF_UP);break;case b.QUARTERLY:r=Math.abs(S.diff(L,"quarter")||1),T=d(i).div(r).toFixed(2,d.ROUND_HALF_UP);break;case b.MONTHLY:r=Math.abs(S.diff(L,"month")||1),T=d(i).div(r).toFixed(2,d.ROUND_HALF_UP);break;case b.FORTNIGHTLY:{const A=S.diff(L,"day")+1;r=A<=14?1:Math.ceil(A/14),T=d(i).div(r).toFixed(2,d.ROUND_HALF_UP);break}case b.WEEKLY:{const A=S.diff(L,"day")+1;r=A<=7?1:Math.ceil(A/7),T=d(i).div(r).toFixed(2,d.ROUND_HALF_UP);break}}Y.value=T,C.value=Math.floor(r),l!=null&&l.value&&((M=(O=l==null?void 0:l.value)==null?void 0:O.values)!=null&&M.amount)&&((n=(g=l==null?void 0:l.value)==null?void 0:g.values)==null?void 0:n.paymentPlan)===D.CUSTOM_INSTALLMENT_AMOUNT&&z({amount:(y=(o=l==null?void 0:l.value)==null?void 0:o.values)==null?void 0:y.amount,count:C.value,totalAmountDue:Number(((c=m.scheduleData)==null?void 0:c.total_amount_due)||0)})};return ee(()=>{ae([()=>{var e;return(e=l.value)==null?void 0:e.values.amount},()=>{var e;return(e=l.value)==null?void 0:e.values.first_payment_date},()=>{var e;return(e=l.value)==null?void 0:e.values.plan},()=>{var e;return(e=l.value)==null?void 0:e.values.paymentPlanSchedule},()=>{var e;return(e=m.scheduleData)==null?void 0:e.last_payment_date}],()=>{var a,f,i,L;const e=((a=l.value)==null?void 0:a.values.paymentPlanSchedule)||((f=l.value)==null?void 0:f.values.plan);Q({first_payment_date:(i=l.value)==null?void 0:i.values.first_payment_date,plan:e,last_payment_date:(L=m.scheduleData)==null?void 0:L.last_payment_date})},{immediate:!0})}),R({getValues:()=>{var e;return(e=l.value)==null?void 0:e.values},validate:()=>{var e;return(e=l.value)==null?void 0:e.validate()}}),(e,a)=>{var r,T;const f=X,i=J,L=K,S=G;return p(),N(s(te),{ref_key:"paymentPlanRef",ref:l,"validation-schema":s(B),class:"flex flex-col gap-x-8 gap-y-4","initial-values":{paymentPlan:s(V).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,paymentPlanSchedule:((r=m.scheduleData)==null?void 0:r.default_payment_plan)||0,plan:((T=m.scheduleData)==null?void 0:T.default_payment_plan)||0,first_payment_date:new Date,amount:0}},{default:v(({values:u})=>{var O,M,g;return[t("div",ue,[t("div",de,[a[0]||(a[0]=t("label",{class:"form-item__label"},"Address:",-1)),t("div",_e,_((O=m.scheduleData)==null?void 0:O.address)+" "+_((M=m.scheduleData)==null?void 0:M.suburb),1)]),t("div",re,[a[1]||(a[1]=t("label",{class:"form-item__label"},"Total amount due:",-1)),t("div",ce," $"+_(new(s(d))(((g=m.scheduleData)==null?void 0:g.total_amount_due)||0).toFixed(2)),1)])]),x(s(k),{name:"paymentPlan",as:"div",class:"form-item"},{default:v(({errorMessage:n,field:o,handleChange:y})=>[a[2]||(a[2]=t("label",{class:"form-item__label is-required"},"Payment Plan",-1)),t("div",pe,[x(f,{modelValue:o.value,"onUpdate:modelValue":c=>o.value=c,options:W.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Plan",class:w(["w-full",{"p-invalid":n}]),onValueChange:y},null,8,["modelValue","onUpdate:modelValue","options","class","onValueChange"]),n?(p(),N(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:v(()=>[P(_(n),1)]),_:2},1024)):h("",!0)])]),_:1,__:[2]}),[s(V).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS,s(V).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT].includes(u==null?void 0:u.paymentPlan)?(p(),N(s(k),{key:0,name:"paymentPlanSchedule",as:"div",class:"form-item"},{default:v(({errorMessage:n,field:o,handleChange:y})=>[a[3]||(a[3]=t("label",{class:"form-item__label is-required"},"Payment Schedule",-1)),t("div",ye,[x(f,{modelValue:o.value,"onUpdate:modelValue":c=>o.value=c,options:j.value,"option-label":"label","option-value":"value",placeholder:"Select a Payment Schedule",class:w(["w-full",{"p-invalid":n}]),onValueChange:y},null,8,["modelValue","onUpdate:modelValue","options","class","onValueChange"]),n?(p(),N(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:v(()=>[P(_(n),1)]),_:2},1024)):h("",!0)])]),_:1,__:[3]})):h("",!0),le(x(s(k),{name:"plan",as:"div",class:"form-item"},{default:v(({errorMessage:n,field:o,handleChange:y})=>[a[4]||(a[4]=t("label",{class:"form-item__label is-required"},"Payment plan (Legacy)",-1)),t("div",fe,[x(f,{modelValue:o.value,"onUpdate:modelValue":c=>o.value=c,options:m.scheduleData.dict_payment_plan,class:w([{"p-invalid":n},"w-full"]),"option-label":"label","option-value":"value",placeholder:"Select a Payment Plan",onValueChange:y},null,8,["modelValue","onUpdate:modelValue","options","class","onValueChange"]),n?(p(),N(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:v(()=>[P(_(n),1)]),_:2},1024)):h("",!0)])]),_:1,__:[4]},512),[[ne,!1]]),x(s(k),{name:"first_payment_date",as:"div",class:"form-item"},{default:v(({errorMessage:n,field:o,handleChange:y})=>[a[5]||(a[5]=t("label",{class:"form-item__label is-required"},"First payment date",-1)),t("div",ve,[x(L,{modelValue:o.value,"onUpdate:modelValue":c=>o.value=c,"min-date":new Date,"max-date":s(U)(m.scheduleData.last_payment_date).toDate(),"date-format":"dd/mm/yy","show-icon":"",class:w(["w-full",{"p-invalid":n}]),onValueChange:y},null,8,["modelValue","onUpdate:modelValue","min-date","max-date","class","onValueChange"]),n?(p(),N(i,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:v(()=>[P(_(n),1)]),_:2},1024)):h("",!0)])]),_:1,__:[5]}),(u==null?void 0:u.paymentPlan)===s(V).Flexirates.PaymentPlan.CUSTOM_INSTALLMENT_AMOUNT?(p(),N(s(k),{key:1,name:"amount",as:"div",class:"form-item"},{default:v(({errorMessage:n,field:o,handleChange:y})=>{var c;return[a[6]||(a[6]=t("label",{class:"form-item__label is-required"},"Amount",-1)),t("div",he,[x(S,{modelValue:o.value,"onUpdate:modelValue":A=>o.value=A,min:0,"min-fraction-digits":2,"max-fraction-digits":2,max:Number(((c=m.scheduleData)==null?void 0:c.total_amount_due)||0),class:w({"p-invalid":n}),onValueChange:y},null,8,["modelValue","onUpdate:modelValue","max","class","onValueChange"]),u.amount>0&&Number($.value)>0?(p(),E("div",Pe," The last payment amount is: $"+_($.value),1)):h("",!0),n?(p(),N(i,{key:1,class:"mt-2",severity:"error",variant:"simple"},{default:v(()=>[P(_(n),1)]),_:2},1024)):h("",!0)])]}),_:2,__:[6]},1024)):h("",!0),t("div",Le,[a[7]||(a[7]=t("label",{class:"form-item__label"},"Last payment date :",-1)),t("div",Te,[t("span",be,_(m.scheduleData.last_payment_date?s(U)(m.scheduleData.last_payment_date).format("DD/MM/YYYY"):""),1)])]),(u==null?void 0:u.paymentPlan)===s(V).Flexirates.PaymentPlan.CALCULATED_INSTALLMENTS?(p(),E("div",Ne,[a[8]||(a[8]=t("label",{class:"form-item__label"},"Regular payment amount :",-1)),t("div",xe,[t("span",Ae,_(Y.value?`$${new(s(d))(Y.value).toFixed(2)}`:""),1)])])):h("",!0),t("div",De,[t("label",Ue,[P("Number of payments during "+_(s(U)().year())+" ",1),m.scheduleData.last_payment_date?(p(),E(q,{key:0},[P(" - "+_(s(U)(m.scheduleData.last_payment_date).year()),1)],64)):h("",!0),a[9]||(a[9]=P(" :"))]),t("span",Se,[(u==null?void 0:u.paymentPlan)===s(V).Flexirates.PaymentPlan.PAY_FULL_AMOUNT_NOW?(p(),E(q,{key:0},[P(" 1 ")],64)):(p(),E(q,{key:1},[P(_(C.value?C.value:""),1)],64))])])]}),_:1},8,["validation-schema","initial-values"])}}});export{Ie as _};
