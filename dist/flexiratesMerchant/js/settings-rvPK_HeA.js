import{d as U,k as B,O as N,r as v,W as q,f as S,a as g,o as n,b as e,t as r,u as t,e as c,z as w,p as y,q as h,w as m,j as p,s as M,bY as R}from"./index-DWWVg5mm.js";import{s as T}from"./index-DNtGhG9S.js";import{s as $}from"./index-CutZZiSb.js";import{s as z}from"./index-B_CyaZAL.js";import{_ as D}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C4fkh9Oe.js";import"./index-CjzDW1dI.js";import"./index-W8aGMMs9.js";import"./index-BEwkorqa.js";import"./index-Br9OVcmc.js";const I={class:"settings-container"},j={class:"settings-header"},E={class:"settings-title"},F={class:"settings-content"},L={key:0,class:"loading-container"},O={key:1},W={class:"password-section max-w-200"},Y={class:"p-grid"},A={class:"p-col-12 p-md-4"},G={class:"field"},H={for:"currentPassword"},J={class:"p-col-12 p-md-4"},K={class:"field"},Q={for:"newPassword"},X={class:"p-col-12 p-md-4"},Z={class:"field"},ss={for:"confirmPassword"},es={class:"form-actions"},os=U({__name:"settings",setup(ts){const{t:o}=B(),k=N(),f=v(!1),_=v(!1),a=v(!1),s=q({currentPassword:"",newPassword:"",confirmPassword:""});S(async()=>{V()});const V=async()=>{f.value=!0;try{await new Promise(i=>setTimeout(i,500))}catch(i){console.error("Failed to load user settings",i)}finally{f.value=!1}},b=async()=>{if(a.value=!0,!(!s.currentPassword||!s.newPassword||!s.confirmPassword)&&s.newPassword===s.confirmPassword){_.value=!0;try{const{code:i}=await R({password:s.currentPassword,new_password:s.newPassword});i===0&&(s.currentPassword="",s.newPassword="",s.confirmPassword="",a.value=!1,k.add({severity:"success",summary:o("common.success"),detail:o("user.settings.passwordUpdated"),life:3e3}))}finally{_.value=!1}}};return(i,d)=>{const x=z,P=$,u=T,C=M;return n(),g("div",I,[e("div",j,[e("div",E,[e("h1",null,r(t(o)("user.settings.title")),1),e("p",null,r(t(o)("user.settings.subtitle")),1)])]),e("div",F,[f.value?(n(),g("div",L,[c(x)])):(n(),g("div",O,[e("div",W,[e("div",Y,[e("div",A,[e("div",G,[e("label",H,r(t(o)("user.settings.currentPassword")),1),c(P,{id:"currentPassword",modelValue:s.currentPassword,"onUpdate:modelValue":d[0]||(d[0]=l=>s.currentPassword=l),feedback:!1,"toggle-mask":"",class:h(["w-full",{"p-invalid":a.value&&!s.currentPassword}])},null,8,["modelValue","class"]),a.value&&!s.currentPassword?(n(),w(u,{key:0,variant:"simple",severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.currentPasswordRequired")),1)]),_:1})):y("",!0)])]),e("div",J,[e("div",K,[e("label",Q,r(t(o)("user.settings.newPassword")),1),c(P,{id:"newPassword",modelValue:s.newPassword,"onUpdate:modelValue":d[1]||(d[1]=l=>s.newPassword=l),"toggle-mask":"",class:h(["w-full",{"p-invalid":a.value&&!s.newPassword}])},null,8,["modelValue","class"]),a.value&&!s.newPassword?(n(),w(u,{key:0,variant:"simple",severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.newPasswordRequired")),1)]),_:1})):y("",!0)])]),e("div",X,[e("div",Z,[e("label",ss,r(t(o)("user.settings.confirmPassword")),1),c(P,{id:"confirmPassword",modelValue:s.confirmPassword,"onUpdate:modelValue":d[2]||(d[2]=l=>s.confirmPassword=l),feedback:!1,"toggle-mask":"",class:h(["w-full",{"p-invalid":a.value&&!s.confirmPassword}])},null,8,["modelValue","class"]),a.value&&!s.confirmPassword?(n(),w(u,{key:0,variant:"simple",severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.confirmPasswordRequired")),1)]),_:1})):a.value&&s.newPassword!==s.confirmPassword?(n(),w(u,{key:1,severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.passwordsDoNotMatch")),1)]),_:1})):y("",!0)])])]),e("div",es,[c(C,{label:t(o)("user.settings.updatePassword"),icon:"pi pi-lock",loading:_.value,class:"p-button-primary",severity:"warn",onClick:b},null,8,["label","loading"])])])]))])])}}}),ps=D(os,[["__scopeId","data-v-ec2778f4"]]);export{ps as default};
