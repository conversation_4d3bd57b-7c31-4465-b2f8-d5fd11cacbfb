import{_ as y}from"./BaseFileUpload-DIyBFyns.js";import{S as s,_}from"./BaseSearch.vue_vue_type_style_index_0_lang-C46xnAep.js";import{_ as a}from"./BaseTag-DKUhplpL.js";import{d as h,r as o,c as r,a as k,b as l,e as t,t as S,o as C}from"./index-DWWVg5mm.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DNtGhG9S.js";import"./index-M77XcG3U.js";import"./index-BEwkorqa.js";import"./index-IMPOxVZV.js";import"./index-W8aGMMs9.js";import"./index-T2UmNbVz.js";import"./index-DDWh5zyb.js";import"./index-C4fkh9Oe.js";import"./index-7nJYUDnq.js";import"./index-DNL_tiqf.js";import"./index-DcXu5dWS.js";const E={class:"test p-4"},w={class:"mb-8"},A={class:"mt-4 p-4 bg-gray-100 rounded-lg"},R={class:"whitespace-pre-wrap"},T={class:"mb-8"},B={class:"tag-container mb-8"},F={class:"flex flex-wrap gap-2 mb-6"},N={class:"flex flex-wrap gap-2"},U={class:"status-list w-60 border border-gray-200 rounded bg-gray-50"},O={class:"status-item flex items-center p-2 border-b border-gray-200"},P={class:"status-item flex items-center p-2 border-b border-gray-200"},D={class:"status-item flex items-center p-2 border-b border-gray-200"},I={class:"status-item flex items-center p-2 border-b border-gray-200"},M={class:"status-item flex items-center p-2"},L=h({__name:"test",setup(G){const p=o([]),i=o({keyword:"",category:"",status:"",dateRange:null,price:null,priceRange:[null,null],inStock:!1,rating:"",featured:!1}),m=o([{label:"All",value:""},{label:"电子产品",value:"electronics"},{label:"服装",value:"clothing"},{label:"家居",value:"home"},{label:"书籍",value:"books"},{label:"食品1",value:"food"}]),c=o([{label:"All",value:""},{label:"Active",value:"active"},{label:"Inactive",value:"inactive"},{label:"Pending",value:"pending"}]),b=o([{label:"5星",value:5},{label:"4星及以上",value:4},{label:"3星及以上",value:3},{label:"2星及以上",value:2},{label:"1星及以上",value:1}]),f=r(()=>[{name:"keyword",label:"What are you looking for?",type:s.TEXT,placeholder:"Search for Customer name, email, etc.",maxlength:50,defaultValue:"",width:"420px"},{name:"category",label:"Category",type:s.SELECT,placeholder:"All",options:m.value,defaultValue:"",width:"200px"},{name:"status",label:"Status",type:s.SELECT,placeholder:"All",options:c.value,defaultValue:"",width:"200px"}]),g=r(()=>[{name:"dateRange",label:"上架日期",type:s.DATE_RANGE,placeholder:"选择日期范围",defaultValue:null},{name:"price",label:"价格",type:s.NUMBER,placeholder:"输入价格",min:0,max:1e4,step:10,defaultValue:null},{name:"priceRange",label:"价格区间",type:s.NUMBER_RANGE,min:0,max:1e4,step:10,defaultValue:[null,null]},{name:"inStock",label:"仅显示有库存商品",type:s.CHECKBOX,defaultValue:!1},{name:"rating",label:"商品评分",type:s.RADIO,options:b.value,defaultValue:""},{name:"featured",label:"精选商品",type:s.SWITCH,defaultValue:!1}]),v=()=>{console.log("搜索参数:",i.value)},x=r(()=>JSON.stringify(i.value,null,2)),n=u=>{console.log(`${u} tag clicked!`)};return(u,e)=>(C(),k("div",E,[e[11]||(e[11]=l("h2",{class:"text-2xl font-bold mb-6"}," BaseSearch 与 More Filters 示例 ",-1)),l("div",w,[t(_,{modelValue:i.value,"onUpdate:modelValue":e[0]||(e[0]=d=>i.value=d),"basic-search-fields":f.value,"advanced-search-fields":g.value,loading:!1,onSearch:v},null,8,["modelValue","basic-search-fields","advanced-search-fields"]),l("div",A,[e[6]||(e[6]=l("h3",{class:"text-lg font-bold mb-2"}," 当前搜索内容: ",-1)),l("pre",R,S(x.value),1)])]),e[12]||(e[12]=l("h2",{class:"text-2xl font-bold mb-6 mt-8"}," 文件上传组件示例 ",-1)),l("div",T,[t(y,{"model-value":p.value,"onUpdate:modelValue":e[1]||(e[1]=d=>p.value=d),multiple:!0,"max-files":2,accept:"image/*"},null,8,["model-value"])]),e[13]||(e[13]=l("h2",{class:"text-2xl font-bold mb-6 mt-8"}," 标签组件示例 ",-1)),l("div",B,[e[8]||(e[8]=l("h3",{class:"text-lg font-bold mb-4"}," 基本标签类型 ",-1)),l("div",F,[t(a,{text:"Default"}),t(a,{text:"Paid",type:"paid"}),t(a,{text:"Upcoming",type:"upcoming"}),t(a,{text:"Failed",type:"failed"})]),e[9]||(e[9]=l("h3",{class:"text-lg font-bold mb-4"}," 可点击标签示例 ",-1)),l("div",N,[t(a,{text:"Default Clickable",clickable:"",onClick:e[2]||(e[2]=()=>n("default"))}),t(a,{text:"Paid Clickable",type:"paid",clickable:"",onClick:e[3]||(e[3]=()=>n("paid"))}),t(a,{text:"Upcoming Clickable",type:"upcoming",clickable:"",onClick:e[4]||(e[4]=()=>n("upcoming"))}),t(a,{text:"Failed Clickable",type:"failed",clickable:"",onClick:e[5]||(e[5]=()=>n("failed"))})]),e[10]||(e[10]=l("h3",{class:"text-lg font-bold mb-4 mt-6"}," 模拟状态列表 ",-1)),l("div",U,[e[7]||(e[7]=l("div",{class:"border-b border-gray-200 px-4 py-2 font-bold"}," Status ",-1)),l("div",O,[t(a,{text:"Paid",type:"paid"})]),l("div",P,[t(a,{text:"Upcoming",type:"upcoming"})]),l("div",D,[t(a,{text:"Paid",type:"paid"})]),l("div",I,[t(a,{text:"Failed",type:"failed"})]),l("div",M,[t(a,{text:"Paid",type:"paid"})])])])]))}}),se=V(L,[["__scopeId","data-v-b98e2530"]]);export{se as default};
