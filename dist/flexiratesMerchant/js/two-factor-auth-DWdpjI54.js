import{s as ge}from"./index-DNtGhG9S.js";import{I as pe,J as me,K as we,L as ye,a as L,o as F,E as ve,G as Ce,M as Ee,e as O,q as ie,A as Be,N as be,d as Ae,k as Te,O as Ie,x as Ne,r as z,c as Se,i as Pe,f as Re,b as P,p as x,t as k,u as M,j as Pt,s as _e,z as Rt,w as Me,P as ke,Q as Fe}from"./index-DWWVg5mm.js";import{s as De}from"./index-B_CyaZAL.js";import{s as Le}from"./index-W8aGMMs9.js";import{s as qe}from"./index-BEwkorqa.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-Br9OVcmc.js";var Ve=pe`
    .p-inputotp {
        display: flex;
        align-items: center;
        gap: dt('inputotp.gap');
    }

    .p-inputotp-input {
        text-align: center;
        width: dt('inputotp.input.width');
    }

    .p-inputotp-input.p-inputtext-sm {
        text-align: center;
        width: dt('inputotp.input.sm.width');
    }

    .p-inputotp-input.p-inputtext-lg {
        text-align: center;
        width: dt('inputotp.input.lg.width');
    }
`,ze={root:"p-inputotp p-component",pcInputText:"p-inputotp-input"},Ke=me.extend({name:"inputotp",style:Ve,classes:ze}),Oe={name:"BaseInputOtp",extends:qe,props:{readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},length:{type:Number,default:4},mask:{type:Boolean,default:!1},integerOnly:{type:Boolean,default:!1}},style:Ke,provide:function(){return{$pcInputOtp:this,$parentInstance:this}}},se={name:"InputOtp",extends:Oe,inheritAttrs:!1,emits:["change","focus","blur"],data:function(){return{tokens:[]}},watch:{modelValue:{immediate:!0,handler:function(t){this.tokens=t?t.split(""):new Array(this.length)}}},methods:{getTemplateAttrs:function(t){return{value:this.tokens[t]}},getTemplateEvents:function(t){var i=this;return{input:function(e){return i.onInput(e,t)},keydown:function(e){return i.onKeyDown(e)},focus:function(e){return i.onFocus(e)},blur:function(e){return i.onBlur(e)},paste:function(e){return i.onPaste(e)}}},onInput:function(t,i){this.tokens[i]=t.target.value,this.updateModel(t),t.inputType==="deleteContentBackward"?this.moveToPrev(t):(t.inputType==="insertText"||t.inputType==="deleteContentForward"||we()&&t instanceof CustomEvent)&&this.moveToNext(t)},updateModel:function(t){var i=this.tokens.join("");this.writeValue(i,t),this.$emit("change",{originalEvent:t,value:i})},moveToPrev:function(t){var i=this.findPrevInput(t.target);i&&(i.focus(),i.select())},moveToNext:function(t){var i=this.findNextInput(t.target);i&&(i.focus(),i.select())},findNextInput:function(t){var i=t.nextElementSibling;if(i)return i.nodeName==="INPUT"?i:this.findNextInput(i)},findPrevInput:function(t){var i=t.previousElementSibling;if(i)return i.nodeName==="INPUT"?i:this.findPrevInput(i)},onFocus:function(t){t.target.select(),this.$emit("focus",t)},onBlur:function(t){this.$emit("blur",t)},onClick:function(t){setTimeout(function(){return t.target.select()},1)},onKeyDown:function(t){if(!(t.ctrlKey||t.metaKey))switch(t.code){case"ArrowLeft":this.moveToPrev(t),t.preventDefault();break;case"ArrowUp":case"ArrowDown":t.preventDefault();break;case"Backspace":t.target.value.length===0&&(this.moveToPrev(t),t.preventDefault());break;case"ArrowRight":this.moveToNext(t),t.preventDefault();break;case"Enter":case"NumpadEnter":case"Tab":break;default:(this.integerOnly&&!(t.code!=="Space"&&Number(t.key)>=0&&Number(t.key)<=9)||this.tokens.join("").length>=this.length&&t.code!=="Delete")&&t.preventDefault();break}},onPaste:function(t){var i=t.clipboardData.getData("text");if(i.length){var n=i.substring(0,this.length);(!this.integerOnly||!isNaN(n))&&(this.tokens=n.split(""),this.updateModel(t))}t.preventDefault()}},computed:{inputMode:function(){return this.integerOnly?"numeric":"text"},inputType:function(){return this.mask?"password":"text"}},components:{OtpInputText:Le}};function He(o,t,i,n,e,r){var s=ye("OtpInputText");return F(),L("div",Be({class:o.cx("root")},o.ptmi("root")),[(F(!0),L(ve,null,Ce(o.length,function(u){return Ee(o.$slots,"default",{key:u,events:r.getTemplateEvents(u-1),attrs:r.getTemplateAttrs(u-1),index:u},function(){return[O(s,{value:e.tokens[u-1],type:r.inputType,class:ie(o.cx("pcInputText")),name:o.$formName,inputmode:r.inputMode,variant:o.variant,readonly:o.readonly,disabled:o.disabled,size:o.size,invalid:o.invalid,tabindex:o.tabindex,unstyled:o.unstyled,onInput:function(c){return r.onInput(c,u-1)},onFocus:t[0]||(t[0]=function(a){return r.onFocus(a)}),onBlur:t[1]||(t[1]=function(a){return r.onBlur(a)}),onPaste:t[2]||(t[2]=function(a){return r.onPaste(a)}),onKeydown:t[3]||(t[3]=function(a){return r.onKeyDown(a)}),onClick:t[4]||(t[4]=function(a){return r.onClick(a)}),pt:o.ptm("pcInputText")},null,8,["value","type","class","name","inputmode","variant","readonly","disabled","size","invalid","tabindex","unstyled","onInput","pt"])]})}),128))],16)}se.render=He;var Y={},nt,_t;function Je(){return _t||(_t=1,nt=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),nt}var rt={},K={},Mt;function H(){if(Mt)return K;Mt=1;let o;const t=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return K.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},K.getSymbolTotalCodewords=function(n){return t[n]},K.getBCHDigit=function(i){let n=0;for(;i!==0;)n++,i>>>=1;return n},K.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');o=n},K.isKanjiModeEnabled=function(){return typeof o<"u"},K.toSJIS=function(n){return o(n)},K}var ot={},kt;function Tt(){return kt||(kt=1,function(o){o.L={bit:1},o.M={bit:0},o.Q={bit:3},o.H={bit:2};function t(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return o.L;case"m":case"medium":return o.M;case"q":case"quartile":return o.Q;case"h":case"high":return o.H;default:throw new Error("Unknown EC Level: "+i)}}o.isValid=function(n){return n&&typeof n.bit<"u"&&n.bit>=0&&n.bit<4},o.from=function(n,e){if(o.isValid(n))return n;try{return t(n)}catch{return e}}}(ot)),ot}var it,Ft;function je(){if(Ft)return it;Ft=1;function o(){this.buffer=[],this.length=0}return o.prototype={get:function(t){const i=Math.floor(t/8);return(this.buffer[i]>>>7-t%8&1)===1},put:function(t,i){for(let n=0;n<i;n++)this.putBit((t>>>i-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(t){const i=Math.floor(this.length/8);this.buffer.length<=i&&this.buffer.push(0),t&&(this.buffer[i]|=128>>>this.length%8),this.length++}},it=o,it}var st,Dt;function xe(){if(Dt)return st;Dt=1;function o(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}return o.prototype.set=function(t,i,n,e){const r=t*this.size+i;this.data[r]=n,e&&(this.reservedBit[r]=!0)},o.prototype.get=function(t,i){return this.data[t*this.size+i]},o.prototype.xor=function(t,i,n){this.data[t*this.size+i]^=n},o.prototype.isReserved=function(t,i){return this.reservedBit[t*this.size+i]},st=o,st}var at={},Lt;function Ye(){return Lt||(Lt=1,function(o){const t=H().getSymbolSize;o.getRowColCoords=function(n){if(n===1)return[];const e=Math.floor(n/7)+2,r=t(n),s=r===145?26:Math.ceil((r-13)/(2*e-2))*2,u=[r-7];for(let a=1;a<e-1;a++)u[a]=u[a-1]-s;return u.push(6),u.reverse()},o.getPositions=function(n){const e=[],r=o.getRowColCoords(n),s=r.length;for(let u=0;u<s;u++)for(let a=0;a<s;a++)u===0&&a===0||u===0&&a===s-1||u===s-1&&a===0||e.push([r[u],r[a]]);return e}}(at)),at}var ut={},qt;function Ge(){if(qt)return ut;qt=1;const o=H().getSymbolSize,t=7;return ut.getPositions=function(n){const e=o(n);return[[0,0],[e-t,0],[0,e-t]]},ut}var lt={},Ut;function Qe(){return Ut||(Ut=1,function(o){o.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};o.isValid=function(e){return e!=null&&e!==""&&!isNaN(e)&&e>=0&&e<=7},o.from=function(e){return o.isValid(e)?parseInt(e,10):void 0},o.getPenaltyN1=function(e){const r=e.size;let s=0,u=0,a=0,c=null,d=null;for(let E=0;E<r;E++){u=a=0,c=d=null;for(let f=0;f<r;f++){let p=e.get(E,f);p===c?u++:(u>=5&&(s+=t.N1+(u-5)),c=p,u=1),p=e.get(f,E),p===d?a++:(a>=5&&(s+=t.N1+(a-5)),d=p,a=1)}u>=5&&(s+=t.N1+(u-5)),a>=5&&(s+=t.N1+(a-5))}return s},o.getPenaltyN2=function(e){const r=e.size;let s=0;for(let u=0;u<r-1;u++)for(let a=0;a<r-1;a++){const c=e.get(u,a)+e.get(u,a+1)+e.get(u+1,a)+e.get(u+1,a+1);(c===4||c===0)&&s++}return s*t.N2},o.getPenaltyN3=function(e){const r=e.size;let s=0,u=0,a=0;for(let c=0;c<r;c++){u=a=0;for(let d=0;d<r;d++)u=u<<1&2047|e.get(c,d),d>=10&&(u===1488||u===93)&&s++,a=a<<1&2047|e.get(d,c),d>=10&&(a===1488||a===93)&&s++}return s*t.N3},o.getPenaltyN4=function(e){let r=0;const s=e.data.length;for(let a=0;a<s;a++)r+=e.data[a];return Math.abs(Math.ceil(r*100/s/5)-10)*t.N4};function i(n,e,r){switch(n){case o.Patterns.PATTERN000:return(e+r)%2===0;case o.Patterns.PATTERN001:return e%2===0;case o.Patterns.PATTERN010:return r%3===0;case o.Patterns.PATTERN011:return(e+r)%3===0;case o.Patterns.PATTERN100:return(Math.floor(e/2)+Math.floor(r/3))%2===0;case o.Patterns.PATTERN101:return e*r%2+e*r%3===0;case o.Patterns.PATTERN110:return(e*r%2+e*r%3)%2===0;case o.Patterns.PATTERN111:return(e*r%3+(e+r)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}o.applyMask=function(e,r){const s=r.size;for(let u=0;u<s;u++)for(let a=0;a<s;a++)r.isReserved(a,u)||r.xor(a,u,i(e,a,u))},o.getBestMask=function(e,r){const s=Object.keys(o.Patterns).length;let u=0,a=1/0;for(let c=0;c<s;c++){r(c),o.applyMask(c,e);const d=o.getPenaltyN1(e)+o.getPenaltyN2(e)+o.getPenaltyN3(e)+o.getPenaltyN4(e);o.applyMask(c,e),d<a&&(a=d,u=c)}return u}}(lt)),lt}var W={},Vt;function ae(){if(Vt)return W;Vt=1;const o=Tt(),t=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return W.getBlocksCount=function(e,r){switch(r){case o.L:return t[(e-1)*4+0];case o.M:return t[(e-1)*4+1];case o.Q:return t[(e-1)*4+2];case o.H:return t[(e-1)*4+3];default:return}},W.getTotalCodewordsCount=function(e,r){switch(r){case o.L:return i[(e-1)*4+0];case o.M:return i[(e-1)*4+1];case o.Q:return i[(e-1)*4+2];case o.H:return i[(e-1)*4+3];default:return}},W}var ct={},Q={},zt;function $e(){if(zt)return Q;zt=1;const o=new Uint8Array(512),t=new Uint8Array(256);return function(){let n=1;for(let e=0;e<255;e++)o[e]=n,t[n]=e,n<<=1,n&256&&(n^=285);for(let e=255;e<512;e++)o[e]=o[e-255]}(),Q.log=function(n){if(n<1)throw new Error("log("+n+")");return t[n]},Q.exp=function(n){return o[n]},Q.mul=function(n,e){return n===0||e===0?0:o[t[n]+t[e]]},Q}var Kt;function We(){return Kt||(Kt=1,function(o){const t=$e();o.mul=function(n,e){const r=new Uint8Array(n.length+e.length-1);for(let s=0;s<n.length;s++)for(let u=0;u<e.length;u++)r[s+u]^=t.mul(n[s],e[u]);return r},o.mod=function(n,e){let r=new Uint8Array(n);for(;r.length-e.length>=0;){const s=r[0];for(let a=0;a<e.length;a++)r[a]^=t.mul(e[a],s);let u=0;for(;u<r.length&&r[u]===0;)u++;r=r.slice(u)}return r},o.generateECPolynomial=function(n){let e=new Uint8Array([1]);for(let r=0;r<n;r++)e=o.mul(e,new Uint8Array([1,t.exp(r)]));return e}}(ct)),ct}var ft,Ot;function Ze(){if(Ot)return ft;Ot=1;const o=We();function t(i){this.genPoly=void 0,this.degree=i,this.degree&&this.initialize(this.degree)}return t.prototype.initialize=function(n){this.degree=n,this.genPoly=o.generateECPolynomial(this.degree)},t.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(n.length+this.degree);e.set(n);const r=o.mod(e,this.genPoly),s=this.degree-r.length;if(s>0){const u=new Uint8Array(this.degree);return u.set(r,s),u}return r},ft=t,ft}var dt={},ht={},gt={},Ht;function ue(){return Ht||(Ht=1,gt.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}),gt}var D={},Jt;function le(){if(Jt)return D;Jt=1;const o="[0-9]+",t="[A-Z $%*+\\-./:]+";let i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+`)(?:.|[\r
]))+`;D.KANJI=new RegExp(i,"g"),D.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),D.BYTE=new RegExp(n,"g"),D.NUMERIC=new RegExp(o,"g"),D.ALPHANUMERIC=new RegExp(t,"g");const e=new RegExp("^"+i+"$"),r=new RegExp("^"+o+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return D.testKanji=function(a){return e.test(a)},D.testNumeric=function(a){return r.test(a)},D.testAlphanumeric=function(a){return s.test(a)},D}var jt;function J(){return jt||(jt=1,function(o){const t=ue(),i=le();o.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},o.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},o.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},o.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},o.MIXED={bit:-1},o.getCharCountIndicator=function(r,s){if(!r.ccBits)throw new Error("Invalid mode: "+r);if(!t.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?r.ccBits[0]:s<27?r.ccBits[1]:r.ccBits[2]},o.getBestModeForData=function(r){return i.testNumeric(r)?o.NUMERIC:i.testAlphanumeric(r)?o.ALPHANUMERIC:i.testKanji(r)?o.KANJI:o.BYTE},o.toString=function(r){if(r&&r.id)return r.id;throw new Error("Invalid mode")},o.isValid=function(r){return r&&r.bit&&r.ccBits};function n(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return o.NUMERIC;case"alphanumeric":return o.ALPHANUMERIC;case"kanji":return o.KANJI;case"byte":return o.BYTE;default:throw new Error("Unknown mode: "+e)}}o.from=function(r,s){if(o.isValid(r))return r;try{return n(r)}catch{return s}}}(ht)),ht}var xt;function Xe(){return xt||(xt=1,function(o){const t=H(),i=ae(),n=Tt(),e=J(),r=ue(),s=7973,u=t.getBCHDigit(s);function a(f,p,T){for(let N=1;N<=40;N++)if(p<=o.getCapacity(N,T,f))return N}function c(f,p){return e.getCharCountIndicator(f,p)+4}function d(f,p){let T=0;return f.forEach(function(N){const R=c(N.mode,p);T+=R+N.getBitsLength()}),T}function E(f,p){for(let T=1;T<=40;T++)if(d(f,T)<=o.getCapacity(T,p,e.MIXED))return T}o.from=function(p,T){return r.isValid(p)?parseInt(p,10):T},o.getCapacity=function(p,T,N){if(!r.isValid(p))throw new Error("Invalid QR Code version");typeof N>"u"&&(N=e.BYTE);const R=t.getSymbolTotalCodewords(p),A=i.getTotalCodewordsCount(p,T),S=(R-A)*8;if(N===e.MIXED)return S;const w=S-c(N,p);switch(N){case e.NUMERIC:return Math.floor(w/10*3);case e.ALPHANUMERIC:return Math.floor(w/11*2);case e.KANJI:return Math.floor(w/13);case e.BYTE:default:return Math.floor(w/8)}},o.getBestVersionForData=function(p,T){let N;const R=n.from(T,n.M);if(Array.isArray(p)){if(p.length>1)return E(p,R);if(p.length===0)return 1;N=p[0]}else N=p;return a(N.mode,N.getLength(),R)},o.getEncodedBits=function(p){if(!r.isValid(p)||p<7)throw new Error("Invalid QR Code version");let T=p<<12;for(;t.getBCHDigit(T)-u>=0;)T^=s<<t.getBCHDigit(T)-u;return p<<12|T}}(dt)),dt}var pt={},Yt;function tn(){if(Yt)return pt;Yt=1;const o=H(),t=1335,i=21522,n=o.getBCHDigit(t);return pt.getEncodedBits=function(r,s){const u=r.bit<<3|s;let a=u<<10;for(;o.getBCHDigit(a)-n>=0;)a^=t<<o.getBCHDigit(a)-n;return(u<<10|a)^i},pt}var mt={},wt,Gt;function en(){if(Gt)return wt;Gt=1;const o=J();function t(i){this.mode=o.NUMERIC,this.data=i.toString()}return t.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(n){let e,r,s;for(e=0;e+3<=this.data.length;e+=3)r=this.data.substr(e,3),s=parseInt(r,10),n.put(s,10);const u=this.data.length-e;u>0&&(r=this.data.substr(e),s=parseInt(r,10),n.put(s,u*3+1))},wt=t,wt}var yt,Qt;function nn(){if(Qt)return yt;Qt=1;const o=J(),t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(n){this.mode=o.ALPHANUMERIC,this.data=n}return i.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){let r;for(r=0;r+2<=this.data.length;r+=2){let s=t.indexOf(this.data[r])*45;s+=t.indexOf(this.data[r+1]),e.put(s,11)}this.data.length%2&&e.put(t.indexOf(this.data[r]),6)},yt=i,yt}var vt,$t;function rn(){if($t)return vt;$t=1;const o=J();function t(i){this.mode=o.BYTE,typeof i=="string"?this.data=new TextEncoder().encode(i):this.data=new Uint8Array(i)}return t.getBitsLength=function(n){return n*8},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(i){for(let n=0,e=this.data.length;n<e;n++)i.put(this.data[n],8)},vt=t,vt}var Ct,Wt;function on(){if(Wt)return Ct;Wt=1;const o=J(),t=H();function i(n){this.mode=o.KANJI,this.data=n}return i.getBitsLength=function(e){return e*13},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(n){let e;for(e=0;e<this.data.length;e++){let r=t.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);r=(r>>>8&255)*192+(r&255),n.put(r,13)}},Ct=i,Ct}var Et={exports:{}},Zt;function sn(){return Zt||(Zt=1,function(o){var t={single_source_shortest_paths:function(i,n,e){var r={},s={};s[n]=0;var u=t.PriorityQueue.make();u.push(n,0);for(var a,c,d,E,f,p,T,N,R;!u.empty();){a=u.pop(),c=a.value,E=a.cost,f=i[c]||{};for(d in f)f.hasOwnProperty(d)&&(p=f[d],T=E+p,N=s[d],R=typeof s[d]>"u",(R||N>T)&&(s[d]=T,u.push(d,T),r[d]=c))}if(typeof e<"u"&&typeof s[e]>"u"){var A=["Could not find a path from ",n," to ",e,"."].join("");throw new Error(A)}return r},extract_shortest_path_from_predecessor_list:function(i,n){for(var e=[],r=n;r;)e.push(r),i[r],r=i[r];return e.reverse(),e},find_path:function(i,n,e){var r=t.single_source_shortest_paths(i,n,e);return t.extract_shortest_path_from_predecessor_list(r,e)},PriorityQueue:{make:function(i){var n=t.PriorityQueue,e={},r;i=i||{};for(r in n)n.hasOwnProperty(r)&&(e[r]=n[r]);return e.queue=[],e.sorter=i.sorter||n.default_sorter,e},default_sorter:function(i,n){return i.cost-n.cost},push:function(i,n){var e={value:i,cost:n};this.queue.push(e),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};o.exports=t}(Et)),Et.exports}var Xt;function an(){return Xt||(Xt=1,function(o){const t=J(),i=en(),n=nn(),e=rn(),r=on(),s=le(),u=H(),a=sn();function c(A){return unescape(encodeURIComponent(A)).length}function d(A,S,w){const C=[];let _;for(;(_=A.exec(w))!==null;)C.push({data:_[0],index:_.index,mode:S,length:_[0].length});return C}function E(A){const S=d(s.NUMERIC,t.NUMERIC,A),w=d(s.ALPHANUMERIC,t.ALPHANUMERIC,A);let C,_;return u.isKanjiModeEnabled()?(C=d(s.BYTE,t.BYTE,A),_=d(s.KANJI,t.KANJI,A)):(C=d(s.BYTE_KANJI,t.BYTE,A),_=[]),S.concat(w,C,_).sort(function(h,m){return h.index-m.index}).map(function(h){return{data:h.data,mode:h.mode,length:h.length}})}function f(A,S){switch(S){case t.NUMERIC:return i.getBitsLength(A);case t.ALPHANUMERIC:return n.getBitsLength(A);case t.KANJI:return r.getBitsLength(A);case t.BYTE:return e.getBitsLength(A)}}function p(A){return A.reduce(function(S,w){const C=S.length-1>=0?S[S.length-1]:null;return C&&C.mode===w.mode?(S[S.length-1].data+=w.data,S):(S.push(w),S)},[])}function T(A){const S=[];for(let w=0;w<A.length;w++){const C=A[w];switch(C.mode){case t.NUMERIC:S.push([C,{data:C.data,mode:t.ALPHANUMERIC,length:C.length},{data:C.data,mode:t.BYTE,length:C.length}]);break;case t.ALPHANUMERIC:S.push([C,{data:C.data,mode:t.BYTE,length:C.length}]);break;case t.KANJI:S.push([C,{data:C.data,mode:t.BYTE,length:c(C.data)}]);break;case t.BYTE:S.push([{data:C.data,mode:t.BYTE,length:c(C.data)}])}}return S}function N(A,S){const w={},C={start:{}};let _=["start"];for(let l=0;l<A.length;l++){const h=A[l],m=[];for(let g=0;g<h.length;g++){const b=h[g],y=""+l+g;m.push(y),w[y]={node:b,lastCount:0},C[y]={};for(let B=0;B<_.length;B++){const v=_[B];w[v]&&w[v].node.mode===b.mode?(C[v][y]=f(w[v].lastCount+b.length,b.mode)-f(w[v].lastCount,b.mode),w[v].lastCount+=b.length):(w[v]&&(w[v].lastCount=b.length),C[v][y]=f(b.length,b.mode)+4+t.getCharCountIndicator(b.mode,S))}}_=m}for(let l=0;l<_.length;l++)C[_[l]].end=0;return{map:C,table:w}}function R(A,S){let w;const C=t.getBestModeForData(A);if(w=t.from(S,C),w!==t.BYTE&&w.bit<C.bit)throw new Error('"'+A+'" cannot be encoded with mode '+t.toString(w)+`.
 Suggested mode is: `+t.toString(C));switch(w===t.KANJI&&!u.isKanjiModeEnabled()&&(w=t.BYTE),w){case t.NUMERIC:return new i(A);case t.ALPHANUMERIC:return new n(A);case t.KANJI:return new r(A);case t.BYTE:return new e(A)}}o.fromArray=function(S){return S.reduce(function(w,C){return typeof C=="string"?w.push(R(C,null)):C.data&&w.push(R(C.data,C.mode)),w},[])},o.fromString=function(S,w){const C=E(S,u.isKanjiModeEnabled()),_=T(C),l=N(_,w),h=a.find_path(l.map,"start","end"),m=[];for(let g=1;g<h.length-1;g++)m.push(l.table[h[g]].node);return o.fromArray(p(m))},o.rawSplit=function(S){return o.fromArray(E(S,u.isKanjiModeEnabled()))}}(mt)),mt}var te;function un(){if(te)return rt;te=1;const o=H(),t=Tt(),i=je(),n=xe(),e=Ye(),r=Ge(),s=Qe(),u=ae(),a=Ze(),c=Xe(),d=tn(),E=J(),f=an();function p(l,h){const m=l.size,g=r.getPositions(h);for(let b=0;b<g.length;b++){const y=g[b][0],B=g[b][1];for(let v=-1;v<=7;v++)if(!(y+v<=-1||m<=y+v))for(let I=-1;I<=7;I++)B+I<=-1||m<=B+I||(v>=0&&v<=6&&(I===0||I===6)||I>=0&&I<=6&&(v===0||v===6)||v>=2&&v<=4&&I>=2&&I<=4?l.set(y+v,B+I,!0,!0):l.set(y+v,B+I,!1,!0))}}function T(l){const h=l.size;for(let m=8;m<h-8;m++){const g=m%2===0;l.set(m,6,g,!0),l.set(6,m,g,!0)}}function N(l,h){const m=e.getPositions(h);for(let g=0;g<m.length;g++){const b=m[g][0],y=m[g][1];for(let B=-2;B<=2;B++)for(let v=-2;v<=2;v++)B===-2||B===2||v===-2||v===2||B===0&&v===0?l.set(b+B,y+v,!0,!0):l.set(b+B,y+v,!1,!0)}}function R(l,h){const m=l.size,g=c.getEncodedBits(h);let b,y,B;for(let v=0;v<18;v++)b=Math.floor(v/3),y=v%3+m-8-3,B=(g>>v&1)===1,l.set(b,y,B,!0),l.set(y,b,B,!0)}function A(l,h,m){const g=l.size,b=d.getEncodedBits(h,m);let y,B;for(y=0;y<15;y++)B=(b>>y&1)===1,y<6?l.set(y,8,B,!0):y<8?l.set(y+1,8,B,!0):l.set(g-15+y,8,B,!0),y<8?l.set(8,g-y-1,B,!0):y<9?l.set(8,15-y-1+1,B,!0):l.set(8,15-y-1,B,!0);l.set(g-8,8,1,!0)}function S(l,h){const m=l.size;let g=-1,b=m-1,y=7,B=0;for(let v=m-1;v>0;v-=2)for(v===6&&v--;;){for(let I=0;I<2;I++)if(!l.isReserved(b,v-I)){let V=!1;B<h.length&&(V=(h[B]>>>y&1)===1),l.set(b,v-I,V),y--,y===-1&&(B++,y=7)}if(b+=g,b<0||m<=b){b-=g,g=-g;break}}}function w(l,h,m){const g=new i;m.forEach(function(I){g.put(I.mode.bit,4),g.put(I.getLength(),E.getCharCountIndicator(I.mode,l)),I.write(g)});const b=o.getSymbolTotalCodewords(l),y=u.getTotalCodewordsCount(l,h),B=(b-y)*8;for(g.getLengthInBits()+4<=B&&g.put(0,4);g.getLengthInBits()%8!==0;)g.putBit(0);const v=(B-g.getLengthInBits())/8;for(let I=0;I<v;I++)g.put(I%2?17:236,8);return C(g,l,h)}function C(l,h,m){const g=o.getSymbolTotalCodewords(h),b=u.getTotalCodewordsCount(h,m),y=g-b,B=u.getBlocksCount(h,m),v=g%B,I=B-v,V=Math.floor(g/B),G=Math.floor(y/B),fe=G+1,It=V-G,de=new a(It);let Z=0;const $=new Array(B),Nt=new Array(B);let X=0;const he=new Uint8Array(l.buffer);for(let j=0;j<B;j++){const et=j<I?G:fe;$[j]=he.slice(Z,Z+et),Nt[j]=de.encode($[j]),Z+=et,X=Math.max(X,et)}const tt=new Uint8Array(g);let St=0,q,U;for(q=0;q<X;q++)for(U=0;U<B;U++)q<$[U].length&&(tt[St++]=$[U][q]);for(q=0;q<It;q++)for(U=0;U<B;U++)tt[St++]=Nt[U][q];return tt}function _(l,h,m,g){let b;if(Array.isArray(l))b=f.fromArray(l);else if(typeof l=="string"){let V=h;if(!V){const G=f.rawSplit(l);V=c.getBestVersionForData(G,m)}b=f.fromString(l,V||40)}else throw new Error("Invalid data");const y=c.getBestVersionForData(b,m);if(!y)throw new Error("The amount of data is too big to be stored in a QR Code");if(!h)h=y;else if(h<y)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+y+`.
`);const B=w(h,m,b),v=o.getSymbolSize(h),I=new n(v);return p(I,h),T(I),N(I,h),A(I,m,0),h>=7&&R(I,h),S(I,B),isNaN(g)&&(g=s.getBestMask(I,A.bind(null,I,m))),s.applyMask(g,I),A(I,m,g),{modules:I,version:h,errorCorrectionLevel:m,maskPattern:g,segments:b}}return rt.create=function(h,m){if(typeof h>"u"||h==="")throw new Error("No input text");let g=t.M,b,y;return typeof m<"u"&&(g=t.from(m.errorCorrectionLevel,t.M),b=c.from(m.version),y=s.from(m.maskPattern),m.toSJISFunc&&o.setToSJISFunction(m.toSJISFunc)),_(h,b,g,y)},rt}var Bt={},bt={},ee;function ce(){return ee||(ee=1,function(o){function t(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let n=i.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+i);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(r){return[r,r]}))),n.length===6&&n.push("F","F");const e=parseInt(n.join(""),16);return{r:e>>24&255,g:e>>16&255,b:e>>8&255,a:e&255,hex:"#"+n.slice(0,6).join("")}}o.getOptions=function(n){n||(n={}),n.color||(n.color={});const e=typeof n.margin>"u"||n.margin===null||n.margin<0?4:n.margin,r=n.width&&n.width>=21?n.width:void 0,s=n.scale||4;return{width:r,scale:r?4:s,margin:e,color:{dark:t(n.color.dark||"#000000ff"),light:t(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},o.getScale=function(n,e){return e.width&&e.width>=n+e.margin*2?e.width/(n+e.margin*2):e.scale},o.getImageWidth=function(n,e){const r=o.getScale(n,e);return Math.floor((n+e.margin*2)*r)},o.qrToImageData=function(n,e,r){const s=e.modules.size,u=e.modules.data,a=o.getScale(s,r),c=Math.floor((s+r.margin*2)*a),d=r.margin*a,E=[r.color.light,r.color.dark];for(let f=0;f<c;f++)for(let p=0;p<c;p++){let T=(f*c+p)*4,N=r.color.light;if(f>=d&&p>=d&&f<c-d&&p<c-d){const R=Math.floor((f-d)/a),A=Math.floor((p-d)/a);N=E[u[R*s+A]?1:0]}n[T++]=N.r,n[T++]=N.g,n[T++]=N.b,n[T]=N.a}}}(bt)),bt}var ne;function ln(){return ne||(ne=1,function(o){const t=ce();function i(e,r,s){e.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=s,r.width=s,r.style.height=s+"px",r.style.width=s+"px"}function n(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}o.render=function(r,s,u){let a=u,c=s;typeof a>"u"&&(!s||!s.getContext)&&(a=s,s=void 0),s||(c=n()),a=t.getOptions(a);const d=t.getImageWidth(r.modules.size,a),E=c.getContext("2d"),f=E.createImageData(d,d);return t.qrToImageData(f.data,r,a),i(E,c,d),E.putImageData(f,0,0),c},o.renderToDataURL=function(r,s,u){let a=u;typeof a>"u"&&(!s||!s.getContext)&&(a=s,s=void 0),a||(a={});const c=o.render(r,s,a),d=a.type||"image/png",E=a.rendererOpts||{};return c.toDataURL(d,E.quality)}}(Bt)),Bt}var At={},re;function cn(){if(re)return At;re=1;const o=ce();function t(e,r){const s=e.a/255,u=r+'="'+e.hex+'"';return s<1?u+" "+r+'-opacity="'+s.toFixed(2).slice(1)+'"':u}function i(e,r,s){let u=e+r;return typeof s<"u"&&(u+=" "+s),u}function n(e,r,s){let u="",a=0,c=!1,d=0;for(let E=0;E<e.length;E++){const f=Math.floor(E%r),p=Math.floor(E/r);!f&&!c&&(c=!0),e[E]?(d++,E>0&&f>0&&e[E-1]||(u+=c?i("M",f+s,.5+p+s):i("m",a,0),a=0,c=!1),f+1<r&&e[E+1]||(u+=i("h",d),d=0)):a++}return u}return At.render=function(r,s,u){const a=o.getOptions(s),c=r.modules.size,d=r.modules.data,E=c+a.margin*2,f=a.color.light.a?"<path "+t(a.color.light,"fill")+' d="M0 0h'+E+"v"+E+'H0z"/>':"",p="<path "+t(a.color.dark,"stroke")+' d="'+n(d,c,a.margin)+'"/>',T='viewBox="0 0 '+E+" "+E+'"',R='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+T+' shape-rendering="crispEdges">'+f+p+`</svg>
`;return typeof u=="function"&&u(null,R),R},At}var oe;function fn(){if(oe)return Y;oe=1;const o=Je(),t=un(),i=ln(),n=cn();function e(r,s,u,a,c){const d=[].slice.call(arguments,1),E=d.length,f=typeof d[E-1]=="function";if(!f&&!o())throw new Error("Callback required as last argument");if(f){if(E<2)throw new Error("Too few arguments provided");E===2?(c=u,u=s,s=a=void 0):E===3&&(s.getContext&&typeof c>"u"?(c=a,a=void 0):(c=a,a=u,u=s,s=void 0))}else{if(E<1)throw new Error("Too few arguments provided");return E===1?(u=s,s=a=void 0):E===2&&!s.getContext&&(a=u,u=s,s=void 0),new Promise(function(p,T){try{const N=t.create(u,a);p(r(N,s,a))}catch(N){T(N)}})}try{const p=t.create(u,a);c(null,r(p,s,a))}catch(p){c(p)}}return Y.create=t.create,Y.toCanvas=e.bind(null,i.render),Y.toDataURL=e.bind(null,i.renderToDataURL),Y.toString=e.bind(null,function(r,s,u){return n.render(r,u)}),Y}var dn=fn();const hn=be(dn),gn={class:"two-factor-auth-container"},pn={class:"two-factor-auth-card"},mn={key:0,class:"loading-container"},wn={key:1},yn={key:0,class:"setup-step"},vn={class:"setup-instructions"},Cn={class:"qr-code-container"},En={class:"qr-code-main"},Bn={key:0,class:"qr-code"},bn={class:"scan-instruction"},An={class:"qr-code-apps"},Tn={class:"app-download"},In=["src"],Nn={class:"app-download"},Sn=["src"],Pn={key:0,class:"secret-key-container"},Rn={class:"secret-key-wrapper"},_n={key:0,class:"secret-key"},Mn={class:"action-buttons"},kn={key:1,class:"verification-step"},Fn={class:"verification-description"},Dn={class:"verification-form"},Ln={class:"verification-code-container"},qn={class:"verification-code-label"},Un={class:"flex items-center justify-center w-full mt-6"},Vn={class:"action-buttons"},zn=Ae({__name:"two-factor-auth",setup(o){const{t}=Te(),i=Pe(),n=Ie(),e=Ne(),r=()=>{f.value&&f.value.secret&&navigator.clipboard.writeText(f.value.secret).then(()=>{E.value=!0,n.add({severity:"success",summary:t("common.success"),detail:t("user.twoFactorAuth.secretKeyCopied"),life:3e3}),setTimeout(()=>{E.value=!1},3e3)}).catch(()=>{n.add({severity:"error",summary:t("common.error"),detail:t("user.twoFactorAuth.copyFailed"),life:3e3})})},s=z(!1),u=z(!1),a=z(!1),c=z(""),d=z(""),E=z(!1),f=z({secret:"",google2fa_url:"",ios_download_path:"",android_download_path:"",is_bind:!1}),p=l=>{l&&f.value.google2fa_url&&hn.toCanvas(l,f.value.google2fa_url,{width:240,margin:0})},T=Se(()=>{const l=String(c.value);return l&&l.length===6&&/^\d+$/.test(l)?(C(),!0):!1}),N=async()=>{s.value=!0;try{const{data:l,code:h=1}=await ke();h===0&&(f.value=l,l.is_bind&&(w.value=2))}catch(l){S(l,t("user.twoFactorAuth.errorGettingQRCode"))}finally{s.value=!1}},R=async()=>{s.value=!0;try{const{code:l,message:h}=await Fe({secret:f.value.secret,verify_code:String(c.value)});return l!==0?(d.value=h||t("user.twoFactorAuth.invalidCode"),!1):(d.value="",a.value=!0,!0)}catch(l){return S(l,t("user.twoFactorAuth.errorEnabling")),!1}finally{s.value=!1}},A=()=>{c.value="",d.value=""},S=(l,h)=>{const m=(l==null?void 0:l.message)||h;d.value=m,n.add({severity:"error",summary:t("user.twoFactorAuth.error"),detail:m,life:5e3})},w=z(1),C=async()=>{w.value===1?w.value=2:w.value===2&&T.value&&await R()&&(await e.getUserInfo(),n.add({severity:"success",summary:t("user.twoFactorAuth.success"),detail:t("user.twoFactorAuth.setupComplete"),life:3e3}),i.push("/home"))},_=()=>{w.value>1?(w.value--,A()):i.push("/home")};return Re(async()=>{await N()}),(l,h)=>{const m=De,g=_e,b=ge;return F(),L("div",gn,[P("div",pn,[s.value?(F(),L("div",mn,[O(m)])):(F(),L("div",wn,[w.value===1?(F(),L("div",yn,[P("h2",null,k(M(t)("user.twoFactorAuth.setupStep")),1),P("div",vn,[P("ol",null,[P("li",null,k(M(t)("user.twoFactorAuth.downloadApp")),1),P("li",null,k(M(t)("user.twoFactorAuth.scanQRCode")),1),P("li",null,k(M(t)("user.twoFactorAuth.getVerificationCode")),1)])]),P("div",Cn,[P("div",En,[f.value.google2fa_url?(F(),L("div",Bn,[P("canvas",{id:"qrcode-canvas",ref:p})])):x("",!0),P("p",bn,[h[1]||(h[1]=P("i",{class:"pi pi-mobile"},null,-1)),Pt(" "+k(M(t)("user.twoFactorAuth.scanQRCode")),1)])]),P("div",An,[P("div",Tn,[P("img",{src:f.value.android_download_path,alt:"Google Authenticator",class:"app-icon"},null,8,In),h[2]||(h[2]=P("span",{class:"app-label"},"Android",-1))]),P("div",Nn,[P("img",{src:f.value.ios_download_path,alt:"Authy",class:"app-icon"},null,8,Sn),h[3]||(h[3]=P("span",{class:"app-label"},"iOS",-1))])])]),f.value.secret?(F(),L("div",Pn,[P("h3",null,k(M(t)("user.twoFactorAuth.manualSetup")),1),P("p",null,k(M(t)("user.twoFactorAuth.enterSecretKey")),1),P("div",Rn,[f.value?(F(),L("div",_n,k(f.value.secret),1)):x("",!0),O(g,{icon:"pi pi-copy",severity:"secondary",text:"",rounded:"","aria-label":"Copy",class:"copy-button",onClick:r})])])):x("",!0),P("div",Mn,[O(g,{label:M(t)("common.back"),severity:"secondary",outlined:"",disabled:s.value,onClick:_},null,8,["label","disabled"]),O(g,{label:M(t)("common.next"),severity:"primary",disabled:s.value,onClick:C},null,8,["label","disabled"])])])):w.value===2?(F(),L("div",kn,[P("h2",null,k(M(t)("user.twoFactorAuth.verificationStep")),1),P("p",Fn,k(M(t)("user.twoFactorAuth.enterCodeFromApp")),1),P("div",Dn,[P("div",Ln,[P("div",qn,k(M(t)("user.twoFactorAuth.verificationCode")),1),O(M(se),{id:"verificationCode",modelValue:c.value,"onUpdate:modelValue":h[0]||(h[0]=y=>c.value=y),disabled:u.value,length:6,"input-class":"otp-input",separator:"",size:"large",class:ie({"p-invalid":d.value})},null,8,["modelValue","disabled","class"]),P("div",Un,[d.value?(F(),Rt(b,{key:0,severity:"error",size:"small",variant:"simple"},{default:Me(()=>[Pt(k(d.value),1)]),_:1})):x("",!0)])])]),P("div",Vn,[f.value.is_bind?x("",!0):(F(),Rt(g,{key:0,label:M(t)("common.back"),severity:"secondary",outlined:"",disabled:u.value,onClick:_},null,8,["label","disabled"])),O(g,{style:{"margin-left":"auto"},label:M(t)("user.twoFactorAuth.verifyAndEnable"),severity:"primary",loading:u.value,disabled:!T.value||u.value,onClick:C},null,8,["label","loading","disabled"])])])):x("",!0)]))])])}}}),Gn=Ue(zn,[["__scopeId","data-v-c1026486"]]);export{Gn as default};
