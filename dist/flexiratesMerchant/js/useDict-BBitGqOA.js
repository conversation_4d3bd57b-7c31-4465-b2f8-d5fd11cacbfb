import{bz as p,Z as y,r as v,c as g}from"./index-DWWVg5mm.js";const b=t=>p.get("/getDictDataConst",{params:{type:t}}),h=y("dict",()=>{const t=v({}),n=v({});return{dictCache:t,loadingTypes:n,getDictByType:async e=>{if(t.value[e])return t.value[e];n.value[e]=!0;try{const a=await b(e);return a.data?(t.value[e]=a.data,a.data):[]}catch(a){return console.error(`Failed to fetch dictionary data for type: ${e}`,a),[]}finally{n.value[e]=!1}},clearDictCache:e=>{if(e){const a={...t.value};delete a[e],t.value=a}else t.value={}},isTypeLoading:e=>g(()=>!!n.value[e]),getDictLabel:(e,a)=>{if(!t.value[e])return"";const s=t.value[e].find(i=>i.value===a);return s?s.label:""}}},{persist:!1});function C(t,n){const r=h(),l=v([]),d=Reflect.get(r,"isTypeLoading"),f=Reflect.apply(d,r,[t]),e=async()=>{const c=Reflect.get(r,"getDictByType");return l.value=await Reflect.apply(c,r,[t]),n&&n(l.value),l.value},a=c=>{const o=l.value.find(u=>u.value===c);return o?o.label:""},s=c=>{const o=l.value.find(u=>u.label===c);return o?o.value:void 0},i=g(()=>l.value.map(c=>({label:c.label,value:c.value}))),D=()=>{const c=Reflect.get(r,"clearDictCache");Reflect.apply(c,r,[t])};return e(),{dictList:l,loading:f,loadDict:e,getLabel:a,getValue:s,options:i,clearCache:D}}export{C as u};
