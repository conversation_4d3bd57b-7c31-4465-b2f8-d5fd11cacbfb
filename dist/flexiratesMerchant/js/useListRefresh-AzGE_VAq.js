import{Z as a,r as c,i as n,V as u}from"./index-DWWVg5mm.js";const R=a("list",()=>{const e=c(new Map);return{setNeedRefresh:(s,h)=>{e.value.set(s,h)},getNeedRefresh:s=>e.value.get(s)||!1,clearRefreshFlag:s=>{e.value.delete(s)}}},{persist:!0});function l(e,t){const r=R(),f=n();return u(()=>{r.getNeedRefresh(e)&&(t(),r.clearRefreshFlag(e))}),{backWithRefresh:(o=!0)=>{r.setNeedRefresh(e,!0),o&&f.back()},withRefresh:()=>{r.setNeedRefresh(e,!0)}}}export{l as u};
