import{x as S,c as e}from"./index-DWWVg5mm.js";var i=(a=>(a.HOME_GET_SUM_DATA="cardiniaHomeGetSumData",a.HOME_GET_MONTH_PAYMENT="cardiniaHomeGetMonthPayment",a.RATE_PAYER_DETAIL="cardiniaRatePayerDetail",a.RATE_PAYER_LIST="cardiniaRatePayerList",a.RATE_PAYER_CREATE="cardiniaRatePayerCreate",a.RATE_PAYER_UPDATE="cardiniaRatePayerUpdate",a.RATE_PAYER_NOTE_LIST="cardiniaRatePayerNoteList",a.RATE_PAYER_ADD_NOTE="cardiniaRatePayerAddNote",a.RATE_PAYER_EXPORT="cardiniaRatePayerExport",a.PROPERTIES_LIST="cardiniaPropertiesList",a.PROPERTIES_DETAIL="cardiniaPropertiesDetail",a.PROPERTIES_CREATE="cardiniaPropertiesCreate",a.PROPERTIES_UPDATE="cardiniaPropertiesUpdate",a.PROPERTIES_EXPORT="cardiniaPropertiesExport",a.SETTLEMENT_LIST="cardiniaSettlementList",a.SETTLEMENT_DETAIL="cardiniaSettlementDetail",a.SETTLEMENT_DETAIL_INFO_LIST="cardiniaSettlementDetailInfoList",a.SETTLEMENT_DETAIL_INFO_DETAIL="cardiniaSettlementDetailInfoDetail",a.PAYMENTS_LIST="cardiniaPaymentsList",a.PAYMENTS_DETAIL="cardiniaPaymentsDetail",a.PAYMENTS_EXPORT="cardiniaPaymentsExport",a.SCHEDULE_LIST="cardiniaScheduleList",a.SCHEDULE_DETAIL="cardiniaScheduleDetail",a.SCHEDULE_EXPORT="cardiniaScheduleExport",a.SUPPORT_DETAIL="cardiniaSupportDetail",a.GET_MENU="cardiniaGetMenu",a.PERMISSION_LIST="cardiniaPermissionList",a.ROLE_LIST="cardiniaRoleList",a.ROLE_DETAIL="cardiniaRoleDetail",a.ROLE_CREATE="cardiniaRoleCreate",a.ROLE_UPDATE="cardiniaRoleUpdate",a.ROLE_DELETE="cardiniaRoleDelete",a.USER_LIST="cardiniaUserList",a.USER_DETAIL="cardiniaUserDetail",a.USER_CREATE="cardiniaUserCreate",a.USER_UPDATE="cardiniaUserUpdate",a.USER_DELETE="cardiniaUserDelete",a.NOTIFY_LIST="cardiniaNotifyList",a.NOTIFY_DELETE="cardiniaNotifyDelete",a.NOTIFY_UPDATE_READ="cardiniaNotifyUpdateRead",a.PROPERTY_LIST="cardiniaPropertyList",a.PROPERTY_CANCEL_REQUEST="cardiniaPropertyCancelRequest",a.PROPERTY_CHECK="cardiniaPropertyCheck",a.PAYOUT_LIST="cardiniaPayoutList",a.REPORT_OVERVIEW="cardiniaReportOverview",a.REPORT_MONTHLY="cardiniaReportMonthly",a.REPORT_ANNUAL="cardiniaReportAnnual",a.ARREAR_LIST="arrearList",a))(i||{});function D(){const a=S(),T=t=>{const E=t.toString();return a.hasPermission(E)},R=t=>{const E=t.map(r=>r.toString());return a.hasAnyPermission(E)},c=t=>{const E=t.map(r=>r.toString());return a.hasAllPermissions(E)},_=t=>e(()=>T(t)),n=t=>e(()=>R(t)),d=t=>e(()=>c(t)),A=e(()=>a.userPermissions),L=e(()=>a.isLoggedIn);return{hasPermission:T,hasAnyPermission:R,hasAllPermissions:c,canAccess:_,canAccessAny:n,canAccessAll:d,userPermissions:A,isLoggedIn:L,Permissions:i}}export{i as P,D as u};
