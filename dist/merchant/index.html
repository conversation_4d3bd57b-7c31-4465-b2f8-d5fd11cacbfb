<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" sizes="64x64">
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
  <meta name="renderer" content="webkit" />
  <meta content="Bill Buddy HQ" name="keywords" />
  <title><PERSON> HQ</title>
  <style>
    #nprogress {
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      z-index: 999;
    }

    #nprogress .bar {
      background: #fe4c1c !important;
      background: #fe4c1c !important;
    }

    .nprogress-custom-parent #nprogress .spinner .spinner-icon {
      border-top-color: #fe4c1c !important;
      border-left-color: #fe4c1c !important;
    }
  </style>
  <script type="module" crossorigin src="/js/index-BPwFJVSY.js"></script>
  <link rel="stylesheet" crossorigin href="/css/index-C8h0Efeo.css">
</head>

<body>
  <div id="app">
    <style>
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
    </style>
    <div class="loading-container">
      <object style="width: 200px; height: 200px" data="/loading-svg/animation.svg"
        type="image/svg+xml">
      </object>
      <script>
        document.addEventListener('DOMContentLoaded', () => {
          const loadingContainer = document.querySelector('.loading-container');
          function resetAnimation() {
            const oldEmbed = document.querySelector('.loading-container object');
            if (oldEmbed) {
              const src = oldEmbed.src;
              const width = oldEmbed.style.width;
              const height = oldEmbed.style.height;
              oldEmbed.remove();
              setTimeout(() => {
                const newEmbed = document.createElement('object');
                newEmbed.style.width = width;
                newEmbed.style.height = height;
                newEmbed.src = src;
                loadingContainer.prepend(newEmbed);
              }, 50);
            }
          }
          setInterval(resetAnimation, 6000);
        });
      </script>
    </div>
  </div>
</body>

</html>