import{g as t}from"./getAssetsFile-DC-dlhh8.js";import{d as u,a as s,q as i,u as o,b as p,n as m,y as l,t as y,o as n}from"./index-BPwFJVSY.js";import{_}from"./_plugin-vue_export-helper-DlAUqK2U.js";const x=r=>{switch(r){case 1:return t("merchant/card-type/visa.png");case 2:return t("merchant/card-type/mastercard.png");case 3:return t("merchant/card-type/amex.png");case 4:return t("merchant/card-type/discover.png");case 5:return t("merchant/card-type/diners.png");case 9:return t("merchant/card-type/bpay.png");case 0:case 6:return t("merchant/card-type/carte-blanche.png")}return""},g={class:"base-card-type"},b={key:0,class:"base-card-type__icon"},f=["src"],w={class:"base-card-type__text"},T=u({__name:"BaseCardType",props:{text:{default:""},cardType:{default:null},isShowCardNumber:{type:Boolean,default:!1}},setup(r){const e=r,c=a=>a==null?void 0:a.slice(-4),h=a=>{switch(a){case 1:return{width:"40px",maxWidth:"40px",height:"28px",borderRadius:"4px"};case 2:return{width:"28px",maxWidth:"28px",height:"28px",borderRadius:"4px"};case 3:return{width:"42px",maxWidth:"42px",height:"28px",borderRadius:"4px"};case 4:return{width:"34px",maxWidth:"34px",height:"32px",borderRadius:"4px"};case 5:return{width:"34px",maxWidth:"34px",height:"28px",borderRadius:"4px"};case 9:return{width:"36px",maxWidth:"36px",height:"24px",borderRadius:"6px"};case 0:case 6:return{width:"28px",maxWidth:"28px",height:"28px"}}return{width:"24px",height:"24px",borderRadius:"4px"}};return(a,d)=>(n(),s("div",g,[e.cardType!==null&&o(x)(e.cardType)?(n(),s("div",b,[p("img",{style:m(h(e.cardType)),src:o(x)(e.cardType),alt:"icon"},null,12,f)])):i("",!0),e.isShowCardNumber&&e.text&&c(e.text)?(n(),s(l,{key:1},[d[0]||(d[0]=p("span",{class:"base-card-type__text"}," •••• ",-1)),p("div",w,y(c(e.text)),1)],64)):i("",!0)]))}}),k=_(T,[["__scopeId","data-v-c860afba"]]);export{k as _};
