import{c as D,r as M,b9 as mt,S as J,ba as gt,bb as ht,bc as ee,u as vt,d as Qe,f as wt,P as ge,g as yt,a as he,o as Y,b as Ee,p as ae,I as Te,bd as xt,e as Ze,T as bt,w as te,N as At,q as oe,O as Rt,n as Ye,A as Ct,t as qe,j as we,s as Ot}from"./index-BPwFJVSY.js";import{_ as St}from"./_plugin-vue_export-helper-DlAUqK2U.js";const re=Math.min,K=Math.max,ye=Math.round,ve=Math.floor,W=e=>({x:e,y:e}),Et={left:"right",right:"left",bottom:"top",top:"bottom"},Tt={start:"end",end:"start"};function Le(e,t,o){return K(e,re(t,o))}function fe(e,t){return typeof e=="function"?e(t):e}function G(e){return e.split("-")[0]}function ue(e){return e.split("-")[1]}function et(e){return e==="x"?"y":"x"}function Pe(e){return e==="y"?"height":"width"}function U(e){return["top","bottom"].includes(G(e))?"y":"x"}function _e(e){return et(U(e))}function Lt(e,t,o){o===void 0&&(o=!1);const n=ue(e),i=_e(e),r=Pe(i);let s=i==="x"?n===(o?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(s=xe(s)),[s,xe(s)]}function kt(e){const t=xe(e);return[ke(e),t,ke(t)]}function ke(e){return e.replace(/start|end/g,t=>Tt[t])}function Dt(e,t,o){const n=["left","right"],i=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return o?t?i:n:t?n:i;case"left":case"right":return t?r:s;default:return[]}}function Bt(e,t,o,n){const i=ue(e);let r=Dt(G(e),o==="start",n);return i&&(r=r.map(s=>s+"-"+i),t&&(r=r.concat(r.map(ke)))),r}function xe(e){return e.replace(/left|right|bottom|top/g,t=>Et[t])}function Pt(e){return{top:0,right:0,bottom:0,left:0,...e}}function tt(e){return typeof e!="number"?Pt(e):{top:e,right:e,bottom:e,left:e}}function be(e){const{x:t,y:o,width:n,height:i}=e;return{width:n,height:i,top:o,left:t,right:t+n,bottom:o+i,x:t,y:o}}function Ue(e,t,o){let{reference:n,floating:i}=e;const r=U(t),s=_e(t),l=Pe(s),a=G(t),c=r==="y",d=n.x+n.width/2-i.width/2,u=n.y+n.height/2-i.height/2,p=n[l]/2-i[l]/2;let f;switch(a){case"top":f={x:d,y:n.y-i.height};break;case"bottom":f={x:d,y:n.y+n.height};break;case"right":f={x:n.x+n.width,y:u};break;case"left":f={x:n.x-i.width,y:u};break;default:f={x:n.x,y:n.y}}switch(ue(t)){case"start":f[s]-=p*(o&&c?-1:1);break;case"end":f[s]+=p*(o&&c?-1:1);break}return f}const _t=async(e,t,o)=>{const{placement:n="bottom",strategy:i="absolute",middleware:r=[],platform:s}=o,l=r.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(t));let c=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:d,y:u}=Ue(c,n,a),p=n,f={},m=0;for(let g=0;g<l.length;g++){const{name:h,fn:v}=l[g],{x:w,y:x,data:A,reset:b}=await v({x:d,y:u,initialPlacement:n,placement:p,strategy:i,middlewareData:f,rects:c,platform:s,elements:{reference:e,floating:t}});d=w??d,u=x??u,f={...f,[h]:{...f[h],...A}},b&&m<=50&&(m++,typeof b=="object"&&(b.placement&&(p=b.placement),b.rects&&(c=b.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:d,y:u}=Ue(c,p,a)),g=-1)}return{x:d,y:u,placement:p,strategy:i,middlewareData:f}};async function nt(e,t){var o;t===void 0&&(t={});const{x:n,y:i,platform:r,rects:s,elements:l,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:p=!1,padding:f=0}=fe(t,e),m=tt(f),h=l[p?u==="floating"?"reference":"floating":u],v=be(await r.getClippingRect({element:(o=await(r.isElement==null?void 0:r.isElement(h)))==null||o?h:h.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:c,rootBoundary:d,strategy:a})),w=u==="floating"?{x:n,y:i,width:s.floating.width,height:s.floating.height}:s.reference,x=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),A=await(r.isElement==null?void 0:r.isElement(x))?await(r.getScale==null?void 0:r.getScale(x))||{x:1,y:1}:{x:1,y:1},b=be(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:w,offsetParent:x,strategy:a}):w);return{top:(v.top-b.top+m.top)/A.y,bottom:(b.bottom-v.bottom+m.bottom)/A.y,left:(v.left-b.left+m.left)/A.x,right:(b.right-v.right+m.right)/A.x}}const Mt=e=>({name:"arrow",options:e,async fn(t){const{x:o,y:n,placement:i,rects:r,platform:s,elements:l,middlewareData:a}=t,{element:c,padding:d=0}=fe(e,t)||{};if(c==null)return{};const u=tt(d),p={x:o,y:n},f=_e(i),m=Pe(f),g=await s.getDimensions(c),h=f==="y",v=h?"top":"left",w=h?"bottom":"right",x=h?"clientHeight":"clientWidth",A=r.reference[m]+r.reference[f]-p[f]-r.floating[m],b=p[f]-r.reference[f],E=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c));let y=E?E[x]:0;(!y||!await(s.isElement==null?void 0:s.isElement(E)))&&(y=l.floating[x]||r.floating[m]);const R=A/2-b/2,O=y/2-g[m]/2-1,P=re(u[v],O),q=re(u[w],O),_=P,I=y-g[m]-q,S=y/2-g[m]/2+R,j=Le(_,S,I),$=!a.arrow&&ue(i)!=null&&S!==j&&r.reference[m]/2-(S<_?P:q)-g[m]/2<0,L=$?S<_?S-_:S-I:0;return{[f]:p[f]+L,data:{[f]:j,centerOffset:S-j-L,...$&&{alignmentOffset:L}},reset:$}}}),$t=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var o,n;const{placement:i,middlewareData:r,rects:s,initialPlacement:l,platform:a,elements:c}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:p,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:g=!0,...h}=fe(e,t);if((o=r.arrow)!=null&&o.alignmentOffset)return{};const v=G(i),w=U(l),x=G(l)===l,A=await(a.isRTL==null?void 0:a.isRTL(c.floating)),b=p||(x||!g?[xe(l)]:kt(l)),E=m!=="none";!p&&E&&b.push(...Bt(l,g,m,A));const y=[l,...b],R=await nt(t,h),O=[];let P=((n=r.flip)==null?void 0:n.overflows)||[];if(d&&O.push(R[v]),u){const S=Lt(i,s,A);O.push(R[S[0]],R[S[1]])}if(P=[...P,{placement:i,overflows:O}],!O.every(S=>S<=0)){var q,_;const S=(((q=r.flip)==null?void 0:q.index)||0)+1,j=y[S];if(j&&(!(u==="alignment"?w!==U(j):!1)||P.every(k=>k.overflows[0]>0&&U(k.placement)===w)))return{data:{index:S,overflows:P},reset:{placement:j}};let $=(_=P.filter(L=>L.overflows[0]<=0).sort((L,k)=>L.overflows[1]-k.overflows[1])[0])==null?void 0:_.placement;if(!$)switch(f){case"bestFit":{var I;const L=(I=P.filter(k=>{if(E){const V=U(k.placement);return V===w||V==="y"}return!0}).map(k=>[k.placement,k.overflows.filter(V=>V>0).reduce((V,Oe)=>V+Oe,0)]).sort((k,V)=>k[1]-V[1])[0])==null?void 0:I[0];L&&($=L);break}case"initialPlacement":$=l;break}if(i!==$)return{reset:{placement:$}}}return{}}}};async function Nt(e,t){const{placement:o,platform:n,elements:i}=e,r=await(n.isRTL==null?void 0:n.isRTL(i.floating)),s=G(o),l=ue(o),a=U(o)==="y",c=["left","top"].includes(s)?-1:1,d=r&&a?-1:1,u=fe(t,e);let{mainAxis:p,crossAxis:f,alignmentAxis:m}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return l&&typeof m=="number"&&(f=l==="end"?m*-1:m),a?{x:f*d,y:p*c}:{x:p*c,y:f*d}}const Ft=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var o,n;const{x:i,y:r,placement:s,middlewareData:l}=t,a=await Nt(t,e);return s===((o=l.offset)==null?void 0:o.placement)&&(n=l.arrow)!=null&&n.alignmentOffset?{}:{x:i+a.x,y:r+a.y,data:{...a,placement:s}}}}},Vt=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:o,y:n,placement:i}=t,{mainAxis:r=!0,crossAxis:s=!1,limiter:l={fn:h=>{let{x:v,y:w}=h;return{x:v,y:w}}},...a}=fe(e,t),c={x:o,y:n},d=await nt(t,a),u=U(G(i)),p=et(u);let f=c[p],m=c[u];if(r){const h=p==="y"?"top":"left",v=p==="y"?"bottom":"right",w=f+d[h],x=f-d[v];f=Le(w,f,x)}if(s){const h=u==="y"?"top":"left",v=u==="y"?"bottom":"right",w=m+d[h],x=m-d[v];m=Le(w,m,x)}const g=l.fn({...t,[p]:f,[u]:m});return{...g,data:{x:g.x-o,y:g.y-n,enabled:{[p]:r,[u]:s}}}}}};function Ae(){return typeof window<"u"}function Z(e){return Me(e)?(e.nodeName||"").toLowerCase():"#document"}function B(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function H(e){var t;return(t=(Me(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Me(e){return Ae()?e instanceof Node||e instanceof B(e).Node:!1}function N(e){return Ae()?e instanceof Element||e instanceof B(e).Element:!1}function z(e){return Ae()?e instanceof HTMLElement||e instanceof B(e).HTMLElement:!1}function Xe(e){return!Ae()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof B(e).ShadowRoot}function de(e){const{overflow:t,overflowX:o,overflowY:n,display:i}=F(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+o)&&!["inline","contents"].includes(i)}function Wt(e){return["table","td","th"].includes(Z(e))}function Re(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function $e(e){const t=Ne(),o=N(e)?F(e):e;return["transform","translate","scale","rotate","perspective"].some(n=>o[n]?o[n]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!t&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!t&&(o.filter?o.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(o.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(o.contain||"").includes(n))}function zt(e){let t=X(e);for(;z(t)&&!se(t);){if($e(t))return t;if(Re(t))return null;t=X(t)}return null}function Ne(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function se(e){return["html","body","#document"].includes(Z(e))}function F(e){return B(e).getComputedStyle(e)}function Ce(e){return N(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function X(e){if(Z(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Xe(e)&&e.host||H(e);return Xe(t)?t.host:t}function ot(e){const t=X(e);return se(t)?e.ownerDocument?e.ownerDocument.body:e.body:z(t)&&de(t)?t:ot(t)}function ce(e,t,o){var n;t===void 0&&(t=[]),o===void 0&&(o=!0);const i=ot(e),r=i===((n=e.ownerDocument)==null?void 0:n.body),s=B(i);if(r){const l=De(s);return t.concat(s,s.visualViewport||[],de(i)?i:[],l&&o?ce(l):[])}return t.concat(i,ce(i,[],o))}function De(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function it(e){const t=F(e);let o=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const i=z(e),r=i?e.offsetWidth:o,s=i?e.offsetHeight:n,l=ye(o)!==r||ye(n)!==s;return l&&(o=r,n=s),{width:o,height:n,$:l}}function Fe(e){return N(e)?e:e.contextElement}function ie(e){const t=Fe(e);if(!z(t))return W(1);const o=t.getBoundingClientRect(),{width:n,height:i,$:r}=it(t);let s=(r?ye(o.width):o.width)/n,l=(r?ye(o.height):o.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const Ht=W(0);function rt(e){const t=B(e);return!Ne()||!t.visualViewport?Ht:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function It(e,t,o){return t===void 0&&(t=!1),!o||t&&o!==B(e)?!1:t}function Q(e,t,o,n){t===void 0&&(t=!1),o===void 0&&(o=!1);const i=e.getBoundingClientRect(),r=Fe(e);let s=W(1);t&&(n?N(n)&&(s=ie(n)):s=ie(e));const l=It(r,o,n)?rt(r):W(0);let a=(i.left+l.x)/s.x,c=(i.top+l.y)/s.y,d=i.width/s.x,u=i.height/s.y;if(r){const p=B(r),f=n&&N(n)?B(n):n;let m=p,g=De(m);for(;g&&n&&f!==m;){const h=ie(g),v=g.getBoundingClientRect(),w=F(g),x=v.left+(g.clientLeft+parseFloat(w.paddingLeft))*h.x,A=v.top+(g.clientTop+parseFloat(w.paddingTop))*h.y;a*=h.x,c*=h.y,d*=h.x,u*=h.y,a+=x,c+=A,m=B(g),g=De(m)}}return be({width:d,height:u,x:a,y:c})}function Ve(e,t){const o=Ce(e).scrollLeft;return t?t.left+o:Q(H(e)).left+o}function st(e,t,o){o===void 0&&(o=!1);const n=e.getBoundingClientRect(),i=n.left+t.scrollLeft-(o?0:Ve(e,n)),r=n.top+t.scrollTop;return{x:i,y:r}}function jt(e){let{elements:t,rect:o,offsetParent:n,strategy:i}=e;const r=i==="fixed",s=H(n),l=t?Re(t.floating):!1;if(n===s||l&&r)return o;let a={scrollLeft:0,scrollTop:0},c=W(1);const d=W(0),u=z(n);if((u||!u&&!r)&&((Z(n)!=="body"||de(s))&&(a=Ce(n)),z(n))){const f=Q(n);c=ie(n),d.x=f.x+n.clientLeft,d.y=f.y+n.clientTop}const p=s&&!u&&!r?st(s,a,!0):W(0);return{width:o.width*c.x,height:o.height*c.y,x:o.x*c.x-a.scrollLeft*c.x+d.x+p.x,y:o.y*c.y-a.scrollTop*c.y+d.y+p.y}}function Yt(e){return Array.from(e.getClientRects())}function qt(e){const t=H(e),o=Ce(e),n=e.ownerDocument.body,i=K(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),r=K(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let s=-o.scrollLeft+Ve(e);const l=-o.scrollTop;return F(n).direction==="rtl"&&(s+=K(t.clientWidth,n.clientWidth)-i),{width:i,height:r,x:s,y:l}}function Ut(e,t){const o=B(e),n=H(e),i=o.visualViewport;let r=n.clientWidth,s=n.clientHeight,l=0,a=0;if(i){r=i.width,s=i.height;const c=Ne();(!c||c&&t==="fixed")&&(l=i.offsetLeft,a=i.offsetTop)}return{width:r,height:s,x:l,y:a}}function Xt(e,t){const o=Q(e,!0,t==="fixed"),n=o.top+e.clientTop,i=o.left+e.clientLeft,r=z(e)?ie(e):W(1),s=e.clientWidth*r.x,l=e.clientHeight*r.y,a=i*r.x,c=n*r.y;return{width:s,height:l,x:a,y:c}}function Je(e,t,o){let n;if(t==="viewport")n=Ut(e,o);else if(t==="document")n=qt(H(e));else if(N(t))n=Xt(t,o);else{const i=rt(e);n={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return be(n)}function lt(e,t){const o=X(e);return o===t||!N(o)||se(o)?!1:F(o).position==="fixed"||lt(o,t)}function Jt(e,t){const o=t.get(e);if(o)return o;let n=ce(e,[],!1).filter(l=>N(l)&&Z(l)!=="body"),i=null;const r=F(e).position==="fixed";let s=r?X(e):e;for(;N(s)&&!se(s);){const l=F(s),a=$e(s);!a&&l.position==="fixed"&&(i=null),(r?!a&&!i:!a&&l.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||de(s)&&!a&&lt(e,s))?n=n.filter(d=>d!==s):i=l,s=X(s)}return t.set(e,n),n}function Kt(e){let{element:t,boundary:o,rootBoundary:n,strategy:i}=e;const s=[...o==="clippingAncestors"?Re(t)?[]:Jt(t,this._c):[].concat(o),n],l=s[0],a=s.reduce((c,d)=>{const u=Je(t,d,i);return c.top=K(u.top,c.top),c.right=re(u.right,c.right),c.bottom=re(u.bottom,c.bottom),c.left=K(u.left,c.left),c},Je(t,l,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Gt(e){const{width:t,height:o}=it(e);return{width:t,height:o}}function Qt(e,t,o){const n=z(t),i=H(t),r=o==="fixed",s=Q(e,!0,r,t);let l={scrollLeft:0,scrollTop:0};const a=W(0);function c(){a.x=Ve(i)}if(n||!n&&!r)if((Z(t)!=="body"||de(i))&&(l=Ce(t)),n){const f=Q(t,!0,r,t);a.x=f.x+t.clientLeft,a.y=f.y+t.clientTop}else i&&c();r&&!n&&i&&c();const d=i&&!n&&!r?st(i,l):W(0),u=s.left+l.scrollLeft-a.x-d.x,p=s.top+l.scrollTop-a.y-d.y;return{x:u,y:p,width:s.width,height:s.height}}function Se(e){return F(e).position==="static"}function Ke(e,t){if(!z(e)||F(e).position==="fixed")return null;if(t)return t(e);let o=e.offsetParent;return H(e)===o&&(o=o.ownerDocument.body),o}function at(e,t){const o=B(e);if(Re(e))return o;if(!z(e)){let i=X(e);for(;i&&!se(i);){if(N(i)&&!Se(i))return i;i=X(i)}return o}let n=Ke(e,t);for(;n&&Wt(n)&&Se(n);)n=Ke(n,t);return n&&se(n)&&Se(n)&&!$e(n)?o:n||zt(e)||o}const Zt=async function(e){const t=this.getOffsetParent||at,o=this.getDimensions,n=await o(e.floating);return{reference:Qt(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function en(e){return F(e).direction==="rtl"}const tn={convertOffsetParentRelativeRectToViewportRelativeRect:jt,getDocumentElement:H,getClippingRect:Kt,getOffsetParent:at,getElementRects:Zt,getClientRects:Yt,getDimensions:Gt,getScale:ie,isElement:N,isRTL:en};function ct(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function nn(e,t){let o=null,n;const i=H(e);function r(){var l;clearTimeout(n),(l=o)==null||l.disconnect(),o=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),r();const c=e.getBoundingClientRect(),{left:d,top:u,width:p,height:f}=c;if(l||t(),!p||!f)return;const m=ve(u),g=ve(i.clientWidth-(d+p)),h=ve(i.clientHeight-(u+f)),v=ve(d),x={rootMargin:-m+"px "+-g+"px "+-h+"px "+-v+"px",threshold:K(0,re(1,a))||1};let A=!0;function b(E){const y=E[0].intersectionRatio;if(y!==a){if(!A)return s();y?s(!1,y):n=setTimeout(()=>{s(!1,1e-7)},1e3)}y===1&&!ct(c,e.getBoundingClientRect())&&s(),A=!1}try{o=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch{o=new IntersectionObserver(b,x)}o.observe(e)}return s(!0),r}function on(e,t,o,n){n===void 0&&(n={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=n,c=Fe(e),d=i||r?[...c?ce(c):[],...ce(t)]:[];d.forEach(v=>{i&&v.addEventListener("scroll",o,{passive:!0}),r&&v.addEventListener("resize",o)});const u=c&&l?nn(c,o):null;let p=-1,f=null;s&&(f=new ResizeObserver(v=>{let[w]=v;w&&w.target===c&&f&&(f.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var x;(x=f)==null||x.observe(t)})),o()}),c&&!a&&f.observe(c),f.observe(t));let m,g=a?Q(e):null;a&&h();function h(){const v=Q(e);g&&!ct(g,v)&&o(),g=v,m=requestAnimationFrame(h)}return o(),()=>{var v;d.forEach(w=>{i&&w.removeEventListener("scroll",o),r&&w.removeEventListener("resize",o)}),u==null||u(),(v=f)==null||v.disconnect(),f=null,a&&cancelAnimationFrame(m)}}const rn=Ft,sn=Vt,ln=$t,an=Mt,cn=(e,t,o)=>{const n=new Map,i={platform:tn,...o},r={...i.platform,_c:n};return _t(e,t,{...i,platform:r})};function fn(e){return e!=null&&typeof e=="object"&&"$el"in e}function Be(e){if(fn(e)){const t=e.$el;return Me(t)&&Z(t)==="#comment"?null:t}return e}function ne(e){return typeof e=="function"?e():vt(e)}function un(e){return{name:"arrow",options:e,fn(t){const o=Be(ne(e.element));return o==null?{}:an({element:o,padding:e.padding}).fn(t)}}}function ft(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ge(e,t){const o=ft(e);return Math.round(t*o)/o}function dn(e,t,o){o===void 0&&(o={});const n=o.whileElementsMounted,i=D(()=>{var y;return(y=ne(o.open))!=null?y:!0}),r=D(()=>ne(o.middleware)),s=D(()=>{var y;return(y=ne(o.placement))!=null?y:"bottom"}),l=D(()=>{var y;return(y=ne(o.strategy))!=null?y:"absolute"}),a=D(()=>{var y;return(y=ne(o.transform))!=null?y:!0}),c=D(()=>Be(e.value)),d=D(()=>Be(t.value)),u=M(0),p=M(0),f=M(l.value),m=M(s.value),g=mt({}),h=M(!1),v=D(()=>{const y={position:f.value,left:"0",top:"0"};if(!d.value)return y;const R=Ge(d.value,u.value),O=Ge(d.value,p.value);return a.value?{...y,transform:"translate("+R+"px, "+O+"px)",...ft(d.value)>=1.5&&{willChange:"transform"}}:{position:f.value,left:R+"px",top:O+"px"}});let w;function x(){if(c.value==null||d.value==null)return;const y=i.value;cn(c.value,d.value,{middleware:r.value,placement:s.value,strategy:l.value}).then(R=>{u.value=R.x,p.value=R.y,f.value=R.strategy,m.value=R.placement,g.value=R.middlewareData,h.value=y!==!1})}function A(){typeof w=="function"&&(w(),w=void 0)}function b(){if(A(),n===void 0){x();return}if(c.value!=null&&d.value!=null){w=n(c.value,d.value,x);return}}function E(){i.value||(h.value=!1)}return J([r,s,l,i],x,{flush:"sync"}),J([c,d],b,{flush:"sync"}),J(i,E,{flush:"sync"}),gt()&&ht(A),{x:ee(u),y:ee(p),strategy:ee(f),placement:ee(m),middlewareData:ee(g),isPositioned:ee(h),floatingStyles:v,update:x}}const pn={class:"base-popover-container"},mn=["tabindex"],gn={key:0,class:"base-popover-title"},hn={class:"base-popover-content"},vn=Qe({__name:"BasePopover",props:{trigger:{type:String,default:"hover",validator:e=>["click","focus","hover","contextmenu"].includes(e)},title:{type:String,default:""},effect:{type:String,default:"light"},content:{type:String,default:""},width:{type:[String,Number],default:"auto"},placement:{type:String,default:"bottom",validator:e=>["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"].includes(e)},disabled:{type:Boolean,default:!1},visible:{type:Boolean,default:void 0},offset:{type:Number,default:12},transition:{type:String,default:"el-fade-in-linear"},showArrow:{type:Boolean,default:!0},popperOptions:{type:Object,default:()=>({modifiers:[{name:"computeStyles",options:{gpuAcceleration:!1}}]})},popperClass:{type:String,default:""},popperStyle:{type:[String,Object],default:""},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},tabindex:{type:[Number,String],default:0},teleported:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},virtualTriggering:{type:Boolean,default:!1}},emits:["update:visible","show","beforeEnter","afterEnter","hide","beforeLeave","afterLeave"],setup(e,{expose:t,emit:o}){const n=e,i=o,r=M(null),s=M(null),l=M(null),a=M(!1),c=D(()=>typeof n.width=="number"?`${n.width}px`:n.width);J(()=>n.visible,C=>{C!==void 0&&(a.value=C)}),J(a,C=>{i("update:visible",C),i(C?"show":"hide")});const d=M(n.placement),{floatingStyles:u,middlewareData:p,placement:f,isPositioned:m,update:g}=dn(r,s,{placement:d,strategy:"absolute",middleware:[rn(n.offset),ln(),sn({padding:5}),...n.showArrow?[un({element:l})]:[]],whileElementsMounted:on});J(()=>n.placement,C=>{d.value=C,ge(()=>{g&&g()})});const h=D(()=>{if(typeof n.popperStyle=="string")try{return JSON.parse(n.popperStyle)}catch{return{}}return n.popperStyle}),v=D(()=>({...u.value,width:c.value,...h.value})),w=D(()=>{var je;if(!n.showArrow)return{};if(!((je=p.value)!=null&&je.arrow))return{};const{x:C,y:T}=p.value.arrow,dt={position:"absolute",width:"8px",height:"8px",backgroundColor:n.effect==="dark"?"var(--tw-gray-800)":"white",zIndex:1},me=f.value,pt="rotate(45deg)";let We,ze,He,Ie,le={};return me.includes("top")?(He="-4px",le={borderTop:"none",borderLeft:"none",borderRight:"1px solid var(--tw-gray-200)",borderBottom:"1px solid var(--tw-gray-200)"}):me.includes("bottom")?(We="-4px",le={borderBottom:"none",borderRight:"none",borderLeft:"1px solid var(--tw-gray-200)",borderTop:"1px solid var(--tw-gray-200)"}):me.includes("left")?(ze="-4px",le={borderLeft:"none",borderBottom:"none",borderRight:"1px solid var(--tw-gray-200)",borderTop:"1px solid var(--tw-gray-200)"}):me.includes("right")&&(Ie="-4px",le={borderRight:"none",borderTop:"none",borderLeft:"1px solid var(--tw-gray-200)",borderBottom:"1px solid var(--tw-gray-200)"}),{...dt,...le,transform:pt,top:T!=null?`${T}px`:We,left:C!=null?`${C}px`:Ie,right:ze,bottom:He}}),x=D(()=>n.effect==="dark"?"is-dark":"is-light");let A,b,E;const y=()=>{A&&(window.clearTimeout(A),A=void 0),b&&(window.clearTimeout(b),b=void 0),E&&(window.clearTimeout(E),E=void 0)},R=()=>{n.disabled||(y(),n.showAfter===0?(a.value=!0,P()):A=window.setTimeout(()=>{a.value=!0,P()},n.showAfter))},O=()=>{n.disabled||(y(),n.hideAfter===0?a.value=!1:b=window.setTimeout(()=>{a.value=!1},n.hideAfter))},P=()=>{n.autoClose>0&&(E=window.setTimeout(()=>{a.value=!1},n.autoClose))},q=C=>{const T=C.target;a.value&&!n.virtualTriggering&&r.value&&s.value&&!r.value.contains(T)&&!s.value.contains(T)&&O()};wt(()=>{document.addEventListener("click",q),ge(()=>{n.visible!==void 0&&(a.value=n.visible)})}),yt(()=>{document.removeEventListener("click",q),y()}),J(m,C=>{C?(i("beforeEnter"),ge(()=>{i("afterEnter")})):(i("beforeLeave"),ge(()=>{i("afterLeave")}))}),t({hide:O});const _=M(!1),I=M(!1),S=()=>{n.trigger==="hover"&&(_.value=!0,R())},j=()=>{n.trigger==="hover"&&(_.value=!1,setTimeout(()=>{!_.value&&!I.value&&O()},50))},$=()=>{n.trigger==="hover"&&(I.value=!0)},L=()=>{n.trigger==="hover"&&(I.value=!1,_.value||O())},k=()=>{n.trigger==="click"&&(a.value?O():R())},V=()=>{n.trigger==="focus"&&R()},Oe=()=>{n.trigger==="focus"&&O()},ut=C=>{n.trigger==="contextmenu"&&(C.preventDefault(),a.value?O():R())};return(C,T)=>(Y(),he("div",pn,[Ee("div",{ref_key:"referenceRef",ref:r,class:"base-popover-reference",tabindex:n.disabled?void 0:n.tabindex,onMouseenter:S,onMouseleave:j,onClick:k,onFocus:V,onBlur:Oe,onContextmenu:ut},[Te(C.$slots,"reference",{isOpen:a.value},void 0,!0)],40,mn),(Y(),ae(xt,{to:"body",disabled:!n.teleported},[Ze(bt,{name:n.transition,onBeforeEnter:T[0]||(T[0]=pe=>i("beforeEnter")),onAfterEnter:T[1]||(T[1]=pe=>i("afterEnter")),onBeforeLeave:T[2]||(T[2]=pe=>i("beforeLeave")),onAfterLeave:T[3]||(T[3]=pe=>i("afterLeave"))},{default:te(()=>[a.value||n.persistent?At((Y(),he("div",{key:0,ref_key:"floatingRef",ref:s,class:Ct(["base-popover",[x.value,n.popperClass]]),style:Ye(v.value),role:"tooltip",onMouseenter:$,onMouseleave:L},[n.title?(Y(),he("div",gn,qe(n.title),1)):oe("",!0),Ee("div",hn,[Te(C.$slots,"default",{},()=>[we(qe(n.content),1)],!0)]),n.showArrow?(Y(),he("div",{key:1,ref_key:"arrowRef",ref:l,class:"base-popover-arrow",style:Ye(w.value)},null,4)):oe("",!0)],38)),[[Rt,a.value]]):oe("",!0)]),_:3},8,["name"])],8,["disabled"]))]))}}),wn=St(vn,[["__scopeId","data-v-d09b863a"]]),yn={class:"flex gap-2"},An=Qe({__name:"BaseDataTableActions",props:{disabled:{type:Boolean,default:!1},contentWidth:{default:"auto"},popperClass:{default:""},offset:{default:12},showArrow:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},isShowEdit:{type:Boolean,default:!0},isShowDelete:{type:Boolean,default:!0},isShowDetail:{type:Boolean,default:!0}},emits:["edit","delete","detail"],setup(e,{emit:t}){const o=e,n=t;return(i,r)=>{const s=Ot,l=wn;return Y(),ae(l,{disabled:o.disabled,trigger:"hover",offset:o.offset,"show-arrow":o.showArrow,width:o.contentWidth,"popper-class":o.popperClass,placement:"left"},{reference:te(()=>[Ze(s,{class:"base-data-table-actions",severity:"secondary",icon:"pi pi-ellipsis-h",loading:o.loading},null,8,["loading"])]),default:te(()=>[Te(i.$slots,"default",{},()=>[Ee("div",yn,[o.isShowDetail?(Y(),ae(s,{key:0,severity:"secondary",onClick:r[0]||(r[0]=a=>n("detail"))},{default:te(()=>r[3]||(r[3]=[we(" Details ")])),_:1,__:[3]})):oe("",!0),o.isShowEdit?(Y(),ae(s,{key:1,severity:"secondary",onClick:r[1]||(r[1]=a=>n("edit"))},{default:te(()=>r[4]||(r[4]=[we(" Edit ")])),_:1,__:[4]})):oe("",!0),o.isShowDelete?(Y(),ae(s,{key:2,severity:"secondary",onClick:r[2]||(r[2]=a=>n("delete"))},{default:te(()=>r[5]||(r[5]=[we(" Delete ")])),_:1,__:[5]})):oe("",!0)])])]),_:3},8,["disabled","offset","show-arrow","width","popper-class"])}}});export{An as _};
