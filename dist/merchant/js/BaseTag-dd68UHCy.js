import{d as s,a as n,I as o,j as i,t as r,A as c,o as d}from"./index-BPwFJVSY.js";import{_ as p}from"./_plugin-vue_export-helper-DlAUqK2U.js";const f=s({name:"BaseTag",props:{text:{type:[String,Number],default:""},type:{type:String,default:"default",validator:e=>["default","paid","upcoming","failed"].includes(e)},clickable:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:a}){return{handleClick:()=>{e.clickable&&a("click")}}}});function u(e,a,t,k,m,b){return d(),n("div",{class:c(["base-tag",[`base-tag--${e.type}`,{"base-tag--clickable":e.clickable}]]),onClick:a[0]||(a[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[o(e.$slots,"default",{},()=>[i(r(e.text),1)],!0)],2)}const C=p(f,[["render",u],["__scopeId","data-v-b1a72fd3"]]);export{C as _};
