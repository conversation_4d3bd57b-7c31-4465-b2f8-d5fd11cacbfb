import{d as D}from"./dayjs.min-Bar1rHDB.js";import{d as b,r,c as k,S as u,a as c,o as n,e as w,u as m,A as V,w as g,q as x,b as f,y as B,z as I,p as P,s as S}from"./index-BPwFJVSY.js";import{s as C}from"./index-DlkyKXFh.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z={class:"enhanced-date-picker"},F={key:0,class:"date-presets-panel"},N={class:"date-presets-buttons"},M=b({name:"EnhancedDatePicker",__name:"EnhancedDatePicker",props:{modelValue:{type:[Date,Date],default:null},inline:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!0},placeholder:{type:String,default:"Select Date"},disabled:{type:Boolean,default:!1},class:{type:String,default:""},showPresets:{type:Boolean,default:!0},minDate:{type:Date,default:void 0},maxDate:{type:Date,default:void 0},selectionMode:{type:String,default:"single",validator:a=>["single","multiple","range"].includes(a)},dateFormat:{type:String,default:"dd/mm/yy"}},emits:["update:modelValue","change"],setup(a,{emit:p}){const d=a,i=p,o=r(),v=(t=new Date)=>{const e=D(t);return[{label:"Today",value:e.toDate()},{label:"Tomorrow",value:e.add(1,"day").toDate()},{label:"In 1 week",value:e.add(1,"week").toDate()},{label:"In 1 month",value:e.add(1,"month").toDate()},{label:"In 3 months",value:e.add(3,"month").toDate()},{label:"In 6 months",value:e.add(6,"month").toDate()},{label:"In 1 year",value:e.add(1,"year").toDate()}]},h=k(()=>v()),l=r(d.modelValue);u(()=>d.modelValue,t=>{l.value=t}),u(l,t=>{i("update:modelValue",t)});const y=t=>{var e;o.value&&((e=o.value)!=null&&e.overlayVisible)&&(o.value.overlayVisible=!1),l.value=t.value,i("change",t.value)};return(t,e)=>(n(),c("div",z,[w(m(C),{ref_key:"datepicker",ref:o,modelValue:l.value,"onUpdate:modelValue":e[0]||(e[0]=s=>l.value=s),inline:a.inline,"show-icon":a.showIcon,placeholder:a.placeholder,disabled:a.disabled,class:V(d.class),"min-date":a.minDate,"max-date":a.maxDate,"selection-mode":a.selectionMode,"date-format":a.dateFormat,"pt:panel":"{ class: 'calendar-panel' }","pt:content":"{ class: 'calendar-content' }"},{footer:g(()=>[a.showPresets?(n(),c("div",F,[e[1]||(e[1]=f("div",{class:"date-presets-title"}," Quick Select ",-1)),f("div",N,[(n(!0),c(B,null,I(h.value,s=>(n(),P(m(S),{key:s.label,size:"small",label:s.label,class:"preset-button",rounded:"",onClick:T=>y(s)},null,8,["label","onClick"]))),128))])])):x("",!0)]),_:1},8,["modelValue","inline","show-icon","placeholder","disabled","class","min-date","max-date","selection-mode","date-format"])]))}}),L=E(M,[["__scopeId","data-v-c7c99c92"]]);export{L as E};
