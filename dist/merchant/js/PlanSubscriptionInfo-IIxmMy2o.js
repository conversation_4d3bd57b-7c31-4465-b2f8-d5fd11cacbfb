import{d as T,k as B,a,o,b as t,t as i,u as n,j as y,q as r,y as F,z as E,e as f,s as I}from"./index-BPwFJVSY.js";import{s as A}from"./index-B7pQ2UQX.js";import{F as C}from"./index-Cui9J1ai.js";import{D as L}from"./decimal-LZMqkRTm.js";import{S as N,R as b,b as $,B as c}from"./plan-DCOwcHy2.js";import{f as g}from"./date-D3KeWVvs.js";import{_ as R}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./dayjs.min-Bar1rHDB.js";const U={class:"plan-subscription-info p-6 bg-white rounded-2xl"},V={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},j={class:"mb-6"},w={class:"grid grid-cols-1 gap-4"},G={class:"field"},Y={class:"font-medium"},O={class:"field"},q={class:"font-medium"},W={class:"field"},z={class:"font-medium"},J={class:"field"},H={class:"font-medium"},K={key:0},Q={class:"field"},X={class:"font-medium"},Z={class:"mb-6"},tt={class:"grid grid-cols-1 gap-4"},et={key:0,class:"field"},st={class:"font-medium"},it={key:1},lt={key:0,class:"field"},nt={class:"font-medium"},at={key:0},ot={key:1,class:"field"},dt={class:"font-medium mb-2"},ct={class:"font-medium"},rt={key:0},mt={class:"grid grid-cols-1 gap-4"},ut={class:"field"},yt={class:"font-medium"},ft={class:"mb-6"},bt={class:"grid grid-cols-1 gap-4"},gt={class:"field"},pt={class:"field"},vt={class:"font-medium"},_t={class:"field"},Dt={class:"font-medium"},kt={class:"flex justify-end gap-3 mt-6"},ht=T({name:"PlanSubscriptionInfo",__name:"PlanSubscriptionInfo",props:{planDetails:{type:Object,required:!0},loading:{type:Boolean,default:!1},options:{type:Object,default:()=>({process_type:[],status:[],pricing_model:[],tiered_type:[]})},optionsLoading:{type:Function,default:()=>()=>!1},getProcessTypeLabel:{type:Function,default:e=>e},getStatusLabel:{type:Function,default:e=>e},getPricingModelLabel:{type:Function,default:e=>e},getTieredTypeLabel:{type:Function,default:e=>e}},emits:["edit","back"],setup(e,{emit:D}){const p=D,{t:v}=B(),k=()=>{p("edit")},h=()=>{p("back")},x=l=>l&&{USD:"$",EUR:"€",GBP:"£",CNY:"¥",JPY:"¥",AUD:"A$"}[l]||"A$",_=(l,s="AUD")=>{if(!l||l==="")return"0.00";const u=x(s),m=new L(l).toFixed(2);return`${u}${m}`},P=l=>l?{[c.Daily]:"Daily",[c.Weekly]:"Weekly",[c.Fortnightly]:"Fortnightly",[c.Monthly]:"Monthly",[c.Yearly]:"Yearly",[c.Every3Months]:"Every 3 Months",[c.Every6Months]:"Every 6 Months",[c.Custom]:"Custom"}[l]||l:"",S=l=>l===1?"success":l===2?"warning":"danger";return(l,s)=>{const u=A,m=I;return o(),a("div",U,[t("div",V,[t("div",null,[t("div",j,[s[5]||(s[5]=t("h3",{class:"text-xl font-semibold mb-4"}," Plan Information ",-1)),t("div",w,[t("div",G,[s[0]||(s[0]=t("label",{class:"block text-gray-500 mb-1"},"Plan Name:",-1)),t("div",Y,i(e.planDetails.plan_name),1)]),t("div",O,[s[1]||(s[1]=t("label",{class:"block text-gray-500 mb-1"},"Description:",-1)),t("div",q,i(e.planDetails.description||"No description"),1)]),t("div",W,[s[2]||(s[2]=t("label",{class:"block text-gray-500 mb-1"},"Schedule Type:",-1)),t("div",z,i(e.planDetails.schedule_type===n(N).Recurring?"Recurring":"One-off"),1)]),t("div",J,[s[3]||(s[3]=t("label",{class:"block text-gray-500 mb-1"},"Billing Period:",-1)),t("div",H,[y(i(P(e.planDetails.process_type))+" ",1),e.planDetails.custom_cycle&&e.planDetails.custom_cycle_type?(o(),a("span",K," (Every "+i(e.planDetails.custom_cycle)+" "+i(e.planDetails.custom_cycle_type===1?"days":e.planDetails.custom_cycle_type===2?"weeks":e.planDetails.custom_cycle_type===3?"months":"years")+") ",1)):r("",!0)])]),t("div",Q,[s[4]||(s[4]=t("label",{class:"block text-gray-500 mb-1"},"GST:",-1)),t("div",X,i(e.planDetails.is_inclusive_gst?"Inclusive":"Exclusive"),1)])])]),t("div",Z,[s[9]||(s[9]=t("h3",{class:"text-xl font-semibold mb-4"}," Pricing Information ",-1)),t("div",tt,[e.planDetails.pricing_model?(o(),a("div",et,[s[6]||(s[6]=t("label",{class:"block text-gray-500 mb-1"},"Pricing Model:",-1)),t("div",st,i(e.planDetails.pricing_model===n(b).StandardPricing?"Package Pricing":"Tiered Pricing"),1)])):r("",!0),e.planDetails.prices&&e.planDetails.prices.length>0?(o(),a("div",it,[e.planDetails.pricing_model===n(b).StandardPricing||!e.planDetails.pricing_model?(o(),a("div",lt,[s[7]||(s[7]=t("label",{class:"block text-gray-500 mb-1"},"Price:",-1)),t("div",nt,[y(i(n(C).formatAmount(e.planDetails.prices[0].amount_per_unit))+" ",1),e.planDetails.prices[0].first_unit&&e.planDetails.prices[0].first_unit>1?(o(),a("span",at," per "+i(e.planDetails.prices[0].first_unit)+" units ",1)):r("",!0)])])):e.planDetails.pricing_model===n(b).TieredPricing?(o(),a("div",ot,[s[8]||(s[8]=t("label",{class:"block text-gray-500 mb-1"},"Tiered Pricing:",-1)),t("div",dt," Payment Method: "+i(e.planDetails.tiered_type===n($).Volume?"Volume":"Graduated"),1),(o(!0),a(F,null,E(e.planDetails.prices,(d,M)=>(o(),a("div",{key:M,class:"pl-4 mb-2 border-l-2 border-gray-200"},[t("div",ct,[y(i(d.first_unit)+" - "+i(d.last_unit==="∞"?"Unlimited":d.last_unit)+" units: "+i(_(d.amount_per_unit,d.currency))+" per unit ",1),d.amount_flat_fee&&Number(d.amount_flat_fee)>0?(o(),a("span",rt," + "+i(_(d.amount_flat_fee,d.currency))+" flat fee ",1)):r("",!0)])]))),128))])):r("",!0)])):r("",!0)])]),t("div",null,[s[11]||(s[11]=t("h3",{class:"text-xl font-semibold mb-4"}," Timing ",-1)),t("div",mt,[t("div",ut,[s[10]||(s[10]=t("label",{class:"block text-gray-500 mb-1"},"End Date:",-1)),t("div",yt,i(e.planDetails.is_good_till_cancel?"Good till cancel":n(g)(e.planDetails.end_date)),1)])])])]),t("div",null,[t("div",ft,[s[15]||(s[15]=t("h3",{class:"text-xl font-semibold mb-4"}," Status Information ",-1)),t("div",bt,[t("div",gt,[s[12]||(s[12]=t("label",{class:"block text-gray-500 mb-1"},"Status:",-1)),f(u,{severity:S(e.planDetails.status),value:e.getStatusLabel(e.planDetails.status)},null,8,["severity","value"])]),t("div",pt,[s[13]||(s[13]=t("label",{class:"block text-gray-500 mb-1"},"Created Date:",-1)),t("div",vt,i(n(g)(e.planDetails.created_at)),1)]),t("div",_t,[s[14]||(s[14]=t("label",{class:"block text-gray-500 mb-1"},"Last Updated:",-1)),t("div",Dt,i(n(g)(e.planDetails.updated_at)),1)])])])])]),t("div",kt,[f(m,{type:"button",severity:"secondary",label:n(v)("common.back"),onClick:h},null,8,["label"]),f(m,{type:"button",label:n(v)("common.edit"),onClick:k},null,8,["label"])])])}}}),It=R(ht,[["__scopeId","data-v-bec84988"]]);export{It as default};
