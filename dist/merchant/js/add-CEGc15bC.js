import{u as f,P as l}from"./usePermissions-PkVCxleQ.js";import _ from"./PlanSubscriptionForm-Dsrtpj9s.js";import{u as b}from"./usePlanForm-WYcUl8Fa.js";import{d as g,a as r,u as o,o as i,b as s,e as P}from"./index-BPwFJVSY.js";import{_ as v}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-D7FsVJ-c.js";import"./index-CEksU3zE.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-CCP8zFp2.js";import"./index-DfF1qu_m.js";import"./index-BlFKIn6Y.js";import"./dayjs.min-Bar1rHDB.js";import"./EnhancedDatePicker-DfTmBAld.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./customer-v2cMYA98.js";import"./plan-DCOwcHy2.js";import"./useListRefresh-D-tL0eHu.js";import"./customer-M3EzHCil.js";import"./integrations-C_ojvdY3.js";import"./plan-X86n_BnV.js";import"./dict-CLPQcIyV.js";import"./types-AmnC9OGP.js";const k={key:0,class:"plan-subscription-add"},x={class:"form-wrap bg-white rounded-2xl"},h={key:1,class:"flex justify-center items-center h-64"},y=g({name:"planSubscriptionAdd",__name:"add",setup(E){const{hasPermission:m}=f(),{formData:e,formErrors:n,submitting:p,submitForm:a,options:d,optionsLoading:u}=b("add");return(c,t)=>o(m)(o(l).PLAN_CREATE)?(i(),r("div",k,[s("div",x,[P(_,{"form-data":o(e),"form-errors":o(n),options:o(d),"options-loading":o(u),submitting:o(p),mode:"add",onSubmit:o(a),onCancel:t[0]||(t[0]=S=>c.$router.back())},null,8,["form-data","form-errors","options","options-loading","submitting","onSubmit"])])])):(i(),r("div",h,t[1]||(t[1]=[s("p",{class:"text-gray-500"}," You don't have permission to create plans. ",-1)])))}}),to=v(y,[["__scopeId","data-v-28f95abf"]]);export{to as default};
