import{Z as G,r,Q as v,_ as H,$ as J}from"./index-BPwFJVSY.js";import{g as K,s as w}from"./customer-M3EzHCil.js";import{a as R}from"./usePlanForm-WYcUl8Fa.js";const ee=G("checkout",()=>{const u=r("card"),_=r("AUD"),p=r("form"),f=r(""),l=r(""),b=r(10),N=r({}),y=r({customer_email:"",customer_name:"",logo:"",phone_number:"",theme:"",merchant_name:"",is_payment_method:!1,open_banking_transactions:!1}),C=r({}),t=v({customer_id:"",customer_name:"",customer_email:"",customer_phone:"",return_url:location.href,theme:"",customer_plan:{surcharge_rate:{fee_rate:"2",fee_value:"0"}},customer_bankings:[]}),d=r(null),S=r(!1),h=r(!1),s=v({companyName:"",firstName:"",lastName:"",addressLine1:"",addressLine2:"",city:"",state:"",postcode:"",email:"",bsbNumber:"",accountNumber:"",bankAccountName:""}),o=v({cardNumber:"",expiryDate:"",securityCode:"",nameOnCard:"",email:""}),n=v({country:"",first_name:"",last_name:"",company:"",address_line1:"",address_line2:"",city:"",state:"",postal_code:"",phone:""});function x(e){Object.assign(t,e)}function M(e){Object.assign(s,e)}function E(e){Object.assign(o,e)}function L(e){Object.assign(n,e)}function i(e){p.value=e,e==="success"&&A()}function A(){b.value=10;const e=setInterval(()=>{b.value--,b.value<=0&&clearInterval(e)},1e3);setTimeout(()=>{clearInterval(e),window.location.href=location.origin},1e4)}function m(e){f.value=e}function B(e){u.value=e}function T(e){_.value=e}function F(e){l.value=e,l.value!==e&&k(),O(e)}async function O(e){var I,$,j;const{data:a,code:c,message:z}=await K({customer_id:e});if(c===0){if(Object.assign(y.value,a),y.value.open_banking_transactions?u.value="bankAccount":u.value="card",a.customer_plan&&(t.customer_plan=R(a.customer_plan,t.customer_plan)),t.customer_name||(t.customer_name=a.customer_name),t.customer_email||(t.customer_email=a.customer_email),t.customer_phone||(t.customer_phone=a.phone_number),a.customer_bankings&&Array.isArray(a.customer_bankings)&&a.customer_bankings.length>0){const D=($=(I=a==null?void 0:a.customer_bankings)==null?void 0:I.find(g=>{var P;return g.id===Number((P=a==null?void 0:a.customer_plan_bankings)==null?void 0:P.id)}))==null?void 0:$.id;t.customer_bankings=(j=a.customer_bankings)==null?void 0:j.map(g=>({...g,id:Number(g.id)})),D&&(d.value=Number(D))}V(y.value.theme||"6466f1")}else i("error"),m(z||"Submission failed, please try again")}async function Q(){if(!d.value){window.$toast.add({severity:"error",summary:"Error",detail:"Please select a payment method"});return}try{h.value=!0;const{code:e}=await w({customer_id:l.value,payment_method_id:d.value,customer_email:t.customer_email,customer_name:t.customer_name,phone_number:t.customer_phone,currency:_.value});e===0?(p.value="success",f.value="",A()):(i("error"),m("Submission failed, please try again"))}catch(e){i("error"),m(e instanceof Error?e.message:"Submission failed, please try again")}finally{h.value=!1}}function U(){S.value=!0}function V(e){const a=H(`#${e}`||"#6466f1");Object.keys(a).forEach(c=>{C.value[`--p-primary-${c}`]=a[Number(c)]}),N.value={"--p-primary-color":`#${e}`||"#6466f1","--p-inputtext-border-color":`#${e}`||"#6466f1","--p-textarea-border-color":`#${e}`||"#6466f1","--p-radiobutton-border-color":`#${e}`||"#6466f1","--p-select-border-color":`#${e}`||"#6466f1","--p-inputgroup-addon-border-color":`#${e}`||"#6466f1","--p-button-primary-background":a[500],"--p-button-primary-border-color":a[500],"--p-button-primary-hover-background":a[400],"--p-button-primary-hover-border-color":a[400],"--p-button-primary-active-background":a[600],"--p-button-primary-active-border-color":a[600]},J(a)}function k(){const e=l.value;Object.assign(t,{customer_id:e,customer_name:"",customer_email:"",customer_phone:"",customer_plan:{},customer_bankings:[],theme:""}),Object.assign(s,{companyName:"",firstName:"",lastName:"",addressLine1:"",addressLine2:"",city:"",state:"",postcode:"",email:"",bsbNumber:"",accountNumber:"",bankAccountName:""}),Object.assign(o,{cardNumber:"",expiryDate:"",securityCode:"",nameOnCard:""}),Object.assign(n,{country:"",first_name:"",last_name:"",company:"",address_line1:"",address_line2:"",city:"",state:"",postal_code:"",phone:""}),p.value="form",f.value=""}async function Z(){var e;try{i("loading");const a={customer_id:l.value,customer_name:t.customer_name,customer_email:t.customer_email,phone_number:t.customer_phone,type:u.value==="bankAccount"?2:1,currency:_.value,return_url:t.return_url};u.value==="bankAccount"?a.card={card_number:o.cardNumber,security_code:o.securityCode,name_on_card:o.nameOnCard,expiration_year:o.expiryDate.split("/")[1],expiration_month:o.expiryDate.split("/")[0],email:o.email,company_name:n.company,first_name:n.first_name,last_name:n.last_name,line_1:n.address_line1,line_2:n.address_line2,city:n.city,state:n.state,postcode:n.postal_code,country_iso2:n.country,phone:n.phone}:a.bank={bsb:s.bsbNumber.replace(/-/g,""),account_no:s.accountNumber,account_name:s.bankAccountName,city:s.city,state:s.state,postcode:s.postcode,email:s.email,first_name:s.firstName,last_name:s.lastName,line_1:s.addressLine1,line_2:s.addressLine2,company_name:s.companyName};const c=await w(a);return c.code===0?(e=c.data)!=null&&e.html?{success:!0,message:"",html:c.data.html}:(k(),i("success"),m("Submission failed, please try again"),{success:!0,message:"",html:""}):(i("form"),{success:!1,message:c.message||"Submission failed, please try again",html:""})}catch(a){return i("error"),m(a instanceof Error?a.message:"Submission failed, please try again"),{success:!1,message:a instanceof Error?a.message:"Submission failed, please try again",html:""}}}function q(e){d.value=e}return{config:y,activePaymentMethod:u,selectedCurrency:_,customerInfo:t,bankAccountDetails:s,paymentDetails:o,billingAddress:n,pageStatus:p,pageErrorMessage:f,themeStyles:C,selectedBank:d,isConfirmPaymentMethod:S,isSelectedBankLoading:h,redirectCountdown:b,cssVariables:N,updateConfig:O,setCustomerInfo:x,setBankAccountDetails:M,setPaymentDetails:E,setBillingAddress:L,setPageStatus:i,setPageErrorMessage:m,setActivePaymentMethod:B,setSelectedCurrency:T,setCustomerId:F,resetForm:k,submitInvite:Z,confirmPaymentMethod:Q,cancelPaymentMethod:U,setSelectedBank:q}},{persist:{omit:["pageStatus","customerInfo","pageErrorMessage","selectedBank","isConfirmPaymentMethod","isSelectedBankLoading"]}});export{ee as u};
