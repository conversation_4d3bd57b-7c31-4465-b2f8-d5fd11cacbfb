import{d as P,k as z,J as D,i as j,r as V,l as J,bu as K,Q as O,f as Q,a as W,o as u,b as t,e as r,w as n,u as o,m as _,p as v,q as b,j as p,t as m,v as F,A as Z,s as G,F as H,bJ as X,bK as Y}from"./index-BPwFJVSY.js";import{s as M}from"./index-DUgPT_03.js";import{s as ee}from"./index-Cbodd6BQ.js";import{s as ae}from"./index-DES6SMz0.js";import{s as se}from"./index-BlFKIn6Y.js";import{s as oe}from"./index-CEksU3zE.js";import{t as le,c as te,g as re,e as ie,b as y}from"./index.esm-DOIZz6Kf.js";import{u as ne}from"./useListRefresh-D-tL0eHu.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CCP8zFp2.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DDQWKtix.js";const ce={class:"merchant-create-page"},de={class:"p-4 bg-white rounded-2xl"},ue={class:"field"},pe={for:"user_name",class:"mb-2 block"},fe={class:"field"},_e={for:"email",class:"mb-2 block"},ve={class:"field"},be={for:"password",class:"mb-2 block"},he={class:"field"},we={class:"mb-2 block"},ke={class:"field mb-4"},Ve={class:"flex justify-end mt-6 gap-2"},ye=P({name:"UserCreate",__name:"create",setup(Ue){const{t:a}=z(),U=D(),$=j(),g=V(),h=V(!1),S=J(),{groupList:A}=K(S),{backWithRefresh:C}=ne("usersList",()=>{}),i=O({user_name:"",email:"",password:"",to_fa_check:0,business_ids:[]}),L=V([]),R=async()=>{var e;const f=await X({page:1,page_size:100});L.value=(((e=f.data)==null?void 0:e.data)||[]).map(c=>({name:c.name,id:c.id}))};Q(()=>{R()});const q=le(te({user_name:y().min(3,a("validation.minLength",{min:3})).max(50,a("validation.maxLength",{max:50})).required(),email:y().email(a("validation.emailInvalid")).required(a("validation.emailRequired")),password:y().min(8,a("validation.passwordMinLength")).matches(/[A-Z]/,a("validation.passwordUppercase")).matches(/[a-z]/,a("validation.passwordLowercase")).matches(/\d/,a("validation.passwordNumber")).required(),to_fa_check:ie().min(0,"2FA is required").required(),business_ids:re().optional()})),B=async()=>{var f;h.value=!0;try{const e=await((f=g.value)==null?void 0:f.validate());if(!e.valid){h.value=!1;return}const c={...e.values,mfa_check:e.values.to_fa_check},{code:d}=await Y(c);d===0&&(U.add({severity:"success",summary:a("common.success"),detail:a("merchant.messages.createSuccess","Merchant created successfully")}),C())}catch(e){console.error("Failed to Create a user:",e),U.add({severity:"error",summary:a("common.error"),detail:a("merchant.messages.createFailed","Failed to Create a user")})}finally{h.value=!1}},N=()=>{$.back()};return(f,e)=>{const c=oe,d=se,T=ae,E=ee,I=M,x=G;return u(),W("div",ce,[t("div",de,[r(o(H),{ref_key:"formRef",ref:g,"validation-schema":o(q),class:"merchant-form flex flex-col gap-4",onSubmit:B},{default:n(()=>[r(o(_),{modelValue:i.user_name,"onUpdate:modelValue":e[0]||(e[0]=s=>i.user_name=s),name:"user_name",class:"form-col"},{default:n(({field:s,errorMessage:l})=>[t("div",ue,[t("label",pe,[p(m(o(a)("merchant.form.name","Username"))+" ",1),e[6]||(e[6]=t("span",{class:"text-red-500"},"*",-1))]),r(c,F({id:"user_name"},s,{placeholder:o(a)("merchant.form.name","Username"),class:["w-full",{"p-invalid":l}],"input-props":{autocomplete:"new-password"}}),null,16,["placeholder","class"]),l?(u(),v(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[p(m(l),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),r(o(_),{modelValue:i.email,"onUpdate:modelValue":e[1]||(e[1]=s=>i.email=s),name:"email",class:"form-col"},{default:n(({field:s,errorMessage:l})=>[t("div",fe,[t("label",_e,m(o(a)("merchant.form.email","Email"))+"*",1),r(c,F({id:"email"},s,{placeholder:o(a)("merchant.form.email","Email"),class:["w-full",{"p-invalid":l}],type:"email","input-props":{autocomplete:"new-password"},autocomplete:"new-password"}),null,16,["placeholder","class"]),l?(u(),v(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[p(m(l),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),r(o(_),{modelValue:i.password,"onUpdate:modelValue":e[2]||(e[2]=s=>i.password=s),name:"password",class:"form-col"},{default:n(({field:s,errorMessage:l,handleChange:w})=>[t("div",ve,[t("label",be,m(o(a)("merchant.form.password","Password"))+"*",1),r(T,{id:"password","model-value":s.value,placeholder:o(a)("merchant.form.password","Password"),class:"w-full","toggle-mask":"",root:{autocomplete:"new-password"},"input-props":{autocomplete:"new-password",class:{"p-invalid":l}},"onUpdate:modelValue":w},null,8,["model-value","placeholder","input-props","onUpdate:modelValue"]),l?(u(),v(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[p(m(l),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),r(o(_),{modelValue:i.to_fa_check,"onUpdate:modelValue":e[4]||(e[4]=s=>i.to_fa_check=s),name:"to_fa_check",as:"div",class:"form-col"},{default:n(({errorMessage:s})=>[t("div",he,[t("label",we,m(o(a)("merchant.form.2fa","2FA Authentication")),1),r(E,{modelValue:i.to_fa_check,"onUpdate:modelValue":e[3]||(e[3]=l=>i.to_fa_check=l),options:[{label:"Enabled",value:1},{label:"Disabled",value:0}],"option-label":"label","option-value":"value",class:"w-full",name:"to_fa_check",placeholder:o(a)("merchant.form.select2FA","Select 2FA Option")},null,8,["modelValue","placeholder"])]),s?(u(),v(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[p(m(s),1)]),_:2},1024)):b("",!0)]),_:1},8,["modelValue"]),r(o(_),{modelValue:i.business_ids,"onUpdate:modelValue":e[5]||(e[5]=s=>i.business_ids=s),name:"business_ids",class:"form-col"},{default:n(({field:s,errorMessage:l,handleChange:w})=>[t("div",ke,[e[7]||(e[7]=t("label",{for:"bids",class:"mb-2 block"},"Assign to Business",-1)),r(I,{"model-value":s.value,options:o(A).map(k=>({...k,label:`${k.label} - ${k.value}`})),"option-label":"label","option-value":"value",class:Z(["w-full",{"p-invalid":l}]),placeholder:"Select Assign to Business","onUpdate:modelValue":w},null,8,["model-value","options","class","onUpdate:modelValue"]),l?(u(),v(d,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:n(()=>[p(m(l),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),t("div",Ve,[r(x,{type:"button",label:o(a)("common.cancel"),icon:"pi pi-times",class:"p-button-text",onClick:N},null,8,["label"]),r(x,{type:"submit",label:o(a)("common.save"),icon:"pi pi-check",loading:h.value},null,8,["label","loading"])])]),_:1},8,["validation-schema"])])])}}}),Pe=me(ye,[["__scopeId","data-v-6fdb7ad3"]]);export{Pe as default};
