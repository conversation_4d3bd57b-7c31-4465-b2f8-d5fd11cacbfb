import{d as R,i as D,r as b,Q as F,f as T,a as j,o as m,e as t,w as c,b as e,j as i,u as r,m as _,p as v,q as y,A as z,t as g,v as k,s as U,F as O,bC as P,bD as Q}from"./index-BPwFJVSY.js";import{s as W}from"./index-CEksU3zE.js";import{s as Z}from"./index-BlFKIn6Y.js";import{s as G}from"./index-Cbodd6BQ.js";import{t as H,c as J,b as h}from"./index.esm-DOIZz6Kf.js";import{u as K}from"./useListRefresh-D-tL0eHu.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";const Y={class:"bid-account-create-page bg-white rounded-2xl px-6 p-8"},M={class:"card-body"},ee={class:"grid grid-cols-1 gap-6"},se={class:"field"},ae={class:"field"},te={class:"field"},ne={class:"field"},le={class:"mt-8"},oe={class:"flex justify-end gap-4"},ce=R({name:"userBidAccountCreate",__name:"create",setup(ie){const C=D(),$=b(),x=b(!1),p=b(!1),w=b([]),{backWithRefresh:V}=K("userBidAccountList",()=>{}),S=F({business_id:"",bsb:"",account_no:"",account_name:""}),q=H(J({business_id:h().required("Business is required"),bsb:h().required("BSB is required").matches(/^\d{6}$/,"BSB must be 6 digits"),account_no:h().required("Account number is required").min(4,"Account number must be at least 4 characters").max(20,"Account number must not exceed 20 characters"),account_name:h().required("Account name is required").min(2,"Account name must be at least 2 characters")})),I=async u=>{x.value=!0;try{const{code:a}=await Q(u);a===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Bid account created successfully"}),V())}finally{x.value=!1}},N=()=>{C.back()},E=async()=>{p.value=!0;try{const u=await P({status:1});w.value=u.data.data.map(a=>({label:`${a.business_name} - ${a.business_id}`,value:a.business_id}))}catch(u){window.$toast.add({severity:"error",summary:"Error",detail:u.message||"Failed to fetch business list"})}finally{p.value=!1}};return T(()=>{E()}),(u,a)=>{const L=G,f=Z,B=W,A=U;return m(),j("div",Y,[t(r(O),{ref_key:"formRef",ref:$,class:"max-w-2xl","validation-schema":r(q),"initial-values":S,onSubmit:I},{default:c(()=>[e("div",M,[e("div",ee,[e("div",se,[a[0]||(a[0]=e("label",{for:"business_id",class:"block font-medium text-gray-700 mb-2"},[i(" Business "),e("span",{class:"text-red-500"},"*")],-1)),t(r(_),{id:"business_id",name:"business_id"},{default:c(({field:n,errorMessage:s,handleChange:d})=>[t(L,{modelValue:n.value,"onUpdate:modelValue":l=>n.value=l,class:z([{"p-invalid":s},"w-full"]),options:w.value,"option-label":"label","option-value":"value",placeholder:"Select Business",loading:p.value,disabled:p.value,onValueChange:d},null,8,["modelValue","onUpdate:modelValue","class","options","loading","disabled","onValueChange"]),s?(m(),v(f,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[i(g(s),1)]),_:2},1024)):y("",!0)]),_:1})]),e("div",ae,[a[1]||(a[1]=e("label",{for:"bsb",class:"block font-medium text-gray-700 mb-2"},[i(" BSB "),e("span",{class:"text-red-500"},"*")],-1)),t(r(_),{id:"bsb",name:"bsb"},{default:c(({field:n,errorMessage:s})=>[t(B,k(n,{class:[{"p-invalid":s},"w-full"],placeholder:"Enter 6-digit BSB number (e.g., 123-456)",maxlength:"7",onInput:d=>{const l=d.target;if(l){let o=l.value.replace(/\D/g,"");o.length>3&&(o=`${o.slice(0,3)}-${o.slice(3,6)}`),l.value=o,n.value=o.replace(/\D/g,"")}}}),null,16,["class","onInput"]),s?(m(),v(f,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[i(g(s),1)]),_:2},1024)):y("",!0)]),_:1})]),e("div",te,[a[2]||(a[2]=e("label",{for:"account_no",class:"block font-medium text-gray-700 mb-2"},[i(" Account Number "),e("span",{class:"text-red-500"},"*")],-1)),t(r(_),{id:"account_no",name:"account_no"},{default:c(({field:n,errorMessage:s})=>[t(B,k(n,{class:[{"p-invalid":s},"w-full"],placeholder:"Enter account number",onInput:d=>{const l=d.target;if(l){const o=l.value.replace(/[^a-zA-Z0-9]/g,"");l.value=o,n.value=o}}}),null,16,["class","onInput"]),s?(m(),v(f,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[i(g(s),1)]),_:2},1024)):y("",!0)]),_:1})]),e("div",ne,[a[3]||(a[3]=e("label",{for:"account_name",class:"block font-medium text-gray-700 mb-2"},[i(" Account Name "),e("span",{class:"text-red-500"},"*")],-1)),t(r(_),{id:"account_name",name:"account_name"},{default:c(({field:n,errorMessage:s})=>[t(B,k(n,{class:[{"p-invalid":s},"w-full"],placeholder:"Enter account name"}),null,16,["class"]),s?(m(),v(f,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[i(g(s),1)]),_:2},1024)):y("",!0)]),_:1})])])]),e("div",le,[e("div",oe,[t(A,{type:"button",label:"Cancel",severity:"secondary",onClick:N}),t(A,{type:"submit",label:"Create",loading:x.value,severity:"warn"},null,8,["loading"])])])]),_:1},8,["validation-schema","initial-values"])])}}}),he=X(ce,[["__scopeId","data-v-ea9c679f"]]);export{he as default};
