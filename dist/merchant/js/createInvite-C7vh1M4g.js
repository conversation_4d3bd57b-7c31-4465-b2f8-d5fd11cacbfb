import{D as ae,E as D,a as $,o as d,I as b,q as k,v as f,a1 as x,a2 as H,a3 as C,a4 as E,a5 as P,H as Y,p as _,w as v,N as R,a6 as L,b as p,O as F,T as se,y as U,t as z,A as re,l as ie,i as pe,r as y,d as ne,B as oe,e as m,j as N,u as h,a7 as M,s as le}from"./index-BPwFJVSY.js";import{u as de,P as q}from"./usePermissions-PkVCxleQ.js";import ce from"./createInviteConfirm-Ces5CSgz.js";import ue from"./createInviteSubscriptionForm-2tiwAeDK.js";import{u as me}from"./useListRefresh-D-tL0eHu.js";import{a as ve,b as fe,c as he}from"./customer-M3EzHCil.js";import{c as be}from"./plan-X86n_BnV.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./edit-DVqRIdR4.js";import"./index-DMmJJ4dH.js";import"./index-B7pQ2UQX.js";import"./index-BAFB6y2b.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-DgYXzrxo.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./color-DqhIgFrh.js";import"./customerUpload-CJJNLUYL.js";import"./downloadCenter-xnP8j9T6.js";import"./index-BlFKIn6Y.js";import"./index-SkqdGoZA.js";import"./emailPreview.vue_vue_type_script_setup_true_lang-CHlRa3Od.js";import"./logo-CiIwT2Ho.js";import"./index-DlkyKXFh.js";import"./index-DgBXOSrM.js";import"./index-AtQkrpJ9.js";import"./index-Cbodd6BQ.js";import"./index-wTWY-tN1.js";import"./index-kWy1s9tq.js";import"./index.esm-DOIZz6Kf.js";import"./dayjs.min-Bar1rHDB.js";import"./plan-DCOwcHy2.js";import"./PlanSubscriptionForm-Dsrtpj9s.js";import"./index-D7FsVJ-c.js";import"./index-CCP8zFp2.js";import"./index-DfF1qu_m.js";import"./EnhancedDatePicker-DfTmBAld.js";import"./customer-v2cMYA98.js";import"./usePlanForm-WYcUl8Fa.js";import"./integrations-C_ojvdY3.js";import"./dict-CLPQcIyV.js";import"./types-AmnC9OGP.js";import"./subscriptionFormPreview-CmaF54bP.js";import"./decimal-LZMqkRTm.js";import"./user-D3NCqkKe.js";var ge=ae`
    .p-steplist {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0;
        padding: 0;
        list-style-type: none;
        overflow-x: auto;
    }

    .p-step {
        position: relative;
        display: flex;
        flex: 1 1 auto;
        align-items: center;
        gap: dt('stepper.step.gap');
        padding: dt('stepper.step.padding');
    }

    .p-step:last-of-type {
        flex: initial;
    }

    .p-step-header {
        border: 0 none;
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        cursor: pointer;
        transition:
            background dt('stepper.transition.duration'),
            color dt('stepper.transition.duration'),
            border-color dt('stepper.transition.duration'),
            outline-color dt('stepper.transition.duration'),
            box-shadow dt('stepper.transition.duration');
        border-radius: dt('stepper.step.header.border.radius');
        outline-color: transparent;
        background: transparent;
        padding: dt('stepper.step.header.padding');
        gap: dt('stepper.step.header.gap');
    }

    .p-step-header:focus-visible {
        box-shadow: dt('stepper.step.header.focus.ring.shadow');
        outline: dt('stepper.step.header.focus.ring.width') dt('stepper.step.header.focus.ring.style') dt('stepper.step.header.focus.ring.color');
        outline-offset: dt('stepper.step.header.focus.ring.offset');
    }

    .p-stepper.p-stepper-readonly .p-step {
        cursor: auto;
    }

    .p-step-title {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        color: dt('stepper.step.title.color');
        font-weight: dt('stepper.step.title.font.weight');
        transition:
            background dt('stepper.transition.duration'),
            color dt('stepper.transition.duration'),
            border-color dt('stepper.transition.duration'),
            box-shadow dt('stepper.transition.duration'),
            outline-color dt('stepper.transition.duration');
    }

    .p-step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        color: dt('stepper.step.number.color');
        border: 2px solid dt('stepper.step.number.border.color');
        background: dt('stepper.step.number.background');
        min-width: dt('stepper.step.number.size');
        height: dt('stepper.step.number.size');
        line-height: dt('stepper.step.number.size');
        font-size: dt('stepper.step.number.font.size');
        z-index: 1;
        border-radius: dt('stepper.step.number.border.radius');
        position: relative;
        font-weight: dt('stepper.step.number.font.weight');
    }

    .p-step-number::after {
        content: ' ';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: dt('stepper.step.number.border.radius');
        box-shadow: dt('stepper.step.number.shadow');
    }

    .p-step-active .p-step-header {
        cursor: default;
    }

    .p-step-active .p-step-number {
        background: dt('stepper.step.number.active.background');
        border-color: dt('stepper.step.number.active.border.color');
        color: dt('stepper.step.number.active.color');
    }

    .p-step-active .p-step-title {
        color: dt('stepper.step.title.active.color');
    }

    .p-step:not(.p-disabled):focus-visible {
        outline: dt('focus.ring.width') dt('focus.ring.style') dt('focus.ring.color');
        outline-offset: dt('focus.ring.offset');
    }

    .p-step:has(~ .p-step-active) .p-stepper-separator {
        background: dt('stepper.separator.active.background');
    }

    .p-stepper-separator {
        flex: 1 1 0;
        background: dt('stepper.separator.background');
        width: 100%;
        height: dt('stepper.separator.size');
        transition:
            background dt('stepper.transition.duration'),
            color dt('stepper.transition.duration'),
            border-color dt('stepper.transition.duration'),
            box-shadow dt('stepper.transition.duration'),
            outline-color dt('stepper.transition.duration');
    }

    .p-steppanels {
        padding: dt('stepper.steppanels.padding');
    }

    .p-steppanel {
        background: dt('stepper.steppanel.background');
        color: dt('stepper.steppanel.color');
    }

    .p-stepper:has(.p-stepitem) {
        display: flex;
        flex-direction: column;
    }

    .p-stepitem {
        display: flex;
        flex-direction: column;
        flex: initial;
    }

    .p-stepitem.p-stepitem-active {
        flex: 1 1 auto;
    }

    .p-stepitem .p-step {
        flex: initial;
    }

    .p-stepitem .p-steppanel-content {
        width: 100%;
        padding: dt('stepper.steppanel.padding');
        margin-inline-start: 1rem;
    }

    .p-stepitem .p-steppanel {
        display: flex;
        flex: 1 1 auto;
    }

    .p-stepitem .p-stepper-separator {
        flex: 0 0 auto;
        width: dt('stepper.separator.size');
        height: auto;
        margin: dt('stepper.separator.margin');
        position: relative;
        left: calc(-1 * dt('stepper.separator.size'));
    }

    .p-stepitem .p-stepper-separator:dir(rtl) {
        left: calc(-9 * dt('stepper.separator.size'));
    }

    .p-stepitem:has(~ .p-stepitem-active) .p-stepper-separator {
        background: dt('stepper.separator.active.background');
    }

    .p-stepitem:last-of-type .p-steppanel {
        padding-inline-start: dt('stepper.step.number.size');
    }
`,ye={root:function(t){var r=t.props;return["p-stepper p-component",{"p-readonly":r.linear}]},separator:"p-stepper-separator"},$e=D.extend({name:"stepper",style:ge,classes:ye}),_e={name:"BaseStepper",extends:x,props:{value:{type:[String,Number],default:void 0},linear:{type:Boolean,default:!1}},style:$e,provide:function(){return{$pcStepper:this,$parentInstance:this}}},G={name:"Stepper",extends:_e,inheritAttrs:!1,emits:["update:value"],data:function(){return{d_value:this.value}},watch:{value:function(t){this.d_value=t}},methods:{updateValue:function(t){this.d_value!==t&&(this.d_value=t,this.$emit("update:value",t))},isStepActive:function(t){return this.d_value===t},isStepDisabled:function(){return this.linear}}};function xe(e,t,r,n,o,a){return d(),$("div",f({class:e.cx("root"),role:"tablist"},e.ptmi("root")),[e.$slots.start?b(e.$slots,"start",{key:0}):k("",!0),b(e.$slots,"default"),e.$slots.end?b(e.$slots,"end",{key:1}):k("",!0)],16)}G.render=xe;var we={root:"p-steppanels"},Ce=D.extend({name:"steppanels",classes:we}),ke={name:"BaseStepPanels",extends:x,style:Ce,provide:function(){return{$pcStepPanels:this,$parentInstance:this}}},J={name:"StepPanels",extends:ke,inheritAttrs:!1};function Ie(e,t,r,n,o,a){return d(),$("div",f({class:e.cx("root")},e.ptmi("root")),[b(e.$slots,"default")],16)}J.render=Ie;var Ve={root:function(t){var r=t.instance;return["p-steppanel",{"p-steppanel-active":r.isVertical&&r.active}]},content:"p-steppanel-content"},Pe=D.extend({name:"steppanel",classes:Ve}),K={name:"StepperSeparator",hostName:"Stepper",extends:x,inject:{$pcStepper:{default:null}}};function De(e,t,r,n,o,a){return d(),$("span",f({class:e.cx("separator")},e.ptmo(a.$pcStepper.pt,"separator")),null,16)}K.render=De;var Ae={name:"BaseStepPanel",extends:x,props:{value:{type:[String,Number],default:void 0},asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"DIV"}},style:Pe,provide:function(){return{$pcStepPanel:this,$parentInstance:this}}},Q={name:"StepPanel",extends:Ae,inheritAttrs:!1,inject:{$pcStepper:{default:null},$pcStepItem:{default:null},$pcStepList:{default:null}},data:function(){return{isSeparatorVisible:!1}},mounted:function(){if(this.$el){var t,r,n=C(this.$pcStepper.$el,'[data-pc-name="step"]'),o=E(this.isVertical?(t=this.$pcStepItem)===null||t===void 0?void 0:t.$el:(r=this.$pcStepList)===null||r===void 0?void 0:r.$el,'[data-pc-name="step"]'),a=P(o,n);this.isSeparatorVisible=this.isVertical&&a!==n.length-1}},methods:{getPTOptions:function(t){var r=t==="root"?this.ptmi:this.ptm;return r(t,{context:{active:this.active}})},updateValue:function(t){this.$pcStepper.updateValue(t)}},computed:{active:function(){var t,r,n=this.$pcStepItem?(t=this.$pcStepItem)===null||t===void 0?void 0:t.value:this.value;return n===((r=this.$pcStepper)===null||r===void 0?void 0:r.d_value)},isVertical:function(){return!!this.$pcStepItem},activeValue:function(){var t;return this.isVertical?(t=this.$pcStepItem)===null||t===void 0?void 0:t.value:this.value},id:function(){var t;return"".concat((t=this.$pcStepper)===null||t===void 0?void 0:t.$id,"_steppanel_").concat(this.activeValue)},ariaControls:function(){var t;return"".concat((t=this.$pcStepper)===null||t===void 0?void 0:t.$id,"_step_").concat(this.activeValue)},a11yAttrs:function(){return{id:this.id,role:"tabpanel","aria-controls":this.ariaControls,"data-pc-name":"steppanel","data-p-active":this.active}},dataP:function(){return H({vertical:this.$pcStepItem!=null})}},components:{StepperSeparator:K}},Be=["data-p"];function Oe(e,t,r,n,o,a){var S=Y("StepperSeparator");return a.isVertical?(d(),$(U,{key:0},[e.asChild?b(e.$slots,"default",{key:1,active:a.active,a11yAttrs:a.a11yAttrs,activateCallback:function(i){return a.updateValue(i)}}):(d(),_(se,f({key:0,name:"p-toggleable-content"},e.ptm("transition")),{default:v(function(){return[R((d(),_(L(e.as),f({id:a.id,class:e.cx("root"),role:"tabpanel","aria-controls":a.ariaControls,"data-p":a.dataP},a.getPTOptions("root")),{default:v(function(){return[o.isSeparatorVisible?(d(),_(S,{key:0,"data-p":a.dataP},null,8,["data-p"])):k("",!0),p("div",f({class:e.cx("content"),"data-p":a.dataP},a.getPTOptions("content")),[b(e.$slots,"default",{active:a.active,activateCallback:function(i){return a.updateValue(i)}})],16,Be)]}),_:3},16,["id","class","aria-controls","data-p"])),[[F,a.active]])]}),_:3},16))],64)):(d(),$(U,{key:1},[e.asChild?e.asChild&&a.active?b(e.$slots,"default",{key:1,active:a.active,a11yAttrs:a.a11yAttrs,activateCallback:function(i){return a.updateValue(i)}}):k("",!0):R((d(),_(L(e.as),f({key:0,id:a.id,class:e.cx("root"),role:"tabpanel","aria-controls":a.ariaControls},a.getPTOptions("root")),{default:v(function(){return[b(e.$slots,"default",{active:a.active,activateCallback:function(i){return a.updateValue(i)}})]}),_:3},16,["id","class","aria-controls"])),[[F,a.active]])],64))}Q.render=Oe;var Te={root:"p-steplist"},je=D.extend({name:"steplist",classes:Te}),Ne={name:"BaseStepList",extends:x,style:je,provide:function(){return{$pcStepList:this,$parentInstance:this}}},W={name:"StepList",extends:Ne,inheritAttrs:!1};function Ee(e,t,r,n,o,a){return d(),$("div",f({class:e.cx("root")},e.ptmi("root")),[b(e.$slots,"default")],16)}W.render=Ee;var Le={root:function(t){var r=t.instance;return["p-step",{"p-step-active":r.active,"p-disabled":r.isStepDisabled}]},header:"p-step-header",number:"p-step-number",title:"p-step-title"},ze=D.extend({name:"step",classes:Le}),X={name:"StepperSeparator",hostName:"Stepper",extends:x,inject:{$pcStepper:{default:null}}};function Re(e,t,r,n,o,a){return d(),$("span",f({class:e.cx("separator")},e.ptmo(a.$pcStepper.pt,"separator")),null,16)}X.render=Re;var Fe={name:"BaseStep",extends:x,props:{value:{type:[String,Number],default:void 0},disabled:{type:Boolean,default:!1},asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"DIV"}},style:ze,provide:function(){return{$pcStep:this,$parentInstance:this}}},Z={name:"Step",extends:Fe,inheritAttrs:!1,inject:{$pcStepper:{default:null},$pcStepList:{default:null},$pcStepItem:{default:null}},data:function(){return{isSeparatorVisible:!1,isCompleted:!1}},mounted:function(){if(this.$el&&this.$pcStepList){var t=P(this.$el,C(this.$pcStepper.$el,'[data-pc-name="step"]')),r=P(E(this.$pcStepper.$el,'[data-pc-name="step"][data-p-active="true"]'),C(this.$pcStepper.$el,'[data-pc-name="step"]')),n=C(this.$pcStepper.$el,'[data-pc-name="step"]').length;this.isSeparatorVisible=t!==n-1,this.isCompleted=t<r}},updated:function(){var t=P(this.$el,C(this.$pcStepper.$el,'[data-pc-name="step"]')),r=P(E(this.$pcStepper.$el,'[data-pc-name="step"][data-p-active="true"]'),C(this.$pcStepper.$el,'[data-pc-name="step"]'));this.isCompleted=t<r},methods:{getPTOptions:function(t){var r=t==="root"?this.ptmi:this.ptm;return r(t,{context:{active:this.active,disabled:this.isStepDisabled}})},onStepClick:function(){this.$pcStepper.updateValue(this.activeValue)}},computed:{active:function(){return this.$pcStepper.isStepActive(this.activeValue)},activeValue:function(){var t;return this.$pcStepItem?(t=this.$pcStepItem)===null||t===void 0?void 0:t.value:this.value},isStepDisabled:function(){return!this.active&&(this.$pcStepper.isStepDisabled()||this.disabled)},id:function(){var t;return"".concat((t=this.$pcStepper)===null||t===void 0?void 0:t.$id,"_step_").concat(this.activeValue)},ariaControls:function(){var t;return"".concat((t=this.$pcStepper)===null||t===void 0?void 0:t.$id,"_steppanel_").concat(this.activeValue)},a11yAttrs:function(){return{root:{role:"presentation","aria-current":this.active?"step":void 0,"data-pc-name":"step","data-pc-section":"root","data-p-disabled":this.isStepDisabled,"data-p-active":this.active},header:{id:this.id,role:"tab",taindex:this.disabled?-1:void 0,"aria-controls":this.ariaControls,"data-pc-section":"header",disabled:this.isStepDisabled,onClick:this.onStepClick}}},dataP:function(){return H({disabled:this.isStepDisabled,readonly:this.$pcStepper.linear,active:this.active,completed:this.isCompleted,vertical:this.$pcStepItem!=null})}},components:{StepperSeparator:X}},Ue=["id","tabindex","aria-controls","disabled","data-p"],Me=["data-p"],qe=["data-p"];function He(e,t,r,n,o,a){var S=Y("StepperSeparator");return e.asChild?b(e.$slots,"default",{key:1,class:re(e.cx("root")),active:a.active,value:e.value,a11yAttrs:a.a11yAttrs,activateCallback:a.onStepClick}):(d(),_(L(e.as),f({key:0,class:e.cx("root"),"aria-current":a.active?"step":void 0,role:"presentation","data-p-active":a.active,"data-p-disabled":a.isStepDisabled,"data-p":a.dataP},a.getPTOptions("root")),{default:v(function(){return[p("button",f({id:a.id,class:e.cx("header"),role:"tab",type:"button",tabindex:a.isStepDisabled?-1:void 0,"aria-controls":a.ariaControls,disabled:a.isStepDisabled,onClick:t[0]||(t[0]=function(){return a.onStepClick&&a.onStepClick.apply(a,arguments)}),"data-p":a.dataP},a.getPTOptions("header")),[p("span",f({class:e.cx("number"),"data-p":a.dataP},a.getPTOptions("number")),z(a.activeValue),17,Me),p("span",f({class:e.cx("title"),"data-p":a.dataP},a.getPTOptions("title")),[b(e.$slots,"default")],16,qe)],16,Ue),o.isSeparatorVisible?(d(),_(S,{key:0,"data-p":a.dataP},null,8,["data-p"])):k("",!0)]}),_:3},16,["class","aria-current","data-p-active","data-p-disabled","data-p"]))}Z.render=He;function Ye(e){const{activeBid:t}=ie(),r=pe(),{withRefresh:n}=me("customersList",()=>{}),o=y(!1),a=y(!1),S=y("1"),c=y(null),i=y({customer_name:"",email_primary:"",phone_mobile:"",plan:[],theme:"fe4c1c",logo:[],units:1,surcharge_rate:{fee_rate:"",fee_value:""},gst:{fee_rate:"",fee_value:""}}),B=()=>i.value.logo&&i.value.logo.length>0?i.value.logo[0].url:"",A=async u=>{const s=Object.keys(u).length===0;try{if(e==="create"){a.value=!0;const{code:l,data:g}=await he({customer_name:i.value.customer_name,email_primary:i.value.email_primary,phone_mobile:i.value.phone_mobile,plan:s?null:{logo:i.value.logo,theme:i.value.theme,...u,units:i.value.units}},{headers:{"Business-Id":t}});if(l===0){if(window.$toast.add({severity:"success",summary:"Success",detail:"Customer created successfully"}),s){n(),r.back();return}c.value=g.customer_id,S.value="2",n()}else a.value=!1}else if(e==="invite"){a.value=!0;const{code:l}=await be({...u,customer_id:c.value,units:i.value.units},{headers:{"Business-Id":t}});l===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Subscription created successfully"}),S.value="2",n())}}catch(l){console.error(l)}finally{a.value=!1}},w=async()=>{if(c.value){a.value=!0;try{const{code:u}=await fe({customer_id:c.value,logo:B(),theme:i.value.theme});u===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Email sent successfully"}),S.value="3")}finally{a.value=!1}}};function O(u){c.value=u}async function T(){var l;const{code:u,data:s}=await ve();if(u===0){i.value.logo=[{url:s.logo}],i.value.theme=s.theme;const g=(l=s.fee_config)==null?void 0:l.find(I=>I.business_id===t);i.value.surcharge_rate=g==null?void 0:g.surcharge_rate,i.value.gst=g==null?void 0:g.gst}}return T(),{loading:o,submitting:a,step:S,state:i,handleSubmit:A,setCustomerId:O,customerId:c,sendEmailInvite:w}}const Ge={class:"invite-customer flex justify-between"},Je={class:"py-0 px-2"},Ke={class:"py-8 px-6"},Qe={class:"invite-confirm-button flex pt-6 justify-end gap-2"},We={class:"py-8 px-6 flex flex-col items-center justify-center"},Xe={class:"success-details bg-gray-50 rounded-lg p-6 mb-8 w-full max-w-md"},Ze={class:"flex justify-between py-2 border-b border-gray-200"},et={class:"text-gray-800"},tt={class:"flex justify-between py-2"},at={class:"text-gray-800"},st={class:"flex flex-wrap gap-4 justify-center"},rt=ne({__name:"createInvite",setup(e){const{hasPermission:t}=de(),r=oe(),n=y(r.query.customer_id||""),{state:o,step:a,handleSubmit:S,submitting:c,setCustomerId:i,sendEmailInvite:B}=Ye(n.value?"invite":"create"),A=y(!0);n.value&&i(n.value);const w=y(),O=async()=>{var s;if(!w.value)return;if(await w.value.validate()){const l=await((s=w.value)==null?void 0:s.getFormData());Object.keys(l).length===0&&(A.value=!1),S(l)}},T=async()=>{t(q.CUSTOMER_SEND_INVITE_MAIL)&&await B()};return(u,s)=>{const l=Z,g=W,I=Q,j=le,ee=J,te=G;return d(),$("div",Ge,[m(te,{value:h(a),"onUpdate:value":s[3]||(s[3]=V=>M(a)?a.value=V:null),class:"w-full",linear:""},{default:v(()=>[m(g,{class:"!mb-6"},{default:v(()=>[m(l,{value:"1"},{default:v(()=>s[4]||(s[4]=[N(" Create a subscription ")])),_:1,__:[4]}),m(l,{value:"2"},{default:v(()=>s[5]||(s[5]=[N(" Confirm the form ")])),_:1,__:[5]}),m(l,{value:"3"},{default:v(()=>s[6]||(s[6]=[N(" Sent ")])),_:1,__:[6]})]),_:1}),m(ee,{class:"!p-0 !rounded-lg"},{default:v(()=>[m(I,{class:"!rounded-lg",value:"1"},{default:v(()=>[p("div",Je,[m(ue,{ref_key:"subscriptionFormRef",ref:w,"form-data-state":h(o),"surcharge-rate":h(o).surcharge_rate,gst:h(o).gst,"customer-id":n.value,"is-submit-loading":h(c),onNext:O},null,8,["form-data-state","surcharge-rate","gst","customer-id","is-submit-loading"])])]),_:1}),m(I,{class:"!p-0 !rounded-lg bg-white",value:"2"},{default:v(()=>[p("div",Ke,[m(ce,{"form-data":h(o),"onUpdate:formData":s[0]||(s[0]=V=>M(o)?o.value=V:null)},null,8,["form-data"]),p("div",Qe,[m(j,{label:"Back",loading:h(c),icon:"pi pi-arrow-left",class:"p-button-outlined",onClick:s[1]||(s[1]=V=>u.$router.back())},null,8,["loading"]),A.value&&h(t)(h(q).CUSTOMER_SEND_INVITE_MAIL)?(d(),_(j,{key:0,label:"Send Email",severity:"warn",loading:h(c),icon:"pi pi-arrow-right",onClick:T},null,8,["loading"])):k("",!0)])])]),_:1}),m(I,{value:"3"},{default:v(()=>[p("div",We,[s[10]||(s[10]=p("div",{class:"success-icon mb-6"},[p("i",{class:"pi pi-check-circle text-green-500",style:{"font-size":"5rem"}})],-1)),s[11]||(s[11]=p("h2",{class:"text-2xl font-bold text-gray-800 mb-4 text-center"}," Invitation Sent Successfully! ",-1)),s[12]||(s[12]=p("p",{class:"text-gray-600 mb-8 text-center max-w-lg"}," Your customer will receive the invitation email shortly. Once they confirm authorization, you will be notified and can begin processing payments. ",-1)),p("div",Xe,[s[9]||(s[9]=p("div",{class:"flex justify-between py-2 border-b border-gray-200"},[p("span",{class:"text-gray-600"},"Email Status:"),p("span",{class:"text-green-600 font-medium"},"Sent")],-1)),p("div",Ze,[s[7]||(s[7]=p("span",{class:"text-gray-600"},"Sent Time:",-1)),p("span",et,z(new Date().toLocaleString()),1)]),p("div",tt,[s[8]||(s[8]=p("span",{class:"text-gray-600"},"Recipient:",-1)),p("span",at,z(h(o).email_primary),1)])]),p("div",st,[m(j,{label:"View Customer List",onClick:s[2]||(s[2]=V=>u.$router.push("/customers"))})])])]),_:1})]),_:1})]),_:1},8,["value"])])}}}),Wt=Se(rt,[["__scopeId","data-v-a075905f"]]);export{Wt as default};
