import{s as ee}from"./index-DlkyKXFh.js";import{s as te}from"./index-DgBXOSrM.js";import{s as ae}from"./index-AtQkrpJ9.js";import{s as le}from"./index-Cbodd6BQ.js";import{d as oe,k as ie,Q as ne,r as x,B as se,c as re,a as h,o as m,e as o,b as d,u as l,F as me,w as r,q as f,p as b,y as ue,m as y,v as w,j as S,t as _,s as de,A as F,T as pe}from"./index-BPwFJVSY.js";import{s as ce}from"./index-BlFKIn6Y.js";import{s as fe}from"./index-CEksU3zE.js";import{t as be,c as ve,b as D,d as ye,e as _e,f as ge}from"./index.esm-DOIZz6Kf.js";import{d as q}from"./dayjs.min-Bar1rHDB.js";import{S as B,U as Se}from"./plan-DCOwcHy2.js";import{g as xe}from"./plan-X86n_BnV.js";import De from"./PlanSubscriptionForm-Dsrtpj9s.js";import{u as Ve}from"./usePlanForm-WYcUl8Fa.js";import{S as he}from"./subscriptionFormPreview-CmaF54bP.js";import{_ as we}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-BjQt0t51.js";import"./index-wTWY-tN1.js";import"./index-kWy1s9tq.js";import"./index-D7FsVJ-c.js";import"./index-CCP8zFp2.js";import"./index-DfF1qu_m.js";import"./EnhancedDatePicker-DfTmBAld.js";import"./customer-v2cMYA98.js";import"./useListRefresh-D-tL0eHu.js";import"./usePermissions-PkVCxleQ.js";import"./customer-M3EzHCil.js";import"./integrations-C_ojvdY3.js";import"./dict-CLPQcIyV.js";import"./types-AmnC9OGP.js";import"./decimal-LZMqkRTm.js";import"./user-D3NCqkKe.js";const Pe={class:"flex gap-x-12 form-wrapper flex-col lg:flex-row"},ke={class:"flex flex-col gap-4 lg:flex-row"},Ue={class:"flex justify-between items-center mb-2"},$e={class:"create-invite-base-form-label"},Ie={key:0,class:"flex justify-center items-center h-64"},Fe={class:"flex-1"},qe={class:"flex justify-end mt-4"},Be=oe({__name:"createInviteSubscriptionForm",props:{formDataState:{type:Object,required:!0},customerId:{type:String},surchargeRate:{type:Object,default(){return{fee_rate:"",fee_value:""}}},gst:{type:Object,default(){return{fee_rate:"",fee_value:""}}},isSubmitLoading:{type:Boolean,default:!1}},emits:["next"],setup(n,{expose:C,emit:R}){var $;const c=n,T=R,{t:N}=ie(),s=ne({plan_id:"",planStartDate:new Date(Date.now()+24*60*60*1e3)}),P=x(),j=x(),L=se(),k=x([]),V=x({}),O=re(()=>!s.plan_id||!V.value[s.plan_id]?"":V.value[s.plan_id].plan_name||""),{formData:g,formErrors:E,submitting:Y,submitForm:U,validateForm:z,options:A,optionsLoading:Q,setPlanId:X,loading:G}=Ve("view"),H=be(ve({planStartDate:ge(),plan_id:D().when([],{is:()=>c.customerId,then:a=>a.required("Subscription Plan Template is required"),otherwise:a=>a.optional()}),units:_e().optional().test("units-validation","Unit quantity must be greater than 0",a=>!(g.schedule_type===B.UnitBased&&g.unitBasedModel===Se.TieredPricing&&(!a||a<=0))),name:D().optional(),email_primary:ye().when([],{is:()=>!c.customerId,then:a=>a.concat(D().min(1,N("customersPage.invite.validation.emailRequired")||"Email is required").email("Invalid email format")),otherwise:a=>a.optional()}),phone_mobile:D().optional().test("phone-validation","Phone number must be 6-12 digits",a=>!a||a.trim()===""?!0:/^\d{6,12}$/.test(a))})),J=a=>{X(a)};return Promise.all([xe({page_size:999,status:1},{headers:{"Business-Id":($=L.query)==null?void 0:$.business_id}}).then(a=>{var e;k.value=(e=a.data.data)==null?void 0:e.map(u=>{const p=u.plan_id||"";return V.value[p]=u,{label:u.plan_name||"",value:p}})})]),C({validate:async()=>{var u;const{valid:a,errors:e}=await((u=P.value)==null?void 0:u.validate());return console.log("errors",e),!c.customerId&&!s.plan_id&&a?!0:a?await z():!1},getFormData:async()=>!c.customerId&&!s.plan_id?{}:{...await U(),start_date:q(s.planStartDate).isValid()?q(s.planStartDate).format("YYYY-MM-DD"):""}}),(a,e)=>{const u=fe,p=ce,I=de,K=le,W=ae,Z=te,M=ee;return m(),h("div",Pe,[o(l(me),{ref_key:"formRef",ref:P,"validation-schema":l(H),"initial-values":{},class:"create-invite-base-form space-y-4 min-h-150 flex-1"},{default:r(()=>[n.customerId?f("",!0):(m(),h(ue,{key:0},[d("div",ke,[o(l(y),{modelValue:n.formDataState.customer_name,"onUpdate:modelValue":e[0]||(e[0]=t=>n.formDataState.customer_name=t),name:"customer_name",as:"div",class:"flex flex-col gap-2 w-full lg:w-1/2"},{default:r(({field:t})=>[e[9]||(e[9]=d("label",{class:"create-invite-base-form-label mb-2"}," Customer Name ",-1)),o(u,w(t,{"aria-describedby":"name-help",placeholder:"Please enter customer name",maxlength:"100"}),null,16)]),_:1,__:[9]},8,["modelValue"]),o(l(y),{modelValue:n.formDataState.email_primary,"onUpdate:modelValue":e[1]||(e[1]=t=>n.formDataState.email_primary=t),name:"email_primary",as:"div",class:"flex flex-col gap-2 w-full lg:w-1/2"},{default:r(({field:t,errorMessage:i})=>[e[10]||(e[10]=d("label",{class:"create-invite-base-form-label mb-2"}," Customer Email (Required) ",-1)),o(u,w(t,{"aria-describedby":"name-help",class:{"p-invalid":i},placeholder:"Please enter customer email"}),null,16,["class"]),i?(m(),b(p,{key:0,severity:"error",variant:"simple"},{default:r(()=>[S(_(i),1)]),_:2},1024)):f("",!0)]),_:1,__:[10]},8,["modelValue"])]),o(l(y),{modelValue:n.formDataState.phone_mobile,"onUpdate:modelValue":e[2]||(e[2]=t=>n.formDataState.phone_mobile=t),name:"phone_mobile",as:"div",class:"flex flex-col gap-2"},{default:r(({field:t,errorMessage:i})=>[e[11]||(e[11]=d("label",{class:"create-invite-base-form-label mb-2"}," Phone Number ",-1)),o(u,w(t,{"aria-describedby":"name-help",class:{"p-invalid":i},placeholder:"Please enter phone number"}),null,16,["class"]),i?(m(),b(p,{key:0,severity:"error",variant:"simple"},{default:r(()=>[S(_(i),1)]),_:2},1024)):f("",!0)]),_:1,__:[11]},8,["modelValue"])],64)),o(l(y),{name:"plan_id",as:"div",class:"flex flex-col gap-2"},{default:r(({errorMessage:t,handleChange:i})=>[d("div",Ue,[d("label",$e," Subscription Plan Template ( "+_(c.customerId?"Optional":"Required")+") ",1),o(I,{severity:"secondary",icon:"pi pi-plus",onClick:e[3]||(e[3]=v=>a.$router.push({name:"planSubscriptionAdd"}))})]),o(K,{modelValue:s.plan_id,"onUpdate:modelValue":[e[4]||(e[4]=v=>s.plan_id=v),v=>{J(v),i(v)}],options:k.value,"option-label":"label","option-value":"value",class:F({"p-invalid":t}),placeholder:"Please select a plan",checkmark:"",filter:"","highlight-on-select":!1},null,8,["modelValue","options","class","onUpdate:modelValue"]),t?(m(),b(p,{key:0,severity:"error",variant:"simple"},{default:r(()=>[S(_(t),1)]),_:2},1024)):f("",!0)]),_:1}),s.plan_id?(m(),b(pe,{key:1,name:"fade",mode:"out-in"},{default:r(()=>[l(G)?(m(),h("div",Ie,[o(W)])):(m(),b(De,{key:1,ref_key:"planSubscriptionFormRef",ref:j,class:"plan-subscription-form","form-data":l(g),"form-errors":l(E),options:l(A),"options-loading":l(Q),submitting:l(Y),mode:"view",onSubmit:l(U),onCancel:e[5]||(e[5]=t=>a.$router.back())},null,8,["form-data","form-errors","options","options-loading","submitting","onSubmit"]))]),_:1})):f("",!0),l(g).schedule_type===l(B).UnitBased?(m(),b(l(y),{key:2,modelValue:n.formDataState.units,"onUpdate:modelValue":e[6]||(e[6]=t=>n.formDataState.units=t),name:"units",as:"div",class:"flex flex-col gap-2"},{default:r(({errorMessage:t,handleChange:i})=>[e[12]||(e[12]=d("label",{class:"create-invite-base-form-label mb-2"}," Unit Quantity ",-1)),o(Z,{"model-value":n.formDataState.units,min:1,max:9999,placeholder:"Please enter unit quantity",class:F(["w-full",{"p-invalid":t}]),"onUpdate:modelValue":i},null,8,["model-value","class","onUpdate:modelValue"]),t?(m(),b(p,{key:0,severity:"error",variant:"simple"},{default:r(()=>[S(_(t),1)]),_:2},1024)):f("",!0)]),_:1,__:[12]},8,["modelValue"])):f("",!0),o(l(y),{modelValue:s.planStartDate,"onUpdate:modelValue":e[7]||(e[7]=t=>s.planStartDate=t),name:"planStartDate",as:"div",class:"flex flex-col gap-2"},{default:r(({field:t,errorMessage:i,handleChange:v})=>[e[13]||(e[13]=d("label",{class:"create-invite-base-form-label mb-2"}," Start Date ",-1)),o(M,{"model-value":t.value,"min-date":new Date(Date.now()+24*60*60*1e3),placeholder:"Please select a start date",class:"w-full","date-format":"dd/mm/yy","onUpdate:modelValue":v},null,8,["model-value","min-date","onUpdate:modelValue"]),i?(m(),b(p,{key:0,severity:"error",variant:"simple"},{default:r(()=>[S(_(i),1)]),_:2},1024)):f("",!0)]),_:1,__:[13]},8,["modelValue"])]),_:1},8,["validation-schema"]),d("div",Fe,[o(he,{"form-data":l(g),name:n.formDataState.customer_name||"","plan-name":O.value||"","start-date":s.planStartDate||new Date,"unit-quantity":n.formDataState.units||1,"surcharge-rate":c.surchargeRate,gst:c.gst,"is-show-forecast":!0},null,8,["form-data","name","plan-name","start-date","unit-quantity","surcharge-rate","gst"]),d("div",qe,[o(I,{class:"submit-button",label:"NEXT",severity:"warn",loading:c.isSubmitLoading,onClick:e[8]||(e[8]=t=>T("next"))},null,8,["loading"])])])])}}}),ft=we(Be,[["__scopeId","data-v-fbe8ef2b"]]);export{ft as default};
