import{s as v}from"./index-AtQkrpJ9.js";import C from"./createInvoiceForm-DDUQhUw_.js";import{u as y}from"./useCreateInvoice-th9954fL.js";import{d as h,r as x,H as k,a as t,o as s,y as b,q as w,e as r,b as n,p as I,u as e,A as B}from"./index-BPwFJVSY.js";const S={key:0,class:"flex justify-between bg-white rounded-2xl p-4"},N={key:0,class:"flex justify-center items-center min-h-[200px]"},V={class:"flex flex-col items-center gap-2"},q=h({__name:"createInvoice",setup(j){const{formData:i,handleSubmit:m,schema:c,config:p,options:l,loadings:o,updateCustomers:d}=y("add"),f=x();return(u,a)=>{const _=v,g=k("router-view");return s(),t(b,null,[u.$route.name==="payMyInvoiceCreateInvoice"?(s(),t("div",S,[n("div",{class:B(["w-full",{"md:w-230 ":!e(o).loadingCustomers}])},[e(o).loadingCustomers?(s(),t("div",N,[n("div",V,[r(_),a[0]||(a[0]=n("span",{class:"text-gray-500"},"Loading...",-1))])])):(s(),I(C,{key:1,ref_key:"formWrapperRef",ref:f,"form-data":e(i),schema:e(c),config:e(p),"options-loading":e(o),options:e(l),loadings:e(o),onSubmit:e(m),onUpdateCustomer:e(d)},null,8,["form-data","schema","config","options-loading","options","loadings","onSubmit","onUpdateCustomer"]))],2)])):w("",!0),r(g)],64)}}});export{q as _};
