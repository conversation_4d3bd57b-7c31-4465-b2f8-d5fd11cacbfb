import{_ as m}from"./createInvoice.vue_vue_type_script_setup_true_lang-wSZddhos.js";import{d as p,H as i,a as n,o,p as r}from"./index-BPwFJVSY.js";import"./index-AtQkrpJ9.js";import"./createInvoiceForm-DDUQhUw_.js";import"./index-CCP8zFp2.js";import"./index-CEksU3zE.js";import"./index-DgBXOSrM.js";import"./index-DlkyKXFh.js";import"./index-DgYXzrxo.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-BjQt0t51.js";import"./index-DfF1qu_m.js";import"./index-BlFKIn6Y.js";import"./index-Cbodd6BQ.js";import"./index-wTWY-tN1.js";import"./index-kWy1s9tq.js";import"./index-Cui9J1ai.js";import"./index.esm-DOIZz6Kf.js";import"./dayjs.min-Bar1rHDB.js";import"./decimal-LZMqkRTm.js";import"./user-D3NCqkKe.js";import"./customer-M3EzHCil.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./useCreateInvoice-th9954fL.js";import"./integrations-C_ojvdY3.js";import"./invoice-CnWRWdDk.js";const F=p({__name:"createInvoicePage",setup(a){return(t,c)=>{const e=i("router-view");return o(),n("div",null,[t.$route.name==="payMyInvoiceCreateInvoice"?(o(),r(m,{key:0})):(o(),r(e,{key:1}))])}}});export{F as default};
