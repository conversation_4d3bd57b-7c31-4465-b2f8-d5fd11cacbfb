import{b as t}from"./BillbuddyLogo-FvDXaJ4h.js";import{d as o,a as r,o as s,b as e,u as n}from"./index-BPwFJVSY.js";const i={class:"p-4 bg-white rounded-2xl h-full"},a={class:"flex justify-center mb-4"},c=["src"],f=o({__name:"createInvoicePreview",props:{formData:{type:Object,required:!0,default:()=>({})},options:{type:Object,default:()=>({status:[],process_type:[],currency:[],billing_period:[],billing_period_custom_unit:[],one_off_pricing_model:[],recurringTieredPaymentMethod:[]})},optionsLoading:{type:Function,default:()=>()=>!1}},setup(l){return(d,u)=>(s(),r("div",i,[e("div",a,[e("img",{src:n(t),alt:"logo",class:"w-100"},null,8,c)])]))}});export{f as default};
