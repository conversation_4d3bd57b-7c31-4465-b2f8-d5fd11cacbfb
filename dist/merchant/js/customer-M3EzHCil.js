import{V as s,U as o}from"./index-BPwFJVSY.js";const i=t=>o("/customer/list",{params:t}),r=(t,e)=>s("/customer/create",t,e),a=(t,e)=>s("xero/createCustomer",t,e),m=t=>s("/customer/sendInviteCustomerMail",t),u=t=>s("/customer/delete",{customer_ids:t}),l=t=>o("/customer/detail",{params:{customer_id:t}}),d=t=>{let e=null;return t.customer_id&&t.customer_id.includes("%")?e=t.customer_id:e=encodeURIComponent(t.customer_id),o(`/invitation/info?customer_id=${e}`,{paramsSerializer:{encode:n=>n}})},C=t=>s("/invitation/submit",t),g=t=>s("/trans/create",t),p=t=>s("/customer/update",t),f=t=>o("/customer/export",{params:t}),v=(t,e)=>o("/subscription/detail",{params:{plan_id:t,customer_id:e}}),b=(t,e)=>s("/subscription/cancel",{plan_id:t,customer_id:e}),x=t=>o("/customer/communicationHistory",{params:t}),I=()=>o("/getMerchantConfig"),S=t=>s("/customer/notificationSwitch",t),_=(t,e)=>o("xero/getAllCustomer",{params:t,...e});export{I as a,m as b,r as c,b as d,g as e,l as f,d as g,S as h,x as i,i as j,f as k,v as l,a as m,_ as n,u as r,C as s,p as u};
