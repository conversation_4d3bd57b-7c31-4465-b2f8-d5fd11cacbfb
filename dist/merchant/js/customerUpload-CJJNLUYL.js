import{d as Ve,r as O,c as R,g as Le,S as Me,a as c,o as n,A,q as y,b as d,y as le,z as oe,p as N,w as Oe,j as ye,t as D,u as w,I as q,e as _,s as C,n as be,ac as ke,aJ as Re}from"./index-BPwFJVSY.js";import{u as Pe}from"./downloadCenter-xnP8j9T6.js";import{s as Be}from"./index-BlFKIn6Y.js";import{s as P}from"./index-SkqdGoZA.js";import{_ as Te}from"./_plugin-vue_export-helper-DlAUqK2U.js";const We={key:0,class:"p-fileupload-errors"},He={key:1,class:"p-fileupload-avatar-content"},je={key:0,class:"p-fileupload-avatar-placeholder"},qe={key:1,class:"p-fileupload-avatar-preview"},Je=["src"],Ye={key:0,class:"p-fileupload-avatar-progress"},Ge={class:"p-fileupload-avatar-progress-text"},Xe={key:0,class:"p-fileupload-avatar-actions"},Ke={key:2,class:"p-fileupload-gallery-content"},Qe={key:0,class:"p-fileupload-gallery-placeholder"},Ze={class:"p-fileupload-gallery-grid"},Ie={class:"p-fileupload-gallery-item-preview"},ea=["src","alt"],aa={class:"p-fileupload-gallery-item-overlay"},sa={class:"p-fileupload-gallery-item-info"},ta={class:"p-fileupload-gallery-item-name"},ra={class:"p-fileupload-gallery-item-size"},la={class:"p-fileupload-gallery-item-actions"},oa={key:1,class:"p-fileupload-gallery-actions"},ia={key:3,class:"p-fileupload-logo-content"},na={key:0,class:"p-fileupload-logo-placeholder"},ua={key:1,class:"p-fileupload-logo-preview"},pa=["src"],da={key:0,class:"p-fileupload-logo-progress"},ca={class:"p-fileupload-logo-progress-text"},fa={key:2,class:"absolute top-2 right-2"},va={key:0,class:"p-fileupload-logo-actions"},ga={key:4,class:"p-fileupload-content"},ma={class:"p-fileupload-upload"},ha={key:0},wa={key:1},ya={class:"p-fileupload-row-content"},ba={class:"p-fileupload-file"},ka={class:"p-fileupload-file-thumbnail"},xa=["src","alt"],_a={class:"p-fileupload-file-details"},Fa={class:"p-fileupload-file-name"},Ua={class:"p-fileupload-file-size"},$a={key:0,class:"p-fileupload-file-error"},Ca={key:1,class:"p-fileupload-file-warning"},za={class:"p-fileupload-file-actions"},Ea={key:0,class:"p-fileupload-file-progress"},Da={key:1,class:"p-fileupload-actions"},Sa={key:2,class:"p-fileupload-progress"},Aa={class:"p-fileupload-progress-label"},Na=["accept","multiple","disabled"],Va=Ve({__name:"customerUpload",props:{mode:{type:String,default:"normal",validator:F=>["normal","avatar","gallery","logo"].includes(F)},multiple:{type:Boolean,default:!0},accept:{type:String,default:""},maxSize:{type:Number,default:5*1024*1024},maxFiles:{type:Number,default:0},autoUpload:{type:Boolean,default:!0},uploadFunction:{type:[Function,null],default:null},uploadUrl:{type:String,default:""},headers:{type:Object,default:()=>({})},disabled:{type:Boolean,default:!1},showFileList:{type:Boolean,default:!0},fileListMaxHeight:{type:String,default:"300px"},imageCompressionOptions:{type:Object,default:()=>({enabled:!1,maxWidth:1920,maxHeight:1080,quality:.8,mimeType:"image/jpeg"})},chunkedUpload:{type:Boolean,default:!1},chunkSize:{type:Number,default:2*1024*1024},previewBeforeUpload:{type:Boolean,default:!1},modelValue:{type:Array,default:()=>[]},checkUploadUrl:{type:Boolean,default:!0},formDataName:{type:String,default:"upload_files[]"},fetchRemoteFileInfo:{type:Function,default:null}},emits:["update:modelValue","select","beforeUpload","progress","success","error","remove","exceed","exceedSize"],setup(F,{expose:xe,emit:_e}){const l=F,v=_e,x=new Map,ie=()=>{const e=Date.now(),a=60*60*1e3;for(const[t,s]of x.entries())e-s.timestamp>a&&x.delete(t)};ie();const B=(e,a)=>{const t=new FormData;t.append(l.formDataName,e),a!=null&&a.onProgress&&(setTimeout(()=>a.onProgress(50),500),setTimeout(()=>a.onProgress(100),1e3));const s=a!=null&&a.signal?new AbortController:void 0;return a!=null&&a.signal&&s&&a.signal.addEventListener("abort",()=>{s.abort()}),Pe(t).then(g=>g)};B.supportsChunks=!1;const se=O(null),V=O(!1),T=O([]),o=O([]),z=O(!1),E=O({}),L=O(0),W=O([]),G=R(()=>l.mode==="avatar"),ne=R(()=>l.mode==="gallery"),ue=R(()=>l.mode==="logo"),Fe=R(()=>l.accept.split(",").map(e=>e.trim())),te=R(()=>o.value.length>0),H=R(()=>te.value&&!z.value&&!l.disabled),pe=R(()=>({maxHeight:l.fileListMaxHeight,overflowY:"auto"})),X=["image/jpeg","image/png","image/gif","image/webp","image/svg+xml","image/bmp"],Ue=R(()=>({images:o.value.filter(a=>X.includes(a.file.type)),documents:o.value.filter(a=>!X.includes(a.file.type))})),de=()=>Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15),K=()=>{l.disabled||se.value&&se.value.click()},$e=e=>{if(l.disabled)return;let a=null;e instanceof Event&&e.target?a=e.target.files:e instanceof FileList&&(a=e),!(!a||a.length===0)&&ce(a)},Q=e=>{l.disabled||(e.preventDefault(),V.value=!1,e.dataTransfer&&ce(e.dataTransfer.files))},Z=e=>{l.disabled||(e.preventDefault(),V.value=!0)},I=e=>{l.disabled||(e.preventDefault(),V.value=!1)},ce=async e=>{const a=[];if(ie(),l.maxFiles>1&&e.length+o.value.length>l.maxFiles){v("exceed",{files:e,maxFiles:l.maxFiles,currentCount:o.value.length}),window.$toast.add({severity:"error",summary:"Error",detail:`You have exceeded the maximum number of files (${l.maxFiles})`,life:3e3});return}(l.maxFiles===1||G.value)&&S(),console.log("fileList.value",o.value);for(let t=0;t<e.length;t++){const s=e[t];if(l.maxSize>0&&s.size>l.maxSize){v("exceedSize",{file:s,maxSize:l.maxSize}),window.$toast.add({severity:"error",summary:"Error",detail:"You have exceeded the maximum file size",life:3e3});continue}l.multiple||T.value.length===0?a.push(s):(a.push(s),S())}a.length!==0&&(await Ce(a),l.autoUpload&&(l.uploadUrl||l.uploadFunction||typeof B=="function")&&j())},fe=e=>{var g;if(!l.accept)return!0;const a=Fe.value,t=e.type,s=(g=e.name.split(".").pop())==null?void 0:g.toLowerCase();return!!a.some(u=>!!(u===t||u.endsWith("/*")&&t.startsWith(u.replace("/*","/"))||u.startsWith(".")&&`.${s}`===u))},Ce=async e=>{const a=[];for(const s of e){const g=de();let u="",p="pending",r;(l.accept?fe(s):!0)||(p="error",r=`Unsupported file format: must be a file of type: ${l.accept}`),X.includes(s.type)&&(u=URL.createObjectURL(s));const k=re(s),m=x.get(k),h={file:s,id:g,progress:0,status:p,preview:u,error:r,warning:m?`Note: This file has failed ${m.attempts} upload attempt(s)`:void 0};o.value.push(h),p==="pending"&&a.push(s),m&&m.attempts>1&&p==="pending"&&window.$toast.add({severity:"warn",summary:"Previous Upload Failed",detail:`File "${s.name}" failed to upload previously, retrying`,life:3e3})}const t=e.filter(s=>l.accept?fe(s):!0);T.value=[...T.value,...t],a.length>0&&v("select",a),M()},M=()=>{if(l.multiple){const e=o.value.map(a=>{if(a.status==="success"){let t={url:"",name:a.file.name,size:a.file.size,type:a.file.type,status:"success"};if(a.response){if(typeof a.response=="string")t.url=a.response;else if(typeof a.response=="object")if("url"in a.response)t.url=a.response.url;else if(Array.isArray(a.response.file_path)&&a.response.file_path.length>0)t.url=a.response.file_path[0];else{const s=["url","path","filePath","file_url","fileUrl","src","source"];for(const g of s)if(g in a.response&&typeof a.response[g]=="string"){t.url=a.response[g];break}t.url||(t={...t,...a.response})}}return!t.url&&a.preview&&(t.url=a.preview),t}else{if(a.status==="error")return{name:a.file.name,size:a.file.size,type:a.file.type,status:"error",error:a.error,preview:a.preview||null,url:""};if(a.status==="pending"||a.status==="uploading")return{name:a.file.name,size:a.file.size,type:a.file.type,status:a.status,preview:a.preview||null,url:""}}return null}).filter(a=>a!==null);v("update:modelValue",e)}else if(o.value.length>0){const e=o.value[o.value.length-1];if(e.status==="success"){let a={url:"",name:e.file.name,size:e.file.size,type:e.file.type,status:"success"};if(e.response){if(typeof e.response=="string")a.url=e.response;else if(typeof e.response=="object")if("url"in e.response)a.url=e.response.url;else if(Array.isArray(e.response.file_path)&&e.response.file_path.length>0)a.url=e.response.file_path[0];else{const t=["url","path","filePath","file_url","fileUrl","src","source"];for(const s of t)if(s in e.response&&typeof e.response[s]=="string"){a.url=e.response[s];break}a.url||(a={...a,...e.response})}}!a.url&&e.preview&&(a.url=e.preview),v("update:modelValue",[a])}else v("update:modelValue",[{name:e.file.name,size:e.file.size,type:e.file.type,status:e.status,error:e.error,preview:e.preview||null,url:""}])}else v("update:modelValue",[])},S=()=>{o.value.forEach(e=>{e.preview&&URL.revokeObjectURL(e.preview)}),T.value=[],o.value=[],M()},ve=e=>{const a=o.value.findIndex(s=>s.id===e);if(a===-1)return;const t=o.value[a];t.status==="uploading"&&e in E.value&&(E.value[e].abort(),delete E.value[e]),t.preview&&URL.revokeObjectURL(t.preview),o.value.splice(a,1),T.value=T.value.filter(s=>s!==t.file),v("remove",t.file),M()},ze=e=>new Promise((a,t)=>{if(!l.imageCompressionOptions.enabled||!e.type.startsWith("image/")){a(e);return}const s=new FileReader;s.readAsDataURL(e),s.onload=g=>{var p;const u=new Image;u.src=(p=g.target)==null?void 0:p.result,u.onload=()=>{const{maxWidth:r,maxHeight:f,quality:k,mimeType:m}=l.imageCompressionOptions,h=document.createElement("canvas");let b=u.width,i=u.height;b>r&&(i=Math.round(i*r/b),b=r),i>f&&(b=Math.round(b*f/i),i=f),h.width=b,h.height=i;const U=h.getContext("2d");if(!U){t(new Error("Could not get canvas context"));return}U.drawImage(u,0,0,b,i),h.toBlob($=>{if(!$){t(new Error("Image compression failed"));return}const Y=new File([$],e.name,{type:m||e.type,lastModified:Date.now()});a(Y)},m||e.type,k)},u.onerror=()=>{t(new Error("Failed to load image"))}},s.onerror=()=>{t(new Error("Failed to read file"))}}),ge=e=>e.startsWith("image/")?"pi pi-image":e.startsWith("video/")?"pi pi-video":e.startsWith("audio/")?"pi pi-volume-up":e.includes("pdf")?"pi pi-file-pdf":e.includes("word")||e.includes("document")?"pi pi-file-word":e.includes("excel")||e.includes("sheet")?"pi pi-file-excel":e.includes("powerpoint")||e.includes("presentation")?"pi pi-file-powerpoint":e.includes("zip")||e.includes("compressed")?"pi pi-folder":"pi pi-file",me=e=>e===0?"0 MB":`${(e/(1024*1024)).toFixed(2)} MB`,ee=e=>(Ne(e.id),new Promise((a,t)=>{const{file:s,id:g}=e,u=re(s);if(l.uploadFunction||B){const f=l.uploadFunction||B;e.status="uploading",e.progress=0;const k=new AbortController;E.value[g]={xhr:null,abort:()=>k.abort()};const m=h=>{e.progress=h,ae(),v("progress",{file:s,progress:h})};f(s,{headers:l.headers,onProgress:m,signal:k.signal}).then(h=>{const{code:b,data:i,message:U}=h;if(e.status="success",b===0)Array.isArray(i==null?void 0:i.file_path)&&(i==null?void 0:i.file_path.length)>0?e.response=i.file_path[0]:e.response=h,x.delete(u),M(),v("success",{file:s,response:h}),a();else{e.status="error",e.error=U||"Invalid server response format",v("error",{file:s,error:e.error});const $=x.get(u)||{attempts:0};x.set(u,{timestamp:Date.now(),attempts:$.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:U||"Invalid server response format",life:3e3}),e.status="error",e.error=U||"Invalid server response format",v("error",{file:s,error:e.error}),M(),t(new Error(U||"Invalid server response format"))}}).catch(h=>{if(h.name==="AbortError")return;e.status="error",e.error=h.message||"Upload failed",v("error",{file:s,error:e.error});const b=x.get(u)||{attempts:0};x.set(u,{timestamp:Date.now(),attempts:b.attempts+1}),t(h)});return}const p=new XMLHttpRequest,r=new FormData;r.append("file",s),p.open("POST",l.uploadUrl),Object.entries(l.headers).forEach(([f,k])=>{p.setRequestHeader(f,k.toString())}),p.upload.addEventListener("progress",f=>{if(f.lengthComputable){const k=Math.round(f.loaded*100/f.total);e.progress=k,ae(),v("progress",{file:s,progress:k})}}),p.onload=()=>{if(p.status>=200&&p.status<300)try{const f=JSON.parse(p.responseText);e.status="success",e.response=f,x.delete(u),M(),v("success",{file:s,response:f}),a()}catch{e.status="error",e.error="Invalid server response format",v("error",{file:s,error:e.error});const f=x.get(u)||{attempts:0};x.set(u,{timestamp:Date.now(),attempts:f.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Invalid server response format",life:3e3}),t(new Error("Invalid server response format"))}else{e.status="error",e.error=`${p.status}: ${p.statusText}`,v("error",{file:s,error:e.error});const f=x.get(u)||{attempts:0};x.set(u,{timestamp:Date.now(),attempts:f.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:`${p.status}: ${p.statusText}`,life:3e3}),t(new Error(`Upload failed: ${p.status} ${p.statusText}`))}},p.onerror=()=>{e.status="error",e.error="Network error",console.log("Network error",e),v("error",{file:s,error:"Network error"});const f=x.get(u)||{attempts:0};x.set(u,{timestamp:Date.now(),attempts:f.attempts+1}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Network error",life:3e3}),t(new Error("Network error"))},p.onabort=()=>{e.status="error",e.error="Upload cancelled",e.progress=0,v("error",{file:s,error:"Upload cancelled"}),window.$toast&&window.$toast.add({severity:"info",summary:"Info",detail:"Upload cancelled",life:3e3}),t(new Error("Upload cancelled"))},E.value[g]={xhr:p,abort:()=>p.abort()},p.send(r)})),re=e=>`${e.name}_${e.size}_${e.lastModified}`,Ee=e=>new Promise((a,t)=>{const{file:s,id:g}=e,u=l.chunkSize,p=Math.ceil(s.size/u);let r=0,f=0;if((l.uploadFunction||B)&&s.size>u){const m=l.uploadFunction||B;if(!m.supportsChunks){ee(e).then(a).catch(t);return}e.status="uploading",e.progress=0;const h=new AbortController;E.value[g]={xhr:null,abort:()=>h.abort()};const b=i=>{e.progress=i,ae(),v("progress",{file:s,progress:i})};m(s,{headers:l.headers,onProgress:b,signal:h.signal,chunked:!0,chunkSize:u}).then(i=>{e.status="success",e.response=i,M(),v("success",{file:s,response:i}),a()}).catch(i=>{i.name!=="AbortError"&&(e.status="error",e.error=i.message||"Upload failed",v("error",{file:s,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Upload Error",detail:i.message||"Chunked upload failed",life:3e3}),t(i))});return}const k=async()=>{if(r>=p){e.status="success",M(),a();return}const m=r*u,h=Math.min(s.size,m+u),b=s.slice(m,h),i=new XMLHttpRequest,U=new FormData;U.append("file",b,s.name),U.append("chunk",r.toString()),U.append("chunks",p.toString()),U.append("fileName",s.name),i.open("POST",l.uploadUrl),Object.entries(l.headers).forEach(([$,Y])=>{i.setRequestHeader($,Y.toString())}),i.upload.addEventListener("progress",$=>{if($.lengthComputable){const Y=Math.round($.loaded*100/$.total);f=Math.round((r+Y/100)/p*100),e.progress=f,ae(),v("progress",{file:s,progress:f})}}),i.onload=()=>{if(i.status>=200&&i.status<300)try{if(r===p-1){const $=JSON.parse(i.responseText);e.response=$,v("success",{file:s,response:$})}r++,k()}catch{e.status="error",e.error="Invalid server response format",v("error",{file:s,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Invalid server response format",life:3e3}),t(new Error("Invalid server response format"))}else e.status="error",e.error=`${i.status}: ${i.statusText}`,v("error",{file:s,error:e.error}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:`Chunk upload failed: ${i.status} ${i.statusText}`,life:3e3}),t(new Error(`Upload failed: ${i.status} ${i.statusText}`))},i.onerror=()=>{e.status="error",e.error="Network error",v("error",{file:s,error:"Network error"}),window.$toast&&window.$toast.add({severity:"error",summary:"Error",detail:"Network error during chunk upload",life:3e3}),t(new Error("Network error"))},i.onabort=()=>{e.status="error",e.error="Upload cancelled",e.progress=0,v("error",{file:s,error:"Upload cancelled"}),window.$toast&&window.$toast.add({severity:"info",summary:"Info",detail:"Chunked upload cancelled",life:3e3}),t(new Error("Upload cancelled"))},E.value[g]={xhr:i,abort:()=>i.abort()},i.send(U)};k().catch(t)}),ae=()=>{if(o.value.length===0){L.value=0;return}const e=o.value.reduce((a,t)=>a+t.progress,0);L.value=Math.round(e/o.value.length)},De=()=>{if(l.checkUploadUrl&&!l.uploadFunction&&!l.uploadUrl&&!B)return W.value.push("No upload function or URL provided"),!1;if(o.value.length===0)return W.value.push("No files selected"),!1;const e={files:T.value,cancel:!1};return v("beforeUpload",e),!e.cancel},j=async()=>{if(W.value=[],!De()){W.value.length>0&&window.$toast&&window.$toast.add({severity:"error",summary:"Validation Error",detail:W.value[0],life:3e3});return}z.value=!0;try{const e=o.value.filter(a=>a.status==="pending"&&a.error===void 0);for(const a of e){a.status="uploading",a.progress=0;try{let t=a.file;l.imageCompressionOptions.enabled&&a.file.type.startsWith("image/")&&(t=await ze(a.file)),l.chunkedUpload&&t.size>l.chunkSize?await Ee(a):await ee(a)}catch(t){console.error("File upload failed:",a.file.name,t)}}}catch(e){window.$toast&&window.$toast.add({severity:"error",summary:"Upload Error",detail:e instanceof Error?e.message:"An error occurred during upload",life:3e3})}finally{z.value=!1}},he=()=>{Object.values(E.value).forEach(e=>e.abort()),E.value={},o.value.forEach(e=>{e.status==="uploading"&&(e.status="error",e.error="Upload cancelled",e.progress=0)}),z.value=!1,L.value=0};xe({upload:j,abort:he,clearFiles:S,submit:()=>{H.value&&j()}}),Le(()=>{Object.values(E.value).forEach(e=>e.abort()),o.value.forEach(e=>{e.preview&&URL.revokeObjectURL(e.preview)})});const Se=()=>{if(l.modelValue){if(Array.isArray(l.modelValue)&&l.modelValue.length>0)l.modelValue.forEach(e=>{if(typeof e=="string")J(e);else if(typeof e=="object"&&e!==null){const a=e;"status"in a&&a.status==="error"?J(a.url||"",a.name,a.size,"error",a.error):"url"in a&&J(a.url,a.name,a.size)}});else if(typeof l.modelValue=="string"&&l.modelValue)J(l.modelValue);else if(typeof l.modelValue=="object"&&l.modelValue!==null&&"url"in l.modelValue){const e=l.modelValue;J(e.url,e.name,e.size)}}},J=async(e,a,t,s,g)=>{let u=a||we(e),p=Ae(e);const r=de();if(l.fetchRemoteFileInfo)try{const m=await l.fetchRemoteFileInfo(e);m.type&&(p=m.type),m.name&&(u=m.name)}catch(m){console.warn("Failed to fetch remote file info:",m)}let f="";if(X.includes(p)&&(f=e),p.startsWith("image/")&&!t){const m=new Image;m.onload=()=>{const h=Math.round(m.width*m.height*.25),b=o.value.findIndex(i=>i.id===r);if(b!==-1){const i=new File([new ArrayBuffer(h)],u,{type:p});o.value[b].file=i,o.value=[...o.value]}},m.src=e}const k={file:new File([],u,{type:p}),id:r,progress:100,status:s||"success",response:{url:e},preview:f||""};s==="error"&&g&&(k.error=g),o.value.push(k)},we=e=>{const a=e.split("/");return a[a.length-1].split("?")[0]},Ae=e=>{var g;const t=((g=we(e).split(".").pop())==null?void 0:g.toLowerCase())||"";return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",svg:"image/svg+xml",webp:"image/webp",pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",zip:"application/zip",mp3:"audio/mpeg",mp4:"video/mp4"}[t]||"application/octet-stream"};Me(()=>l.modelValue,()=>{o.value.length===0&&Se()},{deep:!0,immediate:!0});const Ne=e=>{const a=o.value.find(t=>t.id===e);if(a){const t=re(a.file);x.delete(t),a.warning&&(delete a.warning,o.value=[...o.value])}};return(e,a)=>{var t,s,g,u,p;return n(),c("div",{class:A(["p-fileupload p-component",{"p-fileupload-advanced":!G.value,"p-fileupload-avatar":G.value,"p-fileupload-gallery":ne.value,"p-fileupload-logo":ue.value,"p-disabled":F.disabled}])},[W.value.length>0?(n(),c("div",We,[(n(!0),c(le,null,oe(W.value,(r,f)=>(n(),N(w(Be),{key:f,severity:"error",closable:!0},{default:Oe(()=>[ye(D(r),1)]),_:2},1024))),128))])):y("",!0),G.value?(n(),c("div",He,[d("div",{class:A(["p-fileupload-avatar-dropzone",{"p-fileupload-dragover":V.value}]),onClick:K,onDragover:Z,onDragleave:I,onDrop:Q},[o.value.length===0||!o.value[0].preview?(n(),c("div",je,[a[1]||(a[1]=d("i",{class:"pi pi-user p-fileupload-avatar-icon"},null,-1)),q(e.$slots,"upload-text",{},()=>[a[0]||(a[0]=d("div",{class:"p-fileupload-text"}," Drop or click to upload avatar ",-1))],!0)])):(n(),c("div",qe,[d("img",{src:o.value[0].preview,alt:"Avatar Preview"},null,8,Je),((t=o.value[0])==null?void 0:t.progress)!==void 0&&o.value[0].status==="uploading"?(n(),c("div",Ye,[d("div",Ge,D(o.value[0].progress)+"% ",1),_(w(P),{value:o.value[0].progress},null,8,["value"])])):y("",!0),a[2]||(a[2]=d("div",{class:"p-fileupload-avatar-overlay"},[d("div",{class:"p-fileupload-avatar-overlay-content"},[d("i",{class:"pi pi-camera"}),d("span",null,"Change avatar")])],-1))]))],34),o.value.length>0&&!F.autoUpload&&!z.value&&o.value[0].status==="pending"?(n(),c("div",Xe,[_(w(C),{icon:"pi pi-upload",class:"p-button-rounded p-button-success",disabled:!H.value,onClick:j},null,8,["disabled"]),_(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger",onClick:S})])):y("",!0),z.value&&o.value.filter(r=>r.status==="uploading").length>1?(n(),N(w(P),{key:1,value:L.value},null,8,["value"])):y("",!0)])):ne.value?(n(),c("div",Ke,[d("div",{class:A(["p-fileupload-gallery-dropzone",{"p-fileupload-dragover":V.value}]),onClick:K,onDragover:Z,onDragleave:I,onDrop:Q},[o.value.length===0?(n(),c("div",Qe,[q(e.$slots,"upload-icon",{},()=>[a[3]||(a[3]=d("i",{class:"pi pi-images p-fileupload-gallery-icon"},null,-1))],!0),q(e.$slots,"upload-text",{},()=>[a[4]||(a[4]=d("span",null,"Drop or click to upload images",-1))],!0)])):y("",!0)],34),F.showFileList&&o.value.length>0?(n(),c("div",{key:0,class:"p-fileupload-gallery-files",style:be(pe.value)},[d("div",Ze,[(n(!0),c(le,null,oe(Ue.value.images,r=>(n(),c("div",{key:r.id,class:A(["p-fileupload-gallery-item",{"p-fileupload-gallery-item-uploading":r.status==="uploading","p-fileupload-gallery-item-success":r.status==="success","p-fileupload-gallery-item-error":r.status==="error"}])},[d("div",Ie,[r.preview?(n(),c("img",{key:0,src:r.preview,alt:r.file.name},null,8,ea)):(n(),c("i",{key:1,class:A(ge(r.file.type))},null,2))]),d("div",aa,[d("div",sa,[d("span",ta,D(r.file.name),1),d("span",ra,D(me(r.file.size)),1)]),d("div",la,[r.status==="pending"?(n(),N(w(C),{key:0,icon:"pi pi-upload",class:"p-button-rounded p-button-outlined p-button-sm p-button-success",onClick:()=>{H.value&&ee(r)}},null,8,["onClick"])):y("",!0),_(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-outlined p-button-sm p-button-danger",onClick:()=>ve(r.id)},null,8,["onClick"])]),r.status==="uploading"?(n(),N(w(P),{key:0,value:r.progress,class:"p-fileupload-gallery-item-progress"},null,8,["value"])):y("",!0),r.status==="success"?(n(),N(w(ke),{key:1,value:"Success",severity:"success"})):y("",!0),r.status==="error"?(n(),N(w(ke),{key:2,value:"Error",severity:"danger"})):y("",!0)])],2))),128))])],4)):y("",!0),!F.autoUpload&&!z.value&&te.value?(n(),c("div",oa,[_(w(C),{icon:"pi pi-upload",label:"Upload All",disabled:!H.value,onClick:j},null,8,["disabled"]),_(w(C),{icon:"pi pi-times",label:"Clear",class:"p-button-outlined p-button-danger",onClick:S})])):y("",!0),z.value&&o.value.filter(r=>r.status==="uploading").length>1?(n(),N(w(P),{key:2,value:L.value},null,8,["value"])):y("",!0)])):ue.value?(n(),c("div",ia,[d("div",{class:A(["p-fileupload-logo-dropzone",{"p-fileupload-dragover":V.value}]),onClick:K,onDragover:Z,onDragleave:I,onDrop:Q},[o.value.length===0||!((s=o.value[0])!=null&&s.response)?(n(),c("div",na,a[5]||(a[5]=[d("i",{class:"pi pi-plus p-fileupload-logo-icon"},null,-1),d("span",null,"Drop or click to upload logo",-1)]))):(n(),c("div",ua,[d("img",{src:o.value[0].preview||((u=(g=o.value[0])==null?void 0:g.response)==null?void 0:u.url),alt:"Logo Preview"},null,8,pa),((p=o.value[0])==null?void 0:p.progress)!==void 0&&o.value[0].status==="uploading"?(n(),c("div",da,[d("div",ca,D(o.value[0].progress)+"% ",1),_(w(P),{value:o.value[0].progress},null,8,["value"])])):y("",!0)])),o.value.length!==0?(n(),c("div",fa,[d("i",{class:"pi pi-times-circle",style:{"font-size":"18px"},onClick:Re(S,["stop"])})])):y("",!0)],34),o.value.length>0&&!F.autoUpload&&!z.value&&o.value[0].status==="pending"?(n(),c("div",va,[_(w(C),{icon:"pi pi-upload",class:"p-button-rounded p-button-success",disabled:!H.value,onClick:j},null,8,["disabled"]),_(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger",onClick:S})])):y("",!0),z.value&&o.value.filter(r=>r.status==="uploading").length>1?(n(),N(w(P),{key:1,value:L.value},null,8,["value"])):y("",!0)])):(n(),c("div",ga,[d("div",{class:A(["p-fileupload-dropzone",{"p-fileupload-dragover":V.value}]),onClick:K,onDragover:Z,onDragleave:I,onDrop:Q},[q(e.$slots,"upload-area",{},()=>[d("div",ma,[q(e.$slots,"upload-icon",{},()=>[a[6]||(a[6]=d("i",{class:"pi pi-upload p-fileupload-icon"},null,-1))],!0),q(e.$slots,"upload-text",{},()=>[V.value?(n(),c("span",wa,"Release to upload")):(n(),c("span",ha,"Drop files here or click to upload"))],!0)])],!0)],34),F.showFileList&&o.value.length>0?(n(),c("div",{key:0,class:"p-fileupload-files",style:be(pe.value)},[(n(!0),c(le,null,oe(o.value,r=>(n(),c("div",{key:r.id,class:A(["p-fileupload-row",{"p-fileupload-row-uploading":r.status==="uploading","p-fileupload-row-success":r.status==="success","p-fileupload-row-error":r.status==="error","p-fileupload-row-warning":r.warning}])},[d("div",ya,[d("div",ba,[q(e.$slots,"file-item",{file:r.file,preview:r.preview,status:r.status,warning:r.warning},()=>[d("div",ka,[r.preview?(n(),c("img",{key:0,src:r.preview,alt:r.file.name,class:"p-fileupload-file-preview"},null,8,xa)):(n(),c("i",{key:1,class:A(["p-fileupload-file-icon",[ge(r.file.type)]])},null,2))]),d("div",_a,[d("div",Fa,D(r.file.name),1),d("div",Ua,D(me(r.file.size)),1),r.status==="error"?(n(),c("div",$a,D(r.error),1)):y("",!0),r.warning?(n(),c("div",Ca,[a[7]||(a[7]=d("i",{class:"pi pi-exclamation-triangle",style:{"margin-right":"4px"}},null,-1)),ye(" "+D(r.warning),1)])):y("",!0)])],!0)]),d("div",za,[r.status==="pending"?(n(),N(w(C),{key:0,icon:"pi pi-upload",class:"p-button-outlined p-button-rounded p-button-success",onClick:()=>{H.value&&ee(r)}},null,8,["onClick"])):y("",!0),_(w(C),{icon:"pi pi-times",class:"p-button-outlined p-button-rounded p-button-danger",onClick:()=>ve(r.id)},null,8,["onClick"])])]),r.status==="uploading"?(n(),c("div",Ea,[_(w(P),{value:r.progress},null,8,["value"])])):y("",!0)],2))),128))],4)):y("",!0),!F.autoUpload&&!z.value&&te.value?(n(),c("div",Da,[_(w(C),{icon:"pi pi-upload",label:"Upload All",disabled:!H.value,onClick:j},null,8,["disabled"]),_(w(C),{icon:"pi pi-ban",label:"Clear",class:"p-button-outlined p-button-danger",onClick:S})])):y("",!0),z.value&&o.value.filter(r=>r.status==="uploading").length>1?(n(),c("div",Sa,[d("div",Aa,D(L.value)+"% ",1),_(w(P),{value:L.value},null,8,["value"]),_(w(C),{icon:"pi pi-times",class:"p-button-rounded p-button-danger p-button-outlined p-fileupload-cancel",onClick:he})])):y("",!0)])),d("input",{ref_key:"fileInput",ref:se,type:"file",class:"p-hidden",accept:F.accept,multiple:F.multiple,disabled:F.disabled,onChange:$e},null,40,Na)],2)}}}),Wa=Te(Va,[["__scopeId","data-v-cea21cc7"]]);export{Wa as default};
