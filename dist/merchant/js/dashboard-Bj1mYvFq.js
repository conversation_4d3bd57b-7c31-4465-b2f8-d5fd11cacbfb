import{d as f,B as _,i as m,r as b,f as h,H as I,a as i,p as g,u as w,o as n,b as e,q as k,e as l,s as x,y as C,z as M,t as c}from"./index-BPwFJVSY.js";import{_ as V}from"./PMI-logo-4SeFW8S8.js";import{b as B}from"./invoice-CnWRWdDk.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={key:0},N={class:"invoice-dashboard-container mt-4"},R={class:"invoice-dashboard-header"},$={class:"hub"},H={class:"py-5"},P={class:"flex flex-col gap-y-10"},D={class:"flex items-center operate-group"},q={class:"flex items-center operate-group"},O={class:"overview"},S={class:"overview-item-title"},T={class:"overview-item-content"},W={class:"overview-item-count"},j=["onClick"],z=f({__name:"dashboard",setup(A){const v=_(),d=m(),a=b(),u=t=>{d.push({name:"payMyInvoiceInvoiceList",query:t!==null?{status:t}:void 0})};return h(()=>{B().then(t=>{a.value=t.data})}),(t,s)=>{const r=x,p=I("RouterView");return w(v).name==="payMyInvoice"?(n(),i("div",L,[s[9]||(s[9]=e("div",{class:"title"},[e("span",{class:"mt-2"}," Welcome to"),e("img",{src:V,alt:"PMI",class:"logo"})],-1)),e("div",N,[e("div",R,[e("div",$,[s[7]||(s[7]=e("div",{class:"header-subtitle"}," Invoice Hub ",-1)),e("div",H,[s[6]||(s[6]=e("p",{class:"pb-6 text-part"}," PayMyInvoice lets you send digital invoices directly to your customers, making it easy for them to pay you online via card, bank transfer, or any available payment method you've enabled. Whether you're billing for services or issuing one-off charges, this feature gives you full visibility and controls. ",-1)),e("div",P,[e("div",D,[l(r,{label:"CREATE INVOICE",severity:"warn",class:"invoice-btn",onClick:s[0]||(s[0]=o=>t.$router.push({name:"payMyInvoiceCreateInvoice"}))}),s[3]||(s[3]=e("p",{class:"text-part whitespace-pre-line"}," Here you can create your one-off invoices to your customers. ",-1))]),e("div",q,[l(r,{label:"INVOICE LIST",class:"invoice-btn",onClick:s[1]||(s[1]=o=>t.$router.push({name:"payMyInvoiceInvoiceList"}))}),s[4]||(s[4]=e("p",{class:"text-part whitespace-pre-line"}," Here you can find all invoices for both PMI invoices and your recurring invoices. ",-1))]),k("",!0)])])])]),e("div",O,[(n(!0),i(C,null,M(a.value,(o,y)=>(n(),i("div",{key:y,class:"overview-item"},[e("div",S,c(o==null?void 0:o.status_text),1),e("div",T,[e("div",W,c(o==null?void 0:o.count),1),e("div",{class:"flex justify-between text-[#545454]",onClick:F=>u(o==null?void 0:o.status)},s[8]||(s[8]=[e("span",{class:"cursor-pointer"},"View all",-1),e("i",{class:"pi pi-chevron-right cursor-pointer"},null,-1)]),8,j)])]))),128))])])])):(n(),g(p,{key:1}))}}}),U=E(z,[["__scopeId","data-v-28471e0f"]]);export{U as default};
