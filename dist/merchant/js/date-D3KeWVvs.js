import{d as y}from"./dayjs.min-Bar1rHDB.js";import{aY as w}from"./index-BPwFJVSY.js";var M={exports:{}},I=M.exports,Z;function N(){return Z||(Z=1,function(D,U){(function($,l){D.exports=l()})(I,function(){var $={year:0,month:1,day:2,hour:3,minute:4,second:5},l={};return function(C,H,f){var v,n=function(i,o,r){r===void 0&&(r={});var t=new Date(i),e=function(u,a){a===void 0&&(a={});var s=a.timeZoneName||"short",h=u+"|"+s,c=l[h];return c||(c=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:u,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:s}),l[h]=c),c}(o,r);return e.formatToParts(t)},z=function(i,o){for(var r=n(i,o),t=[],e=0;e<r.length;e+=1){var u=r[e],a=u.type,s=u.value,h=$[a];h>=0&&(t[h]=parseInt(s,10))}var c=t[3],g=c===24?0:c,m=t[0]+"-"+t[1]+"-"+t[2]+" "+g+":"+t[4]+":"+t[5]+":000",p=+i;return(f.utc(m).valueOf()-(p-=p%1e3))/6e4},x=H.prototype;x.tz=function(i,o){i===void 0&&(i=v);var r,t=this.utcOffset(),e=this.toDate(),u=e.toLocaleString("en-US",{timeZone:i}),a=Math.round((e-new Date(u))/1e3/60),s=15*-Math.round(e.getTimezoneOffset()/15)-a;if(!Number(s))r=this.utcOffset(0,o);else if(r=f(u,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(s,!0),o){var h=r.utcOffset();r=r.add(t-h,"minute")}return r.$x.$timezone=i,r},x.offsetName=function(i){var o=this.$x.$timezone||f.tz.guess(),r=n(this.valueOf(),o,{timeZoneName:i}).find(function(t){return t.type.toLowerCase()==="timezonename"});return r&&r.value};var T=x.startOf;x.startOf=function(i,o){if(!this.$x||!this.$x.$timezone)return T.call(this,i,o);var r=f(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return T.call(r,i,o).tz(this.$x.$timezone,!0)},f.tz=function(i,o,r){var t=r&&o,e=r||o||v,u=z(+f(),e);if(typeof i!="string")return f(i).tz(e);var a=function(g,m,p){var d=g-60*m*1e3,O=z(d,p);if(m===O)return[d,m];var Y=z(d-=60*(O-m)*1e3,p);return O===Y?[d,O]:[g-60*Math.min(O,Y)*1e3,Math.max(O,Y)]}(f.utc(i,t).valueOf(),u,e),s=a[0],h=a[1],c=f(s).utcOffset(h);return c.$x.$timezone=e,c},f.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},f.tz.setDefault=function(i){v=i}}})}(M)),M.exports}var q=N();const F=w(q);var S={exports:{}},E=S.exports,L;function R(){return L||(L=1,function(D,U){(function($,l){D.exports=l()})(E,function(){var $="minute",l=/[+-]\d\d(?::?\d\d)?/g,C=/([+-]|\d\d)/g;return function(H,f,v){var n=f.prototype;v.utc=function(t){var e={date:t,utc:!0,args:arguments};return new f(e)},n.utc=function(t){var e=v(this.toDate(),{locale:this.$L,utc:!0});return t?e.add(this.utcOffset(),$):e},n.local=function(){return v(this.toDate(),{locale:this.$L,utc:!1})};var z=n.parse;n.parse=function(t){t.utc&&(this.$u=!0),this.$utils().u(t.$offset)||(this.$offset=t.$offset),z.call(this,t)};var x=n.init;n.init=function(){if(this.$u){var t=this.$d;this.$y=t.getUTCFullYear(),this.$M=t.getUTCMonth(),this.$D=t.getUTCDate(),this.$W=t.getUTCDay(),this.$H=t.getUTCHours(),this.$m=t.getUTCMinutes(),this.$s=t.getUTCSeconds(),this.$ms=t.getUTCMilliseconds()}else x.call(this)};var T=n.utcOffset;n.utcOffset=function(t,e){var u=this.$utils().u;if(u(t))return this.$u?0:u(this.$offset)?T.call(this):this.$offset;if(typeof t=="string"&&(t=function(c){c===void 0&&(c="");var g=c.match(l);if(!g)return null;var m=(""+g[0]).match(C)||["-",0,0],p=m[0],d=60*+m[1]+ +m[2];return d===0?0:p==="+"?d:-d}(t),t===null))return this;var a=Math.abs(t)<=16?60*t:t,s=this;if(e)return s.$offset=a,s.$u=t===0,s;if(t!==0){var h=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(s=this.local().add(a+h,$)).$offset=a,s.$x.$localOffset=h}else s=this.utc();return s};var i=n.format;n.format=function(t){var e=t||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return i.call(this,e)},n.valueOf=function(){var t=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*t},n.isUTC=function(){return!!this.$u},n.toISOString=function(){return this.toDate().toISOString()},n.toString=function(){return this.toDate().toUTCString()};var o=n.toDate;n.toDate=function(t){return t==="s"&&this.$offset?v(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():o.call(this)};var r=n.diff;n.diff=function(t,e,u){if(t&&this.$u===t.$u)return r.call(this,t,e,u);var a=this.local(),s=v(t).local();return r.call(a,s,e,u)}}})}(S)),S.exports}var b=R();const j=w(b);y.extend(j);y.extend(F);const V=(D,U="MMM DD h:mm A")=>{const $="-";if(!D)return $;const l=y(D);return l.isValid()?l.format(U):$};export{V as f};
