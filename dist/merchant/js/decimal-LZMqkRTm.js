/*!
 *  decimal.js v10.5.0
 *  An arbitrary-precision Decimal type for JavaScript.
 *  https://github.com/MikeMcl/decimal.js
 *  Copyright (c) 2025 <PERSON> <<EMAIL>>
 *  MIT Licence
 */var ue=9e15,V=1e9,fe="0123456789abcdef",z="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",x="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",ce={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:ue,crypto:!1},we,U,m=!0,ee="[DecimalError] ",H=ee+"Invalid argument: ",me=ee+"Precision limit exceeded",Ne=ee+"crypto unavailable",ve="[object Decimal]",L=Math.floor,C=Math.pow,Ae=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,Le=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,Fe=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,Ee=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,T=1e7,w=7,Oe=9007199254740991,De=z.length-1,le=x.length-1,d={toStringTag:ve};d.absoluteValue=d.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),p(e)};d.ceil=function(){return p(new this.constructor(this),this.e+1,2)};d.clampedTo=d.clamp=function(e,i){var n,r=this,s=r.constructor;if(e=new s(e),i=new s(i),!e.s||!i.s)return new s(NaN);if(e.gt(i))throw Error(H+i);return n=r.cmp(e),n<0?e:r.cmp(i)>0?i:new s(r)};d.comparedTo=d.cmp=function(e){var i,n,r,s,t=this,o=t.d,u=(e=new t.constructor(e)).d,c=t.s,f=e.s;if(!o||!u)return!c||!f?NaN:c!==f?c:o===u?0:!o^c<0?1:-1;if(!o[0]||!u[0])return o[0]?c:u[0]?-f:0;if(c!==f)return c;if(t.e!==e.e)return t.e>e.e^c<0?1:-1;for(r=o.length,s=u.length,i=0,n=r<s?r:s;i<n;++i)if(o[i]!==u[i])return o[i]>u[i]^c<0?1:-1;return r===s?0:r>s^c<0?1:-1};d.cosine=d.cos=function(){var e,i,n=this,r=n.constructor;return n.d?n.d[0]?(e=r.precision,i=r.rounding,r.precision=e+Math.max(n.e,n.sd())+w,r.rounding=1,n=Ie(r,Se(r,n)),r.precision=e,r.rounding=i,p(U==2||U==3?n.neg():n,e,i,!0)):new r(1):new r(NaN)};d.cubeRoot=d.cbrt=function(){var e,i,n,r,s,t,o,u,c,f,l=this,a=l.constructor;if(!l.isFinite()||l.isZero())return new a(l);for(m=!1,t=l.s*C(l.s*l,1/3),!t||Math.abs(t)==1/0?(n=S(l.d),e=l.e,(t=(e-n.length+1)%3)&&(n+=t==1||t==-2?"0":"00"),t=C(n,1/3),e=L((e+1)/3)-(e%3==(e<0?-1:2)),t==1/0?n="5e"+e:(n=t.toExponential(),n=n.slice(0,n.indexOf("e")+1)+e),r=new a(n),r.s=l.s):r=new a(t.toString()),o=(e=a.precision)+3;;)if(u=r,c=u.times(u).times(u),f=c.plus(l),r=k(f.plus(l).times(u),f.plus(c),o+2,1),S(u.d).slice(0,o)===(n=S(r.d)).slice(0,o))if(n=n.slice(o-3,o+1),n=="9999"||!s&&n=="4999"){if(!s&&(p(u,e+1,0),u.times(u).times(u).eq(l))){r=u;break}o+=4,s=1}else{(!+n||!+n.slice(1)&&n.charAt(0)=="5")&&(p(r,e+1,1),i=!r.times(r).times(r).eq(l));break}return m=!0,p(r,e,a.rounding,i)};d.decimalPlaces=d.dp=function(){var e,i=this.d,n=NaN;if(i){if(e=i.length-1,n=(e-L(this.e/w))*w,e=i[e],e)for(;e%10==0;e/=10)n--;n<0&&(n=0)}return n};d.dividedBy=d.div=function(e){return k(this,new this.constructor(e))};d.dividedToIntegerBy=d.divToInt=function(e){var i=this,n=i.constructor;return p(k(i,new n(e),0,1,1),n.precision,n.rounding)};d.equals=d.eq=function(e){return this.cmp(e)===0};d.floor=function(){return p(new this.constructor(this),this.e+1,3)};d.greaterThan=d.gt=function(e){return this.cmp(e)>0};d.greaterThanOrEqualTo=d.gte=function(e){var i=this.cmp(e);return i==1||i===0};d.hyperbolicCosine=d.cosh=function(){var e,i,n,r,s,t=this,o=t.constructor,u=new o(1);if(!t.isFinite())return new o(t.s?1/0:NaN);if(t.isZero())return u;n=o.precision,r=o.rounding,o.precision=n+Math.max(t.e,t.sd())+4,o.rounding=1,s=t.d.length,s<32?(e=Math.ceil(s/3),i=(1/ne(4,e)).toString()):(e=16,i="2.3283064365386962890625e-10"),t=$(o,1,t.times(i),new o(1),!0);for(var c,f=e,l=new o(8);f--;)c=t.times(t),t=u.minus(c.times(l.minus(c.times(l))));return p(t,o.precision=n,o.rounding=r,!0)};d.hyperbolicSine=d.sinh=function(){var e,i,n,r,s=this,t=s.constructor;if(!s.isFinite()||s.isZero())return new t(s);if(i=t.precision,n=t.rounding,t.precision=i+Math.max(s.e,s.sd())+4,t.rounding=1,r=s.d.length,r<3)s=$(t,2,s,s,!0);else{e=1.4*Math.sqrt(r),e=e>16?16:e|0,s=s.times(1/ne(5,e)),s=$(t,2,s,s,!0);for(var o,u=new t(5),c=new t(16),f=new t(20);e--;)o=s.times(s),s=s.times(u.plus(o.times(c.times(o).plus(f))))}return t.precision=i,t.rounding=n,p(s,i,n,!0)};d.hyperbolicTangent=d.tanh=function(){var e,i,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,i=r.rounding,r.precision=e+7,r.rounding=1,k(n.sinh(),n.cosh(),r.precision=e,r.rounding=i)):new r(n.s)};d.inverseCosine=d.acos=function(){var e=this,i=e.constructor,n=e.abs().cmp(1),r=i.precision,s=i.rounding;return n!==-1?n===0?e.isNeg()?_(i,r,s):new i(0):new i(NaN):e.isZero()?_(i,r+4,s).times(.5):(i.precision=r+6,i.rounding=1,e=new i(1).minus(e).div(e.plus(1)).sqrt().atan(),i.precision=r,i.rounding=s,e.times(2))};d.inverseHyperbolicCosine=d.acosh=function(){var e,i,n=this,r=n.constructor;return n.lte(1)?new r(n.eq(1)?0:NaN):n.isFinite()?(e=r.precision,i=r.rounding,r.precision=e+Math.max(Math.abs(n.e),n.sd())+4,r.rounding=1,m=!1,n=n.times(n).minus(1).sqrt().plus(n),m=!0,r.precision=e,r.rounding=i,n.ln()):new r(n)};d.inverseHyperbolicSine=d.asinh=function(){var e,i,n=this,r=n.constructor;return!n.isFinite()||n.isZero()?new r(n):(e=r.precision,i=r.rounding,r.precision=e+2*Math.max(Math.abs(n.e),n.sd())+6,r.rounding=1,m=!1,n=n.times(n).plus(1).sqrt().plus(n),m=!0,r.precision=e,r.rounding=i,n.ln())};d.inverseHyperbolicTangent=d.atanh=function(){var e,i,n,r,s=this,t=s.constructor;return s.isFinite()?s.e>=0?new t(s.abs().eq(1)?s.s/0:s.isZero()?s:NaN):(e=t.precision,i=t.rounding,r=s.sd(),Math.max(r,e)<2*-s.e-1?p(new t(s),e,i,!0):(t.precision=n=r-s.e,s=k(s.plus(1),new t(1).minus(s),n+e,1),t.precision=e+4,t.rounding=1,s=s.ln(),t.precision=e,t.rounding=i,s.times(.5))):new t(NaN)};d.inverseSine=d.asin=function(){var e,i,n,r,s=this,t=s.constructor;return s.isZero()?new t(s):(i=s.abs().cmp(1),n=t.precision,r=t.rounding,i!==-1?i===0?(e=_(t,n+4,r).times(.5),e.s=s.s,e):new t(NaN):(t.precision=n+6,t.rounding=1,s=s.div(new t(1).minus(s.times(s)).sqrt().plus(1)).atan(),t.precision=n,t.rounding=r,s.times(2)))};d.inverseTangent=d.atan=function(){var e,i,n,r,s,t,o,u,c,f=this,l=f.constructor,a=l.precision,h=l.rounding;if(f.isFinite()){if(f.isZero())return new l(f);if(f.abs().eq(1)&&a+4<=le)return o=_(l,a+4,h).times(.25),o.s=f.s,o}else{if(!f.s)return new l(NaN);if(a+4<=le)return o=_(l,a+4,h).times(.5),o.s=f.s,o}for(l.precision=u=a+10,l.rounding=1,n=Math.min(28,u/w+2|0),e=n;e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(m=!1,i=Math.ceil(u/w),r=1,c=f.times(f),o=new l(f),s=f;e!==-1;)if(s=s.times(c),t=o.minus(s.div(r+=2)),s=s.times(c),o=t.plus(s.div(r+=2)),o.d[i]!==void 0)for(e=i;o.d[e]===t.d[e]&&e--;);return n&&(o=o.times(2<<n-1)),m=!0,p(o,l.precision=a,l.rounding=h,!0)};d.isFinite=function(){return!!this.d};d.isInteger=d.isInt=function(){return!!this.d&&L(this.e/w)>this.d.length-2};d.isNaN=function(){return!this.s};d.isNegative=d.isNeg=function(){return this.s<0};d.isPositive=d.isPos=function(){return this.s>0};d.isZero=function(){return!!this.d&&this.d[0]===0};d.lessThan=d.lt=function(e){return this.cmp(e)<0};d.lessThanOrEqualTo=d.lte=function(e){return this.cmp(e)<1};d.logarithm=d.log=function(e){var i,n,r,s,t,o,u,c,f=this,l=f.constructor,a=l.precision,h=l.rounding,g=5;if(e==null)e=new l(10),i=!0;else{if(e=new l(e),n=e.d,e.s<0||!n||!n[0]||e.eq(1))return new l(NaN);i=e.eq(10)}if(n=f.d,f.s<0||!n||!n[0]||f.eq(1))return new l(n&&!n[0]?-1/0:f.s!=1?NaN:n?0:1/0);if(i)if(n.length>1)t=!0;else{for(s=n[0];s%10===0;)s/=10;t=s!==1}if(m=!1,u=a+g,o=B(f,u),r=i?y(l,u+10):B(e,u),c=k(o,r,u,1),W(c.d,s=a,h))do if(u+=10,o=B(f,u),r=i?y(l,u+10):B(e,u),c=k(o,r,u,1),!t){+S(c.d).slice(s+1,s+15)+1==1e14&&(c=p(c,a+1,0));break}while(W(c.d,s+=10,h));return m=!0,p(c,a,h)};d.minus=d.sub=function(e){var i,n,r,s,t,o,u,c,f,l,a,h,g=this,v=g.constructor;if(e=new v(e),!g.d||!e.d)return!g.s||!e.s?e=new v(NaN):g.d?e.s=-e.s:e=new v(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(f=g.d,h=e.d,u=v.precision,c=v.rounding,!f[0]||!h[0]){if(h[0])e.s=-e.s;else if(f[0])e=new v(g);else return new v(c===3?-0:0);return m?p(e,u,c):e}if(n=L(e.e/w),l=L(g.e/w),f=f.slice(),t=l-n,t){for(a=t<0,a?(i=f,t=-t,o=h.length):(i=h,n=l,o=f.length),r=Math.max(Math.ceil(u/w),o)+2,t>r&&(t=r,i.length=1),i.reverse(),r=t;r--;)i.push(0);i.reverse()}else{for(r=f.length,o=h.length,a=r<o,a&&(o=r),r=0;r<o;r++)if(f[r]!=h[r]){a=f[r]<h[r];break}t=0}for(a&&(i=f,f=h,h=i,e.s=-e.s),o=f.length,r=h.length-o;r>0;--r)f[o++]=0;for(r=h.length;r>t;){if(f[--r]<h[r]){for(s=r;s&&f[--s]===0;)f[s]=T-1;--f[s],f[r]+=T}f[r]-=h[r]}for(;f[--o]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(e.d=f,e.e=ie(f,n),m?p(e,u,c):e):new v(c===3?-0:0)};d.modulo=d.mod=function(e){var i,n=this,r=n.constructor;return e=new r(e),!n.d||!e.s||e.d&&!e.d[0]?new r(NaN):!e.d||n.d&&!n.d[0]?p(new r(n),r.precision,r.rounding):(m=!1,r.modulo==9?(i=k(n,e.abs(),0,3,1),i.s*=e.s):i=k(n,e,0,r.modulo,1),i=i.times(e),m=!0,n.minus(i))};d.naturalExponential=d.exp=function(){return ae(this)};d.naturalLogarithm=d.ln=function(){return B(this)};d.negated=d.neg=function(){var e=new this.constructor(this);return e.s=-e.s,p(e)};d.plus=d.add=function(e){var i,n,r,s,t,o,u,c,f,l,a=this,h=a.constructor;if(e=new h(e),!a.d||!e.d)return!a.s||!e.s?e=new h(NaN):a.d||(e=new h(e.d||a.s===e.s?a:NaN)),e;if(a.s!=e.s)return e.s=-e.s,a.minus(e);if(f=a.d,l=e.d,u=h.precision,c=h.rounding,!f[0]||!l[0])return l[0]||(e=new h(a)),m?p(e,u,c):e;if(t=L(a.e/w),r=L(e.e/w),f=f.slice(),s=t-r,s){for(s<0?(n=f,s=-s,o=l.length):(n=l,r=t,o=f.length),t=Math.ceil(u/w),o=t>o?t+1:o+1,s>o&&(s=o,n.length=1),n.reverse();s--;)n.push(0);n.reverse()}for(o=f.length,s=l.length,o-s<0&&(s=o,n=l,l=f,f=n),i=0;s;)i=(f[--s]=f[s]+l[s]+i)/T|0,f[s]%=T;for(i&&(f.unshift(i),++r),o=f.length;f[--o]==0;)f.pop();return e.d=f,e.e=ie(f,r),m?p(e,u,c):e};d.precision=d.sd=function(e){var i,n=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(H+e);return n.d?(i=ke(n.d),e&&n.e+1>i&&(i=n.e+1)):i=NaN,i};d.round=function(){var e=this,i=e.constructor;return p(new i(e),e.e+1,i.rounding)};d.sine=d.sin=function(){var e,i,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,i=r.rounding,r.precision=e+Math.max(n.e,n.sd())+w,r.rounding=1,n=_e(r,Se(r,n)),r.precision=e,r.rounding=i,p(U>2?n.neg():n,e,i,!0)):new r(NaN)};d.squareRoot=d.sqrt=function(){var e,i,n,r,s,t,o=this,u=o.d,c=o.e,f=o.s,l=o.constructor;if(f!==1||!u||!u[0])return new l(!f||f<0&&(!u||u[0])?NaN:u?o:1/0);for(m=!1,f=Math.sqrt(+o),f==0||f==1/0?(i=S(u),(i.length+c)%2==0&&(i+="0"),f=Math.sqrt(i),c=L((c+1)/2)-(c<0||c%2),f==1/0?i="5e"+c:(i=f.toExponential(),i=i.slice(0,i.indexOf("e")+1)+c),r=new l(i)):r=new l(f.toString()),n=(c=l.precision)+3;;)if(t=r,r=t.plus(k(o,t,n+2,1)).times(.5),S(t.d).slice(0,n)===(i=S(r.d)).slice(0,n))if(i=i.slice(n-3,n+1),i=="9999"||!s&&i=="4999"){if(!s&&(p(t,c+1,0),t.times(t).eq(o))){r=t;break}n+=4,s=1}else{(!+i||!+i.slice(1)&&i.charAt(0)=="5")&&(p(r,c+1,1),e=!r.times(r).eq(o));break}return m=!0,p(r,c,l.rounding,e)};d.tangent=d.tan=function(){var e,i,n=this,r=n.constructor;return n.isFinite()?n.isZero()?new r(n):(e=r.precision,i=r.rounding,r.precision=e+10,r.rounding=1,n=n.sin(),n.s=1,n=k(n,new r(1).minus(n.times(n)).sqrt(),e+10,0),r.precision=e,r.rounding=i,p(U==2||U==4?n.neg():n,e,i,!0)):new r(NaN)};d.times=d.mul=function(e){var i,n,r,s,t,o,u,c,f,l=this,a=l.constructor,h=l.d,g=(e=new a(e)).d;if(e.s*=l.s,!h||!h[0]||!g||!g[0])return new a(!e.s||h&&!h[0]&&!g||g&&!g[0]&&!h?NaN:!h||!g?e.s/0:e.s*0);for(n=L(l.e/w)+L(e.e/w),c=h.length,f=g.length,c<f&&(t=h,h=g,g=t,o=c,c=f,f=o),t=[],o=c+f,r=o;r--;)t.push(0);for(r=f;--r>=0;){for(i=0,s=c+r;s>r;)u=t[s]+g[r]*h[s-r-1]+i,t[s--]=u%T|0,i=u/T|0;t[s]=(t[s]+i)%T|0}for(;!t[--o];)t.pop();return i?++n:t.shift(),e.d=t,e.e=ie(t,n),m?p(e,a.precision,a.rounding):e};d.toBinary=function(e,i){return he(this,2,e,i)};d.toDecimalPlaces=d.toDP=function(e,i){var n=this,r=n.constructor;return n=new r(n),e===void 0?n:(O(e,0,V),i===void 0?i=r.rounding:O(i,0,8),p(n,e+n.e+1,i))};d.toExponential=function(e,i){var n,r=this,s=r.constructor;return e===void 0?n=R(r,!0):(O(e,0,V),i===void 0?i=s.rounding:O(i,0,8),r=p(new s(r),e+1,i),n=R(r,!0,e+1)),r.isNeg()&&!r.isZero()?"-"+n:n};d.toFixed=function(e,i){var n,r,s=this,t=s.constructor;return e===void 0?n=R(s):(O(e,0,V),i===void 0?i=t.rounding:O(i,0,8),r=p(new t(s),e+s.e+1,i),n=R(r,!1,e+r.e+1)),s.isNeg()&&!s.isZero()?"-"+n:n};d.toFraction=function(e){var i,n,r,s,t,o,u,c,f,l,a,h,g=this,v=g.d,N=g.constructor;if(!v)return new N(g);if(f=n=new N(1),r=c=new N(0),i=new N(r),t=i.e=ke(v)-g.e-1,o=t%w,i.d[0]=C(10,o<0?w+o:o),e==null)e=t>0?i:f;else{if(u=new N(e),!u.isInt()||u.lt(f))throw Error(H+u);e=u.gt(i)?t>0?i:f:u}for(m=!1,u=new N(S(v)),l=N.precision,N.precision=t=v.length*w*2;a=k(u,i,0,1,1),s=n.plus(a.times(r)),s.cmp(e)!=1;)n=r,r=s,s=f,f=c.plus(a.times(s)),c=s,s=i,i=u.minus(a.times(s)),u=s;return s=k(e.minus(n),r,0,1,1),c=c.plus(s.times(f)),n=n.plus(s.times(r)),c.s=f.s=g.s,h=k(f,r,t,1).minus(g).abs().cmp(k(c,n,t,1).minus(g).abs())<1?[f,r]:[c,n],N.precision=l,m=!0,h};d.toHexadecimal=d.toHex=function(e,i){return he(this,16,e,i)};d.toNearest=function(e,i){var n=this,r=n.constructor;if(n=new r(n),e==null){if(!n.d)return n;e=new r(1),i=r.rounding}else{if(e=new r(e),i===void 0?i=r.rounding:O(i,0,8),!n.d)return e.s?n:e;if(!e.d)return e.s&&(e.s=n.s),e}return e.d[0]?(m=!1,n=k(n,e,0,i,1).times(e),m=!0,p(n)):(e.s=n.s,n=e),n};d.toNumber=function(){return+this};d.toOctal=function(e,i){return he(this,8,e,i)};d.toPower=d.pow=function(e){var i,n,r,s,t,o,u=this,c=u.constructor,f=+(e=new c(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new c(C(+u,f));if(u=new c(u),u.eq(1))return u;if(r=c.precision,t=c.rounding,e.eq(1))return p(u,r,t);if(i=L(e.e/w),i>=e.d.length-1&&(n=f<0?-f:f)<=Oe)return s=Me(c,u,n,r),e.s<0?new c(1).div(s):p(s,r,t);if(o=u.s,o<0){if(i<e.d.length-1)return new c(NaN);if((e.d[i]&1)==0&&(o=1),u.e==0&&u.d[0]==1&&u.d.length==1)return u.s=o,u}return n=C(+u,f),i=n==0||!isFinite(n)?L(f*(Math.log("0."+S(u.d))/Math.LN10+u.e+1)):new c(n+"").e,i>c.maxE+1||i<c.minE-1?new c(i>0?o/0:0):(m=!1,c.rounding=u.s=1,n=Math.min(12,(i+"").length),s=ae(e.times(B(u,r+n)),r),s.d&&(s=p(s,r+5,1),W(s.d,r,t)&&(i=r+10,s=p(ae(e.times(B(u,i+n)),i),i+5,1),+S(s.d).slice(r+1,r+15)+1==1e14&&(s=p(s,r+1,0)))),s.s=o,m=!0,c.rounding=t,p(s,r,t))};d.toPrecision=function(e,i){var n,r=this,s=r.constructor;return e===void 0?n=R(r,r.e<=s.toExpNeg||r.e>=s.toExpPos):(O(e,1,V),i===void 0?i=s.rounding:O(i,0,8),r=p(new s(r),e,i),n=R(r,e<=r.e||r.e<=s.toExpNeg,e)),r.isNeg()&&!r.isZero()?"-"+n:n};d.toSignificantDigits=d.toSD=function(e,i){var n=this,r=n.constructor;return e===void 0?(e=r.precision,i=r.rounding):(O(e,1,V),i===void 0?i=r.rounding:O(i,0,8)),p(new r(n),e,i)};d.toString=function(){var e=this,i=e.constructor,n=R(e,e.e<=i.toExpNeg||e.e>=i.toExpPos);return e.isNeg()&&!e.isZero()?"-"+n:n};d.truncated=d.trunc=function(){return p(new this.constructor(this),this.e+1,1)};d.valueOf=d.toJSON=function(){var e=this,i=e.constructor,n=R(e,e.e<=i.toExpNeg||e.e>=i.toExpPos);return e.isNeg()?"-"+n:n};function S(e){var i,n,r,s=e.length-1,t="",o=e[0];if(s>0){for(t+=o,i=1;i<s;i++)r=e[i]+"",n=w-r.length,n&&(t+=b(n)),t+=r;o=e[i],r=o+"",n=w-r.length,n&&(t+=b(n))}else if(o===0)return"0";for(;o%10===0;)o/=10;return t+o}function O(e,i,n){if(e!==~~e||e<i||e>n)throw Error(H+e)}function W(e,i,n,r){var s,t,o,u;for(t=e[0];t>=10;t/=10)--i;return--i<0?(i+=w,s=0):(s=Math.ceil((i+1)/w),i%=w),t=C(10,w-i),u=e[s]%t|0,r==null?i<3?(i==0?u=u/100|0:i==1&&(u=u/10|0),o=n<4&&u==99999||n>3&&u==49999||u==5e4||u==0):o=(n<4&&u+1==t||n>3&&u+1==t/2)&&(e[s+1]/t/100|0)==C(10,i-2)-1||(u==t/2||u==0)&&(e[s+1]/t/100|0)==0:i<4?(i==0?u=u/1e3|0:i==1?u=u/100|0:i==2&&(u=u/10|0),o=(r||n<4)&&u==9999||!r&&n>3&&u==4999):o=((r||n<4)&&u+1==t||!r&&n>3&&u+1==t/2)&&(e[s+1]/t/1e3|0)==C(10,i-3)-1,o}function Y(e,i,n){for(var r,s=[0],t,o=0,u=e.length;o<u;){for(t=s.length;t--;)s[t]*=i;for(s[0]+=fe.indexOf(e.charAt(o++)),r=0;r<s.length;r++)s[r]>n-1&&(s[r+1]===void 0&&(s[r+1]=0),s[r+1]+=s[r]/n|0,s[r]%=n)}return s.reverse()}function Ie(e,i){var n,r,s;if(i.isZero())return i;r=i.d.length,r<32?(n=Math.ceil(r/3),s=(1/ne(4,n)).toString()):(n=16,s="2.3283064365386962890625e-10"),e.precision+=n,i=$(e,1,i.times(s),new e(1));for(var t=n;t--;){var o=i.times(i);i=o.times(o).minus(o).times(8).plus(1)}return e.precision-=n,i}var k=function(){function e(r,s,t){var o,u=0,c=r.length;for(r=r.slice();c--;)o=r[c]*s+u,r[c]=o%t|0,u=o/t|0;return u&&r.unshift(u),r}function i(r,s,t,o){var u,c;if(t!=o)c=t>o?1:-1;else for(u=c=0;u<t;u++)if(r[u]!=s[u]){c=r[u]>s[u]?1:-1;break}return c}function n(r,s,t,o){for(var u=0;t--;)r[t]-=u,u=r[t]<s[t]?1:0,r[t]=u*o+r[t]-s[t];for(;!r[0]&&r.length>1;)r.shift()}return function(r,s,t,o,u,c){var f,l,a,h,g,v,N,F,q,D,E,P,X,Z,te,J,G,se,I,K,Q=r.constructor,oe=r.s==s.s?1:-1,A=r.d,M=s.d;if(!A||!A[0]||!M||!M[0])return new Q(!r.s||!s.s||(A?M&&A[0]==M[0]:!M)?NaN:A&&A[0]==0||!M?oe*0:oe/0);for(c?(g=1,l=r.e-s.e):(c=T,g=w,l=L(r.e/g)-L(s.e/g)),I=M.length,G=A.length,q=new Q(oe),D=q.d=[],a=0;M[a]==(A[a]||0);a++);if(M[a]>(A[a]||0)&&l--,t==null?(Z=t=Q.precision,o=Q.rounding):u?Z=t+(r.e-s.e)+1:Z=t,Z<0)D.push(1),v=!0;else{if(Z=Z/g+2|0,a=0,I==1){for(h=0,M=M[0],Z++;(a<G||h)&&Z--;a++)te=h*c+(A[a]||0),D[a]=te/M|0,h=te%M|0;v=h||a<G}else{for(h=c/(M[0]+1)|0,h>1&&(M=e(M,h,c),A=e(A,h,c),I=M.length,G=A.length),J=I,E=A.slice(0,I),P=E.length;P<I;)E[P++]=0;K=M.slice(),K.unshift(0),se=M[0],M[1]>=c/2&&++se;do h=0,f=i(M,E,I,P),f<0?(X=E[0],I!=P&&(X=X*c+(E[1]||0)),h=X/se|0,h>1?(h>=c&&(h=c-1),N=e(M,h,c),F=N.length,P=E.length,f=i(N,E,F,P),f==1&&(h--,n(N,I<F?K:M,F,c))):(h==0&&(f=h=1),N=M.slice()),F=N.length,F<P&&N.unshift(0),n(E,N,P,c),f==-1&&(P=E.length,f=i(M,E,I,P),f<1&&(h++,n(E,I<P?K:M,P,c))),P=E.length):f===0&&(h++,E=[0]),D[a++]=h,f&&E[0]?E[P++]=A[J]||0:(E=[A[J]],P=1);while((J++<G||E[0]!==void 0)&&Z--);v=E[0]!==void 0}D[0]||D.shift()}if(g==1)q.e=l,we=v;else{for(a=1,h=D[0];h>=10;h/=10)a++;q.e=a+l*g-1,p(q,u?t+q.e+1:t,o,v)}return q}}();function p(e,i,n,r){var s,t,o,u,c,f,l,a,h,g=e.constructor;e:if(i!=null){if(a=e.d,!a)return e;for(s=1,u=a[0];u>=10;u/=10)s++;if(t=i-s,t<0)t+=w,o=i,l=a[h=0],c=l/C(10,s-o-1)%10|0;else if(h=Math.ceil((t+1)/w),u=a.length,h>=u)if(r){for(;u++<=h;)a.push(0);l=c=0,s=1,t%=w,o=t-w+1}else break e;else{for(l=u=a[h],s=1;u>=10;u/=10)s++;t%=w,o=t-w+s,c=o<0?0:l/C(10,s-o-1)%10|0}if(r=r||i<0||a[h+1]!==void 0||(o<0?l:l%C(10,s-o-1)),f=n<4?(c||r)&&(n==0||n==(e.s<0?3:2)):c>5||c==5&&(n==4||r||n==6&&(t>0?o>0?l/C(10,s-o):0:a[h-1])%10&1||n==(e.s<0?8:7)),i<1||!a[0])return a.length=0,f?(i-=e.e+1,a[0]=C(10,(w-i%w)%w),e.e=-i||0):a[0]=e.e=0,e;if(t==0?(a.length=h,u=1,h--):(a.length=h+1,u=C(10,w-t),a[h]=o>0?(l/C(10,s-o)%C(10,o)|0)*u:0),f)for(;;)if(h==0){for(t=1,o=a[0];o>=10;o/=10)t++;for(o=a[0]+=u,u=1;o>=10;o/=10)u++;t!=u&&(e.e++,a[0]==T&&(a[0]=1));break}else{if(a[h]+=u,a[h]!=T)break;a[h--]=0,u=1}for(t=a.length;a[--t]===0;)a.pop()}return m&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function R(e,i,n){if(!e.isFinite())return Ce(e);var r,s=e.e,t=S(e.d),o=t.length;return i?(n&&(r=n-o)>0?t=t.charAt(0)+"."+t.slice(1)+b(r):o>1&&(t=t.charAt(0)+"."+t.slice(1)),t=t+(e.e<0?"e":"e+")+e.e):s<0?(t="0."+b(-s-1)+t,n&&(r=n-o)>0&&(t+=b(r))):s>=o?(t+=b(s+1-o),n&&(r=n-s-1)>0&&(t=t+"."+b(r))):((r=s+1)<o&&(t=t.slice(0,r)+"."+t.slice(r)),n&&(r=n-o)>0&&(s+1===o&&(t+="."),t+=b(r))),t}function ie(e,i){var n=e[0];for(i*=w;n>=10;n/=10)i++;return i}function y(e,i,n){if(i>De)throw m=!0,n&&(e.precision=n),Error(me);return p(new e(z),i,1,!0)}function _(e,i,n){if(i>le)throw Error(me);return p(new e(x),i,n,!0)}function ke(e){var i=e.length-1,n=i*w+1;if(i=e[i],i){for(;i%10==0;i/=10)n--;for(i=e[0];i>=10;i/=10)n++}return n}function b(e){for(var i="";e--;)i+="0";return i}function Me(e,i,n,r){var s,t=new e(1),o=Math.ceil(r/w+4);for(m=!1;;){if(n%2&&(t=t.times(i),pe(t.d,o)&&(s=!0)),n=L(n/2),n===0){n=t.d.length-1,s&&t.d[n]===0&&++t.d[n];break}i=i.times(i),pe(i.d,o)}return m=!0,t}function de(e){return e.d[e.d.length-1]&1}function qe(e,i,n){for(var r,s,t=new e(i[0]),o=0;++o<i.length;){if(s=new e(i[o]),!s.s){t=s;break}r=t.cmp(s),(r===n||r===0&&t.s===n)&&(t=s)}return t}function ae(e,i){var n,r,s,t,o,u,c,f=0,l=0,a=0,h=e.constructor,g=h.rounding,v=h.precision;if(!e.d||!e.d[0]||e.e>17)return new h(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(i==null?(m=!1,c=v):c=i,u=new h(.03125);e.e>-2;)e=e.times(u),a+=5;for(r=Math.log(C(2,a))/Math.LN10*2+5|0,c+=r,n=t=o=new h(1),h.precision=c;;){if(t=p(t.times(e),c,1),n=n.times(++l),u=o.plus(k(t,n,c,1)),S(u.d).slice(0,c)===S(o.d).slice(0,c)){for(s=a;s--;)o=p(o.times(o),c,1);if(i==null)if(f<3&&W(o.d,c-r,g,f))h.precision=c+=10,n=t=u=new h(1),l=0,f++;else return p(o,h.precision=v,g,m=!0);else return h.precision=v,o}o=u}}function B(e,i){var n,r,s,t,o,u,c,f,l,a,h,g=1,v=10,N=e,F=N.d,q=N.constructor,D=q.rounding,E=q.precision;if(N.s<0||!F||!F[0]||!N.e&&F[0]==1&&F.length==1)return new q(F&&!F[0]?-1/0:N.s!=1?NaN:F?0:N);if(i==null?(m=!1,l=E):l=i,q.precision=l+=v,n=S(F),r=n.charAt(0),Math.abs(t=N.e)<15e14){for(;r<7&&r!=1||r==1&&n.charAt(1)>3;)N=N.times(e),n=S(N.d),r=n.charAt(0),g++;t=N.e,r>1?(N=new q("0."+n),t++):N=new q(r+"."+n.slice(1))}else return f=y(q,l+2,E).times(t+""),N=B(new q(r+"."+n.slice(1)),l-v).plus(f),q.precision=E,i==null?p(N,E,D,m=!0):N;for(a=N,c=o=N=k(N.minus(1),N.plus(1),l,1),h=p(N.times(N),l,1),s=3;;){if(o=p(o.times(h),l,1),f=c.plus(k(o,new q(s),l,1)),S(f.d).slice(0,l)===S(c.d).slice(0,l))if(c=c.times(2),t!==0&&(c=c.plus(y(q,l+2,E).times(t+""))),c=k(c,new q(g),l,1),i==null)if(W(c.d,l-v,D,u))q.precision=l+=v,f=o=N=k(a.minus(1),a.plus(1),l,1),h=p(N.times(N),l,1),s=u=1;else return p(c,q.precision=E,D,m=!0);else return q.precision=E,c;c=f,s+=2}}function Ce(e){return String(e.s*e.s/0)}function j(e,i){var n,r,s;for((n=i.indexOf("."))>-1&&(i=i.replace(".","")),(r=i.search(/e/i))>0?(n<0&&(n=r),n+=+i.slice(r+1),i=i.substring(0,r)):n<0&&(n=i.length),r=0;i.charCodeAt(r)===48;r++);for(s=i.length;i.charCodeAt(s-1)===48;--s);if(i=i.slice(r,s),i){if(s-=r,e.e=n=n-r-1,e.d=[],r=(n+1)%w,n<0&&(r+=w),r<s){for(r&&e.d.push(+i.slice(0,r)),s-=w;r<s;)e.d.push(+i.slice(r,r+=w));i=i.slice(r),r=w-i.length}else r-=s;for(;r--;)i+="0";e.d.push(+i),m&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function Te(e,i){var n,r,s,t,o,u,c,f,l;if(i.indexOf("_")>-1){if(i=i.replace(/(\d)_(?=\d)/g,"$1"),Ee.test(i))return j(e,i)}else if(i==="Infinity"||i==="NaN")return+i||(e.s=NaN),e.e=NaN,e.d=null,e;if(Le.test(i))n=16,i=i.toLowerCase();else if(Ae.test(i))n=2;else if(Fe.test(i))n=8;else throw Error(H+i);for(t=i.search(/p/i),t>0?(c=+i.slice(t+1),i=i.substring(2,t)):i=i.slice(2),t=i.indexOf("."),o=t>=0,r=e.constructor,o&&(i=i.replace(".",""),u=i.length,t=u-t,s=Me(r,new r(n),t,t*2)),f=Y(i,n,T),l=f.length-1,t=l;f[t]===0;--t)f.pop();return t<0?new r(e.s*0):(e.e=ie(f,l),e.d=f,m=!1,o&&(e=k(e,s,u*4)),c&&(e=e.times(Math.abs(c)<54?C(2,c):re.pow(2,c))),m=!0,e)}function _e(e,i){var n,r=i.d.length;if(r<3)return i.isZero()?i:$(e,2,i,i);n=1.4*Math.sqrt(r),n=n>16?16:n|0,i=i.times(1/ne(5,n)),i=$(e,2,i,i);for(var s,t=new e(5),o=new e(16),u=new e(20);n--;)s=i.times(i),i=i.times(t.plus(s.times(o.times(s).minus(u))));return i}function $(e,i,n,r,s){var t,o,u,c,f=e.precision,l=Math.ceil(f/w);for(m=!1,c=n.times(n),u=new e(r);;){if(o=k(u.times(c),new e(i++*i++),f,1),u=s?r.plus(o):r.minus(o),r=k(o.times(c),new e(i++*i++),f,1),o=u.plus(r),o.d[l]!==void 0){for(t=l;o.d[t]===u.d[t]&&t--;);if(t==-1)break}t=u,u=r,r=o,o=t}return m=!0,o.d.length=l+1,o}function ne(e,i){for(var n=e;--i;)n*=e;return n}function Se(e,i){var n,r=i.s<0,s=_(e,e.precision,1),t=s.times(.5);if(i=i.abs(),i.lte(t))return U=r?4:1,i;if(n=i.divToInt(s),n.isZero())U=r?3:2;else{if(i=i.minus(n.times(s)),i.lte(t))return U=de(n)?r?2:3:r?4:1,i;U=de(n)?r?1:4:r?3:2}return i.minus(s).abs()}function he(e,i,n,r){var s,t,o,u,c,f,l,a,h,g=e.constructor,v=n!==void 0;if(v?(O(n,1,V),r===void 0?r=g.rounding:O(r,0,8)):(n=g.precision,r=g.rounding),!e.isFinite())l=Ce(e);else{for(l=R(e),o=l.indexOf("."),v?(s=2,i==16?n=n*4-3:i==8&&(n=n*3-2)):s=i,o>=0&&(l=l.replace(".",""),h=new g(1),h.e=l.length-o,h.d=Y(R(h),10,s),h.e=h.d.length),a=Y(l,10,s),t=c=a.length;a[--c]==0;)a.pop();if(!a[0])l=v?"0p+0":"0";else{if(o<0?t--:(e=new g(e),e.d=a,e.e=t,e=k(e,h,n,r,0,s),a=e.d,t=e.e,f=we),o=a[n],u=s/2,f=f||a[n+1]!==void 0,f=r<4?(o!==void 0||f)&&(r===0||r===(e.s<0?3:2)):o>u||o===u&&(r===4||f||r===6&&a[n-1]&1||r===(e.s<0?8:7)),a.length=n,f)for(;++a[--n]>s-1;)a[n]=0,n||(++t,a.unshift(1));for(c=a.length;!a[c-1];--c);for(o=0,l="";o<c;o++)l+=fe.charAt(a[o]);if(v){if(c>1)if(i==16||i==8){for(o=i==16?4:3,--c;c%o;c++)l+="0";for(a=Y(l,s,i),c=a.length;!a[c-1];--c);for(o=1,l="1.";o<c;o++)l+=fe.charAt(a[o])}else l=l.charAt(0)+"."+l.slice(1);l=l+(t<0?"p":"p+")+t}else if(t<0){for(;++t;)l="0"+l;l="0."+l}else if(++t>c)for(t-=c;t--;)l+="0";else t<c&&(l=l.slice(0,t)+"."+l.slice(t))}l=(i==16?"0x":i==2?"0b":i==8?"0o":"")+l}return e.s<0?"-"+l:l}function pe(e,i){if(e.length>i)return e.length=i,!0}function Re(e){return new this(e).abs()}function Ze(e){return new this(e).acos()}function Ue(e){return new this(e).acosh()}function be(e,i){return new this(e).plus(i)}function Be(e){return new this(e).asin()}function He(e){return new this(e).asinh()}function Ve(e){return new this(e).atan()}function $e(e){return new this(e).atanh()}function Ge(e,i){e=new this(e),i=new this(i);var n,r=this.precision,s=this.rounding,t=r+4;return!e.s||!i.s?n=new this(NaN):!e.d&&!i.d?(n=_(this,t,1).times(i.s>0?.25:.75),n.s=e.s):!i.d||e.isZero()?(n=i.s<0?_(this,r,s):new this(0),n.s=e.s):!e.d||i.isZero()?(n=_(this,t,1).times(.5),n.s=e.s):i.s<0?(this.precision=t,this.rounding=1,n=this.atan(k(e,i,t,1)),i=_(this,t,1),this.precision=r,this.rounding=s,n=e.s<0?n.minus(i):n.plus(i)):n=this.atan(k(e,i,t,1)),n}function We(e){return new this(e).cbrt()}function Xe(e){return p(e=new this(e),e.e+1,2)}function Je(e,i,n){return new this(e).clamp(i,n)}function Ke(e){if(!e||typeof e!="object")throw Error(ee+"Object expected");var i,n,r,s=e.defaults===!0,t=["precision",1,V,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,ue,"maxE",0,ue,"minE",-9e15,0,"modulo",0,9];for(i=0;i<t.length;i+=3)if(n=t[i],s&&(this[n]=ce[n]),(r=e[n])!==void 0)if(L(r)===r&&r>=t[i+1]&&r<=t[i+2])this[n]=r;else throw Error(H+n+": "+r);if(n="crypto",s&&(this[n]=ce[n]),(r=e[n])!==void 0)if(r===!0||r===!1||r===0||r===1)if(r)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[n]=!0;else throw Error(Ne);else this[n]=!1;else throw Error(H+n+": "+r);return this}function Qe(e){return new this(e).cos()}function Ye(e){return new this(e).cosh()}function Pe(e){var i,n,r;function s(t){var o,u,c,f=this;if(!(f instanceof s))return new s(t);if(f.constructor=s,ge(t)){f.s=t.s,m?!t.d||t.e>s.maxE?(f.e=NaN,f.d=null):t.e<s.minE?(f.e=0,f.d=[0]):(f.e=t.e,f.d=t.d.slice()):(f.e=t.e,f.d=t.d?t.d.slice():t.d);return}if(c=typeof t,c==="number"){if(t===0){f.s=1/t<0?-1:1,f.e=0,f.d=[0];return}if(t<0?(t=-t,f.s=-1):f.s=1,t===~~t&&t<1e7){for(o=0,u=t;u>=10;u/=10)o++;m?o>s.maxE?(f.e=NaN,f.d=null):o<s.minE?(f.e=0,f.d=[0]):(f.e=o,f.d=[t]):(f.e=o,f.d=[t]);return}if(t*0!==0){t||(f.s=NaN),f.e=NaN,f.d=null;return}return j(f,t.toString())}if(c==="string")return(u=t.charCodeAt(0))===45?(t=t.slice(1),f.s=-1):(u===43&&(t=t.slice(1)),f.s=1),Ee.test(t)?j(f,t):Te(f,t);if(c==="bigint")return t<0?(t=-t,f.s=-1):f.s=1,j(f,t.toString());throw Error(H+t)}if(s.prototype=d,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=Ke,s.clone=Pe,s.isDecimal=ge,s.abs=Re,s.acos=Ze,s.acosh=Ue,s.add=be,s.asin=Be,s.asinh=He,s.atan=Ve,s.atanh=$e,s.atan2=Ge,s.cbrt=We,s.ceil=Xe,s.clamp=Je,s.cos=Qe,s.cosh=Ye,s.div=je,s.exp=ze,s.floor=xe,s.hypot=ye,s.ln=ei,s.log=ii,s.log10=ri,s.log2=ni,s.max=ti,s.min=si,s.mod=oi,s.mul=ui,s.pow=fi,s.random=ci,s.round=li,s.sign=ai,s.sin=hi,s.sinh=di,s.sqrt=pi,s.sub=gi,s.sum=wi,s.tan=mi,s.tanh=Ni,s.trunc=vi,e===void 0&&(e={}),e&&e.defaults!==!0)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<r.length;)e.hasOwnProperty(n=r[i++])||(e[n]=this[n]);return s.config(e),s}function je(e,i){return new this(e).div(i)}function ze(e){return new this(e).exp()}function xe(e){return p(e=new this(e),e.e+1,3)}function ye(){var e,i,n=new this(0);for(m=!1,e=0;e<arguments.length;)if(i=new this(arguments[e++]),i.d)n.d&&(n=n.plus(i.times(i)));else{if(i.s)return m=!0,new this(1/0);n=i}return m=!0,n.sqrt()}function ge(e){return e instanceof re||e&&e.toStringTag===ve||!1}function ei(e){return new this(e).ln()}function ii(e,i){return new this(e).log(i)}function ni(e){return new this(e).log(2)}function ri(e){return new this(e).log(10)}function ti(){return qe(this,arguments,-1)}function si(){return qe(this,arguments,1)}function oi(e,i){return new this(e).mod(i)}function ui(e,i){return new this(e).mul(i)}function fi(e,i){return new this(e).pow(i)}function ci(e){var i,n,r,s,t=0,o=new this(1),u=[];if(e===void 0?e=this.precision:O(e,1,V),r=Math.ceil(e/w),this.crypto)if(crypto.getRandomValues)for(i=crypto.getRandomValues(new Uint32Array(r));t<r;)s=i[t],s>=429e7?i[t]=crypto.getRandomValues(new Uint32Array(1))[0]:u[t++]=s%1e7;else if(crypto.randomBytes){for(i=crypto.randomBytes(r*=4);t<r;)s=i[t]+(i[t+1]<<8)+(i[t+2]<<16)+((i[t+3]&127)<<24),s>=214e7?crypto.randomBytes(4).copy(i,t):(u.push(s%1e7),t+=4);t=r/4}else throw Error(Ne);else for(;t<r;)u[t++]=Math.random()*1e7|0;for(r=u[--t],e%=w,r&&e&&(s=C(10,w-e),u[t]=(r/s|0)*s);u[t]===0;t--)u.pop();if(t<0)n=0,u=[0];else{for(n=-1;u[0]===0;n-=w)u.shift();for(r=1,s=u[0];s>=10;s/=10)r++;r<w&&(n-=w-r)}return o.e=n,o.d=u,o}function li(e){return p(e=new this(e),e.e+1,this.rounding)}function ai(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function hi(e){return new this(e).sin()}function di(e){return new this(e).sinh()}function pi(e){return new this(e).sqrt()}function gi(e,i){return new this(e).sub(i)}function wi(){var e=0,i=arguments,n=new this(i[e]);for(m=!1;n.s&&++e<i.length;)n=n.plus(i[e]);return m=!0,p(n,this.precision,this.rounding)}function mi(e){return new this(e).tan()}function Ni(e){return new this(e).tanh()}function vi(e){return p(e=new this(e),e.e+1,1)}d[Symbol.for("nodejs.util.inspect.custom")]=d.toString;d[Symbol.toStringTag]="Decimal";var re=d.constructor=Pe(ce);z=new re(z);x=new re(x);export{re as D};
