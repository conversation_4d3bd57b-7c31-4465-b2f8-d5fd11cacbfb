import{s as ke}from"./index-CEksU3zE.js";import{d as Ve,k as $e,l as Se,B as Te,i as Ue,r as y,Q as Y,c as Ie,H as Ee,a as g,p as h,u as a,o as f,e as o,b as t,t as r,s as Ne,Y as Re,j as b,q as x,w as l,y as Fe,a9 as je,F as te,m as k,X as Be,v as W}from"./index-BPwFJVSY.js";import{s as qe}from"./index-DlkyKXFh.js";import{s as Ae}from"./index-DfF1qu_m.js";import{s as De}from"./index-BlFKIn6Y.js";import{s as Le}from"./index-DgBXOSrM.js";import{s as Me}from"./index-BFXZ3UuW.js";import{s as Oe,a as Ye}from"./index-CxOnO8if.js";import{_ as We}from"./BaseCardType-BnVjnEGA.js";import{_ as He}from"./BaseTag-dd68UHCy.js";import{s as Ge}from"./index-B7pQ2UQX.js";import{s as Ke}from"./index-AtQkrpJ9.js";import{t as se}from"./vee-validate-zod-BFI0y82J.js";import{d as H}from"./dayjs.min-Bar1rHDB.js";import{_ as Qe}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{u as Xe}from"./useDict-sWoVNiZj.js";import{u as ze}from"./useListRefresh-D-tL0eHu.js";import{u as Je,P as V}from"./usePermissions-PkVCxleQ.js";import{d as Ze,e as et,u as tt,f as st}from"./customer-M3EzHCil.js";import{g as ot}from"./dict-CLPQcIyV.js";import{u as at}from"./plan-X86n_BnV.js";import{f as it}from"./date-D3KeWVvs.js";import{f as lt}from"./format-3aITXa0f.js";import{g as nt}from"./tagStatus-CiU0ihd_.js";import{_ as rt}from"./detailConfiguration.vue_vue_type_script_setup_true_lang-CPVVAjNe.js";import{_ as mt}from"./detailHistory.vue_vue_type_script_setup_true_lang-CNzrhA4W.js";import{_ as dt}from"./detailPayment.vue_vue_type_script_setup_true_lang-CnbFsttJ.js";import{_ as ct}from"./detailSubscriptionsUpdate.vue_vue_type_script_setup_true_lang-BEt0HOO8.js";import{o as oe,d as ut,s as j,n as pt}from"./types-AmnC9OGP.js";import{_ as ft}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-BjQt0t51.js";import"./index-Cbodd6BQ.js";import"./index-wTWY-tN1.js";import"./index-kWy1s9tq.js";import"./index-BJi8tw4J.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./getAssetsFile-DC-dlhh8.js";import"./BillbuddyLogo-FvDXaJ4h.js";import"./PMI-logo-4SeFW8S8.js";import"./logo-CiIwT2Ho.js";import"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import"./color-DqhIgFrh.js";import"./invioce-payment-Bs21HoFI.js";import"./isUpdated-DXQm7BuR.js";import"./login-bg-kShksnUz.js";import"./plan-DCOwcHy2.js";import"./invoice-B6mg1HKE.js";import"./transaction-CqiD1t2Y.js";import"./BaseDataTable-BVkRnzNI.js";import"./useRequestList-DvWcnSzt.js";import"./transactions-CkZWY3pR.js";import"./PlanSubscriptionForm-Dsrtpj9s.js";import"./EnhancedDatePicker-DfTmBAld.js";import"./customer-v2cMYA98.js";import"./usePlanForm-WYcUl8Fa.js";import"./integrations-C_ojvdY3.js";const yt={key:0,class:"customer-detail rounded-lg"},vt={key:0,class:"flex justify-center items-center py-20"},_t={class:"flex flex-col items-center"},bt={class:"mt-4 text-gray-500"},xt={key:1,class:"flex justify-center items-center py-20"},ht={class:"flex flex-col items-center"},gt={class:"text-red-500 font-medium mb-2"},wt={class:"text-gray-500 mb-4"},Pt={key:2,class:"customer-content flex justify-between items-start"},Ct={class:"customer-details bg-[#09deff] text-[#181349] rounded-lg px-6 py-6 w-1/4 mr-6"},kt={class:"flex justify-end items-center"},Vt={class:"flex justify-between items-center mb-4 detail-left-title"},$t={class:"text-[28px] font-semibold my-2"},St={class:"customer-details"},Tt={class:"details-edit"},Ut={class:"flex items-center gap-2 px-2 py-2 detail-left-item cursor-pointer min-h-8"},It={class:"details-edit"},Et=["title"],Nt={class:"details-edit"},Rt=["title"],Ft={class:"details-edit"},jt=["title"],Bt={class:"customer-content rounded-lg w-3/4"},qt={class:"flex justify-between items-center"},At={class:"tags bg-white rounded-lg w-2/3 px-6 py-4"},Dt={class:"text-[28px] font-bold my-4 flex justify-between items-center"},Lt={class:"flex items-center gap-2"},Mt={class:"customer-content-left bg-white rounded-lg p-4"},Ot={class:"customer-subscriptions mb-8"},Yt={class:"customer-subscriptions-table"},Wt={class:"mr-4"},Ht={class:"mr-2"},Gt={class:"mr-2"},Kt={key:0,class:"flex items-center"},Qt={class:"flex justify-end gap-2"},Xt={class:"flex gap-2"},zt={class:"customer-payments mb-8"},Jt={class:"customer-payments-table relative"},Zt={class:"mb-8"},es={class:"flex justify-center items-start px-4"},ts={class:"configuration w-1/2 mr-6"},ss={class:"history w-1/2"},os={key:3,class:"flex justify-center items-center py-20"},as={class:"flex flex-col items-center"},is={class:"text-gray-500"},ls={class:"mb-2 block"},ns={class:"mb-2 block"},rs={class:"mb-2 block"},ms={class:"flex justify-end gap-2"},ds={class:"grid gap-4"},cs={class:"mb-2 block"},us={class:"mb-2 block"},ps={class:"mb-2 block"},fs={class:"p-8 create-invoice-dialog"},ys={class:"mt-6 flex justify-center gap-4"},vs=Ve({__name:"detail",setup(_s){const{backWithRefresh:ae}=ze("customersList",()=>{}),{hasPermission:w}=Je(),{t:i}=$e(),ie=Re(),B=Se(),_=Te(),G=Ue(),K=y(),q=y(!0),S=y(null),le=se(oe({amount:pt({required_error:i("customersPage.payments.enterAmount")}).min(.01,i("customersPage.payments.amountMustBePositive")),description:j(),date:ut({required_error:i("customersPage.payments.selectDate")})})),ne=se(oe({name:j().min(1,i("validation.required")),email_primary:j().email(i("validation.emailInvalid")),phone_mobile:j().min(1,i("validation.required"))})),{getLabel:re}=Xe("subscription_status"),A=y(),Q=y(null),m=y(null),T=y(!1),U=y(!1),$=y(!1),D=y(!1),I=y(!1),L=y(!1),p=Y({amount:0,description:"",date:new Date}),v=Y({name:"",email_primary:"",phone_mobile:""}),E=Y({planId:"",customerId:""}),me=y([]),de=n=>{w(V.SUBSCRIPTION_CANCEL)&&window.$confirm.require({message:`
            Are you sure you want to cancel?
            Last Payment Date: ${(n==null?void 0:n.last_payment_date)||""}
            `,header:"WARNING: This can’t be undone.",icon:"pi pi-exclamation-triangle",rejectProps:{label:"NO, KEEP SUBSCRIPTION",severity:"secondary",outlined:!0},acceptProps:{label:"YES, CANCEL"},accept:async()=>{var c;const{code:e}=await Ze(n.plan_id,(c=m.value)==null?void 0:c.customer_id);e===0&&(window.$toast.add({severity:"success",summary:i("common.success")}),await N())},reject:()=>{}})},X=y(""),ce=async n=>{w(V.SUBSCRIPTION_UPDATE)&&(X.value=n.plan_id,E.customerId=_.params.id,E.planId=X.value,I.value=!0)},ue=async()=>{var e,c;if(!(!w(V.TRANS_CREATE)||!(await((e=A.value)==null?void 0:e.validate())).valid))try{U.value=!0;const{code:d}=await et({customer_id:_.params.id,amount:p.amount,description:p.description,date:H(p.date).format("YYYY-MM-DD")});d===0&&(window.$toast.add({severity:"success",summary:i("common.success"),detail:i("customersPage.payments.paymentCreated"),life:3e3}),T.value=!1,(c=K.value)==null||c.search(),ae())}catch(d){console.log(d)}finally{U.value=!1}},pe=()=>{var n,e,c,d;w(V.CUSTOMER_UPDATE)&&(B.activeBid=(n=m.value)==null?void 0:n.business_id,v.name=((e=m.value)==null?void 0:e.name)||"",v.email_primary=((c=m.value)==null?void 0:c.email_primary)||"",v.phone_mobile=((d=m.value)==null?void 0:d.phone_mobile)||"",$.value=!0)},z=async()=>{var e;if(!(!w(V.CUSTOMER_UPDATE)||!(await((e=Q.value)==null?void 0:e.validate())).valid))try{D.value=!0;const{code:c}=await tt({...m.value,customer_id:_.params.id,customer_name:v.name,email_primary:v.email_primary,phone_mobile:v.phone_mobile});c===0&&(window.$toast.add({severity:"success",summary:i("common.success"),detail:i("common.saveSuccess")||"保存成功"}),$.value=!1,await N())}catch(c){console.error("Failed to update customer:",c),window.$toast.add({severity:"error",summary:i("common.error"),detail:"Failed to update customer",life:3e3})}finally{D.value=!1}},N=async()=>{var n;try{const{code:e,data:c}=await st(String((n=_.params)==null?void 0:n.id));e===0&&(m.value=c)}catch(e){console.error("获取客户详情失败:",e)}},fe=n=>{w(V.SUBSCRIPTION_MOD_PAYMENT_METHOD)&&window.$confirm.require({message:"Are you sure you want to update the payment method?",header:"WARNING: This can’t be undone.",icon:"pi pi-exclamation-triangle",acceptProps:{label:"YES, UPDATE"},accept:async()=>{try{const{code:e}=await at({plan_id:n.plan_id,customer_id:n.customer_id});e===0&&window.$toast.add({severity:"success",summary:i("common.success"),detail:i("customersPage.payments.paymentUpdated")})}catch(e){console.error("Failed to update payment method:",e),window.$toast.add({severity:"error",summary:i("common.error"),detail:"Failed to update payment method"})}}})},M=async()=>{var n;q.value=!0,S.value=null;try{await Promise.all([N(),ot("plan_process_type").then(e=>{e.code===0&&(me.value=e.data)})])}catch{S.value=i("common.requestFailed"),(n=window.$toast)==null||n.add({severity:"error",summary:i("common.error"),detail:i("common.requestFailedDescription"),life:5e3})}finally{q.value=!1}};M();const ye=Ie(()=>{var c;const n=H((c=m.value)==null?void 0:c.created_at);return H().diff(n,"day")<=7}),ve=()=>{var n;B.activeBid=(n=m.value)==null?void 0:n.business_id,G.push({path:"/customers/createInvite",query:{customer_id:_.params.id}})},_e=()=>{var n,e,c,d,R;B.activeBid=(n=m.value)==null?void 0:n.business_id,G.push({path:`/customers/detail/${_.params.id}/createInvoice`,query:{name:(e=m.value)==null?void 0:e.name,phone:(c=m.value)==null?void 0:c.phone_mobile,email:(d=m.value)==null?void 0:d.email_primary,address:(R=m.value)==null?void 0:R.address,customer_id:_.params.id}})};return(n,e)=>{var Z,ee;const c=Ke,d=Ne,R=Ge,P=Oe,be=He,xe=We,he=Ye,J=Me,ge=Le,C=De,we=Ae,Pe=qe,F=Be,O=ke,Ce=Ee("RouterView");return a(_).name==="customersDetail"?(f(),g("div",yt,[q.value?(f(),g("div",vt,[t("div",_t,[o(c,{style:{width:"50px",height:"50px"},"stroke-width":"4"}),t("span",bt,r(a(i)("common.loading")),1)])])):S.value?(f(),g("div",xt,[t("div",ht,[e[18]||(e[18]=t("i",{class:"pi pi-exclamation-triangle text-4xl text-red-500 mb-4"},null,-1)),t("span",gt,r(S.value),1),t("span",wt,r(a(i)("common.requestFailedDescription")),1),o(d,{icon:"pi pi-refresh",label:a(i)("common.refresh"),onClick:M},null,8,["label"])])])):m.value?(f(),g("div",Pt,[t("div",Ct,[t("div",kt,[o(d,{variant:"outlined",icon:"pi pi-pen-to-square",label:"Edit details",onClick:pe})]),t("div",Vt,[t("div",$t,r(m.value.name),1)]),t("div",St,[t("div",Tt,[e[19]||(e[19]=t("div",{class:"font-semibold mb-2"}," Customer ID ",-1)),t("div",Ut,[t("span",{onClick:e[0]||(e[0]=s=>{var u;return((u=m.value)==null?void 0:u.customer_id)&&a(ie)(m.value.customer_id)})},r((Z=m.value)==null?void 0:Z.customer_id),1)])]),t("div",It,[e[20]||(e[20]=t("div",{class:"font-semibold mb-2"}," Customer since ",-1)),t("div",{class:"flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8",title:a(i)("common.copy")},[t("span",null,r((ee=m.value)==null?void 0:ee.created_at),1)],8,Et)]),t("div",Nt,[e[21]||(e[21]=t("div",{class:"font-semibold mb-2"}," Phone Number ",-1)),t("div",{class:"flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8",title:a(i)("common.copy")},[t("span",null,r(m.value.phone_mobile),1)],8,Rt)]),t("div",Ft,[e[22]||(e[22]=t("div",{class:"font-semibold mb-2"}," Billing Email ",-1)),t("div",{class:"flex items-center gap-2 px-2 py-2 cursor-pointer detail-left-item min-h-8",title:a(i)("common.copy")},[t("span",null,r(m.value.email_primary),1)],8,jt)])])]),t("div",Bt,[t("div",qt,[t("div",At,[e[23]||(e[23]=b(" Tags: ")),ye.value?(f(),h(R,{key:0,severity:"success",value:"New Customer"})):x("",!0)])]),t("div",Dt,[e[24]||(e[24]=t("span",null,"Subscriptions",-1)),t("div",Lt,[o(d,{round:"",label:"Create Invoice",severity:"warn",onClick:e[1]||(e[1]=s=>L.value=!0)}),o(d,{icon:"pi pi-plus",round:"",onClick:ve})])]),t("div",Mt,[t("div",Ot,[t("div",Yt,[o(he,{value:m.value.customer_plan},{empty:l(()=>e[26]||(e[26]=[t("div",{class:"text-center text-gray-500"}," No plan ",-1)])),default:l(()=>[o(P,{header:"Plan"},{body:l(({data:s})=>[t("span",Wt,r(s.plan_name),1)]),_:1}),o(P,{header:"Status"},{body:l(({data:s})=>[o(be,{text:a(re)(s.status),type:a(nt)(s.status)},null,8,["text","type"])]),_:1}),o(P,{field:"frequency",header:"Frequency"},{body:l(({data:s})=>[t("span",Ht,r(a(lt)(s)),1)]),_:1}),o(P,{header:"Next payment date"},{body:l(({data:s})=>[t("span",Gt,[b(r(a(it)(s.next_process_date,"MMM DD"))+" ",1),s!=null&&s.amount?(f(),g(Fe,{key:0},[b(" For $ "+r(s.amount.amount)+" "+r(s.amount.currency),1)],64)):x("",!0)])]),_:1}),o(P,{header:"Pay method"},{body:l(({data:s})=>[s.account_no?(f(),g("div",Kt,[o(xe,{"card-type":s==null?void 0:s.credit_brand,text:s==null?void 0:s.account_no,"is-show-card-number":!0},null,8,["card-type","text"])])):x("",!0)]),_:1}),o(P,{class:"w-64 !text-end",style:{width:"50px"}},{header:l(()=>e[25]||(e[25]=[t("div",{class:"flex items-center justify-center flex-1"},[t("i",{class:"pi pi-cog !text-xl"})],-1)])),body:l(({data:s})=>[t("div",Qt,[o(Qe,null,{default:l(()=>[t("div",Xt,[o(d,{label:"Cancel",severity:"danger",onClick:u=>de(s)},null,8,["onClick"]),o(d,{label:"Update Pay method",severity:"danger",onClick:u=>fe(s)},null,8,["onClick"]),o(d,{label:"Update",severity:"info",onClick:u=>ce(s)},null,8,["onClick"])])]),_:2},1024)])]),_:1})]),_:1},8,["value"])])]),t("div",zt,[e[27]||(e[27]=t("div",{class:"customer-payments-title flex justify-between items-center"},[t("div",{class:"text-[28px] font-bold my-4"}," Transactions ")],-1)),o(J),t("div",Jt,[o(dt,{id:a(_).params.id,ref_key:"detailPaymentRef",ref:K},null,8,["id"])])]),t("div",Zt,[e[30]||(e[30]=t("div",{class:"flex justify-between items-center"},[t("div",{class:"text-[28px] font-bold my-4"}," Communication ")],-1)),o(J),t("div",es,[t("div",ts,[e[28]||(e[28]=t("div",{class:"text-[22px] font-bold my-4"}," Configuration ",-1)),o(rt,{"customer-id":a(_).params.id,list:m.value.communication_config},null,8,["customer-id","list"])]),t("div",ss,[e[29]||(e[29]=t("div",{class:"text-[22px] font-bold my-4"}," History ",-1)),o(mt,{"customer-id":a(_).params.id},null,8,["customer-id"])])])])])])])):(f(),g("div",os,[t("div",as,[e[31]||(e[31]=t("i",{class:"pi pi-info-circle text-4xl text-gray-400 mb-4"},null,-1)),t("span",is,r(a(i)("common.noDataFound")),1),o(d,{icon:"pi pi-refresh",label:a(i)("common.refresh"),class:"mt-4",onClick:M},null,8,["label"])])])),o(F,{visible:T.value,"onUpdate:visible":e[9]||(e[9]=s=>T.value=s),modal:"",header:a(i)("customersPage.payments.createPayment"),style:{width:"550px"}},je({default:l(()=>[o(a(te),{ref_key:"paymentFormRef",ref:A,class:"space-y-4","validation-schema":a(le)},{default:l(()=>[o(a(k),{modelValue:p.amount,"onUpdate:modelValue":e[3]||(e[3]=s=>p.amount=s),name:"amount",as:"div",class:"flex flex-col"},{default:l(({errorMessage:s})=>[t("label",ls,r(a(i)("customersPage.payments.amount")),1),o(ge,{modelValue:p.amount,"onUpdate:modelValue":e[2]||(e[2]=u=>p.amount=u),min:.01,mode:"currency",currency:"USD",locale:"en-US",class:"w-full"},null,8,["modelValue"]),s?(f(),h(C,{key:0,severity:"error",class:"mt-2 w-full",variant:"simple"},{default:l(()=>[b(r(s),1)]),_:2},1024)):x("",!0)]),_:1},8,["modelValue"]),o(a(k),{modelValue:p.description,"onUpdate:modelValue":e[5]||(e[5]=s=>p.description=s),name:"description",as:"div",class:"flex flex-col"},{default:l(({errorMessage:s})=>[t("label",ns,r(a(i)("customersPage.payments.description")),1),o(we,{modelValue:p.description,"onUpdate:modelValue":e[4]||(e[4]=u=>p.description=u),rows:"3",class:"w-full",placeholder:a(i)("customersPage.payments.enterDescription")},null,8,["modelValue","placeholder"]),s?(f(),h(C,{key:0,severity:"error",class:"mt-2",variant:"simple"},{default:l(()=>[b(r(s),1)]),_:2},1024)):x("",!0)]),_:1},8,["modelValue"]),o(a(k),{modelValue:p.date,"onUpdate:modelValue":e[7]||(e[7]=s=>p.date=s),name:"date",as:"div",class:"flex flex-col"},{default:l(({errorMessage:s})=>[t("label",rs,r(a(i)("customersPage.payments.date")),1),o(Pe,{modelValue:p.date,"onUpdate:modelValue":e[6]||(e[6]=u=>p.date=u),class:"w-full"},null,8,["modelValue"]),s?(f(),h(C,{key:0,severity:"error",class:"mt-2",variant:"simple"},{default:l(()=>[b(r(s),1)]),_:2},1024)):x("",!0)]),_:1},8,["modelValue"])]),_:1},8,["validation-schema"])]),_:2},[A.value?{name:"footer",fn:l(()=>[t("div",ms,[o(d,{label:a(i)("customersPage.payments.cancelPayment"),severity:"secondary",loading:U.value,onClick:e[8]||(e[8]=s=>T.value=!1)},null,8,["label","loading"]),o(d,{label:a(i)("customersPage.payments.confirmPayment"),loading:U.value,onClick:ue},null,8,["label","loading"])])]),key:"0"}:void 0]),1032,["visible","header"]),o(F,{visible:$.value,"onUpdate:visible":e[14]||(e[14]=s=>$.value=s),header:a(i)("customersPage.dialogs.editCustomer"),modal:!0,class:"p-fluid",style:{width:"550px"}},{footer:l(()=>[o(d,{label:a(i)("common.cancel"),icon:"pi pi-times",text:"",onClick:e[13]||(e[13]=s=>$.value=!1)},null,8,["label"]),o(d,{label:a(i)("common.save"),icon:"pi pi-check",loading:D.value,onClick:z},null,8,["label","loading"])]),default:l(()=>[o(a(te),{ref_key:"customerEditFormRef",ref:Q,"validation-schema":a(ne),onSubmit:z},{default:l(()=>[t("div",ds,[o(a(k),{modelValue:v.name,"onUpdate:modelValue":e[10]||(e[10]=s=>v.name=s),name:"name",as:"div",class:"flex flex-col"},{default:l(({field:s,errorMessage:u})=>[t("label",cs,r(a(i)("customersPage.form.customerName")),1),o(O,W(s,{placeholder:a(i)("customersPage.form.customerNamePlaceholder"),class:"w-full"}),null,16,["placeholder"]),u?(f(),h(C,{key:0,severity:"error",class:"mt-2",variant:"simple"},{default:l(()=>[b(r(u),1)]),_:2},1024)):x("",!0)]),_:1},8,["modelValue"]),o(a(k),{modelValue:v.email_primary,"onUpdate:modelValue":e[11]||(e[11]=s=>v.email_primary=s),name:"email_primary",as:"div",class:"flex flex-col"},{default:l(({field:s,errorMessage:u})=>[t("label",us,r(a(i)("customersPage.form.email")),1),o(O,W(s,{placeholder:a(i)("customersPage.form.emailPlaceholder"),class:"w-full"}),null,16,["placeholder"]),u?(f(),h(C,{key:0,severity:"error",class:"mt-2",variant:"simple"},{default:l(()=>[b(r(u),1)]),_:2},1024)):x("",!0)]),_:1},8,["modelValue"]),o(a(k),{modelValue:v.phone_mobile,"onUpdate:modelValue":e[12]||(e[12]=s=>v.phone_mobile=s),name:"phone_mobile",as:"div",class:"flex flex-col"},{default:l(({field:s,errorMessage:u})=>[t("label",ps,r(a(i)("customersPage.form.phoneNumber")),1),o(O,W(s,{placeholder:a(i)("customersPage.form.phoneNumberPlaceholder"),class:"w-full"}),null,16,["placeholder"]),u?(f(),h(C,{key:0,severity:"error",class:"mt-2",variant:"simple"},{default:l(()=>[b(r(u),1)]),_:2},1024)):x("",!0)]),_:1},8,["modelValue"])])]),_:1},8,["validation-schema"])]),_:1},8,["visible","header"]),o(F,{visible:I.value,"onUpdate:visible":e[16]||(e[16]=s=>I.value=s),header:"Update",modal:!0,class:"p-fluid",style:{width:"850px"}},{default:l(()=>[o(ct,{"customer-id":E.customerId,"plan-id":E.planId,"onUpdate:success":e[15]||(e[15]=()=>{N(),I.value=!1})},null,8,["customer-id","plan-id"])]),_:1},8,["visible"]),o(F,{visible:L.value,"onUpdate:visible":e[17]||(e[17]=s=>L.value=s),"dismissable-mask":!0,modal:!0,"pt:root:class":"!border-0 !bg-[#ffe3e8] !rounded-[20px]","pt:mask:class":"backdrop-blur-sm",style:{width:"500px"}},{container:l(({closeCallback:s})=>[t("div",fs,[e[32]||(e[32]=t("div",{class:"text-xl text-[#1B1548]"}," This invoice is through your PayMyInvoice service, as is used to receive once off payments from your customer. Would you like to proceed? ",-1)),t("div",ys,[o(d,{label:"CANCEL",severity:"primary",class:"btn-pop",onClick:s},null,8,["onClick"]),o(d,{label:"YES",severity:"warn",class:"btn-pop",onClick:_e})])])]),_:1},8,["visible"])])):(f(),h(Ce,{key:1}))}}}),ko=ft(vs,[["__scopeId","data-v-38556b3e"]]);export{ko as default};
