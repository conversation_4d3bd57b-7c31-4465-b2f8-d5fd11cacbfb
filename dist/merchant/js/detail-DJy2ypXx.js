import{_ as E}from"./BaseTag-dd68UHCy.js";import{s as N}from"./index-AtQkrpJ9.js";import{d as R,J as j,B as $,i as F,r as y,f as I,a as l,o,b as t,e as n,p as T,q as b,s as U,t as i,u as g,bE as V,bF as P}from"./index-BPwFJVSY.js";import{f as A}from"./date-D3KeWVvs.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./dayjs.min-Bar1rHDB.js";const J={class:"bid-account-detail-page"},M={class:"card"},z={class:"card-header"},G={class:"flex items-center justify-between"},H={class:"flex gap-2"},K={key:0,class:"card-body"},L={class:"flex justify-center items-center py-8"},O={key:1,class:"card-body"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},W={class:"detail-section"},X={class:"detail-item"},Y={class:"value"},Z={class:"detail-item"},tt={class:"value"},et={class:"detail-item"},st={class:"value"},at={class:"detail-item"},it={class:"value"},ot={class:"detail-item"},lt={class:"value"},nt={class:"detail-section"},dt={class:"detail-item"},ct={class:"value"},rt={class:"detail-item"},ut={class:"value"},vt={class:"detail-item"},_t={class:"value"},pt={class:"detail-item"},mt={class:"value"},ft={class:"detail-item"},yt={class:"value"},bt={key:0,class:"mt-6 pt-6 border-t border-gray-200"},gt={class:"flex gap-3"},At={key:2,class:"card-body"},Bt=R({name:"userBidAccountDetail",__name:"detail",setup(kt){const c=j(),B=$(),u=F(),r=y(!1),s=y(null),v=async()=>{const a=B.params.id;if(a){r.value=!0;try{const e=await V(a);s.value=e.data}catch(e){c.add({severity:"error",summary:"Error",detail:e.message||"Failed to fetch account details",life:3e3})}finally{r.value=!1}}},k=a=>{switch(a){case 1:return"Approved";case 2:return"Rejected";case 3:return"Pending";default:return"Unknown"}},h=a=>{switch(a){case 1:return"paid";case 2:return"failed";case 3:return"pending";default:return"pending"}},x=()=>{u.push("/user/bidAccount/list")},w=()=>{s.value&&u.push(`/user/bidAccount/edit/${s.value.id}`)},_=async a=>{if(s.value)try{await P({id:s.value.id.toString(),type:a}),c.add({severity:"success",summary:"Success",detail:"Account status updated successfully",life:3e3}),await v()}catch(e){c.add({severity:"error",summary:"Error",detail:e.message||"Failed to update account status",life:3e3})}};return I(()=>{v()}),(a,e)=>{var p,m,f;const d=U,S=N,C=E;return o(),l("div",J,[t("div",M,[t("div",z,[t("div",G,[e[2]||(e[2]=t("h2",null,"Bid Account Detail",-1)),t("div",H,[n(d,{label:"Back",severity:"secondary",icon:"pi pi-arrow-left",onClick:x}),s.value?(o(),T(d,{key:0,label:"Edit",severity:"warn",icon:"pi pi-pencil",onClick:w})):b("",!0)])])]),r.value?(o(),l("div",K,[t("div",L,[n(S)])])):s.value?(o(),l("div",O,[t("div",Q,[t("div",W,[e[8]||(e[8]=t("h3",{class:"section-title"}," Basic Information ",-1)),t("div",X,[e[3]||(e[3]=t("label",null,"Business",-1)),t("div",Y,i(s.value.business_id),1)]),t("div",Z,[e[4]||(e[4]=t("label",null,"Account Name",-1)),t("div",tt,i(s.value.account_name),1)]),t("div",et,[e[5]||(e[5]=t("label",null,"BSB",-1)),t("div",st,i(s.value.bsb),1)]),t("div",at,[e[6]||(e[6]=t("label",null,"Account Number",-1)),t("div",it,i(s.value.account_no),1)]),t("div",ot,[e[7]||(e[7]=t("label",null,"Status",-1)),t("div",lt,[n(C,{text:k(s.value.status),type:h(s.value.status)},null,8,["text","type"])])])]),t("div",nt,[e[14]||(e[14]=t("h3",{class:"section-title"}," User Information ",-1)),t("div",dt,[e[9]||(e[9]=t("label",null,"Creator",-1)),t("div",ct,i(((p=s.value.user)==null?void 0:p.name)||"-"),1)]),t("div",rt,[e[10]||(e[10]=t("label",null,"Reviewer",-1)),t("div",ut,i(((m=s.value.reviewer)==null?void 0:m.name)||"-"),1)]),t("div",vt,[e[11]||(e[11]=t("label",null,"Admin",-1)),t("div",_t,i(((f=s.value.admin)==null?void 0:f.name)||"-"),1)]),t("div",pt,[e[12]||(e[12]=t("label",null,"Created Date",-1)),t("div",mt,i(g(A)(s.value.created_at)),1)]),t("div",ft,[e[13]||(e[13]=t("label",null,"Updated Date",-1)),t("div",yt,i(g(A)(s.value.updated_at)),1)])])]),s.value.status===3?(o(),l("div",bt,[e[15]||(e[15]=t("h3",{class:"section-title"}," Actions ",-1)),t("div",gt,[n(d,{label:"Approve",severity:"success",icon:"pi pi-check",onClick:e[0]||(e[0]=D=>_("3"))}),n(d,{label:"Reject",severity:"danger",icon:"pi pi-times",onClick:e[1]||(e[1]=D=>_("1"))})])])):b("",!0)])):(o(),l("div",At,e[16]||(e[16]=[t("div",{class:"text-center py-8"},[t("p",{class:"text-gray-500"}," Account not found ")],-1)])))])])}}}),Et=q(Bt,[["__scopeId","data-v-06f1d979"]]);export{Et as default};
