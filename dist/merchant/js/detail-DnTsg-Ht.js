import{s as D}from"./index-DfF1qu_m.js";import{s as j}from"./index-CCP8zFp2.js";import{d as k,r as n,f as M,a as Y,o as E,b as e,t as h,e as o,w as L,u as g,s as N}from"./index-BPwFJVSY.js";import{s as T,a as B}from"./index-CxOnO8if.js";import{E as _}from"./EnhancedDatePicker-DfTmBAld.js";import{c as C}from"./report-BieIWVzh.js";import{a as $,b as V}from"./format-3aITXa0f.js";import{d as w}from"./dayjs.min-Bar1rHDB.js";import{u as G,i as P,a as U,b as I,d as J,e as S,c as F}from"./installCanvasRenderer-p5Q0jy4r.js";import{i as R,a as H}from"./install-CHL8RKGY.js";import{i as q}from"./install-Db2jL4kZ.js";import{i as z}from"./install-W_lq_5hT.js";import{_ as K}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CEksU3zE.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-D7FsVJ-c.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./plan-DCOwcHy2.js";const Q={class:"px-6 rounded-lg bg-white flex"},W={class:"chart w-4/5 border-r-1 border-gray-300"},X={class:"px-10 py-8 text-2xl flex justify-between items-center"},Z={class:"text-base"},tt={class:"mt-6 px-10"},et={class:"explore w-1/5 p-8",hidden:""},at={class:"filter"},ot={class:"filter-item"},st={class:"filter-item"},lt={class:"filter-item"},it={class:"filter-item"},nt={class:"filter-item"},rt={class:"feedback mt-32"},mt=k({__name:"detail",setup(dt){G([U,I,R,J,q,S,H,F,z]);const u=n(),s=n({grid:{bottom:"50px",left:"80px",right:"80px",top:"15px"},color:["#9373f6","#dc9256"],legend:{left:"45px",bottom:5},xAxis:[{type:"category",data:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],axisPointer:{type:"shadow"}}],yAxis:[{type:"value",max:250,interval:50,axisLabel:{formatter:V}},{type:"value",min:0,max:25,interval:5,axisLabel:{formatter:"{value} %"}}],series:[{name:"Gross volume",type:"bar",data:[2,4.9,7,23.2,25.6,76.7,135.6,162.2,32.6,20,6.4,3.3]},{name:"Year on year",type:"line",yAxisIndex:1,data:[2,2.2,3.3,4.5,6.3,10.2,20.3,23.4,23,16.5,12,6.2]}]}),f=n([{header:"Month",grossVolume:"Gross volume",yearOnYear:"Year on year"}]),A=()=>{C().then(l=>{const t=l.data;b.value=`$${$(t.total.payment_amount)}`,s.value.series[0].data=Object.values(t.monthly).map(a=>Number(a.payment_amount)),s.value.series[1].data=Object.values(t.yoy.monthly).map(a=>Number(a.payment_amount)),s.value.xAxis[0].data=Object.keys(t.monthly).map(a=>a);const i=Math.max(...Object.values(t.monthly).map(a=>a.payment_amount)),r=Math.max(...Object.values(t.yoy.monthly).map(a=>a.payment_amount)),{max:m,interval:c}=v(i);s.value.yAxis[0]={type:"value",axisLabel:{formatter:a=>V(a)},max:m,interval:c},s.value.yAxis[1]={type:"value",min:0,max:v(r).max,interval:v(r).interval,axisLabel:{formatter:"{value} %"}},Object.keys(t.monthly).forEach(a=>{f.value.push({header:a,grossVolume:`$${$(t.monthly[a].payment_amount)}`,yearOnYear:`${t.yoy.monthly[a].payment_amount}%`})}),u.value&&u.value.setOption(s.value)})};function v(l){const i=10**Math.floor(Math.log10(l)),r=Math.ceil(l/i)*i,m=r/5;return{max:r,interval:m}}M(()=>{const l=document.getElementById("amount");l&&(u.value=P(l),s.value&&u.value.setOption(s.value)),Promise.all([A()])});const p=n(new Date),y=n(),x=n(""),b=n("$0"),O=n(0);return(l,t)=>{const i=T,r=B,m=N,c=j,a=D;return E(),Y("div",null,[t[15]||(t[15]=e("div",{class:"m-2 w-4/5 flex justify-end px-10"},null,-1)),e("div",Q,[e("div",W,[e("div",X,[e("div",null,[t[4]||(t[4]=e("span",{class:"font-semibold mr-4"},"Gross Transaction Value ($)",-1)),e("span",null,h(b.value),1)]),e("div",Z," Last updated "+h(O.value)+" hours ago ",1)]),t[5]||(t[5]=e("div",{class:"chart-content w-full h-[500px]"},[e("div",{id:"amount",style:{width:"100%",height:"100%"}})],-1)),e("div",tt,[o(r,{value:f.value},{default:L(()=>[o(i,{field:"header",style:{"min-width":"200px"}}),o(i,{field:"grossVolume",style:{"min-width":"200px"}}),o(i,{field:"yearOnYear",style:{"min-width":"200px"}})]),_:1},8,["value"])]),t[6]||(t[6]=e("div",{class:"mt-10 px-10"},[e("div",{class:"font-semibold mb-2"}," How is this calculated? "),e("div",{class:"text-[#777] mb-2 leading-6"}," Gross volume is the total amount of money received from all payment transactions, The data here is based on your default currency AUD. ")],-1))]),e("div",et,[t[14]||(t[14]=e("div",{class:"font-semibold mb-8 text-lg"}," Explore ",-1)),e("div",at,[e("div",ot,[t[7]||(t[7]=e("div",{class:"filter-name"}," Time period ",-1)),e("div",null,[o(_,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=d=>p.value=d),"min-date":g(w)().toDate()},null,8,["modelValue","min-date"])])]),e("div",st,[t[8]||(t[8]=e("div",{class:"filter-name"}," Time interval ",-1)),e("div",null,[o(_,{modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=d=>p.value=d),"min-date":g(w)().toDate()},null,8,["modelValue","min-date"])])]),e("div",lt,[t[9]||(t[9]=e("div",{class:"filter-name"}," Group by ",-1)),e("div",null,[o(m,{label:"Add group",icon:"pi pi-plus",variant:"text"})])]),e("div",it,[t[10]||(t[10]=e("div",{class:"filter-name"}," Filter by ",-1)),e("div",null,[o(m,{label:"Add filter",icon:"pi pi-plus",variant:"text"})])]),e("div",nt,[t[12]||(t[12]=e("div",{class:"filter-name"}," Percent change ",-1)),e("div",null,[o(c,{modelValue:y.value,"onUpdate:modelValue":t[2]||(t[2]=d=>y.value=d),"input-id":"year",name:"checked",value:"Year-over-year"},null,8,["modelValue"]),t[11]||(t[11]=e("label",{for:"year"}," Year-over-year ",-1))])])]),e("div",rt,[t[13]||(t[13]=e("div",{class:"p-2 mb-4"}," Do you have any feedback on this metric? ",-1)),e("div",null,[o(a,{modelValue:x.value,"onUpdate:modelValue":t[3]||(t[3]=d=>x.value=d),rows:"5",cols:"25"},null,8,["modelValue"]),o(m,{label:"Share feedback",severity:"help",variant:"text",icon:"pi pi-send"})])])])])])}}}),Ct=K(mt,[["__scopeId","data-v-b87311bc"]]);export{Ct as default};
