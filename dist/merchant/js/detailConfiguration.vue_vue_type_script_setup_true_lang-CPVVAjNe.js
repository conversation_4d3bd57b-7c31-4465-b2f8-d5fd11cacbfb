import{B as f}from"./BaseDataTable-BVkRnzNI.js";import{s as h}from"./index-CCP8zFp2.js";import{u as y}from"./useDict-sWoVNiZj.js";import{d,r as g,p as s,o as l,w as n,a as w,A as C,j as b,t as k,u as x}from"./index-BPwFJVSY.js";import{h as v}from"./customer-M3EzHCil.js";const j=d({__name:"detailConfiguration",props:{list:{},customerId:{}},setup(r){const i=r,{getLabel:m}=y("notification_type"),c=g([{field:"type",header:"Notification type",style:{width:"120px"},template:"type"},{field:"email",header:"Email",style:{width:"80px"},template:"email"}]),p=e=>{window.$confirm.require({header:"Confirmation",message:"Are you sure you want to update the email?",acceptLabel:"Yes",rejectLabel:"No",accept:()=>{var t;e.__is_loading=!0,v({customer_id:i.customerId,notification_type:e.type,status:((t=e.email)==null?void 0:t.toString())||"0"}).then(()=>{e.__is_loading=!1}).catch(()=>{e.email=e.email?0:1,e.__is_loading=!1})},reject:()=>{e.email=e.email?0:1},onHide:()=>{e.__is_loading=!1,e.email=e.email?0:1}})};return(e,t)=>{const u=h,_=f;return l(),s(_,{columns:c.value,value:i.list,"total-records":i.list.length,paginator:!1,lazy:!1,"data-key":"id","show-search-bar":!1,"show-multiple-column":!1,"table-style":"min-width: 15rem"},{type:n(({data:o})=>[b(k(x(m)(o==null?void 0:o.type)),1)]),email:n(({data:o})=>[o.__is_loading?(l(),w("i",{key:0,class:C({"pi pi-spin pi-spinner":o.__is_loading})},null,2)):(l(),s(u,{key:1,modelValue:o.email,"onUpdate:modelValue":a=>o.email=a,"false-value":0,"true-value":1,binary:"",onChange:a=>p(o)},null,8,["modelValue","onUpdate:modelValue","onChange"]))]),_:1},8,["columns","value","total-records"])}}});export{j as _};
