import{B as _}from"./BaseDataTable-BVkRnzNI.js";import{u as h}from"./useDict-sWoVNiZj.js";import{u as w}from"./useRequestList-DvWcnSzt.js";import{d as x,r as D,p as b,o as C,w as s,j as o,t as r,u as t}from"./index-BPwFJVSY.js";import{i as T}from"./customer-M3EzHCil.js";import{f as B}from"./date-D3KeWVvs.js";const z=x({__name:"detailHistory",props:{customerId:{}},setup(i){const l=w({requestFn:T,immediate:!0,defaultParams:{customer_id:i.customerId}}),m=D([{field:"created_at",header:"Date/Time",style:{width:"120px"},template:"created_at"},{field:"type",header:"Type",style:{width:"80px"},template:"type"},{field:"detail",header:"Detail",style:{width:"100px"}}]),{list:n,loading:u,total:p,onPageChange:c,failed:d,failureMessage:f}=l,{getLabel:g}=h("communication_type");return(k,a)=>{const y=_;return C(),b(y,{columns:m.value,value:t(n),loading:t(u),"total-records":t(p),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,"show-multiple-column":!1,scrollable:!0,failed:t(d),"failure-message":t(f),"table-style":"min-width: 15rem",onPage:a[0]||(a[0]=e=>t(c)(e))},{created_at:s(({data:e})=>[o(r(t(B)(e==null?void 0:e.created_at)),1)]),type:s(({data:e})=>[o(r(t(g)(e==null?void 0:e.type)),1)]),_:1},8,["columns","value","loading","total-records","failed","failure-message"])}}});export{z as _};
