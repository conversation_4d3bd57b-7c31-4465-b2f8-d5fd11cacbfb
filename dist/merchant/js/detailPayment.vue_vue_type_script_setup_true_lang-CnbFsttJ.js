import{B as S}from"./BaseDataTable-BVkRnzNI.js";import{_ as q}from"./BaseTag-dd68UHCy.js";import{d as F,k as N,r as k,p as A,o as C,w as a,e as L,u as t,j as r,t as o}from"./index-BPwFJVSY.js";import{u as v}from"./useRequestList-DvWcnSzt.js";import{g as W}from"./transactions-CkZWY3pR.js";import{u as z}from"./dict-CLPQcIyV.js";import{f as I}from"./date-D3KeWVvs.js";import{a as U}from"./tagStatus-CiU0ihd_.js";const K=F({__name:"detailPayment",props:{id:{default:""}},setup(i,{expose:m}){const u=i,{t:c}=N(),{getDictByType:p,getDictLabel:d}=z(),f=v({requestFn:W,immediate:!1,page_size:20}),_=k([{field:"date",header:"Date",style:{minWidth:"150px"},template:"date",sortField:"date"},{field:"Name of Subscription",header:"Name of Subscription",style:{minWidth:"150px"},template:"name",sortField:"name"},{field:"amount",header:"Amount",style:{minWidth:"110px"},template:"amount"},{field:"status",header:c("customersPage.columns.status"),template:"status"},{field:"action",header:"",style:{width:"50px"},template:"action"}]),{list:g,loading:y,total:h,setParams:b,onPageChange:x,failed:D,failureMessage:B,search:n}=f,w=s=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(s);return b({customer_id:u.id}),n(),p("trans_status"),m({search:n}),(s,V)=>{const P=q,T=S;return C(),A(T,{value:t(g),columns:_.value,scrollable:!0,"show-multiple-column":!1,loading:t(y),paginator:!0,rows:20,"total-records":t(h),lazy:!0,"data-key":"id","sort-mode":"single","sort-field":s.$route.query.sort_by,"sort-order":s.$route.query.sort_order==="desc"?-1:1,"search-placeholder":"Search","type-placeholder":"Filter By",failed:t(D),"failure-message":t(B),"show-search-bar":!1,onPage:t(x)},{amount:a(({data:e})=>[r(o(w(e==null?void 0:e.payment_amount))+" "+o(e==null?void 0:e.payment_currency),1)]),name:a(({data:e})=>{var l;return[r(o((l=e==null?void 0:e.customer_plan)==null?void 0:l.plan_name),1)]}),date:a(({data:e})=>[r(o(t(I)(e.created_at)),1)]),status:a(({data:e})=>[L(P,{text:t(d)("trans_status",e.status),type:t(U)(e.status)},null,8,["text","type"])]),_:1},8,["value","columns","loading","total-records","sort-field","sort-order","failed","failure-message","onPage"])}}});export{K as _};
