import{d as O,r as f,a as S,o,p as d,u as t,e as n,w as m,q as v,b as y,m as g,A as Z,j as D,t as I,s as z,F as A}from"./index-BPwFJVSY.js";import{s as Q}from"./index-DlkyKXFh.js";import{s as G}from"./index-BlFKIn6Y.js";import{s as H}from"./index-DgBXOSrM.js";import{s as J}from"./index-AtQkrpJ9.js";import{t as K}from"./vee-validate-zod-BFI0y82J.js";import{d as W}from"./dayjs.min-Bar1rHDB.js";import{S as V,U as w}from"./plan-DCOwcHy2.js";import{a as X}from"./plan-X86n_BnV.js";import ee from"./PlanSubscriptionForm-Dsrtpj9s.js";import{u as te}from"./usePlanForm-WYcUl8Fa.js";import{o as ae,n as se,d as oe,Z as ne}from"./types-AmnC9OGP.js";const ie={key:0,class:"flex justify-center items-center"},le={class:"flex pt-6 justify-end"},ge=O({__name:"detailSubscriptionsUpdate",props:{planId:{default:""},customerId:{default:""}},emits:["update:success"],setup(F,{emit:C}){const i=F,P=C,{formData:l,formErrors:h,submitting:k,submitForm:b,options:x,optionsLoading:B,validateForm:U,setPlanId:T,setCustomerId:$,loading:q}=te("customerView"),u=f(!1),j=f(),s=f({planStartDate:new Date(Date.now()+24*60*60*1e3),units:1});$(i.customerId),T(i.planId);const N=K(ae({planStartDate:oe({required_error:"Start date is required",invalid_type_error:"Invalid start date"}),units:se().optional().superRefine((r,e)=>{l.schedule_type===V.UnitBased&&l.unitBasedModel===w.TieredPricing&&(!r||r<=0)&&e.addIssue({code:ne.custom,message:"Unit quantity must be greater than 0"})})})),L=async()=>{if(await U())try{u.value=!0;const e=await b(),{code:p}=await X({...e,units:s.value.units,start_date:W(s.value.planStartDate).format("YYYY-MM-DD"),customer_id:i.customerId,plan_id:i.planId});p===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Subscription updated successfully"}),P("update:success"))}catch(e){console.error(e)}finally{u.value=!1}};return(r,e)=>{const p=J,R=H,_=G,Y=Q,E=z;return o(),S("div",null,[t(q)?(o(),S("div",ie,[n(p)])):(o(),d(t(A),{key:1,ref_key:"planFormRef",ref:j,"validation-schema":t(N),"initial-values":{planStartDate:new Date(Date.now()+24*60*60*1e3)},class:"create-invite-base-form space-y-4 min-h-150 flex-1",onSubmit:L},{default:m(()=>[n(ee,{"form-data":t(l),"form-errors":t(h),options:t(x),"options-loading":t(B),submitting:t(k),mode:"view","customer-id":i.customerId,onSubmit:t(b),onCancel:e[0]||(e[0]=a=>r.$router.back())},null,8,["form-data","form-errors","options","options-loading","submitting","customer-id","onSubmit"]),t(l).schedule_type===t(V).UnitBased&&t(l).unitBasedModel===t(w).TieredPricing?(o(),d(t(g),{key:0,modelValue:s.value.units,"onUpdate:modelValue":e[1]||(e[1]=a=>s.value.units=a),name:"units",as:"div",class:"flex flex-col gap-2"},{default:m(({errorMessage:a,handleChange:c})=>[e[3]||(e[3]=y("label",{class:"create-invite-base-form-label font-medium mb-2"}," Unit Quantity ",-1)),n(R,{"model-value":s.value.units,min:1,max:9999,placeholder:"Please enter unit quantity",class:Z(["w-full",{"p-invalid":a}]),onValueChange:c},null,8,["model-value","class","onValueChange"]),a?(o(),d(_,{key:0,severity:"error",variant:"simple"},{default:m(()=>[D(I(a),1)]),_:2},1024)):v("",!0)]),_:1,__:[3]},8,["modelValue"])):v("",!0),n(t(g),{name:"planStartDate",as:"div",class:"flex flex-col gap-2"},{default:m(({errorMessage:a,handleChange:c})=>[e[4]||(e[4]=y("label",{class:"create-invite-base-form-label font-medium mb-2"}," Start Date (Required) ",-1)),n(Y,{modelValue:s.value.planStartDate,"onUpdate:modelValue":e[2]||(e[2]=M=>s.value.planStartDate=M),"min-date":new Date(Date.now()+24*60*60*1e3),placeholder:"Please select a start date","date-format":"dd/mm/yy",class:"w-full","input-class":{"p-invalid":a},onValueChange:c},null,8,["modelValue","min-date","input-class","onValueChange"]),a?(o(),d(_,{key:0,severity:"error",variant:"simple"},{default:m(()=>[D(I(a),1)]),_:2},1024)):v("",!0)]),_:1,__:[4]}),y("div",le,[n(E,{type:"submit",loading:u.value,label:"SUBMIT"},null,8,["loading"])])]),_:1},8,["validation-schema","initial-values"]))])}}});export{ge as _};
