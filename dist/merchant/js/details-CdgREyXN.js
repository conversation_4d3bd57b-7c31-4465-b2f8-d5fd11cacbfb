import{_ as bt}from"./BaseCardType-BnVjnEGA.js";import{s as wt}from"./index-BYNLvGzw.js";import{s as xt}from"./index-BFXZ3UuW.js";import{_ as kt}from"./BaseTag-dd68UHCy.js";import{F as dt,f as y}from"./index-Cui9J1ai.js";import{s as l}from"./index-CQ6UwYfx.js";import{d as Dt,B as Et,r as g,f as Tt,a as d,u as e,o,b as t,e as i,p as b,q as r,t as m,w,y as mt,z as ct,n as Bt,A as f,j as Ft}from"./index-BPwFJVSY.js";import{u as $t}from"./useDict-sWoVNiZj.js";import{u as Ct,P as At}from"./usePermissions-PkVCxleQ.js";import{T as rt}from"./transaction-CqiD1t2Y.js";import{a as Lt}from"./transactions-CkZWY3pR.js";import{a as Nt}from"./tagStatus-CiU0ihd_.js";import{_ as Pt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./getAssetsFile-DC-dlhh8.js";import"./BillbuddyLogo-FvDXaJ4h.js";import"./PMI-logo-4SeFW8S8.js";import"./logo-CiIwT2Ho.js";import"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import"./color-DqhIgFrh.js";import"./invioce-payment-Bs21HoFI.js";import"./isUpdated-DXQm7BuR.js";import"./login-bg-kShksnUz.js";import"./dict-CLPQcIyV.js";import"./invoice-B6mg1HKE.js";import"./plan-DCOwcHy2.js";const jt={key:0,class:"flex flex-col gap-y-6 lg:flex-row lg:gap-x-6 mt-4"},St={class:"transaction-content"},It={key:0},zt={class:"flex flex-col gap-y-4"},Rt={class:"mt-10 mb-16"},Vt={class:"mb-4"},qt={class:"mt-16"},Mt={key:1},Ot={class:"headLine flex justify-between items-center"},Ut={class:"flex flex-col gap-y-4"},Yt={class:"flex items-center"},Gt={class:"text-3xl font-semibold mr-2"},Ht={class:"text-3xl font-semibold mr-2"},Jt={key:0,class:"pi pi-check"},Kt={key:1,class:"pi pi-times"},Qt={class:"ml-2"},Wt={class:"flex items-center gap-x-2"},Xt={class:"headLine-content"},Zt={class:"transactions-detail-left col-span-2 mt-10"},ts={class:"time-line mb-16"},ss={key:0,class:"pi pi-verified",style:{color:"#4ade80"}},es={key:1,class:"pi pi-times",style:{color:"#ff0000"}},is={key:2,class:"pi pi-id-card"},as={class:"flex flex-col gap-y-2"},ls={class:"transaction-timeline-title"},ns={class:"transaction-timeline-content"},os={class:"payment-breakdown"},ds={class:"w-full border-t border-gray-200"},ms=["onClick"],cs={key:0,class:"w-full bg-gray-50 px-2 py-3 border-b border-gray-200"},rs={class:"text-sm text-gray-600"},_s={class:"text-sm"},us={key:0,class:"payment-method"},hs={class:"mt-16"},vs={class:"w-full flex flex-wrap"},ps={class:"payment-detail lg:w-1/2 h-10 md:w-full"},fs={class:"content"},ys={class:"payment-detail lg:w-1/2 h-10 md:w-full"},gs={class:"content"},bs={class:"payment-detail lg:w-1/2 h-10 md:w-full"},ws={class:"content"},xs={class:"payment-detail lg:w-1/2 h-10 md:w-full"},ks={class:"content"},Ds={class:"payment-detail lg:w-1/2 h-10 md:w-full"},Es={class:"content text-[#9400ff]"},Ts={class:"payment-detail lg:w-1/2 h-10 md:w-full"},Bs={key:0,class:"content"},Fs={key:1,class:"content"},$s={class:"transactions-detail"},Cs={key:0},As={key:1},Ls={class:"transaction-details"},Ns={class:"transaction-details"},Ps={class:"details-edit"},js={class:"details-edit-content"},Ss={class:"details-edit"},Is={class:"details-edit-content flex items-center"},zs={class:"details-edit"},Rs={class:"details-edit-content"},Vs={key:0,class:"customer-info"},qs={class:"details-edit"},Ms={class:"details-edit-content"},Os={class:"details-edit"},Us={class:"details-edit-content"},Ys={class:"details-edit"},Gs={class:"details-edit-content"},Hs={key:1,class:"flex justify-center items-center h-64"},Js=Dt({__name:"details",setup(Ks){const _t=Et(),{hasPermission:ut}=Ct(),a=g({status:0}),u=g(!0),{getLabel:ht}=$t("trans_status"),_=g([{id:1,field:"Payment Amount",isFieldBold:!1,amount:""},{id:2,field:"Fees",isFieldBold:!1,amount:"",isExpanded:!1,details:[]},{id:3,field:"Net amount",isFieldBold:!0,amount:""}]),vt=c=>{c.id===2&&c.isExpanded!==void 0&&(c.isExpanded=!c.isExpanded)},pt=()=>{var c;u.value=!0,Lt((c=_t.params)==null?void 0:c.id).then(s=>{_.value[0].amount=`${y(s.data.payment_amount)} ${s.data.payment_currency}`,_.value[1].amount=`${y(s.data.fee_amount||0)} ${s.data.payment_currency}`,_.value[1].details=s.data.settlement_details,_.value[2].amount=`${y(s.data.payment_amount)} ${s.data.net_currency}`,a.value=s.data,u.value=!1}).catch(()=>{u.value=!1})};return Tt(()=>{Promise.all([pt()])}),(c,s)=>{var k,D,E,T,B,F,$,C,A,L,N,P,j,S,I,z,R,V,q,M,O,U,Y,G,H,J,K,Q,W,X,Z,tt,st,et,it,at,lt;const ft=kt,h=xt,yt=wt,x=bt;return e(ut)(e(At).TRANS_DETAIL)?(o(),d("div",jt,[t("div",St,[u.value?(o(),d("div",It,[t("div",zt,[i(e(l),{width:"60%",height:"3rem"}),i(e(l),{width:"40%",height:"1.5rem"})]),t("div",Rt,[i(e(l),{width:"20%",height:"2rem",class:"mb-4"}),i(e(l),{width:"100%",height:"1rem",class:"mb-2"}),i(e(l),{width:"100%",height:"1rem",class:"mb-2"}),i(e(l),{width:"80%",height:"1rem"})]),t("div",Vt,[i(e(l),{width:"30%",height:"2rem",class:"mb-4"}),i(e(l),{width:"100%",height:"3.5rem",class:"mb-2"}),i(e(l),{width:"100%",height:"3.5rem",class:"mb-2"}),i(e(l),{width:"100%",height:"3.5rem"})]),t("div",qt,[i(e(l),{width:"30%",height:"2rem",class:"mb-4"}),i(e(l),{width:"100%",height:"1.5rem",class:"mb-2"}),i(e(l),{width:"100%",height:"1.5rem",class:"mb-2"}),i(e(l),{width:"100%",height:"1.5rem",class:"mb-2"}),i(e(l),{width:"100%",height:"1.5rem",class:"mb-2"}),i(e(l),{width:"100%",height:"1.5rem",class:"mb-2"})])])):(o(),d("div",Mt,[t("div",Ot,[t("div",Ut,[t("div",Yt,[t("span",Gt,m(e(dt).formatAmount((k=a.value)==null?void 0:k.payment_amount)),1),t("span",Ht,m((D=a.value)==null?void 0:D.payment_currency),1),a.value.status?(o(),b(ft,{key:0,type:e(Nt)(a.value.status)},{default:w(()=>{var n;return[a.value.status===e(rt).SUCCEEDED?(o(),d("i",Jt)):a.value.status===e(rt).FAILED?(o(),d("i",Kt)):r("",!0),t("span",Qt,m(e(ht)((n=a.value)==null?void 0:n.status)),1)]}),_:1},8,["type"])):r("",!0)]),t("div",Wt,[s[0]||(s[0]=t("span",{class:"headLine-title"},"Charged to ",-1)),t("span",Xt,m((T=(E=a.value)==null?void 0:E.customer)==null?void 0:T.name),1)])])]),t("div",Zt,[t("div",ts,[s[1]||(s[1]=t("div",{class:"flex justify-between items-center"},[t("div",{class:"time-line-title"}," Timeline ")],-1)),i(h),i(yt,{value:(B=a.value)==null?void 0:B.timelines,class:"transaction-timeline w-full flex flex-col justify-start my-4"},{marker:w(({index:n,item:v})=>[t("span",null,[n===0&&v.status===3?(o(),d("i",ss)):n===0&&v.status===2?(o(),d("i",es)):(o(),d("i",is))])]),content:w(({item:n})=>[t("div",as,[t("span",ls,m(n.title),1),t("span",ns,m(n.created_at),1)])]),_:1},8,["value"])]),t("div",os,[s[2]||(s[2]=t("div",null,[t("div",{class:"text-xl font-semibold my-4"}," Ratepayer ")],-1)),t("div",ds,[(o(!0),d(mt,null,ct(_.value,(n,v)=>{var nt,ot;return o(),d("div",{key:n.id,class:"w-full flex flex-wrap"},[t("div",{class:f(["payment-detail py-5 px-2 md:w-full border-gray-200 flex items-center justify-between hover:bg-gray-100",{"border-b":v!==_.value.length-1||n.id===2&&n.isExpanded}]),style:Bt(n.id===2?"cursor: pointer":""),onClick:p=>n.id===2?vt(n):null},[t("div",{class:f(["payment-label w-60",{"font-semibold":n.isFieldBold}])},[n.id===2&&((nt=n.details)!=null&&nt.length)?(o(),d("i",{key:0,class:f(["pi mr-2",n.isExpanded?"pi-chevron-down":"pi-chevron-right"]),style:{"margin-left":"8px","font-size":"12px"}},null,2)):r("",!0),Ft(" "+m(n.field),1)],2),t("div",{class:f(["payment-content",{"font-semibold":n.isFieldBold}])},m(n.amount),3)],14,ms),n.id===2&&n.isExpanded&&((ot=n.details)!=null&&ot.length)?(o(),d("div",cs,[(o(!0),d(mt,null,ct(n.details,(p,gt)=>(o(),d("div",{key:gt,class:"flex justify-between items-center py-2 pl-8"},[t("div",rs,m(p.amount_type_text),1),t("div",_s,m(e(dt).formatAmount(p.settlement_amount))+" "+m(p.settlement_currency),1)]))),128))])):r("",!0)])}),128))])]),(F=a.value)!=null&&F.customer_banking?(o(),d("div",us,[t("div",hs,[s[3]||(s[3]=t("div",{class:"text-xl font-semibold my-4"}," Payment method ",-1)),i(h)]),t("div",vs,[t("div",ps,[s[4]||(s[4]=t("div",{class:"payment-label font-semibold w-60"}," ID ",-1)),t("div",fs,m(($=a.value)==null?void 0:$.trans_no),1)]),t("div",ys,[s[5]||(s[5]=t("div",{class:"payment-label font-semibold w-60"}," Owner ",-1)),t("div",gs,m((A=(C=a.value)==null?void 0:C.customer_banking)==null?void 0:A.account_name),1)]),t("div",bs,[s[6]||(s[6]=t("div",{class:"payment-label font-semibold w-60"}," Number ",-1)),t("div",ws,[(L=a.value)!=null&&L.customer_banking?(o(),b(x,{key:0,text:(P=(N=a.value)==null?void 0:N.customer_banking)==null?void 0:P.account_no,"is-show-card-number":!0},null,8,["text"])):r("",!0)])]),t("div",xs,[s[7]||(s[7]=t("div",{class:"payment-label font-semibold w-60"}," Address ",-1)),t("div",ks,m(((S=(j=a.value)==null?void 0:j.customer_banking)==null?void 0:S.bill_address)||"No address"),1)]),t("div",Ds,[s[8]||(s[8]=t("div",{class:"payment-label font-semibold w-60"}," Fingerprint ",-1)),t("div",Es,m((z=(I=a.value)==null?void 0:I.customer_banking)==null?void 0:z.account_no),1)]),t("div",Ts,[s[9]||(s[9]=t("div",{class:"payment-label font-semibold w-60"}," Expires ",-1)),(V=(R=a.value)==null?void 0:R.customer_banking)!=null&&V.expiration_month?(o(),d("div",Bs,m(`${(M=(q=a.value)==null?void 0:q.customer_banking)==null?void 0:M.expiration_month}/${(U=(O=a.value)==null?void 0:O.customer_banking)==null?void 0:U.expiration_year}`),1)):(o(),d("div",Fs," - "))])])])):r("",!0)])]))]),t("div",$s,[u.value?(o(),d("div",Cs,[i(e(l),{width:"60%",height:"3rem",class:"mb-4"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-4"}),i(e(l),{width:"60%",height:"3rem",class:"mt-8 mb-4"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem",class:"mb-2"}),i(e(l),{width:"100%",height:"2.75rem"})])):(o(),d("div",As,[t("div",Ls,[s[13]||(s[13]=t("div",{class:"title"}," Details ",-1)),i(h),t("div",Ns,[t("div",Ps,[s[10]||(s[10]=t("div",{class:"details-edit-title"}," Payment ID ",-1)),t("div",js,m((Y=a.value)==null?void 0:Y.trans_no),1)]),t("div",Ss,[s[11]||(s[11]=t("div",{class:"details-edit-title"}," Payment method ",-1)),t("div",Is,[(G=a.value)!=null&&G.customer_banking?(o(),b(x,{key:0,"card-type":(J=(H=a.value)==null?void 0:H.customer_banking)==null?void 0:J.credit_brand,text:(Q=(K=a.value)==null?void 0:K.customer_banking)==null?void 0:Q.account_no,"is-show-card-number":!0},null,8,["card-type","text"])):r("",!0)])]),t("div",zs,[s[12]||(s[12]=t("div",{class:"details-edit-title"}," Last updated ",-1)),t("div",Rs,[t("span",null,m(((X=(W=a.value)==null?void 0:W.plan)==null?void 0:X.updated_at)||"-"),1)])])])]),(Z=a.value)!=null&&Z.customer?(o(),d("div",Vs,[s[17]||(s[17]=t("div",{class:"title"}," Ratepayer ",-1)),i(h),t("div",null,[t("div",qs,[s[14]||(s[14]=t("div",{class:"details-edit-title"}," ID ",-1)),t("div",Ms,m((st=(tt=a.value)==null?void 0:tt.customer)==null?void 0:st.customer_id),1)]),t("div",Os,[s[15]||(s[15]=t("div",{class:"details-edit-title"}," Name ",-1)),t("div",Us,m((it=(et=a.value)==null?void 0:et.customer)==null?void 0:it.name),1)]),t("div",Ys,[s[16]||(s[16]=t("div",{class:"details-edit-title"}," Description ",-1)),t("div",Gs,m(((lt=(at=a.value)==null?void 0:at.customer)==null?void 0:lt.description)||"-"),1)])])])):r("",!0)]))])])):(o(),d("div",Hs,s[18]||(s[18]=[t("p",{class:"text-gray-500"}," You don't have permission to view transaction details. ",-1)])))}}}),we=Pt(Js,[["__scopeId","data-v-9c46fd0b"]]);export{we as default};
