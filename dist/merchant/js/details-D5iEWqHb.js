import{i as T,r as L,Q as x,f as S,d as B,k as N,B as C,a as E,u as t,o as r,b as y,p as k,t as $,e as P,q as D,s as j}from"./index-BPwFJVSY.js";import{s as I}from"./index-AtQkrpJ9.js";import{u as A,P as d}from"./usePermissions-PkVCxleQ.js";import{b as F,r as V}from"./plan-X86n_BnV.js";import q from"./PlanSubscriptionInfo-IIxmMy2o.js";import{u as R}from"./dict-CLPQcIyV.js";import{_ as U}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-B7pQ2UQX.js";import"./index-Cui9J1ai.js";import"./decimal-LZMqkRTm.js";import"./plan-DCOwcHy2.js";import"./date-D3KeWVvs.js";import"./dayjs.min-Bar1rHDB.js";function M(_){const o=T(),{hasPermission:i}=A(),{getDictByType:u,isTypeLoading:m}=R(),l=L(!1),c=x({plan_id:"",plan_name:"",amount:"",process_type:null,end_date:new Date,status:null,description:null,schedule_type:1,currency:null,billing_period:null,prices:[],tiered_type:null,surcharge_rate:{fee_rate:"",fee_value:""}}),a=L({status:[],process_type:[]}),g=e=>{if(e==null)return"N/A";const s=a.value.process_type.find(n=>n.value===e);return s?s.label:e},b=e=>{if(e==null)return"N/A";const s=a.value.status.find(n=>n.value===e);return s?s.label:e},h=e=>{if(e==null)return"N/A";const s=a.value.fee_type.find(n=>n.value===e);return s?s.label:e},w=async()=>{try{const{code:e,data:s}=await F(_);e===0&&s?Object.assign(c,{...s}):(window.$toast.add({severity:"error",summary:"Error",detail:"Failed to fetch plan details",life:3e3}),o.push({name:"planSubscriptionList"}))}catch(e){console.error("Error fetching plan details:",e),window.$toast.add({severity:"error",summary:"Error",detail:"An error occurred while fetching plan details",life:3e3}),o.push({name:"planSubscriptionList"})}},f=()=>{i(d.PLAN_UPDATE)&&o.push({name:"planSubscriptionEdit",params:{id:_}})},p=()=>{o.push({name:"planSubscriptionList"})};return S(async()=>{l.value=!0;try{await Promise.all([u("plan_process_type").then(e=>{a.value.process_type=e}),u("plan_fee_type").then(e=>{a.value.fee_type=e}),u("plan_status").then(e=>{a.value.status=e})]),await w()}catch(e){console.error("Error loading plan details and dictionaries:",e),window.$toast.add({severity:"error",summary:"Error",detail:"An error occurred while loading plan details",life:3e3})}finally{l.value=!1}}),{planDetails:c,loading:l,options:a,optionsLoading:m,getProcessTypeLabel:g,getStatusLabel:b,getFeeTypeLabel:h,goToEdit:f,goBack:p}}const O={key:0,class:"plan-subscription-details py-4"},Q={class:"flex justify-between items-center mb-4"},Y={class:"text-3xl"},z={class:"flex gap-2"},G={key:0,class:"bg-white flex justify-center items-center h-64 rounded-xl"},H={key:1,class:"flex justify-center items-center h-64"},J=B({name:"planSubscriptionDetails",__name:"details",setup(_){const{hasPermission:o}=A(),{t:i}=N(),m=C().params.id,{planDetails:l,loading:c,options:a,optionsLoading:g,getProcessTypeLabel:b,getStatusLabel:h,getFeeTypeLabel:w,goToEdit:f,goBack:p}=M(m),e=()=>{o(d.PLAN_DELETE)&&window.$confirm.require({message:"Are you sure you want to delete this plan?",header:"Confirmation",icon:"pi pi-exclamation-triangle",accept:async()=>{try{const{code:s}=await V([m]);s===0&&(window.$toast.add({severity:"success",summary:"Successful",detail:"Plan deleted successfully",life:3e3}),p())}catch(s){console.error("Error deleting plan:",s),window.$toast.add({severity:"error",summary:"Error",detail:"An error occurred while deleting the plan",life:3e3})}}})};return(s,n)=>{const v=j;return t(o)(t(d).PLAN_DETAIL)?(r(),E("div",O,[y("div",Q,[y("div",Y,$(t(i)("planSubscription.dialogs.planDetails")),1),y("div",z,[P(v,{icon:"pi pi-arrow-left",label:t(i)("common.back"),severity:"secondary",onClick:t(p)},null,8,["label","onClick"]),t(o)(t(d).PLAN_UPDATE)?(r(),k(v,{key:0,icon:"pi pi-pencil",label:t(i)("common.edit"),onClick:t(f)},null,8,["label","onClick"])):D("",!0),t(o)(t(d).PLAN_DELETE)?(r(),k(v,{key:1,icon:"pi pi-trash",label:t(i)("common.delete"),severity:"danger",onClick:e},null,8,["label"])):D("",!0)])]),t(c)?(r(),E("div",G,[P(t(I))])):(r(),k(q,{key:1,"plan-details":t(l),loading:t(c),options:t(a),"options-loading":t(g),"get-process-type-label":t(b),"get-status-label":t(h),"get-fee-type-label":t(w),onEdit:t(f),onBack:t(p)},null,8,["plan-details","loading","options","options-loading","get-process-type-label","get-status-label","get-fee-type-label","onEdit","onBack"]))])):(r(),E("div",H,n[0]||(n[0]=[y("p",{class:"text-gray-500"}," You don't have permission to view plan details. ",-1)])))}}}),ce=U(J,[["__scopeId","data-v-66b4435e"]]);export{ce as default};
