import{s as U}from"./index-B7pQ2UQX.js";import{d as D,B as S,i as E,r as c,f as $,a as d,o as u,q as V,e as o,w as j,j as I,t as i,s as R,b as s,u as x,bM as q}from"./index-BPwFJVSY.js";import{s as F}from"./index-BlFKIn6Y.js";import{s as G}from"./index-AtQkrpJ9.js";import{f as w}from"./date-D3KeWVvs.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./dayjs.min-Bar1rHDB.js";const z={class:"merchant-details"},H={key:0,class:"flex justify-content-center align-items-center min-h-screen"},J={key:1,class:"flex flex-column align-items-center gap-3 p-4"},K={key:2},L={class:"flex align-items-center mb-4"},O={class:"text-xl font-medium m-0"},Q={class:"card"},W={class:"section"},X={class:"info-list"},Y={class:"info-item"},Z={class:"info-value"},ss={class:"info-item"},es={class:"info-value"},ts={class:"info-item"},as={class:"info-value"},os={class:"info-item"},is={class:"info-value"},ns={class:"info-item"},ls={class:"info-value"},rs={class:"info-item"},cs={class:"info-value"},ds={class:"info-item"},us={class:"info-value"},vs=D({name:"UserDetails",__name:"details",setup(_s){const f=S(),m=E(),t=c(null),l=c(!0),r=c(!1),v=c(""),p=async()=>{const a=f.params.id;if(!a){r.value=!0,v.value="Merchants is required",l.value=!1;return}l.value=!0,r.value=!1;try{const e=await q(a);t.value=e.data}catch(e){r.value=!0,v.value=e.message||"Failed to load merchant details"}finally{l.value=!1}},A=a=>a===void 0?"Unknown":a===1?"Active":"Inactive",B=a=>a===void 0?"Unknown":{1:"Admin",2:"User",3:"Guest"}[String(a)]||"Unknown",M=()=>{m.back()},N=()=>{m.push({name:"userEdit",params:{id:f.params.id}})};return $(()=>{p()}),(a,e)=>{var b,g;const C=G,T=F,_=R,h=U;return u(),d("div",z,[l.value?(u(),d("div",H,[o(C,{"stroke-width":"3"})])):r.value?(u(),d("div",J,[o(T,{severity:"error",closable:!1,class:"w-full md:w-6"},{default:j(()=>[I(i(v.value),1)]),_:1}),o(_,{label:"Retry",icon:"pi pi-refresh",onClick:p})])):t.value?(u(),d("div",K,[s("div",L,[o(_,{icon:"pi pi-arrow-left",text:"",class:"mr-3",onClick:M}),s("h1",O,i(t.value.name),1),o(_,{icon:"pi pi-pencil",text:"",class:"ml-auto",onClick:N})]),s("div",Q,[s("div",W,[e[7]||(e[7]=s("h2",{class:"section-title"}," Basic Information ",-1)),s("div",X,[s("div",Y,[e[0]||(e[0]=s("div",{class:"info-label"}," Status ",-1)),s("div",Z,[o(h,{value:A(t.value.status),severity:t.value.status===1?"success":"danger"},null,8,["value","severity"])])]),s("div",ss,[e[1]||(e[1]=s("div",{class:"info-label"}," Type ",-1)),s("div",es,i(B(t.value.type)),1)]),s("div",ts,[e[2]||(e[2]=s("div",{class:"info-label"}," Email ",-1)),s("div",as,i(t.value.email||"N/A"),1)]),s("div",os,[e[3]||(e[3]=s("div",{class:"info-label"}," 2FA Status ",-1)),s("div",is,[o(h,{value:t.value.mfa_check===1?"Enabled":"Disabled",severity:t.value.mfa_check===1?"success":"warning"},null,8,["value","severity"])])]),s("div",ns,[e[4]||(e[4]=s("div",{class:"info-label"}," Assign to Business ",-1)),s("div",ls,i(((g=(b=t.value)==null?void 0:b.merchant_user_business)==null?void 0:g.map(n=>{var y,k;return`${(y=n==null?void 0:n.business)==null?void 0:y.business_id} - ${(k=n==null?void 0:n.business)==null?void 0:k.business_name}`}).join(", "))||"N/A"),1)]),s("div",rs,[e[5]||(e[5]=s("div",{class:"info-label"}," Created ",-1)),s("div",cs,i(t.value.created_at?x(w)(t.value.created_at):"N/A"),1)]),s("div",ds,[e[6]||(e[6]=s("div",{class:"info-label"}," Updated ",-1)),s("div",us,i(t.value.updated_at?x(w)(t.value.updated_at):"N/A"),1)])])])])])):V("",!0)])}}}),ks=P(vs,[["__scopeId","data-v-29c38741"]]);export{ks as default};
