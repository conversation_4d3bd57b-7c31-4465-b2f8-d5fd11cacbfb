import{d as D,B as w,i as T,J as C,r as c,f as P,a as _,o as v,b as s,q as A,e as l,u as n,s as m,w as M,j as I,t as i,bz as E}from"./index-BPwFJVSY.js";import{s as V}from"./index-BlFKIn6Y.js";import{s as N}from"./index-AtQkrpJ9.js";import{_ as b}from"./BaseTag-dd68UHCy.js";import{f as p}from"./date-D3KeWVvs.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./dayjs.min-Bar1rHDB.js";const j={class:"business-details"},U={class:"card mb-4"},R={class:"card-header"},q={class:"flex items-center justify-between"},H={key:0,class:"flex justify-center items-center py-8"},O={key:1,class:"flex flex-col items-center gap-3 p-4"},Y={key:2,class:"space-y-6"},z={class:"card"},G={class:"card-body"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},L={class:"info-item"},K={class:"info-value"},Q={class:"info-item"},W={class:"info-value"},X={class:"info-item"},Z={class:"info-value"},ss={class:"info-item"},es={class:"info-value"},ts={class:"info-item"},is={class:"info-value"},as={class:"info-item"},os={class:"info-value"},ls={class:"info-item"},ns={class:"info-value"},ds={class:"card"},rs={class:"card-body"},cs={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},_s={class:"info-item"},vs={class:"info-value"},us={class:"info-item"},fs={class:"info-value"},ms={class:"info-item"},bs={class:"info-value"},ps={class:"card"},gs={class:"card-body"},hs={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ys={class:"info-item"},Fs={class:"info-value"},Ss={class:"info-item"},ks={class:"info-value"},xs={class:"card"},Bs={class:"card-body"},Ds={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},ws={class:"info-item"},Ts={class:"info-value"},Cs={class:"info-item"},Ps={class:"info-value"},As={class:"info-item"},Ms={class:"info-value"},Is={class:"card"},Es={class:"card-body"},Vs={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ns={class:"info-item"},$s={class:"info-value"},js={class:"info-item"},Us={class:"info-value"},Rs={class:"card"},qs={class:"card-body"},Hs={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Os={class:"info-item"},Ys={class:"info-value"},zs={class:"info-item"},Gs={class:"info-value"},Js={class:"info-item"},Ls={class:"info-value"},Ks={class:"info-item"},Qs={class:"info-value"},Ws={class:"info-item"},Xs={class:"info-value"},Zs={class:"info-item"},se={class:"info-value"},ee={class:"info-item"},te={class:"info-value"},ie={class:"card"},ae={class:"card-body"},oe={class:"grid grid-cols-1 gap-6"},le={class:"info-item"},ne={class:"info-value"},de={class:"card"},re={class:"card-body"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},_e={class:"info-item"},ve={class:"info-value"},ue={class:"info-item"},fe={class:"info-value"},me=D({name:"userBidDetails",__name:"details",setup(be){const g=w(),h=T(),y=C(),d=c(!0),r=c(!1),u=c(""),t=c(null),F=o=>{switch(o){case 1:return"Enabled";case 2:return"Disabled";case 3:return"Business Terms Configuration";default:return"Unknown"}},S=o=>{switch(o){case 1:return"paid";case 2:return"failed";case 3:return"upcoming";default:return"default"}},k=o=>{switch(o){case 1:return"Daily Settlement";case 2:return"Monthly Settlement";default:return"Unknown"}},x=o=>o===1?"Enabled":"Disabled",a=o=>!o||o.length===0?"N/A":o.map(e=>e.fee_rate==="1"?`${e.fee_value}%`:`$${e.fee_value}`).join(", "),f=async()=>{const o=g.params.id;if(!o){r.value=!0,u.value="Business is required",d.value=!1;return}d.value=!0,r.value=!1;try{const e=await E(o);t.value=e.data}catch(e){r.value=!0,u.value=e.message||"Failed to load business details",y.add({severity:"error",summary:"Error",detail:e.message||"Failed to load business details",life:3e3})}finally{d.value=!1}},B=()=>{h.back()};return P(()=>{f()}),(o,e)=>(v(),_("div",j,[s("div",U,[s("div",R,[s("div",q,[e[0]||(e[0]=s("h2",null,"Business Detail",-1)),l(n(m),{label:"Back",severity:"secondary",icon:"pi pi-arrow-left",onClick:B})])])]),d.value?(v(),_("div",H,[l(n(N),{"stroke-width":"3"})])):r.value?(v(),_("div",O,[l(n(V),{severity:"error",closable:!1,class:"w-full"},{default:M(()=>[I(i(u.value),1)]),_:1}),l(n(m),{label:"Retry",icon:"pi pi-refresh",onClick:f})])):t.value?(v(),_("div",Y,[s("div",z,[e[8]||(e[8]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Basic Information ")],-1)),s("div",G,[s("div",J,[s("div",L,[e[1]||(e[1]=s("label",{class:"info-label"},"Merchant ID",-1)),s("div",K,i(t.value.merchant_id),1)]),s("div",Q,[e[2]||(e[2]=s("label",{class:"info-label"},"Business",-1)),s("div",W,i(t.value.merchant_name)+" - "+i(t.value.business_id),1)]),s("div",X,[e[3]||(e[3]=s("label",{class:"info-label"},"Status",-1)),s("div",Z,[l(b,{text:F(t.value.status),type:S(t.value.status)},null,8,["text","type"])])]),s("div",ss,[e[4]||(e[4]=s("label",{class:"info-label"},"Settlement Type",-1)),s("div",es,i(k(t.value.settlement_type)),1)]),s("div",ts,[e[5]||(e[5]=s("label",{class:"info-label"},"BPAY Switch",-1)),s("div",is,[l(b,{text:x(t.value.bpay_switch),type:t.value.bpay_switch===1?"paid":"failed"},null,8,["text","type"])])]),s("div",as,[e[6]||(e[6]=s("label",{class:"info-label"},"Created Date",-1)),s("div",os,i(n(p)(t.value.created_at)),1)]),s("div",ls,[e[7]||(e[7]=s("label",{class:"info-label"},"Updated Date",-1)),s("div",ns,i(n(p)(t.value.updated_at)),1)])])])]),s("div",ds,[e[12]||(e[12]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Account Fees ")],-1)),s("div",rs,[s("div",cs,[s("div",_s,[e[9]||(e[9]=s("label",{class:"info-label"},"Account Setup Fee",-1)),s("div",vs,i(a(t.value.account_fees.account_setup_fee)),1)]),s("div",us,[e[10]||(e[10]=s("label",{class:"info-label"},"Account Termination Fee",-1)),s("div",fs,i(a(t.value.account_fees.account_termination_fee)),1)]),s("div",ms,[e[11]||(e[11]=s("label",{class:"info-label"},"Minimum Monthly Transaction Fee",-1)),s("div",bs,i(a(t.value.account_fees.minimum_monthly_transaction_fee)),1)])])])]),s("div",ps,[e[15]||(e[15]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Integration Fees ")],-1)),s("div",gs,[s("div",hs,[s("div",ys,[e[13]||(e[13]=s("label",{class:"info-label"},"Integration Setup Fee",-1)),s("div",Fs,i(a(t.value.integration_fees.integration_setup_fee)),1)]),s("div",Ss,[e[14]||(e[14]=s("label",{class:"info-label"},"Integration Customisation & Development Fee",-1)),s("div",ks,i(a(t.value.integration_fees.integration_customisation_and_development_fee)),1)])])])]),s("div",xs,[e[19]||(e[19]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Platform Fees ")],-1)),s("div",Bs,[s("div",Ds,[s("div",ws,[e[16]||(e[16]=s("label",{class:"info-label"},"User Licence Fee",-1)),s("div",Ts,i(a(t.value.platform_fees.user_licence_fee)),1)]),s("div",Cs,[e[17]||(e[17]=s("label",{class:"info-label"},"Extended Helpdesk Fee",-1)),s("div",Ps,i(a(t.value.platform_fees.extended_helpdesk_fee)),1)]),s("div",As,[e[18]||(e[18]=s("label",{class:"info-label"},"Hosting & Maintenance Fee",-1)),s("div",Ms,i(a(t.value.platform_fees.hosting_maintenance_fee)),1)])])])]),s("div",Is,[e[22]||(e[22]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Bank Account Fees ")],-1)),s("div",Es,[s("div",Vs,[s("div",Ns,[e[20]||(e[20]=s("label",{class:"info-label"},"Per Transaction Fee",-1)),s("div",$s,i(a(t.value.bank_account_fees.per_transaction_fee)),1)]),s("div",js,[e[21]||(e[21]=s("label",{class:"info-label"},"Per Dishonour Fee",-1)),s("div",Us,i(a(t.value.bank_account_fees.per_dishonour_fee)),1)])])])]),s("div",Rs,[e[30]||(e[30]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Credit Card Fees ")],-1)),s("div",qs,[s("div",Hs,[s("div",Os,[e[23]||(e[23]=s("label",{class:"info-label"},"Per Transaction Standard Fee",-1)),s("div",Ys,i(a(t.value.credit_card_fees.per_transaction_standard_fee)),1)]),s("div",zs,[e[24]||(e[24]=s("label",{class:"info-label"},"Per Transaction Premium Fee",-1)),s("div",Gs,i(a(t.value.credit_card_fees.per_transaction_premium_fee)),1)]),s("div",Js,[e[25]||(e[25]=s("label",{class:"info-label"},"Domestic VISA Fee",-1)),s("div",Ls,i(a(t.value.credit_card_fees.domestic_visa_fee)),1)]),s("div",Ks,[e[26]||(e[26]=s("label",{class:"info-label"},"Domestic Mastercard Fee",-1)),s("div",Qs,i(a(t.value.credit_card_fees.domestic_mastercard_fee)),1)]),s("div",Ws,[e[27]||(e[27]=s("label",{class:"info-label"},"International VISA Fee",-1)),s("div",Xs,i(a(t.value.credit_card_fees.international_visa_fee)),1)]),s("div",Zs,[e[28]||(e[28]=s("label",{class:"info-label"},"International Mastercard Fee",-1)),s("div",se,i(a(t.value.credit_card_fees.international_mastercard_fee)),1)]),s("div",ee,[e[29]||(e[29]=s("label",{class:"info-label"},"EFTPOS Fee",-1)),s("div",te,i(a(t.value.credit_card_fees.eftpos_fee)),1)])])])]),s("div",ie,[e[32]||(e[32]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," BPAY Fees ")],-1)),s("div",ae,[s("div",oe,[s("div",le,[e[31]||(e[31]=s("label",{class:"info-label"},"Per Transaction Standard Fee",-1)),s("div",ne,i(a(t.value.bpay_fees.per_transaction_standard_fee)),1)])])])]),s("div",de,[e[35]||(e[35]=s("div",{class:"card-header"},[s("h3",{class:"section-title"}," Other Fees ")],-1)),s("div",re,[s("div",ce,[s("div",_e,[e[33]||(e[33]=s("label",{class:"info-label"},"Surcharge Fee",-1)),s("div",ve,i(a(t.value.surcharge_fees.surcharge_fee)),1)]),s("div",ue,[e[34]||(e[34]=s("label",{class:"info-label"},"GST",-1)),s("div",fe,i(a(t.value.gst.gst)),1)])])])])])):A("",!0)]))}}),xe=$(me,[["__scopeId","data-v-81ea9210"]]);export{xe as default};
