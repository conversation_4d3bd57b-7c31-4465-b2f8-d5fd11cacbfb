import{b4 as n,Z as o,r as s,c as l}from"./index-BPwFJVSY.js";const u=t=>n.get("/getDictDataConst",{params:{type:t}}),y=o("dict",()=>{const t=s({}),r=s({});return{dictCache:t,loadingTypes:r,getDictByType:async e=>{if(t.value[e])return t.value[e];r.value[e]=!0;try{const a=await u(e);return a.data?(t.value[e]=a.data,a.data):[]}catch(a){return console.error(`Failed to fetch dictionary data for type: ${e}`,a),[]}finally{r.value[e]=!1}},clearDictCache:e=>{if(e){const a={...t.value};delete a[e],t.value=a}else t.value={}},isTypeLoading:e=>l(()=>!!r.value[e]),getDictLabel:(e,a)=>{if(!t.value[e])return"";const c=t.value[e].find(i=>i.value===a);return c?c.label:""}}},{persist:!1});export{u as g,y as u};
