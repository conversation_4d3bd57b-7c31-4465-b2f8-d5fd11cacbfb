import{d as z,k as J,J as M,i as O,B as W,r as _,l as Z,bu as G,f as H,P as K,a as Q,o as h,b as i,e as d,w as m,u as l,m as y,p as k,q as g,t as p,j as U,v as X,s as Y,F as ee,bJ as ae,bM as se,bN as te}from"./index-BPwFJVSY.js";import{s as le}from"./index-DUgPT_03.js";import{s as oe}from"./index-Cbodd6BQ.js";import{s as re}from"./index-DES6SMz0.js";import{s as ne}from"./index-BlFKIn6Y.js";import{s as ie}from"./index-CEksU3zE.js";import{t as de,c as me,g as ce,e as ue,b as F}from"./index.esm-DOIZz6Kf.js";import{u as pe}from"./useListRefresh-D-tL0eHu.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CCP8zFp2.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DDQWKtix.js";const ve={class:"merchant-edit-page"},_e={class:"p-4 bg-white rounded-2xl"},he={class:"field"},be={for:"user_name",class:"mb-2 block"},we={class:"field"},Ve={for:"email",class:"mb-2 block"},ye={class:"field"},ke={for:"new-password",class:"mb-2 block"},ge={class:"field"},Ue={class:"mb-2 block"},xe={class:"field mb-4"},Fe={class:"flex justify-end mt-6 gap-2"},$e=z({name:"UserEdit",__name:"edit",setup(Re){const{t:s}=J(),x=M(),L=O(),A=W(),$=_(),v=_(!1),R=_(A.params.id),{backWithRefresh:P}=pe("usersList",()=>{}),E=Z(),{groupList:I}=G(E),r=_({user_name:"",email:"",password:"",mfa_check:0,business_ids:[]}),S=_(!1),N=_([]),T=async()=>{var e;try{const a=await ae({page:1,page_size:100});N.value=(((e=a.data)==null?void 0:e.data)||[]).map(c=>({name:c.name,id:c.id||0}))}catch(a){console.error("Failed to fetch roles:",a),x.add({severity:"error",summary:s("common.error"),detail:s("merchant.role.messages.fetchRolesFailed","Failed to fetch roles"),life:3e3})}},B=async()=>{try{v.value=!0;const e=await se(R.value),{name:a,email:c,mfa_check:o,merchant_user_business:u=[]}=e.data;r.value={user_name:a,email:c,password:"",mfa_check:o,business_ids:u==null?void 0:u.map(b=>b.business_id)}}catch(e){console.error("Failed to fetch merchant detail:",e),x.add({severity:"error",summary:s("common.error"),detail:s("merchant.messages.updateFailed","Failed to fetch merchant detail"),life:3e3})}finally{v.value=!1}},q=de(me({user_name:F().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})).required(),email:F().email(s("validation.emailInvalid")).required(s("validation.emailRequired")),password:F().test("password-validation",s("validation.passwordMinLength"),e=>!e||e===""?!0:e.length>=8).test("password-uppercase",s("validation.passwordUppercase"),e=>!e||e===""?!0:/[A-Z]/.test(e)).test("password-lowercase",s("validation.passwordLowercase"),e=>!e||e===""?!0:/[a-z]/.test(e)).test("password-number",s("validation.passwordNumber"),e=>!e||e===""?!0:/\d/.test(e)),mfa_check:ue().required(),business_ids:ce().optional()})),D=async()=>{var e,a,c;v.value=!0;try{const o=await((e=$.value)==null?void 0:e.validate());if(!o.valid){v.value=!1;return}const u={...o.values,roles:((c=(a=o==null?void 0:o.values)==null?void 0:a.roles)==null?void 0:c.map(w=>w==null?void 0:w.id))||[]};u.password||delete u.password;const{code:b}=await te(R.value,u);b===0&&(x.add({severity:"success",summary:s("common.success"),detail:s("merchant.messages.updateSuccess","Merchant updated successfully")}),P(),B())}catch(o){console.error("Failed to update user:",o)}finally{v.value=!1}},j=()=>{L.back()};return H(()=>{Promise.all([T(),B()]).then(()=>{K(()=>{S.value=!0})})}),(e,a)=>{const c=ie,o=ne,u=re,b=oe,w=le,C=Y;return h(),Q("div",ve,[i("div",_e,[d(l(ee),{ref_key:"formRef",ref:$,"validation-schema":l(q),class:"merchant-form flex flex-col gap-4",onSubmit:D},{default:m(()=>[d(l(y),{modelValue:r.value.user_name,"onUpdate:modelValue":a[0]||(a[0]=t=>r.value.user_name=t),name:"user_name",class:"form-col"},{default:m(({field:t,errorMessage:n,handleChange:V})=>[i("div",he,[i("label",be,p(l(s)("merchant.form.name","Username"))+"*",1),d(c,{id:"user_name",modelValue:t.value,"onUpdate:modelValue":f=>t.value=f,placeholder:l(s)("merchant.form.name","Username"),class:"w-full",autocomplete:"new-password",onValueChange:V},null,8,["modelValue","onUpdate:modelValue","placeholder","onValueChange"]),n?(h(),k(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[U(p(n),1)]),_:2},1024)):g("",!0)])]),_:1},8,["modelValue"]),d(l(y),{modelValue:r.value.email,"onUpdate:modelValue":a[1]||(a[1]=t=>r.value.email=t),name:"email",class:"form-col"},{default:m(({field:t,errorMessage:n})=>[i("div",we,[i("label",Ve,p(l(s)("merchant.form.email","Email"))+"*",1),d(c,X({id:"email"},t,{placeholder:l(s)("merchant.form.email","Email"),class:"w-full",type:"email",autocomplete:"new-password"}),null,16,["placeholder"]),n?(h(),k(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[U(p(n),1)]),_:2},1024)):g("",!0)])]),_:1},8,["modelValue"]),d(l(y),{modelValue:r.value.password,"onUpdate:modelValue":a[2]||(a[2]=t=>r.value.password=t),name:"password",as:"div"},{default:m(({field:t,errorMessage:n,handleChange:V})=>[i("div",ye,[i("label",ke,p(l(s)("merchant.form.password","Password")),1),d(u,{id:"new-password",modelValue:t.value,"onUpdate:modelValue":f=>t.value=f,placeholder:l(s)("merchant.form.password","Password"),class:"w-full","toggle-mask":"",feedback:!0,autocomplete:"new-password",disabled:!S.value,"input-props":{autocomplete:"new-password"},onValueChange:V},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","onValueChange"]),a[6]||(a[6]=i("div",{class:"text-sm text-gray-500 mt-2"}," If you do not fill in the password, the password will remain unchanged ",-1)),n?(h(),k(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[U(p(n),1)]),_:2},1024)):g("",!0)])]),_:1},8,["modelValue"]),d(l(y),{modelValue:r.value.mfa_check,"onUpdate:modelValue":a[4]||(a[4]=t=>r.value.mfa_check=t),name:"mfa_check",as:"div",class:"form-col"},{default:m(({errorMessage:t})=>[i("div",ge,[i("label",Ue,p(l(s)("merchant.form.2fa","2FA Authentication")),1),d(b,{modelValue:r.value.mfa_check,"onUpdate:modelValue":a[3]||(a[3]=n=>r.value.mfa_check=n),options:[{label:"Enabled",value:1},{label:"Disabled",value:0}],"option-label":"label","option-value":"value",class:"w-full",name:"to_fa_check",placeholder:l(s)("merchant.form.select2FA","Select 2FA Option")},null,8,["modelValue","placeholder"])]),t?(h(),k(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[U(p(t),1)]),_:2},1024)):g("",!0)]),_:1},8,["modelValue"]),d(l(y),{modelValue:r.value.business_ids,"onUpdate:modelValue":a[5]||(a[5]=t=>r.value.business_ids=t),name:"business_ids",class:"form-col"},{default:m(({field:t,errorMessage:n,handleChange:V})=>[i("div",xe,[a[7]||(a[7]=i("label",{for:"bids",class:"mb-2 block"},"Assign to Business",-1)),d(w,{"model-value":t.value,options:l(I).map(f=>({...f,label:`${f.label} - ${f.value}`})),"option-label":"label","option-value":"value",class:"w-full",placeholder:"Select Assign to Business","onUpdate:modelValue":V},null,8,["model-value","options","onUpdate:modelValue"]),n?(h(),k(o,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[U(p(n),1)]),_:2},1024)):g("",!0)]),i("div",Fe,[d(C,{type:"button",label:l(s)("common.cancel"),icon:"pi pi-times",class:"p-button-text",onClick:j},null,8,["label"]),d(C,{type:"submit",label:l(s)("common.save"),icon:"pi pi-check",loading:v.value},null,8,["label","loading"])])]),_:1},8,["modelValue"])]),_:1},8,["validation-schema"])])])}}}),Me=fe($e,[["__scopeId","data-v-a528a17e"]]);export{Me as default};
