import{d as U,i as j,B as z,r as p,Q as G,f as O,a as q,o as r,p as f,e as t,w as i,b as s,j as l,u,m as v,q as y,A as Q,t as h,v as $,s as W,F as H,bE as J,bC as K,bG as X}from"./index-BPwFJVSY.js";import{s as Y}from"./index-CEksU3zE.js";import{s as Z}from"./index-BlFKIn6Y.js";import{s as M}from"./index-Cbodd6BQ.js";import{s as ee}from"./index-AtQkrpJ9.js";import{t as se,c as ae,b as g}from"./index.esm-DOIZz6Kf.js";import{u as te}from"./useListRefresh-D-tL0eHu.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";const ne={class:"bid-account-edit-page bg-white rounded-2xl px-6 p-8"},ie={key:0,class:"flex"},le={class:"card-body"},ce={class:"grid grid-cols-1 gap-6"},re={class:"field"},ue={class:"field"},de={class:"field"},me={class:"field"},pe={class:"mt-4"},fe={class:"flex justify-end gap-3"},_e=U({name:"userBidAccountEdit",__name:"edit",setup(be){const E=j(),S=z(),F=p(),B=p(!1),w=p(!1),_=p(!1),V=p([]),{backWithRefresh:L}=te("userBidAccountList",()=>{}),d=G({business_id:"",bsb:"",account_no:"",account_name:""}),N=se(ae({business_id:g().required("Business is required"),bsb:g().required("BSB is required").matches(/^\d{6}$/,"BSB must be 6 digits"),account_no:g().required("Account number is required"),account_name:g().required("Account name is required")})),R=async()=>{const o=S.params.id;if(o){w.value=!0;try{const{data:e,code:c}=await J(o);c===0&&(d.business_id=e.business_id,d.bsb=e.bsb,d.account_no=e.account_no,d.account_name=e.account_name)}catch(e){window.$toast.add({severity:"error",summary:"Error",detail:e.message||"Failed to fetch account details",life:3e3})}finally{w.value=!1}}},D=async o=>{const e=S.params.id;if(e){B.value=!0;try{const{code:c}=await X({id:e,...o});c===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Bid account updated successfully"}),L())}catch(c){window.$toast.add({severity:"error",summary:"Error",detail:c.message||"Failed to update bid account"})}finally{B.value=!1}}},I=()=>{E.back()},P=async()=>{_.value=!0;try{const o=await K();V.value=o.data.data.map(e=>({label:`${e.business_name} - ${e.business_id}`,value:e.business_id}))}catch(o){window.$toast.add({severity:"error",summary:"Error",detail:o.message||"Failed to fetch business list"})}finally{_.value=!1}};return O(async()=>{await Promise.all([P(),R()])}),(o,e)=>{const c=ee,T=M,b=Z,x=Y,A=W;return r(),q("div",ne,[w.value?(r(),q("div",ie,[t(c)])):(r(),f(u(H),{key:1,ref_key:"formRef",ref:F,class:"max-w-2xl","validation-schema":u(N),"initial-values":d,onSubmit:D},{default:i(()=>[s("div",le,[s("div",ce,[s("div",re,[e[0]||(e[0]=s("label",{for:"business_id",class:"block font-medium text-gray-700 mb-2"},[l(" Business "),s("span",{class:"text-red-500"},"*")],-1)),t(u(v),{id:"business_id",name:"business_id"},{default:i(({field:n,errorMessage:a,handleChange:k})=>[t(T,{modelValue:n.value,"onUpdate:modelValue":m=>n.value=m,class:Q([{"p-invalid":a},"w-full"]),options:V.value,"option-label":"label","option-value":"value",placeholder:"Select Business",loading:_.value,disabled:_.value,onValueChange:k},null,8,["modelValue","onUpdate:modelValue","class","options","loading","disabled","onValueChange"]),a?(r(),f(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:i(()=>[l(h(a),1)]),_:2},1024)):y("",!0)]),_:1})]),s("div",ue,[e[1]||(e[1]=s("label",{for:"bsb",class:"block font-medium text-gray-700 mb-2"},[l(" BSB "),s("span",{class:"text-red-500"},"*")],-1)),t(u(v),{id:"bsb",name:"bsb"},{default:i(({field:n,errorMessage:a})=>[t(x,$(n,{class:[{"p-invalid":a},"w-full"],placeholder:"Enter 6-digit BSB number (e.g., 123456)",maxlength:"6",onInput:k=>{const m=k.target;if(m){const C=m.value.replace(/\D/g,"");m.value=C,n.value=C}}}),null,16,["class","onInput"]),a?(r(),f(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:i(()=>[l(h(a),1)]),_:2},1024)):y("",!0)]),_:1})]),s("div",de,[e[2]||(e[2]=s("label",{for:"account_no",class:"block font-medium text-gray-700 mb-2"},[l(" Account Number "),s("span",{class:"text-red-500"},"*")],-1)),t(u(v),{id:"account_no",name:"account_no"},{default:i(({field:n,errorMessage:a})=>[t(x,$(n,{class:[{"p-invalid":a},"w-full"],placeholder:"Enter account number"}),null,16,["class"]),a?(r(),f(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:i(()=>[l(h(a),1)]),_:2},1024)):y("",!0)]),_:1})]),s("div",me,[e[3]||(e[3]=s("label",{for:"account_name",class:"block font-medium text-gray-700 mb-2"},[l(" Account Name "),s("span",{class:"text-red-500"},"*")],-1)),t(u(v),{id:"account_name",name:"account_name"},{default:i(({field:n,errorMessage:a})=>[t(x,$(n,{class:[{"p-invalid":a},"w-full"],placeholder:"Enter account name"}),null,16,["class"]),a?(r(),f(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:i(()=>[l(h(a),1)]),_:2},1024)):y("",!0)]),_:1})])])]),s("div",pe,[s("div",fe,[t(A,{type:"button",label:"Cancel",severity:"secondary",onClick:I}),t(A,{type:"submit",label:"Update",loading:B.value,severity:"warn"},null,8,["loading"])])])]),_:1},8,["validation-schema","initial-values"]))])}}}),Ce=oe(_e,[["__scopeId","data-v-69be3c16"]]);export{Ce as default};
