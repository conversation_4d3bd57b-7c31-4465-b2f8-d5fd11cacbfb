import{s as u}from"./index-AtQkrpJ9.js";import _ from"./createInvoiceForm-DDUQhUw_.js";import{u as g}from"./useCreateInvoice-th9954fL.js";import{d as x,r as h,a as n,o as t,p as C,u as e,b as a,e as k}from"./index-BPwFJVSY.js";const v={class:"flex bg-white rounded-2xl p-4"},b={key:0,class:"flex justify-center items-center min-h-[200px]"},y={class:"flex flex-col items-center gap-2"},R=x({__name:"editInvoice",setup(B){const{formData:r,handleEditSubmit:i,schema:c,config:m,options:d,loadings:o,updateCustomers:p}=g("edit"),f=h();return(S,s)=>{const l=u;return t(),n("div",v,[e(o).loadingDetail||e(o).loadingCustomers?(t(),n("div",b,[a("div",y,[k(l),s[0]||(s[0]=a("span",{class:"text-gray-500"},"Loading...",-1))])])):(t(),C(_,{key:1,ref_key:"formWrapperRef",ref:f,mode:"edit",config:e(m),"form-data":e(r),schema:e(c),"options-loading":e(o),options:e(d),loadings:e(o),onSubmit:e(i),onUpdateCustomer:e(p)},null,8,["config","form-data","schema","options-loading","options","loadings","onSubmit","onUpdateCustomer"]))])}}});export{R as _};
