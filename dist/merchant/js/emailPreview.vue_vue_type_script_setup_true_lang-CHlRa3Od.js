import{d as i}from"./logo-CiIwT2Ho.js";import{d as l,a,o as c,b as e,h as p,n as d,t as u}from"./index-BPwFJVSY.js";const y={class:"email-container mt-4"},m={class:"email-preview bg-white rounded-lg overflow-hidden shadow-md"},f={class:"flex flex-col items-center"},F={class:"w-full bg-white p-6 flex justify-center"},g={class:"mb-4 cursor-pointer"},h=["src"],b={class:"w-full py-4 flex flex-col items-center"},v={class:"w-full px-8 pb-8"},x=["innerHTML"],_=l({__name:"emailPreview",props:{headerColor:{type:String,default:"#fe4c1c"},logoSrc:{type:Array,default:()=>[]},content:{type:String,default:`<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    Hi [Customer Name],\r
</p>\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    We’re excited to introduce you to a simple way to view your subscription and payment details.\r
</p>\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    You can now securely access your <strong>subscription information</strong> and <strong>payment options</strong> directly through the link below — no login required.\r
</p>\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    <strong>What you can do through the link:</strong>\r
</p>\r
<ul class="ak-ul" style="color:#292A2E;font-family:&quot;font-size:16px;background-color:#FFFFFF;">\r
    <li>\r
        <p style="font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;">\r
            View your subscription plan and billing history\r
        </p>\r
    </li>\r
    <li>\r
        <p style="font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;">\r
            See upcoming payment dates and amounts\r
        </p>\r
    </li>\r
    <li>\r
        <p style="font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;">\r
            Make secure payments if needed\r
        </p>\r
    </li>\r
</ul>\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    If you notice any incorrect details or need to make changes to your subscription, please contact us directly.\r
</p>\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    <strong>Access your subscription and payment page here:</strong><br />\r
    👉 <a href="[Jump Link]" class="cc-1sdo59k">Access Your Subscription &amp; Payments</a>\r
</p>\r
<hr />\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    <strong>Security Information</strong><br />\r
    Your privacy and security are important to us.<br />\r
    The link provided is unique to your account and protected with industry-standard encryption.<br />\r
    We recommend accessing it from a trusted device.\r
</p>\r
<hr />\r
<p style="font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;">\r
    <strong>Support Information</strong><br />\r
    If you have any questions or need help, please contact the business directly using the details they have provided.<br />\r
    We’re here to assist you if needed.\r
</p>\r
`}},setup(o){const n=o,r=s=>{var t;return((t=s[0])==null?void 0:t.url)||i};return(s,t)=>(c(),a("div",null,[e("div",y,[e("div",m,[t[0]||(t[0]=p('<div class="bg-gray-900 py-2 px-3 flex items-center"><div class="flex items-center space-x-2"><div class="flex space-x-1"><div class="w-2.5 h-2.5 rounded-full bg-red-500"></div><div class="w-2.5 h-2.5 rounded-full bg-yellow-500"></div><div class="w-2.5 h-2.5 rounded-full bg-green-500"></div></div></div><div class="ml-4 bg-gray-800 rounded-full text-gray-300 text-xs py-1 px-3 flex items-center"><i class="pi pi-lock text-green-400 mr-1"></i><span>https://email.com</span></div></div>',1)),e("div",f,[e("div",F,[e("div",g,[e("img",{src:r(n.logoSrc),alt:"Company Logo",class:"w-24 h-24 object-contain",onerror:"this.src='data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Crect%20x%3D%220%22%20y%3D%220%22%20width%3D%2220%22%20height%3D%2220%22%20fill%3D%22%23e2e8f0%22%2F%3E%3C%2Fsvg%3E'"},null,8,h)])]),e("div",b,[e("div",{class:"headline w-full h-12 font-bold text-2xl text-white flex items-center pl-8 mb-8",style:d({"background-color":`${n.headerColor}`})},u("Your Headline Here"),4),e("div",v,[e("p",{innerHTML:n.content},null,8,x)])])])])])]))}});export{_};
