import{s as L}from"./index-CTms-Les.js";import{s as C}from"./index-BlFKIn6Y.js";import{s as F}from"./index-CEksU3zE.js";import{u as x}from"./checkout-CD3XOa-G.js";import{t as S}from"./vee-validate-zod-BFI0y82J.js";import{d as P,r as T,p as i,w as d,e as n,b as t,u as a,m as c,q as b,v as y,j as f,t as p,F as $,o as m}from"./index-BPwFJVSY.js";import{o as E,s as k}from"./types-AmnC9OGP.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./customer-M3EzHCil.js";import"./usePlanForm-WYcUl8Fa.js";import"./dayjs.min-Bar1rHDB.js";import"./useListRefresh-D-tL0eHu.js";import"./usePermissions-PkVCxleQ.js";import"./customer-v2cMYA98.js";import"./plan-DCOwcHy2.js";import"./integrations-C_ojvdY3.js";import"./plan-X86n_BnV.js";import"./dict-CLPQcIyV.js";const _={class:"field mb-4"},j={class:"form-row mb-4"},q={class:"field mb-4"},O={class:"field mb-4"},R={class:"field mb-4"},g={class:"field mb-4"},z={class:"form-row mb-4"},G={class:"field mb-4"},H={class:"field mb-4"},J={class:"form-row mb-4"},K={class:"field mb-4"},Q={class:"field mb-4"},W={class:"field mb-4"},X={class:"field mb-4"},Y={class:"field mb-4"},Z=P({__name:"formBankAccountDetails",props:{readonly:{type:Boolean,default:!1}},setup(r,{expose:D}){const o=x(),A=T(),w=S(E({companyName:k().optional(),firstName:k().min(2,"First name must be at least 2 characters"),lastName:k().min(2,"Last name must be at least 2 characters"),addressLine1:k().min(5,"Address must be at least 5 characters"),addressLine2:k().optional(),city:k().min(2,"City must be at least 2 characters"),state:k().min(2,"State is required"),postcode:k().regex(/^\d{4}$/,"Postcode must be 4 digits"),email:k().email("Please enter a valid email address"),bsbNumber:k().regex(/^\d{3}-\d{3}$/,"BSB number must be in the format 123-456"),accountNumber:k().regex(/^\d{6,10}$/,"Account number must be between 6-10 digits"),bankAccountName:k().min(2,"Bank account name must be at least 2 characters")})),N=async()=>{var e;return await((e=A.value)==null?void 0:e.validate())};return D({validate:N,resetForm:()=>{var V;(V=A.value)==null||V.resetForm()},submitForm:async()=>{const V=await N();return V.valid?V.values:!1},getCurrentValues:()=>{var V;return(V=A.value)==null?void 0:V.values}}),(V,e)=>{const v=F,u=C,B=L;return m(),i(a($),{ref_key:"formRef",ref:A,"validation-schema":a(w),"initial-values":a(o).bankAccountDetails,class:"space-y-6"},{default:d(()=>[n(a(c),{modelValue:a(o).bankAccountDetails.companyName,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).bankAccountDetails.companyName=l),name:"companyName"},{default:d(({field:l,errorMessage:s})=>[t("div",_,[e[12]||(e[12]=t("label",{class:"mb-2 block"},"Company Name (Optional)",-1)),n(v,y(l,{placeholder:"Company name",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),t("div",j,[n(a(c),{modelValue:a(o).bankAccountDetails.firstName,"onUpdate:modelValue":e[1]||(e[1]=l=>a(o).bankAccountDetails.firstName=l),as:"div",name:"firstName",class:"form-col"},{default:d(({field:l,errorMessage:s})=>[t("div",q,[e[13]||(e[13]=t("label",{class:"mb-2 block"},"First Name",-1)),n(v,y(l,{placeholder:"First name",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),n(a(c),{modelValue:a(o).bankAccountDetails.lastName,"onUpdate:modelValue":e[2]||(e[2]=l=>a(o).bankAccountDetails.lastName=l),as:"div",name:"lastName",class:"form-col"},{default:d(({field:l,errorMessage:s})=>[t("div",O,[e[14]||(e[14]=t("label",{class:"mb-2 block"},"Last Name",-1)),n(v,y(l,{placeholder:"Last name",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"])]),n(a(c),{modelValue:a(o).bankAccountDetails.addressLine1,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).bankAccountDetails.addressLine1=l),name:"addressLine1"},{default:d(({field:l,errorMessage:s})=>[t("div",R,[e[15]||(e[15]=t("label",{class:"mb-2 block"},"Address Line 1",-1)),n(v,y(l,{placeholder:"Address line 1",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),n(a(c),{modelValue:a(o).bankAccountDetails.addressLine2,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).bankAccountDetails.addressLine2=l),name:"addressLine2"},{default:d(({field:l,errorMessage:s})=>[t("div",g,[e[16]||(e[16]=t("label",{class:"mb-2 block"},"Address Line 2 (Optional)",-1)),n(v,y(l,{placeholder:"Address line 2",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),t("div",z,[n(a(c),{modelValue:a(o).bankAccountDetails.city,"onUpdate:modelValue":e[5]||(e[5]=l=>a(o).bankAccountDetails.city=l),as:"div",name:"city",class:"form-col"},{default:d(({field:l,errorMessage:s})=>[t("div",G,[e[17]||(e[17]=t("label",{class:"mb-2 block"},"City",-1)),n(v,y(l,{placeholder:"City",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),n(a(c),{modelValue:a(o).bankAccountDetails.state,"onUpdate:modelValue":e[6]||(e[6]=l=>a(o).bankAccountDetails.state=l),as:"div",name:"state",class:"form-col"},{default:d(({field:l,errorMessage:s})=>[t("div",H,[e[18]||(e[18]=t("label",{class:"mb-2 block"},"State",-1)),n(v,y(l,{placeholder:"State",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"])]),t("div",J,[n(a(c),{modelValue:a(o).bankAccountDetails.postcode,"onUpdate:modelValue":e[7]||(e[7]=l=>a(o).bankAccountDetails.postcode=l),as:"div",name:"postcode",class:"form-col"},{default:d(({field:l,errorMessage:s})=>[t("div",K,[e[19]||(e[19]=t("label",{class:"mb-2 block"},"Postcode",-1)),n(v,y(l,{placeholder:"Postcode",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),n(a(c),{modelValue:a(o).bankAccountDetails.email,"onUpdate:modelValue":e[8]||(e[8]=l=>a(o).bankAccountDetails.email=l),as:"div",name:"email",class:"form-col"},{default:d(({field:l,errorMessage:s})=>[t("div",Q,[e[20]||(e[20]=t("label",{class:"mb-2 block"},"Email Address",-1)),n(v,y(l,{placeholder:"Email address",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"])]),n(a(c),{modelValue:a(o).bankAccountDetails.bsbNumber,"onUpdate:modelValue":e[9]||(e[9]=l=>a(o).bankAccountDetails.bsbNumber=l),name:"bsbNumber"},{default:d(({field:l,errorMessage:s,handleChange:U})=>[t("div",W,[e[21]||(e[21]=t("label",{class:"mb-2 block"},"BSB Number",-1)),n(B,{"model-value":l.value,placeholder:"Enter BSB number (no dashes, 6 digits)",mask:"999-999",class:"w-full",disabled:r.readonly,"onUpdate:modelValue":U},null,8,["model-value","disabled","onUpdate:modelValue"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),n(a(c),{modelValue:a(o).bankAccountDetails.accountNumber,"onUpdate:modelValue":e[10]||(e[10]=l=>a(o).bankAccountDetails.accountNumber=l),name:"accountNumber"},{default:d(({field:l,errorMessage:s})=>[t("div",X,[e[22]||(e[22]=t("label",{class:"mb-2 block"},"Account Number",-1)),n(v,y(l,{placeholder:"Enter bank account number",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"]),n(a(c),{modelValue:a(o).bankAccountDetails.bankAccountName,"onUpdate:modelValue":e[11]||(e[11]=l=>a(o).bankAccountDetails.bankAccountName=l),name:"bankAccountName"},{default:d(({field:l,errorMessage:s})=>[t("div",Y,[e[23]||(e[23]=t("label",{class:"mb-2 block"},"Bank Account Name",-1)),n(v,y(l,{placeholder:"Name of bank account",class:"w-full",disabled:r.readonly}),null,16,["disabled"]),s?(m(),i(u,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:d(()=>[f(p(s),1)]),_:2},1024)):b("",!0)])]),_:1},8,["modelValue"])]),_:1},8,["validation-schema","initial-values"])}}}),Ve=I(Z,[["__scopeId","data-v-210ef5cb"]]);export{Ve as default};
