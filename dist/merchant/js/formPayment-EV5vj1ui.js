import{s as B}from"./index-Cbodd6BQ.js";import{s as Y}from"./index-CTms-Les.js";import{s as T}from"./index-BlFKIn6Y.js";import{s as E}from"./index-CEksU3zE.js";import{U as O,d as $,r as w,a as R,e as d,u as a,F as j,w as r,b as m,m as u,p as f,q as y,v as b,j as v,t as _,o as c}from"./index-BPwFJVSY.js";import{u as G}from"./checkout-CD3XOa-G.js";import{t as z}from"./vee-validate-zod-BFI0y82J.js";import{o as H,s as p}from"./types-AmnC9OGP.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./customer-M3EzHCil.js";import"./usePlanForm-WYcUl8Fa.js";import"./dayjs.min-Bar1rHDB.js";import"./useListRefresh-D-tL0eHu.js";import"./usePermissions-PkVCxleQ.js";import"./customer-v2cMYA98.js";import"./plan-DCOwcHy2.js";import"./integrations-C_ojvdY3.js";import"./plan-X86n_BnV.js";import"./dict-CLPQcIyV.js";const K=()=>O("/getCountry"),Q={class:"field mb-4 mt-4"},W={class:"form-row mb-4"},X={class:"field"},Z={class:"field"},M={class:"field mb-4"},ee={class:"billing-address-form mt-6"},le={class:"field mb-4"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},se={class:"field"},te={class:"field"},de={class:"field mb-4"},ie={class:"field mb-4"},oe={class:"field mb-4"},re={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"},ne={class:"field"},me={class:"field"},ue={class:"field"},pe={class:"field mb-4"},ce={class:"field mb-4"},fe=$({__name:"formPayment",setup(ye,{expose:S}){const t=G(),h=w(),F=z(H({cardNumber:p().min(13,"Card number must be between 13 and 19 digits").max(19,"Card number must be between 13 and 19 digits").refine(i=>{const l=i.replace(/\D/g,"");let o=0,n=!1;for(let g=l.length-1;g>=0;g--){let V=Number.parseInt(l.charAt(g));n&&(V*=2,V>9&&(V-=9)),o+=V,n=!n}return o%10===0},"Invalid card number"),expiryDate:p().regex(/^\d{2}\/\d{2}$/,"Expiry date must be in MM/YY format").refine(i=>{const[l,o]=i.split("/"),n=new Date,g=n.getFullYear()%100,V=n.getMonth()+1,e=Number.parseInt(l),s=Number.parseInt(o);return e<1||e>12?!1:!(s<g||s===g&&e<V)},"Card has expired or invalid date"),securityCode:p().regex(/^\d{3,4}$/,"Security code must be 3 or 4 digits"),nameOnCard:p().min(2,"Name must be at least 2 characters").refine(i=>i.includes(" "),"Please enter your full name as it appears on the card"),email:p().email("Invalid email"),country:p().min(1,"Country/Region is required"),firstName:p().min(1,"First name is required"),lastName:p().min(1,"Last name is required"),company:p().optional(),address:p().min(1,"Address is required"),addressLine2:p().optional(),city:p().min(1,"City is required"),state:p().min(1,"State/territory is required"),postcode:p().min(1,"Postcode is required"),phone:p().min(1,"Phone is required")})),A=w(!1),x=w(),N=w(),U=async()=>{var l;return await((l=h.value)==null?void 0:l.validate())},P=()=>{var i;(i=h.value)==null||i.resetForm()},D=async()=>{const i=await U();return i.valid?(t.setBillingAddress({country:i.values.country,first_name:i.values.firstName,last_name:i.values.lastName,company:i.values.company,address_line1:i.values.address,address_line2:i.values.addressLine2,city:i.values.city,state:i.values.state,postal_code:i.values.postcode,phone:i.values.phone}),i.values):!1},I=()=>{var i;return(i=h.value)==null?void 0:i.values},L=async()=>{A.value=!0;const{code:i,data:l}=await K();i===0&&(x.value=l.map(o=>({name:o.name,id:o.id,iso2:o.iso2,state:o.state})),t.billingAddress.country&&q(t.billingAddress.country,!0)),A.value=!1},q=(i,l=!1)=>{l||(t.billingAddress.state="");const o=x.value.find(n=>n.iso2===i);o&&o.state&&o.state.length>0?N.value=o==null?void 0:o.state:N.value=[]};return L(),S({validate:U,resetForm:P,submitForm:D,getCurrentValues:I}),(i,l)=>{const o=E,n=T,g=Y,V=B;return c(),R("div",null,[d(a(j),{ref_key:"formRef",ref:h,"validation-schema":a(F),class:"space-y-6",onSubmit:D},{default:r(()=>[l[17]||(l[17]=m("span",{class:"text-gray-600 mb-4 italic"}," All fields are required unless marked as optional. ",-1)),d(a(u),{modelValue:a(t).paymentDetails.cardNumber,"onUpdate:modelValue":l[0]||(l[0]=e=>a(t).paymentDetails.cardNumber=e),name:"cardNumber"},{default:r(({field:e,errorMessage:s})=>[m("div",Q,[d(o,b(e,{placeholder:"Card Number",class:"w-full"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m("div",W,[d(a(u),{modelValue:a(t).paymentDetails.expiryDate,"onUpdate:modelValue":l[1]||(l[1]=e=>a(t).paymentDetails.expiryDate=e),as:"div",name:"expiryDate",class:"form-col"},{default:r(({field:e,errorMessage:s})=>[m("div",X,[d(g,{"model-value":e.value,placeholder:"Expiration date (MM/YY)",mask:"99/99",class:"w-full"},null,8,["model-value"]),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),d(a(u),{modelValue:a(t).paymentDetails.securityCode,"onUpdate:modelValue":l[2]||(l[2]=e=>a(t).paymentDetails.securityCode=e),as:"div",name:"securityCode",class:"form-col"},{default:r(({field:e,errorMessage:s})=>[m("div",Z,[d(o,b(e,{placeholder:"Please enter the CVV(Security code)",class:"w-full"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"])]),d(a(u),{modelValue:a(t).paymentDetails.nameOnCard,"onUpdate:modelValue":l[3]||(l[3]=e=>a(t).paymentDetails.nameOnCard=e),name:"nameOnCard"},{default:r(({field:e,errorMessage:s})=>[m("div",M,[d(o,b(e,{placeholder:"Name on card",class:"w-full"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m("div",ee,[l[15]||(l[15]=m("h3",{class:"text-xl font-semibold mb-4"}," Billing address ",-1)),l[16]||(l[16]=m("p",{class:"text-gray-600 mb-4"}," Select the address that matches your card or payment method. ",-1)),m("div",le,[d(a(u),{modelValue:a(t).billingAddress.country,"onUpdate:modelValue":l[4]||(l[4]=e=>a(t).billingAddress.country=e),name:"country"},{default:r(({field:e,errorMessage:s,handleChange:k})=>[d(V,{id:"country",modelValue:e.value,"onUpdate:modelValue":[C=>e.value=C,C=>{k(C),q(C)}],class:"w-full",options:x.value,"option-label":"name","option-value":"iso2",placeholder:"Select country",loading:A.value,filter:"","show-clear":""},null,8,["modelValue","onUpdate:modelValue","options","loading"]),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),m("div",ae,[m("div",se,[d(a(u),{modelValue:a(t).billingAddress.first_name,"onUpdate:modelValue":l[5]||(l[5]=e=>a(t).billingAddress.first_name=e),name:"firstName"},{default:r(({field:e,errorMessage:s})=>[d(o,b(e,{id:"firstName",class:"w-full",placeholder:"First name"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),m("div",te,[d(a(u),{modelValue:a(t).billingAddress.last_name,"onUpdate:modelValue":l[6]||(l[6]=e=>a(t).billingAddress.last_name=e),name:"lastName"},{default:r(({field:e,errorMessage:s})=>[d(o,b(e,{id:"lastName",class:"w-full",placeholder:"Last name"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])])]),m("div",de,[d(a(u),{modelValue:a(t).billingAddress.company,"onUpdate:modelValue":l[7]||(l[7]=e=>a(t).billingAddress.company=e),name:"company"},{default:r(({field:e})=>[d(o,b(e,{id:"company",class:"w-full",placeholder:"Company (optional)"}),null,16)]),_:1},8,["modelValue"])]),m("div",ie,[d(a(u),{modelValue:a(t).billingAddress.address_line1,"onUpdate:modelValue":l[8]||(l[8]=e=>a(t).billingAddress.address_line1=e),name:"address"},{default:r(({field:e,errorMessage:s})=>[d(o,b(e,{id:"address",class:"w-full",placeholder:"Address"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),m("div",oe,[d(a(u),{modelValue:a(t).billingAddress.address_line2,"onUpdate:modelValue":l[9]||(l[9]=e=>a(t).billingAddress.address_line2=e),name:"addressLine2"},{default:r(({field:e})=>[d(o,b(e,{id:"addressLine2",class:"w-full",placeholder:"Address line 2 (optional)"}),null,16)]),_:1},8,["modelValue"])]),m("div",re,[m("div",ne,[d(a(u),{modelValue:a(t).billingAddress.city,"onUpdate:modelValue":l[10]||(l[10]=e=>a(t).billingAddress.city=e),name:"city"},{default:r(({field:e,errorMessage:s})=>[d(o,b(e,{id:"city",class:"w-full",placeholder:"City"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),m("div",me,[d(a(u),{modelValue:a(t).billingAddress.state,"onUpdate:modelValue":l[11]||(l[11]=e=>a(t).billingAddress.state=e),name:"state"},{default:r(({field:e,errorMessage:s,handleChange:k})=>[d(V,{id:"state",class:"w-full","model-value":e.value,options:N.value,"option-label":"name","option-value":"name",placeholder:"Select state",loading:A.value,disabled:!a(t).billingAddress.country,filter:"","onUpdate:modelValue":k},null,8,["model-value","options","loading","disabled","onUpdate:modelValue"]),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),m("div",ue,[d(a(u),{modelValue:a(t).billingAddress.postal_code,"onUpdate:modelValue":l[12]||(l[12]=e=>a(t).billingAddress.postal_code=e),name:"postcode"},{default:r(({field:e,errorMessage:s})=>[d(o,b(e,{id:"postcode",class:"w-full",placeholder:"Postcode"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])])]),m("div",pe,[d(a(u),{modelValue:a(t).billingAddress.phone,"onUpdate:modelValue":l[13]||(l[13]=e=>a(t).billingAddress.phone=e),name:"phone"},{default:r(({field:e,errorMessage:s})=>[d(o,b(e,{id:"phone",class:"w-full",placeholder:"Phone (required)"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"])]),d(a(u),{modelValue:a(t).paymentDetails.email,"onUpdate:modelValue":l[14]||(l[14]=e=>a(t).paymentDetails.email=e),name:"email"},{default:r(({field:e,errorMessage:s})=>[m("div",ce,[d(o,b(e,{placeholder:"Billing Email",class:"w-full"}),null,16),s?(c(),f(n,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:r(()=>[v(_(s),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"])])]),_:1,__:[17]},8,["validation-schema"])])}}}),Ee=J(fe,[["__scopeId","data-v-7629e3e6"]]);export{Ee as default};
