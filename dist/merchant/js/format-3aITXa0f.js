import{S as d,B as n,d as u,p as a}from"./plan-DCOwcHy2.js";const y=(e,r=1)=>{const t=["","k","M","B","T"],c=Math.floor(e!==0?Math.log(e)/Math.log(1e3):0),s=Math.min(c,t.length-1),f=e/1e3**s,i=10**r;let o=(Math.round(f*i)/i).toFixed(r);return o=o.replace(/\.0+$/,"").replace(/\.$/,""),o+t[s]};function M(e){const r=[{divider:1e9,suffix:"B"},{divider:1e6,suffix:"M"},{divider:1e3,suffix:"K"}];for(const t of r)if(e>=t.divider)return`${(e/t.divider).toFixed(1)}${t.suffix}`;return e.toString()}const h=e=>e.schedule_type===d.OneOff?"One-off":e.process_type?e.process_type===n.Custom?`
    ${e.custom_cycle} 
    ${u[e.custom_cycle_type]}
    `:a[e.process_type]||e.process_type:"";export{y as a,M as b,h as f};
