import{U as F,d as me,i as pe,r as h,f as _e,a as c,o as d,b as e,q as Y,e as o,w as x,u as a,s as fe,j as w,t as r,n as A,A as E,y as D,z as L,T as se,N as R,ab as ye}from"./index-BPwFJVSY.js";import{s as ve}from"./index-Cbodd6BQ.js";import{s as he}from"./index-B7pQ2UQX.js";import{s as ge}from"./index-DlkyKXFh.js";import{f as q,F as P}from"./index-Cui9J1ai.js";import{d as S}from"./dayjs.min-Bar1rHDB.js";import{s as u}from"./index-CQ6UwYfx.js";import{E as xe}from"./EnhancedDatePicker-DfTmBAld.js";import{u as be,P as m}from"./usePermissions-PkVCxleQ.js";import{i as we}from"./install-Db2jL4kZ.js";import{u as Ae,i as Te,a as Se,b as ke,d as Me,c as Ee,e as De}from"./installCanvasRenderer-p5Q0jy4r.js";import{_ as Pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";const Ce=()=>F("/home/<USER>"),B=O=>F("/home/<USER>",{params:O}),ae=(O,f)=>F("/home/<USER>",{params:{start_date:O,end_date:f}}),Ye={class:"home"},Le={class:"merchant-common-page"},Oe={class:"w-full max-w-8xl"},Ve={key:0,class:"summary mb-6"},Ue={class:"summary-title flex justify-between items-center mb-6"},Ne={class:"summary-content grid grid-cols-1 gap-y-4 lg:gap-y-0 lg:grid-cols-2 gap-x-28"},Re={class:"summary-item border-style px-8 py-4"},Ge={class:"summary-item border-style px-8 py-4"},qe={key:1,class:"summary mb-6"},Be={class:"summary-title flex justify-between items-center mb-6"},Fe={class:"date-picker-wrapper grey-color"},He={class:"flex justify-end mt-2"},Ie={class:"summary-content grid grid-cols-1 gap-y-4 lg:gap-y-0 lg:grid-cols-2 gap-x-28"},$e={class:"summary-item border-style px-8 py-4"},je={class:"item-amount"},ze={class:"item-trend flex items-center gap-2"},Xe={class:"summary-item border-style px-8 py-4"},We={class:"item-amount"},Je={class:"item-trend flex items-center gap-2"},Ke={key:2,class:"mb-8"},Qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Ze={class:"flex items-center gap-2 text-2xl font-semibold text-[#545454]"},et={class:"font-semibold text-lg my-2"},tt={class:"text-2xl"},st=["onClick"],at={key:0,class:"w-full max-w-8xl"},ot={class:"grid lg:grid-cols-4 gap-8 mt-8"},nt={class:"display-item"},it={class:"item-title"},rt={class:"pi pi-question-circle cursor-pointer",style:{color:"#b5b5b5","margin-left":"10px"}},lt={class:"display-item-amount"},ct={class:"flex items-center gap-2 px-1"},dt={class:"text-base"},ut={class:"display-item"},mt={class:"item-title"},pt={class:"pi pi-question-circle cursor-pointer",style:{color:"#b5b5b5","margin-left":"10px"}},_t={class:"display-item-amount"},ft={class:"flex items-center gap-2 px-1"},yt={class:"text-base"},vt={class:"display-item"},ht={class:"item-title"},gt={class:"pi pi-question-circle cursor-pointer",style:{color:"#b5b5b5","margin-left":"10px"}},xt={class:"display-item-amount"},bt={class:"flex items-center gap-2 px-1"},wt={class:"text-base"},At={class:"display-item"},Tt={class:"item-title"},St={class:"pi pi-question-circle cursor-pointer",style:{color:"#b5b5b5","margin-left":"10px"}},kt={class:"display-item-amount"},Mt={class:"flex items-center gap-2 px-1"},Et={class:"text-base"},Dt={class:"merchant-common-page mt-8"},Pt={class:"w-full max-w-8xl"},Ct={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Yt={key:0,class:"lg:col-span-2"},Lt={class:"mb-4 flex gap-2 items-center"},Ot={class:"mr-6"},Vt={class:"mr-6 customer-picker"},Ut={class:"bg-white rounded-lg p-6 h-[376px] shadow-sm hover:shadow-md transition-shadow"},Nt={class:"w-full h-full"},Rt={key:0,class:"flex flex-col justify-center items-center h-[90%]"},Gt={key:1,id:"chart",style:{width:"100%",height:"100%"}},qt={key:1,class:"space-y-4"},Bt={class:"flex items-center gap-2 font-semibold text-xl"},Ft={key:0,class:"text-2xl"},Ht={class:"text-2xl"},It=["onClick"],$t=me({__name:"index",setup(O){Ae([Se,ke,Me,Ee,De,we]);const{hasPermission:f,hasAnyPermission:V}=be(),H=pe(),oe=h(!1),k=h(!0),l=h(),g=h(),C=h([S().subtract(1,"month").toDate(),S().toDate()]),T=h();_e(()=>{setTimeout(()=>{oe.value=!0},300);const n=[];f(m.HOME_GET_SUM_DATA)&&n.push(Ce().then(t=>{l.value=t.data,ce(t.data)}).catch(t=>{console.error("Failed to fetch sum data:",t)})),f(m.HOME_GET_PAYMENT_DATA)&&n.push(B({days:G.value.code}).then(t=>{const i=t.data.map(b=>b==null?void 0:b.succeeded_amount),M=t.data.map(b=>b.day),y=document.getElementById("chart");if(!y)return;T.value=Te(y);const p={graphic:[{type:"text",left:"0",top:"middle",rotation:Math.PI/2,style:{text:"Payout Transaction",fontSize:14,fill:"#464646"}}],xAxis:{type:"category",data:M,name:"Transaction Count",nameLocation:"center",nameGap:30},yAxis:[{type:"value",min:0,max:40,interval:10}],legend:{data:["Series 1"],icon:"circle"},color:["#ff5f00","#09deff"],grid:{left:"45px",right:"10%",bottom:"15%",top:"11%"},series:[{name:"Series 1",data:i,type:"line",lineStyle:{color:"#ff5f00"}}]};T.value&&T.value.setOption(p)}).catch(t=>{console.error("Failed to fetch payment data:",t)})),f(m.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY)&&n.push(ae(S().subtract(1,"month").format("YYYY-MM-DD"),S().format("YYYY-MM-DD")).then(t=>{g.value=t.data}).catch(t=>{console.error("Failed to fetch business summary:",t)})),n.length>0?Promise.all(n).finally(()=>{k.value=!1}):k.value=!1});const I=h([{name:"Pending payments",amount:"0.00",currency:"AUD",detail:"View all payments",to:{name:"transactionsList",query:{status:3}}},{name:"Confirmed funds",amount:"0.00",currency:"AUD",detail:"View confirmed payments",to:{name:"transactionsList",query:{status:1}}},{name:"Pending payouts",amount:"0.00",currency:"AUD",detail:"View payouts",to:{name:"payoutList",query:{status:0}}}]),G=h({name:"Last 7 days",code:"7"}),ne=h([{name:"Last 7 days",code:"7"},{name:"Last 30 days",code:"30"},{name:"Last 60 days",code:"60"}]),$=h(new Date),j=h([{name:"Active customers",amount:"8",detail:"View customers",to:{name:"customersList",query:{category:1}}},{name:"Customers at risk",amount:"2",detail:"View customers",to:{name:"customersList",query:{category:5}}},{name:"Failed payments",amount:"$0.00",detail:"View failed payments",to:{name:"transactionsList",query:{status:2}}}]),U=h(),ie=n=>{n.name==="transactionsList"&&!V([m.TRANS_LIST])||n.name==="payoutList"&&!V([m.PAYOUT_LIST])||H.push({name:n.name,query:n.query})},re=n=>{n.name==="customersList"&&!V([m.CUSTOMER_LIST])||n.name==="transactionsList"&&!V([m.TRANS_LIST])||H.push({name:n.name,query:n.query})},le={"Pending payments":"pending_amount","Confirmed funds":"succeeded_amount","Pending payouts":"pending_payout"},N={"Active customers":"active_customer","Failed payments":"failed_amount","Customers at risk":"risk_customer"},ce=n=>{I.value.forEach(t=>{const i=le[t.name];i&&(t.amount=q(String(n[i])))}),j.value.forEach(t=>{const i=N[t.name];i&&(i==="active_customer"||i==="risk_customer"?t.amount=String(n[i]):i==="failed_amount"?t.amount=q(String(n[i])):t.amount=q(String(n[i])))})},de=async()=>{var t;if(!f(m.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY)||(U.value&&((t=U.value)!=null&&t.overlayVisible)&&(U.value.overlayVisible=!1),C.value[1]===null))return;const n={start_date:S(C.value[0]).format("YYYY-MM-DD")||null,end_date:S(C.value[1]).format("YYYY-MM-DD")||null};try{const i=await ae(n.start_date,n.end_date);i.code===0&&(g.value=i.data)}catch(i){console.error("Failed to fetch business summary:",i)}},ue=async n=>{if(f(m.HOME_GET_PAYMENT_DATA))try{const t=await B({days:n.value.code}),i=t.data.map(p=>p==null?void 0:p.succeeded_amount),M=t.data.map(p=>p.day),y={graphic:[{type:"text",left:"0",top:"middle",rotation:Math.PI/2,style:{text:"Payout Transaction",fontSize:14,fill:"#464646"}}],xAxis:{type:"category",data:M,name:"Transaction Count",nameLocation:"center",nameGap:30},yAxis:[{type:"value"}],legend:{data:["Series 1"],icon:"circle"},color:["#ff5f00","#09deff"],grid:{left:"45px",right:"10%",bottom:"15%",top:"11%"},series:[{name:"Series 1",data:i,type:"line",lineStyle:{color:"#ff5f00"}}]};T.value&&T.value&&T.value.setOption(y)}catch(t){console.error("Failed to fetch chart data:",t)}},z=async n=>{if(f(m.HOME_GET_PAYMENT_DATA))try{const t=await B({date:S(n).format("YYYY-MM-DD")}),i=t.data.map(p=>p==null?void 0:p.succeeded_amount),M=t.data.map(p=>p.day),y={graphic:[{type:"text",left:"0",top:"middle",rotation:Math.PI/2,style:{text:"Payout Transaction",fontSize:14,fill:"#464646"}}],xAxis:{type:"category",data:M,name:"Transaction Count",nameLocation:"center",nameGap:30},yAxis:[{type:"value"}],legend:{data:["Series 1"],icon:"circle"},color:["#ff5f00","#09deff"],grid:{left:"45px",right:"10%",bottom:"15%",top:"11%"},series:[{name:"Series 1",data:i,type:"line",lineStyle:{color:"#ff5f00"}}]};T.value&&T.value&&T.value.setOption(y)}catch(t){console.error("Failed to fetch date chart data:",t)}};return(n,t)=>{var X,W,J,K,Q,Z,ee,te;const i=fe,M=ge,y=he,p=ve,b=ye;return d(),c("div",Ye,[t[20]||(t[20]=e("div",{class:"welcome px-6 py-6 pt-4"},[e("div",{class:"welcome-title"}," Welcome to Bill Buddy ")],-1)),e("div",Le,[o(se,{name:"fade",appear:""},{default:x(()=>[e("div",Oe,[e("div",null,[a(f)(a(m).HOME_GET_BUSINESS_PERFORMANCE_SUMMARY)&&k.value?(d(),c("section",Ve,[e("div",Ue,[e("div",null,[t[3]||(t[3]=e("div",{class:"font-bold text-[16px] mb-2"}," Business Performance Summary ",-1)),o(a(u),{width:"300px",height:"40px"})]),e("div",null,[o(a(u),{width:"120px",height:"40px"})])]),e("div",Ne,[e("div",Re,[o(a(u),{width:"150px",height:"36px"}),o(a(u),{width:"120px",height:"24px",class:"mb-2"}),o(a(u),{width:"240px",height:"28px"})]),e("div",Ge,[o(a(u),{width:"150px",height:"36px"}),o(a(u),{width:"120px",height:"24px",class:"mb-2"}),o(a(u),{width:"240px",height:"28px"})])])])):a(f)(a(m).HOME_GET_BUSINESS_PERFORMANCE_SUMMARY)&&g.value?(d(),c("section",qe,[e("div",Be,[e("div",null,[t[5]||(t[5]=e("div",{class:"font-bold text-[16px] mb-2"}," Business Performance Summary ",-1)),e("div",Fe,[o(M,{ref_key:"summaryDatePicker",ref:U,modelValue:C.value,"onUpdate:modelValue":t[0]||(t[0]=s=>C.value=s),"selection-mode":"range","date-format":"dd/mm/yy","manual-input":!1},{footer:x(()=>[e("div",He,[o(i,{onClick:de},{default:x(()=>t[4]||(t[4]=[w(" confirm ")])),_:1,__:[4]})])]),_:1},8,["modelValue"])])])]),e("div",Ie,[e("div",$e,[e("div",je,r(a(P).formatAmount(g.value.total_revenue)),1),t[7]||(t[7]=e("div",{class:"item-subtitle mb-1 grey-color font-semibold"}," Gross Revenue ",-1)),e("div",ze,[o(y,{class:"rounded-md p-2",style:A({backgroundColor:g.value.total_revenue_rate.charAt(0)==="+"?"#e1ffa9":"#ffe3e8"})},{default:x(()=>[e("i",{class:E(["pi",g.value.total_revenue_rate.charAt(0)==="+"?"pi-arrow-up-right":"pi-arrow-down-right"])},null,2),e("span",null,r(g.value.total_revenue_rate.slice(1)),1)]),_:1},8,["style"]),t[6]||(t[6]=e("span",null," vs. previous period selected",-1))])]),e("div",Xe,[e("div",We,r(a(P).formatAmount(g.value.net_revenue)),1),t[9]||(t[9]=e("div",{class:"item-subtitle mb-1 grey-color font-semibold"}," Net Revenue ",-1)),e("div",Je,[o(y,{class:"rounded-md p-2",style:A({backgroundColor:g.value.net_revenue_rate.charAt(0)==="+"?"#e1ffa9":"#ffe3e8"})},{default:x(()=>[e("i",{class:E(["pi",g.value.net_revenue_rate.charAt(0)==="+"?"pi-arrow-up-right":"pi-arrow-down-right"])},null,2),e("span",null,r(g.value.net_revenue_rate.slice(1)),1)]),_:1},8,["style"]),t[8]||(t[8]=e("span",null," vs. previous period selected",-1))])])])])):Y("",!0),a(f)(a(m).HOME_GET_SUM_DATA)?(d(),c("section",Ke,[t[10]||(t[10]=e("h2",{class:"text-xl font-semibold mb-4"}," Today ",-1)),e("div",Qe,[k.value?(d(),c(D,{key:0},L(3,s=>e("div",{key:s,class:"bg-white rounded-2xl p-6 border border-[#545454]"},[o(a(u),{width:"180px",height:"28px"}),o(a(u),{width:"120px",height:"28px",class:"my-2"}),o(a(u),{width:"140px",height:"20px"})])),64)):(d(!0),c(D,{key:1},L(I.value,(s,_)=>(d(),c("div",{key:_,class:"bg-white rounded-2xl p-6 border border-[#545454]"},[e("div",Ze,r(s.name),1),e("div",et,[e("span",tt,r(s.amount[0]),1),w(r(s.amount.slice(1))+" "+r(s.currency),1)]),e("div",{class:"text-primary-600 underline cursor-pointer",onClick:v=>ie(s.to)},r(s.detail),9,st)]))),128))])])):Y("",!0)])])]),_:1})]),a(f)(a(m).HOME_GET_SUM_DATA)?(d(),c("div",at,[e("div",ot,[k.value?(d(),c(D,{key:0},L(4,s=>e("div",{key:s,class:"display-item"},[o(a(u),{width:"200px",height:"24px",class:"mb-4"}),o(a(u),{width:"170px",height:"36px",class:"mb-5"}),o(a(u),{width:"100%",height:"16px",class:"mb-2"}),o(a(u),{width:"100%",height:"16px"})])),64)):(d(),c(D,{key:1},[e("div",nt,[e("div",it,[t[11]||(t[11]=w(" Daily Avg. Revenue ")),R(e("i",rt,null,512),[[b,"Average revenue amount for each day since first transaction on account."]])]),e("div",lt,[w(r(a(P).formatAmount((X=l.value)==null?void 0:X.daily_average_revenue))+" ",1),o(y,{style:A([{"border-radius":"16px",background:"#e1ffa9",color:"#545454","font-weight":"500","margin-left":"5px"},{backgroundColor:((W=l.value)==null?void 0:W.daily_average_revenue.charAt(0))==="+"?"#e1ffa9":"#ffe3e8"}])},{default:x(()=>{var s,_,v;return[e("div",ct,[e("i",{class:E(["pi",((s=l.value)==null?void 0:s.daily_average_revenue.charAt(0))==="+"?"pi-arrow-up":"pi-arrow-down"]),style:A([{color:"#8bf160","font-weight":"700"},{color:((_=l.value)==null?void 0:_.daily_average_revenue_rate.charAt(0))==="+"?"#8bf160":"#ff5757"}])},null,6),e("span",dt,r((v=l.value)==null?void 0:v.daily_average_revenue_rate.slice(1)),1)])]}),_:1},8,["style"])])]),e("div",ut,[e("div",mt,[t[12]||(t[12]=w(" Successful Payment Rate ")),R(e("i",pt,null,512),[[b,"The number of successful payments divided by all transactions."]])]),e("div",_t,[w(r(a(P).formatAmount((J=l.value)==null?void 0:J.successful_payment))+" ",1),o(y,{style:A([{"border-radius":"16px",background:"#e1ffa9",color:"#545454","font-weight":"500","margin-left":"5px"},{backgroundColor:((K=l.value)==null?void 0:K.successful_payment_rate.charAt(0))==="+"?"#e1ffa9":"#ffe3e8"}])},{default:x(()=>{var s,_,v;return[e("div",ft,[e("i",{class:E(["pi",((s=l.value)==null?void 0:s.successful_payment_rate.charAt(0))==="+"?"pi-arrow-up":"pi-arrow-down"]),style:A([{color:"#8bf160","font-weight":"700"},{color:((_=l.value)==null?void 0:_.successful_payment_rate.charAt(0))==="+"?"#8bf160":"#ff5757"}])},null,6),e("span",yt,r((v=l.value)==null?void 0:v.successful_payment_rate.slice(1)),1)])]}),_:1},8,["style"])])]),e("div",vt,[e("div",ht,[t[13]||(t[13]=w(" Avg. Daily Transaction Count ")),R(e("i",gt,null,512),[[b,"Total Transaction count divided by number of days since first transaction."]])]),e("div",xt,[w(r(a(P).formatAmount((Q=l.value)==null?void 0:Q.average_daily_transaction_count))+" ",1),o(y,{style:A([{"border-radius":"16px",background:"#e1ffa9",color:"#545454","font-weight":"500","margin-left":"5px"},{backgroundColor:((Z=l.value)==null?void 0:Z.average_daily_transaction_count_rate.charAt(0))==="+"?"#e1ffa9":"#ffe3e8"}])},{default:x(()=>{var s,_,v;return[e("div",bt,[e("i",{class:E(["pi",((s=l.value)==null?void 0:s.average_daily_transaction_count_rate.charAt(0))==="+"?"pi-arrow-up":"pi-arrow-down"]),style:A([{color:"#8bf160","font-weight":"700"},{color:((_=l.value)==null?void 0:_.average_daily_transaction_count_rate.charAt(0))==="+"?"#8bf160":"#ff5757"}])},null,6),e("span",wt,r((v=l.value)==null?void 0:v.average_daily_transaction_count_rate.slice(1)),1)])]}),_:1},8,["style"])])]),e("div",At,[e("div",Tt,[t[14]||(t[14]=w(" Avg. Transaction Amount ")),R(e("i",St,null,512),[[b,"Total Transaction volume divided by Total count of transactions."]])]),e("div",kt,[w(r(a(P).formatAmount((ee=l.value)==null?void 0:ee.average_transaction_amount))+" ",1),o(y,{style:A([{"border-radius":"16px",background:"#e1ffa9",color:"#545454","font-weight":"500","margin-left":"5px"},{backgroundColor:((te=l.value)==null?void 0:te.average_transaction_amount_rate.charAt(0))==="+"?"#e1ffa9":"#ffe3e8"}])},{default:x(()=>{var s,_,v;return[e("div",Mt,[e("i",{class:E(["pi",((s=l.value)==null?void 0:s.average_transaction_amount_rate.charAt(0))==="+"?"pi-arrow-up":"pi-arrow-down"]),style:A([{color:"#8bf160","font-weight":"700"},{color:((_=l.value)==null?void 0:_.average_transaction_amount_rate.charAt(0))==="+"?"#8bf160":"#ff5757"}])},null,6),e("span",Et,r((v=l.value)==null?void 0:v.average_transaction_amount_rate.slice(1)),1)])]}),_:1},8,["style"])])])],64))])])):Y("",!0),e("div",Dt,[o(se,{name:"fade",appear:""},{default:x(()=>[e("div",Pt,[e("section",null,[e("div",Ct,[a(f)(a(m).HOME_GET_PAYMENT_DATA)?(d(),c("div",Yt,[t[18]||(t[18]=e("h2",{class:"text-xl font-semibold mb-4"}," Overview ",-1)),e("div",Lt,[e("div",Ot,[o(xe,{modelValue:$.value,"onUpdate:modelValue":[t[1]||(t[1]=s=>$.value=s),z],"min-date":a(S)().toDate(),onChange:z},null,8,["modelValue","min-date"])]),e("div",Vt,[o(p,{modelValue:G.value,"onUpdate:modelValue":t[2]||(t[2]=s=>G.value=s),options:ne.value,"option-label":"name",class:"bg-transparent",onChange:ue},{dropdownicon:x(()=>t[15]||(t[15]=[e("i",{class:"pi pi-sort-down-fill",style:{color:"#ff5f01"}},null,-1)])),_:1},8,["modelValue","options"])]),t[16]||(t[16]=e("div",{class:"flex-auto"}," Last updated 9 hours ago ",-1))]),e("div",Ut,[t[17]||(t[17]=e("div",{class:"mr-4 text-xl font-semibold text-[#545454]"}," Collected Payments ",-1)),e("div",Nt,[k.value?(d(),c("div",Rt,[o(a(u),{width:"90%",height:"200px"})])):(d(),c("div",Gt))])])])):Y("",!0),a(f)(a(m).HOME_GET_SUM_DATA)?(d(),c("div",qt,[t[19]||(t[19]=e("div",{class:"text-xl font-semibold mb-6"}," Account health ",-1)),k.value?(d(),c(D,{key:0},L(3,s=>e("div",{key:s,class:"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"},[o(a(u),{width:"180px",height:"28px"}),o(a(u),{width:"80px",height:"32px",class:"my-2"}),o(a(u),{width:"140px",height:"20px"})])),64)):(d(!0),c(D,{key:1},L(j.value,(s,_)=>(d(),c("div",{key:_,class:"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"},[e("div",Bt,r(s.name),1),e("div",{class:E(["font-semibold text-xl my-2",N[s.name]==="active_customer"?"text-[#39b54a]":"text-[#eb001b]"])},[N[s.name]==="active_customer"||N[s.name]==="risk_customer"?(d(),c("span",Ft,r(s.amount),1)):(d(),c(D,{key:1},[e("span",Ht,r(s.amount[0]),1),e("span",null,r(s.amount.slice(1)),1)],64))],2),e("div",{class:"text-primary-600 underline cursor-pointer",onClick:v=>re(s.to)},r(s.detail),9,It)]))),128))])):Y("",!0)])])])]),_:1})])])}}}),us=Pe($t,[["__scopeId","data-v-1fd86ce5"]]);export{us as default};
