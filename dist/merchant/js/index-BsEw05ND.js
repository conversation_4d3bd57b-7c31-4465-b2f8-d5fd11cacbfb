import{s as o}from"./index-CQ6UwYfx.js";import{d as _,i as w,r as p,J as C,k,a as l,o as d,b as s,y as u,e,u as a}from"./index-BPwFJVSY.js";import{a as E}from"./customer-M3EzHCil.js";import{E as F,u as U}from"./edit-DVqRIdR4.js";import{_ as B}from"./emailPreview.vue_vue_type_script_setup_true_lang-CHlRa3Od.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DMmJJ4dH.js";import"./index-B7pQ2UQX.js";import"./index-BAFB6y2b.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-DgYXzrxo.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./color-DqhIgFrh.js";import"./customerUpload-CJJNLUYL.js";import"./downloadCenter-xnP8j9T6.js";import"./index-BlFKIn6Y.js";import"./index-SkqdGoZA.js";import"./logo-CiIwT2Ho.js";const T={class:"notification"},L={class:"notification-container flex justify-between items-start gap-4"},S={class:"notification-edit w-3/5"},I={class:"title-container mb-6 pb-4 border-[#d8d8d8] border-b-2"},V={class:"edit-skeleton-container"},j={class:"notification-preview w-2/5"},D={class:"preview-skeleton"},J=_({__name:"index",setup(O){const b=w(),m=p("#fe4c1c"),n=p([]),f=p(""),h=C(),{t:c}=k(),r=p(!0),g=t=>{m.value=t},x=t=>{f.value=t},y=async t=>{r.value=!0;try{const i={notification_type:t,content:null,logo:n.value[0].url,theme:m.value};(await U(i)).code===0&&(h.add({severity:"success",summary:c("common.success","Success"),detail:"Successfully updated",life:3e3}),b.push({name:"customersList"}))}catch(i){console.error("Update notification error:",i),h.add({severity:"error",summary:c("common.error","Error"),detail:c("common.operationFailed","Operation failed"),life:3e3})}finally{r.value=!1}};return(async()=>{r.value=!0;try{const t=await E();t.code===0&&(n.value=[{url:t.data.logo}],m.value=t.data.theme)}catch(t){console.error("Fetch config error:",t),h.add({severity:"error",summary:c("common.error","Error"),detail:c("common.fetchFailed","Failed to fetch data"),life:3e3})}finally{r.value=!1}})(),(t,i)=>(d(),l("div",T,[s("div",L,[s("div",S,[r.value?(d(),l(u,{key:0},[s("div",I,[e(a(o),{class:"mb-2",height:"40px",width:"80%"}),e(a(o),{class:"mb-2",height:"20px",width:"100%"}),e(a(o),{class:"mb-2",height:"20px",width:"90%"})]),s("div",V,[e(a(o),{class:"mb-4",height:"60px",width:"100%"}),e(a(o),{class:"mb-4",height:"50px",width:"40%"}),e(a(o),{class:"mb-6",height:"100px",width:"100%"}),e(a(o),{class:"mb-4",height:"200px",width:"100%"}),e(a(o),{class:"mb-4",height:"50px",width:"30%","border-radius":"16px"})])],64)):(d(),l(u,{key:1},[i[1]||(i[1]=s("div",{class:"title-container mb-6 pb-4 border-[#d8d8d8] border-b-2"},[s("div",{class:"title text-[26.6px] font-extrabold mb-2"}," Brand Your Experience "),s("div",{class:"subtitle text-[#545454]"}," Create a professional payment experience - customise how your payment pages and email notifications will appear to customers. ")],-1)),s("div",null,[e(F,{logo:n.value,"onUpdate:logo":i[0]||(i[0]=v=>n.value=v),"onUpdate:selectedColor":g,"onUpdate:changeContent":x,"onUpdate:changeTemplateType":y},null,8,["logo"])])],64))]),s("div",j,[r.value?(d(),l(u,{key:0},[e(a(o),{class:"mb-4",height:"40px",width:"50%"}),s("div",D,[e(a(o),{class:"mb-4",height:"60px",width:"100%"}),e(a(o),{class:"mb-4",height:"250px",width:"100%","border-radius":"8px"})])],64)):(d(),l(u,{key:1},[i[2]||(i[2]=s("div",{class:"text-[26.6px] font-extrabold mb-2"}," Preview ",-1)),e(B,{"header-color":m.value,"logo-src":n.value,content:f.value},null,8,["header-color","logo-src","content"])],64))])])]))}}),re=N(J,[["__scopeId","data-v-be505074"]]);export{re as default};
