import{d as _,r as b,f as m,g,a as f,b as s,h,t as e,e as r,w as l,u as c,s as p,n as k,i as B,j as v,o as N}from"./index-BPwFJVSY.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const w={class:"not-found"},y={class:"content"},C={class:"error-container"},I={class:"error-message"},V={class:"error-description"},x={class:"actions"},H={class:"progress-container"},S={class:"progress-text"},T={class:"progress-bar"},D=_({__name:"index",setup(F){const d=B(),i=()=>{d.push({path:"/"})},u=()=>{d.back()},n=b(0);let t=null;return m(()=>{t=setInterval(()=>{n.value+=1,n.value>=100&&(t&&(clearInterval(t),t=null),i())},50)}),g(()=>{t&&(clearInterval(t),t=null)}),(o,a)=>(N(),f("div",w,[s("div",y,[s("div",C,[a[2]||(a[2]=h('<div class="error-code" data-v-d6db7782><span class="digit" data-v-d6db7782>4</span><div class="planet-container" data-v-d6db7782><div class="planet" data-v-d6db7782></div><div class="orbit" data-v-d6db7782><div class="satellite" data-v-d6db7782></div></div></div><span class="digit" data-v-d6db7782>4</span></div>',1)),s("h2",I,e(o.$t("message.pageNotFound")),1),s("p",V,e(o.$t("message.pageNotFoundDescription")),1),s("div",x,[r(c(p),{class:"p-button-primary go-back-btn",onClick:u},{default:l(()=>[a[0]||(a[0]=s("i",{class:"pi pi-arrow-left"},null,-1)),v(" "+e(o.$t("button.goBack")),1)]),_:1,__:[0]}),r(c(p),{class:"p-button-outlined home-btn",onClick:i},{default:l(()=>[a[1]||(a[1]=s("i",{class:"pi pi-home"},null,-1)),v(" "+e(o.$t("button.backToHome")),1)]),_:1,__:[1]})]),s("div",H,[s("div",S,e(o.$t("message.redirectingToHome"))+" "+e(n.value)+"% ",1),s("div",T,[s("div",{class:"progress-fill",style:k({width:`${n.value}%`})},null,4)])])])])]))}}),E=$(D,[["__scopeId","data-v-d6db7782"]]);export{E as default};
