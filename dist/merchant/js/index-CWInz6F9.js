import{V as q,U as oe,d as ie,k as ne,J as me,r as _,c as re,Q as P,a as de,o as b,e as t,b as m,u as l,s as ce,w as s,j as y,t as g,F as $,m as h,p as C,q as S,v as V,X as ue}from"./index-BPwFJVSY.js";import{s as pe}from"./index-BlFKIn6Y.js";import{s as fe}from"./index-CEksU3zE.js";import{B as ve}from"./BaseDataTable-BVkRnzNI.js";import{_ as _e}from"./BaseTag-dd68UHCy.js";import{_ as be}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{S as ye,_ as ge}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{t as A}from"./vee-validate-zod-BFI0y82J.js";import{u as xe}from"./useListRefresh-D-tL0eHu.js";import{u as he}from"./useRequestList-DvWcnSzt.js";import{f as Ce}from"./date-D3KeWVvs.js";import{o as L,s as k}from"./types-AmnC9OGP.js";import"./index-CxOnO8if.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./index-BFXZ3UuW.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./dayjs.min-Bar1rHDB.js";const Se=d=>oe("/xero/getContactList",{params:d}),Ve=d=>q("/xero/addContact",d),ke=d=>q("/xero/updateContact",d),we={class:"contact-page"},Te={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},De={class:"flex justify-end gap-2 mt-4"},Be={class:"mb-2"},Ee={class:"flex justify-end gap-2 mt-4"},oa=ie({name:"contactList",__name:"index",setup(d){const{t:w}=ne(),E=me(),W=_([{field:"id",header:"ID",style:{width:"80px"}},{field:"name",header:"Name",style:{minWidth:"120px"}},{field:"email",header:"Email",style:{minWidth:"180px"}},{field:"contact_status",header:"Status",style:{minWidth:"100px"},template:"status"},{field:"contact_id",header:"Xero Contact ID",style:{minWidth:"200px"}},{field:"created_at",header:w("common.created","Created Date"),template:"created_at",sortable:!0,style:{minWidth:"180px"}},{field:"action",header:"",template:"action",style:{width:"50px"}}]),{list:j,loading:N,total:X,refresh:T,search:U,onPageChange:R,failed:O,failureMessage:z,setSearchParams:F}=he({requestFn:Se});xe("contactList",T);const G=i=>i&&{ACTIVE:"paid",ARCHIVED:"failed",INACTIVE:"warning"}[i]||"upcoming",H=i=>{const{sortField:e,sortOrder:B}=i;F({sort_by:e,sort_order:B===1?"asc":"desc"}),U()},D=_({name:""}),J=re(()=>[{name:"name",label:"Name",type:ye.TEXT,placeholder:"Search by name",maxlength:50,defaultValue:""}]),p=_(!1),f=_(!1),r=_(!1),M=A(L({name:k().min(1,{message:"Name is required"}),email:k().email({message:"Please enter a valid email"})})),Q=A(L({name:k().min(1,{message:"Name is required"}),email:k().email({message:"Please enter a valid email"})})),c=P({name:"",email:""}),o=P({id:null,contact_id:"",name:"",email:""}),K=()=>{c.name="",c.email="",p.value=!0},Y=i=>{o.id=i.id,o.contact_id=i.contact_id,o.name=i.name,o.email=i.email,f.value=!0},Z=i=>{r.value=!0,Ve(i).then(e=>{e.code===0&&(E.add({severity:"success",summary:w("common.success","Success"),detail:"Contact created successfully",life:3e3}),p.value=!1,T()),r.value=!1}).catch(()=>{r.value=!1})},ee=async()=>{r.value=!0;try{(await ke({id:o.id,contact_id:o.contact_id,name:o.name,email:o.email})).code===0&&(E.add({severity:"success",summary:w("common.success","Success"),detail:"Contact updated successfully",life:3e3}),f.value=!1,T()),r.value=!1}catch{r.value=!1}},ae=()=>{F(D.value),U()};return(i,e)=>{const B=ge,u=ce,te=be,le=_e,se=ve,v=fe,x=pe,I=ue;return b(),de("div",we,[t(B,{modelValue:D.value,"onUpdate:modelValue":e[0]||(e[0]=a=>D.value=a),loading:l(N),"basic-search-fields":J.value,onSearch:ae},null,8,["modelValue","loading","basic-search-fields"]),m("div",Te,[t(u,{label:"Add Contact",icon:"pi pi-plus",onClick:K})]),t(se,{"show-search-bar":!1,value:l(j),columns:W.value,scrollable:!0,"show-multiple-column":!1,loading:l(N),paginator:!0,rows:20,"total-records":l(X),lazy:!0,"data-key":"id",failed:l(O),"failure-message":l(z),"striped-rows":!0,"search-placeholder":"Search contacts...",onPage:e[1]||(e[1]=a=>l(R)(a)),onSort:H},{action:s(({data:a})=>[t(te,{"content-width":"50px"},{default:s(()=>[t(u,{icon:"pi pi-pencil",text:"",onClick:n=>Y(a)},null,8,["onClick"])]),_:2},1024)]),status:s(({data:a})=>[t(le,{text:a.contact_status,type:G(a.contact_status)},null,8,["text","type"])]),created_at:s(({data:a})=>[y(g(l(Ce)(a.created_at)),1)]),_:1},8,["value","columns","loading","total-records","failed","failure-message"]),t(I,{visible:p.value,"onUpdate:visible":e[5]||(e[5]=a=>p.value=a),modal:"",header:"Add Contact",style:{width:"500px"},closable:!0},{default:s(()=>[t(l($),{"validation-schema":l(M),class:"flex flex-col gap-4",onSubmit:Z},{default:s(()=>[t(l(h),{modelValue:c.name,"onUpdate:modelValue":e[2]||(e[2]=a=>c.name=a),as:"div",class:"mb-2",name:"name"},{default:s(({field:a,errorMessage:n})=>[e[10]||(e[10]=m("label",{for:"name",class:"mb-2 block"},"Name",-1)),t(v,V({id:"name",class:"w-full"},a),null,16),n?(b(),C(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[y(g(n),1)]),_:2},1024)):S("",!0)]),_:1,__:[10]},8,["modelValue"]),t(l(h),{modelValue:c.email,"onUpdate:modelValue":e[3]||(e[3]=a=>c.email=a),as:"div",class:"mb-2",name:"email"},{default:s(({field:a,errorMessage:n})=>[e[11]||(e[11]=m("label",{for:"email",class:"mb-2 block"},"Email",-1)),t(v,V({id:"email",class:"w-full"},a),null,16),n?(b(),C(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[y(g(n),1)]),_:2},1024)):S("",!0)]),_:1,__:[11]},8,["modelValue"]),m("div",De,[t(u,{type:"button",label:"Cancel",severity:"secondary",onClick:e[4]||(e[4]=a=>p.value=!1)}),t(u,{type:"submit",label:"Submit",loading:r.value},null,8,["loading"])])]),_:1},8,["validation-schema"])]),_:1},8,["visible"]),t(I,{visible:f.value,"onUpdate:visible":e[9]||(e[9]=a=>f.value=a),modal:"",header:"Edit Contact",style:{width:"500px"},closable:!0},{default:s(()=>[t(l($),{"validation-schema":l(Q),class:"flex flex-col gap-4",onSubmit:ee},{default:s(()=>[m("div",Be,[e[12]||(e[12]=m("label",{for:"contact_id",class:"mb-2 block"},"Contact ID (Xero)",-1)),t(v,{id:"contact_id",class:"w-full",value:o.contact_id,disabled:""},null,8,["value"])]),t(l(h),{modelValue:o.name,"onUpdate:modelValue":e[6]||(e[6]=a=>o.name=a),as:"div",class:"mb-2",name:"name"},{default:s(({field:a,errorMessage:n})=>[e[13]||(e[13]=m("label",{for:"edit_name",class:"mb-2 block"},"Name",-1)),t(v,V({id:"edit_name",class:"w-full"},a),null,16),n?(b(),C(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[y(g(n),1)]),_:2},1024)):S("",!0)]),_:1,__:[13]},8,["modelValue"]),t(l(h),{modelValue:o.email,"onUpdate:modelValue":e[7]||(e[7]=a=>o.email=a),as:"div",class:"mb-2",name:"email"},{default:s(({field:a,errorMessage:n})=>[e[14]||(e[14]=m("label",{for:"edit_email",class:"mb-2 block"},"Email",-1)),t(v,V({id:"edit_email",class:"w-full"},a),null,16),n?(b(),C(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[y(g(n),1)]),_:2},1024)):S("",!0)]),_:1,__:[14]},8,["modelValue"]),m("div",Ee,[t(u,{type:"button",label:"Cancel",severity:"secondary",onClick:e[8]||(e[8]=a=>f.value=!1)}),t(u,{type:"submit",label:"Update",loading:r.value},null,8,["loading"])])]),_:1},8,["validation-schema"])]),_:1},8,["visible"])])}}});export{oa as default};
