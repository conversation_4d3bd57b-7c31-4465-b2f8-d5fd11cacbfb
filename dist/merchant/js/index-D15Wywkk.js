import{U as Q,V as I,d as ye,k as be,J as _e,W as xe,r as f,c as ge,Q as O,a as R,o as y,e as l,b as a,u as o,s as he,w as s,j as b,t as c,F as X,m as g,p as h,q as _,v as w,X as Ae}from"./index-BPwFJVSY.js";import{s as ke}from"./index-Cbodd6BQ.js";import{s as Ve}from"./index-BlFKIn6Y.js";import{s as Ce}from"./index-CEksU3zE.js";import{B as Se}from"./BaseDataTable-BVkRnzNI.js";import{_ as De}from"./BaseTag-dd68UHCy.js";import{_ as Te}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{S as G,_ as we}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{t as H}from"./vee-validate-zod-BFI0y82J.js";import{u as Be}from"./useDict-sWoVNiZj.js";import{u as Ne}from"./useListRefresh-D-tL0eHu.js";import{u as Ue}from"./useRequestList-DvWcnSzt.js";import{f as F}from"./date-D3KeWVvs.js";import{o as J,s as A}from"./types-AmnC9OGP.js";import{_ as $e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-CxOnO8if.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./index-BFXZ3UuW.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";const Ee=r=>Q("xero/getAccountList",{params:r}),Fe=r=>Q("xero/getAccountDetail",{params:r}),Ie=r=>I("xero/addAccount",r),qe=r=>I("xero/updateAccount",r),Pe=r=>I("xero/deleteAccount",r),We={class:"account-page"},Le={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},je={class:"flex justify-end gap-2 mt-4"},ze={class:"flex justify-end gap-2 mt-4"},Oe={key:0,class:"p-3"},Re={class:"grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4"},Xe={class:"text-base"},Ge={class:"text-base"},He={class:"text-base"},Je={class:"mt-1"},Qe={class:"font-mono break-all"},Ke={class:"font-mono break-all"},Ye={class:"text-base"},Ze={class:"text-base"},Me={class:"text-base"},et={class:"text-base"},tt={class:"flex justify-end gap-2 mt-8"},at=ye({name:"accountList",__name:"index",setup(r){const{t:C}=be(),B=_e(),K=xe(),Y=f([{field:"id",header:"ID",style:{width:"80px"}},{field:"account_code",header:"Account Code",style:{minWidth:"120px"}},{field:"name",header:"Name",style:{minWidth:"120px"}},{field:"type",header:"Type",style:{minWidth:"100px"}},{field:"status",header:"Status",style:{minWidth:"100px"},template:"status"},{field:"bank_account_number",header:"Bank Account Number",style:{minWidth:"150px"}},{field:"bank_account_type",header:"Bank Account Type",style:{minWidth:"120px"}},{field:"created_at",header:C("common.created","Created Date"),template:"created_at",sortable:!0,style:{minWidth:"180px"}},{field:"action",header:"",template:"action",style:{width:"50px"},alignFrozen:"right",frozen:!0}]),{list:Z,loading:q,total:M,refresh:S,search:P,onPageChange:ee,failed:te,failureMessage:ae,setSearchParams:W}=Ue({requestFn:Ee});Ne("accountList",S);const L=n=>n&&{ACTIVE:"paid",ARCHIVED:"failed",INACTIVE:"warning"}[n]||"upcoming",le=n=>{const{sortField:e,sortOrder:$}=n;W({sort_by:e,sort_order:$===1?"asc":"desc"}),P()},N=f({name:""}),se=ge(()=>[{name:"name",label:"Name",type:G.TEXT,placeholder:"Search by name",maxlength:50,defaultValue:""},{name:"account_code",label:"Account Code",type:G.TEXT,placeholder:"Search by account code",maxlength:50,defaultValue:""}]),k=f(!1),V=f(!1),D=f(!1),d=f(!1),U=f([]),oe=H(J({account_code:A().min(1,{message:"Account code is required"}),name:A().min(1,{message:"Name is required"}),type:A().min(1,{message:"Please select a type"})})),ne=H(J({account_code:A().min(1,{message:"Account code is required"}),name:A().min(1,{message:"Name is required"}),type:A().min(1,{message:"Please select a type"})})),p=O({account_code:"",name:"",type:""}),u=O({id:null,name:"",type:"",account_code:""}),m=f(null),ie=()=>{p.account_code="",p.name="",p.type="",k.value=!0},ce=n=>{u.id=n.id,u.name=n.name,u.type=n.type,u.account_code=n.account_code,V.value=!0},ue=n=>{d.value=!0,Fe({id:n.id}).then(e=>{e.code===0&&(m.value=e.data,D.value=!0),d.value=!1}).catch(()=>{d.value=!1})},de=n=>{K.require({message:"Are you sure you want to delete this account?",header:"Delete Confirmation",icon:"pi pi-exclamation-triangle",acceptClass:"p-button-danger",accept:()=>{d.value=!0,Pe({id:n.id}).then(e=>{e.code===0&&(B.add({severity:"success",summary:C("common.success","Success"),detail:"Account deleted successfully",life:3e3}),S()),d.value=!1}).catch(()=>{d.value=!1})}})},me=n=>{d.value=!0,Ie(n).then(e=>{e.code===0&&(B.add({severity:"success",summary:C("common.success","Success"),detail:"Account created successfully",life:3e3}),k.value=!1,S()),d.value=!1}).catch(()=>{d.value=!1})},re=async()=>{d.value=!0;try{(await qe({id:u.id,name:u.name,type:u.type,account_code:u.account_code})).code===0&&(B.add({severity:"success",summary:C("common.success","Success"),detail:"Account updated successfully",life:3e3}),V.value=!1,S()),d.value=!1}catch{d.value=!1}},pe=()=>{W(N.value),P()};return Be("xero_account_type",n=>{U.value=n}),(n,e)=>{const $=we,v=he,ve=Te,j=De,fe=Se,T=Ce,x=Ve,z=ke,E=Ae;return y(),R("div",We,[l($,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=t=>N.value=t),loading:o(q),"basic-search-fields":se.value,onSearch:pe},null,8,["modelValue","loading","basic-search-fields"]),a("div",Le,[l(v,{label:"Add Account",icon:"pi pi-plus",onClick:ie})]),l(fe,{"show-search-bar":!1,value:o(Z),columns:Y.value,scrollable:!0,"show-multiple-column":!1,loading:o(q),paginator:!0,rows:20,"total-records":o(M),lazy:!0,"data-key":"id",failed:o(te),"failure-message":o(ae),"striped-rows":!0,"search-placeholder":"Search accounts...",onPage:e[1]||(e[1]=t=>o(ee)(t)),onSort:le},{action:s(({data:t})=>[l(ve,{"content-width":"120px"},{default:s(()=>[l(v,{icon:"pi pi-eye",text:"",title:"View Details",onClick:i=>ue(t)},null,8,["onClick"]),l(v,{icon:"pi pi-pencil",text:"",title:"Edit",onClick:i=>ce(t)},null,8,["onClick"]),l(v,{icon:"pi pi-trash",text:"",severity:"danger",title:"Delete",onClick:i=>de(t)},null,8,["onClick"])]),_:2},1024)]),status:s(({data:t})=>[l(j,{text:t.status,type:L(t.status)},null,8,["text","type"])]),created_at:s(({data:t})=>[b(c(o(F)(t.created_at)),1)]),_:1},8,["value","columns","loading","total-records","failed","failure-message"]),l(E,{visible:k.value,"onUpdate:visible":e[6]||(e[6]=t=>k.value=t),modal:"",header:"Add Account",style:{width:"500px"},closable:!0},{default:s(()=>[l(o(X),{"validation-schema":o(oe),class:"flex flex-col gap-4",onSubmit:me},{default:s(()=>[l(o(g),{modelValue:p.account_code,"onUpdate:modelValue":e[2]||(e[2]=t=>p.account_code=t),as:"div",class:"mb-2",name:"account_code"},{default:s(({field:t,errorMessage:i})=>[e[14]||(e[14]=a("label",{for:"account_code",class:"mb-2 block"},"Account Code",-1)),l(T,w({id:"account_code",class:"w-full"},t,{placeholder:"e.g., test001"}),null,16),i?(y(),h(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[b(c(i),1)]),_:2},1024)):_("",!0)]),_:1,__:[14]},8,["modelValue"]),l(o(g),{modelValue:p.name,"onUpdate:modelValue":e[3]||(e[3]=t=>p.name=t),as:"div",class:"mb-2",name:"name"},{default:s(({field:t,errorMessage:i})=>[e[15]||(e[15]=a("label",{for:"name",class:"mb-2 block"},"Name",-1)),l(T,w({id:"name",class:"w-full"},t),null,16),i?(y(),h(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[b(c(i),1)]),_:2},1024)):_("",!0)]),_:1,__:[15]},8,["modelValue"]),l(o(g),{"model-value":p.type,as:"div",class:"mb-2",name:"type"},{default:s(({errorMessage:t})=>[e[16]||(e[16]=a("label",{for:"type",class:"mb-2 block"},"Type",-1)),l(z,{id:"type",modelValue:p.type,"onUpdate:modelValue":e[4]||(e[4]=i=>p.type=i),class:"w-full",options:U.value,"option-label":"label","option-value":"value",placeholder:"Select type"},null,8,["modelValue","options"]),t?(y(),h(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[b(c(t),1)]),_:2},1024)):_("",!0)]),_:1,__:[16]},8,["model-value"]),a("div",je,[l(v,{type:"button",label:"Cancel",severity:"secondary",onClick:e[5]||(e[5]=t=>k.value=!1)}),l(v,{type:"submit",label:"Submit",loading:d.value},null,8,["loading"])])]),_:1},8,["validation-schema"])]),_:1},8,["visible"]),l(E,{visible:V.value,"onUpdate:visible":e[11]||(e[11]=t=>V.value=t),modal:"",header:"Edit Account",style:{width:"500px"},closable:!0},{default:s(()=>[l(o(X),{"validation-schema":o(ne),class:"flex flex-col gap-4",onSubmit:re},{default:s(()=>[l(o(g),{modelValue:u.account_code,"onUpdate:modelValue":e[7]||(e[7]=t=>u.account_code=t),as:"div",class:"mb-2",name:"account_code"},{default:s(({field:t,errorMessage:i})=>[e[17]||(e[17]=a("label",{for:"account_code",class:"mb-2 block"},"Account Code",-1)),l(T,w({id:"account_code",class:"w-full"},t,{placeholder:"e.g., test001"}),null,16),i?(y(),h(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[b(c(i),1)]),_:2},1024)):_("",!0)]),_:1,__:[17]},8,["modelValue"]),l(o(g),{modelValue:u.name,"onUpdate:modelValue":e[8]||(e[8]=t=>u.name=t),as:"div",class:"mb-2",name:"name"},{default:s(({field:t,errorMessage:i})=>[e[18]||(e[18]=a("label",{for:"edit_name",class:"mb-2 block"},"Name",-1)),l(T,w({id:"edit_name",class:"w-full"},t),null,16),i?(y(),h(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[b(c(i),1)]),_:2},1024)):_("",!0)]),_:1,__:[18]},8,["modelValue"]),l(o(g),{"model-value":u.type,as:"div",class:"mb-2",name:"type"},{default:s(({errorMessage:t})=>[e[19]||(e[19]=a("label",{for:"edit_type",class:"mb-2 block"},"Type",-1)),l(z,{id:"edit_type",modelValue:u.type,"onUpdate:modelValue":e[9]||(e[9]=i=>u.type=i),class:"w-full",options:U.value,"option-label":"label","option-value":"value",placeholder:"Select type"},null,8,["modelValue","options"]),t?(y(),h(x,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:s(()=>[b(c(t),1)]),_:2},1024)):_("",!0)]),_:1,__:[19]},8,["model-value"]),a("div",ze,[l(v,{type:"button",label:"Cancel",severity:"secondary",onClick:e[10]||(e[10]=t=>V.value=!1)}),l(v,{type:"submit",label:"Update",loading:d.value},null,8,["loading"])])]),_:1},8,["validation-schema"])]),_:1},8,["visible"]),l(E,{visible:D.value,"onUpdate:visible":e[13]||(e[13]=t=>D.value=t),modal:"",header:"Account Details",style:{width:"600px"},closable:!0},{default:s(()=>[m.value?(y(),R("div",Oe,[a("div",Re,[a("div",null,[e[20]||(e[20]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Account Code ",-1)),a("div",Xe,c(m.value.account_code),1)]),a("div",null,[e[21]||(e[21]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Name ",-1)),a("div",Ge,c(m.value.name),1)]),a("div",null,[e[22]||(e[22]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Type ",-1)),a("div",He,c(m.value.type),1)]),a("div",null,[e[23]||(e[23]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Status ",-1)),a("div",Je,[l(j,{text:m.value.status,type:L(m.value.status)},null,8,["text","type"])])]),a("div",null,[e[24]||(e[24]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Account ID ",-1)),a("div",Qe,c(m.value.account_id),1)]),a("div",null,[e[25]||(e[25]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Tenant ID ",-1)),a("div",Ke,c(m.value.tenant_id),1)]),a("div",null,[e[26]||(e[26]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Bank Account Number ",-1)),a("div",Ye,c(m.value.bank_account_number||"-"),1)]),a("div",null,[e[27]||(e[27]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Bank Account Type ",-1)),a("div",Ze,c(m.value.bank_account_type||"-"),1)]),a("div",null,[e[28]||(e[28]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Created Date ",-1)),a("div",Me,c(o(F)(m.value.created_at)),1)]),a("div",null,[e[29]||(e[29]=a("h3",{class:"text-gray-600 text-sm font-medium mb-1"}," Updated Date ",-1)),a("div",et,c(o(F)(m.value.updated_at)),1)])]),a("div",tt,[l(v,{type:"button",label:"Close",class:"p-button-primary",onClick:e[12]||(e[12]=t=>D.value=!1)})])])):_("",!0)]),_:1},8,["visible"])])}}}),$t=$e(at,[["__scopeId","data-v-c7254378"]]);export{$t as default};
