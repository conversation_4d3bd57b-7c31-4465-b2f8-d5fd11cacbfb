import{Z as Rt,r as A,D as Ie,E as ke,a1 as Q,a2 as we,a as c,o as s,I as E,p as w,q as y,v as f,t as R,A as C,a6 as N,as as Oe,ah as It,G as kt,ay as wt,aB as Lt,aA as Y,aC as F,aD as Ct,ax as O,a3 as ye,a4 as te,a_ as Ke,ai as Ee,b as p,N as Ae,H as B,w as P,e as L,T as re,y as x,z as $,j as Bt,d as Z,B as De,i as Le,k as Te,c as K,l as St,bX as Ce,bu as U,S as _t,s as Pt,u as I,a7 as ee,X as Ft,F as Ht,m as Nt,ap as X,au as vt,b1 as Pe,aH as Ut,bY as $t,ag as Zt,aR as Gt,aS as jt,aV as qt,aT as Wt,f as Xt,R as Yt,bZ as Qt,b_ as Jt}from"./index-BPwFJVSY.js";import{s as ei}from"./index-AtQkrpJ9.js";import{s as ti}from"./index-D7FsVJ-c.js";import{s as ii}from"./index-CCP8zFp2.js";import{O as Mt}from"./index-BjQt0t51.js";import{s as ni}from"./index-DUgPT_03.js";import{s as ri}from"./index-BFXZ3UuW.js";import{u as ai,n as si,g as oi,d as li}from"./getAssetsFile-DC-dlhh8.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{s as xt}from"./index-CBr4Z_s8.js";import{b as ui}from"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import{s as di}from"./index-BJi8tw4J.js";import{b as ci}from"./BillbuddyLogo-FvDXaJ4h.js";import"./index-CEksU3zE.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-kWy1s9tq.js";import"./PMI-logo-4SeFW8S8.js";import"./logo-CiIwT2Ho.js";import"./color-DqhIgFrh.js";import"./invioce-payment-Bs21HoFI.js";import"./isUpdated-DXQm7BuR.js";import"./login-bg-kShksnUz.js";import"./index-DDQWKtix.js";const ze=Rt("layout",()=>{const t=A(!0),e=A(!1),i=A(!1);return{isSidebarVisible:t,isSidebarSlim:e,isDarkTheme:i,setSidebarMode:m=>{e.value=m==="slim"},toggleSidebarVisible:()=>{t.value=!t.value},toggleDarkTheme:()=>{i.value=!i.value}}},{persist:!0});var mi=Ie`
    .p-avatar {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: dt('avatar.width');
        height: dt('avatar.height');
        font-size: dt('avatar.font.size');
        background: dt('avatar.background');
        color: dt('avatar.color');
        border-radius: dt('avatar.border.radius');
    }

    .p-avatar-image {
        background: transparent;
    }

    .p-avatar-circle {
        border-radius: 50%;
    }

    .p-avatar-circle img {
        border-radius: 50%;
    }

    .p-avatar-icon {
        font-size: dt('avatar.icon.size');
        width: dt('avatar.icon.size');
        height: dt('avatar.icon.size');
    }

    .p-avatar img {
        width: 100%;
        height: 100%;
    }

    .p-avatar-lg {
        width: dt('avatar.lg.width');
        height: dt('avatar.lg.width');
        font-size: dt('avatar.lg.font.size');
    }

    .p-avatar-lg .p-avatar-icon {
        font-size: dt('avatar.lg.icon.size');
        width: dt('avatar.lg.icon.size');
        height: dt('avatar.lg.icon.size');
    }

    .p-avatar-xl {
        width: dt('avatar.xl.width');
        height: dt('avatar.xl.width');
        font-size: dt('avatar.xl.font.size');
    }

    .p-avatar-xl .p-avatar-icon {
        font-size: dt('avatar.xl.icon.size');
        width: dt('avatar.xl.icon.size');
        height: dt('avatar.xl.icon.size');
    }

    .p-avatar-group {
        display: flex;
        align-items: center;
    }

    .p-avatar-group .p-avatar + .p-avatar {
        margin-inline-start: dt('avatar.group.offset');
    }

    .p-avatar-group .p-avatar {
        border: 2px solid dt('avatar.group.border.color');
    }

    .p-avatar-group .p-avatar-lg + .p-avatar-lg {
        margin-inline-start: dt('avatar.lg.group.offset');
    }

    .p-avatar-group .p-avatar-xl + .p-avatar-xl {
        margin-inline-start: dt('avatar.xl.group.offset');
    }
`,fi={root:function(e){var i=e.props;return["p-avatar p-component",{"p-avatar-image":i.image!=null,"p-avatar-circle":i.shape==="circle","p-avatar-lg":i.size==="large","p-avatar-xl":i.size==="xlarge"}]},label:"p-avatar-label",icon:"p-avatar-icon"},pi=ke.extend({name:"avatar",style:mi,classes:fi}),hi={name:"BaseAvatar",extends:Q,props:{label:{type:String,default:null},icon:{type:String,default:null},image:{type:String,default:null},size:{type:String,default:"normal"},shape:{type:String,default:"square"},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:pi,provide:function(){return{$pcAvatar:this,$parentInstance:this}}};function ie(t){"@babel/helpers - typeof";return ie=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ie(t)}function gt(t,e,i){return(e=bi(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function bi(t){var e=vi(t,"string");return ie(e)=="symbol"?e:e+""}function vi(t,e){if(ie(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var r=i.call(t,e);if(ie(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ot={name:"Avatar",extends:hi,inheritAttrs:!1,emits:["error"],methods:{onError:function(e){this.$emit("error",e)}},computed:{dataP:function(){return we(gt(gt({},this.shape,this.shape),this.size,this.size))}}},gi=["aria-labelledby","aria-label","data-p"],yi=["data-p"],Ii=["data-p"],ki=["src","alt","data-p"];function wi(t,e,i,r,o,n){return s(),c("div",f({class:t.cx("root"),"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel},t.ptmi("root"),{"data-p":n.dataP}),[E(t.$slots,"default",{},function(){return[t.label?(s(),c("span",f({key:0,class:t.cx("label")},t.ptm("label"),{"data-p":n.dataP}),R(t.label),17,yi)):t.$slots.icon?(s(),w(N(t.$slots.icon),{key:1,class:C(t.cx("icon"))},null,8,["class"])):t.icon?(s(),c("span",f({key:2,class:[t.cx("icon"),t.icon]},t.ptm("icon"),{"data-p":n.dataP}),null,16,Ii)):t.image?(s(),c("img",f({key:3,src:t.image,alt:t.ariaLabel,onError:e[0]||(e[0]=function(){return n.onError&&n.onError.apply(n,arguments)})},t.ptm("image"),{"data-p":n.dataP}),null,16,ki)):y("",!0)]})],16,gi)}Ot.render=wi;var Li=Ie`
    .p-menu {
        background: dt('menu.background');
        color: dt('menu.color');
        border: 1px solid dt('menu.border.color');
        border-radius: dt('menu.border.radius');
        min-width: 12.5rem;
    }

    .p-menu-list {
        margin: 0;
        padding: dt('menu.list.padding');
        outline: 0 none;
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: dt('menu.list.gap');
    }

    .p-menu-item-content {
        transition:
            background dt('menu.transition.duration'),
            color dt('menu.transition.duration');
        border-radius: dt('menu.item.border.radius');
        color: dt('menu.item.color');
    }

    .p-menu-item-link {
        cursor: pointer;
        display: flex;
        align-items: center;
        text-decoration: none;
        overflow: hidden;
        position: relative;
        color: inherit;
        padding: dt('menu.item.padding');
        gap: dt('menu.item.gap');
        user-select: none;
        outline: 0 none;
    }

    .p-menu-item-label {
        line-height: 1;
    }

    .p-menu-item-icon {
        color: dt('menu.item.icon.color');
    }

    .p-menu-item.p-focus .p-menu-item-content {
        color: dt('menu.item.focus.color');
        background: dt('menu.item.focus.background');
    }

    .p-menu-item.p-focus .p-menu-item-icon {
        color: dt('menu.item.icon.focus.color');
    }

    .p-menu-item:not(.p-disabled) .p-menu-item-content:hover {
        color: dt('menu.item.focus.color');
        background: dt('menu.item.focus.background');
    }

    .p-menu-item:not(.p-disabled) .p-menu-item-content:hover .p-menu-item-icon {
        color: dt('menu.item.icon.focus.color');
    }

    .p-menu-overlay {
        box-shadow: dt('menu.shadow');
    }

    .p-menu-submenu-label {
        background: dt('menu.submenu.label.background');
        padding: dt('menu.submenu.label.padding');
        color: dt('menu.submenu.label.color');
        font-weight: dt('menu.submenu.label.font.weight');
    }

    .p-menu-separator {
        border-block-start: 1px solid dt('menu.separator.border.color');
    }
`,Ci={root:function(e){var i=e.props;return["p-menu p-component",{"p-menu-overlay":i.popup}]},start:"p-menu-start",list:"p-menu-list",submenuLabel:"p-menu-submenu-label",separator:"p-menu-separator",end:"p-menu-end",item:function(e){var i=e.instance;return["p-menu-item",{"p-focus":i.id===i.focusedOptionId,"p-disabled":i.disabled()}]},itemContent:"p-menu-item-content",itemLink:"p-menu-item-link",itemIcon:"p-menu-item-icon",itemLabel:"p-menu-item-label"},Si=ke.extend({name:"menu",style:Li,classes:Ci}),Pi={name:"BaseMenu",extends:Q,props:{popup:{type:Boolean,default:!1},model:{type:Array,default:null},appendTo:{type:[String,Object],default:"body"},autoZIndex:{type:Boolean,default:!0},baseZIndex:{type:Number,default:0},tabindex:{type:Number,default:0},ariaLabel:{type:String,default:null},ariaLabelledby:{type:String,default:null}},style:Si,provide:function(){return{$pcMenu:this,$parentInstance:this}}},Kt={name:"Menuitem",hostName:"Menu",extends:Q,inheritAttrs:!1,emits:["item-click","item-mousemove"],props:{item:null,templates:null,id:null,focusedOptionId:null,index:null},methods:{getItemProp:function(e,i){return e&&e.item?Ke(e.item[i]):void 0},getPTOptions:function(e){return this.ptm(e,{context:{item:this.item,index:this.index,focused:this.isItemFocused(),disabled:this.disabled()}})},isItemFocused:function(){return this.focusedOptionId===this.id},onItemClick:function(e){var i=this.getItemProp(this.item,"command");i&&i({originalEvent:e,item:this.item.item}),this.$emit("item-click",{originalEvent:e,item:this.item,id:this.id})},onItemMouseMove:function(e){this.$emit("item-mousemove",{originalEvent:e,item:this.item,id:this.id})},visible:function(){return typeof this.item.visible=="function"?this.item.visible():this.item.visible!==!1},disabled:function(){return typeof this.item.disabled=="function"?this.item.disabled():this.item.disabled},label:function(){return typeof this.item.label=="function"?this.item.label():this.item.label},getMenuItemProps:function(e){return{action:f({class:this.cx("itemLink"),tabindex:"-1"},this.getPTOptions("itemLink")),icon:f({class:[this.cx("itemIcon"),e.icon]},this.getPTOptions("itemIcon")),label:f({class:this.cx("itemLabel")},this.getPTOptions("itemLabel"))}}},computed:{dataP:function(){return we({focus:this.isItemFocused(),disabled:this.disabled()})}},directives:{ripple:It}},Mi=["id","aria-label","aria-disabled","data-p-focused","data-p-disabled","data-p"],xi=["data-p"],Oi=["href","target"],Ki=["data-p"],Ei=["data-p"];function Ai(t,e,i,r,o,n){var m=Ee("ripple");return n.visible()?(s(),c("li",f({key:0,id:i.id,class:[t.cx("item"),i.item.class],role:"menuitem",style:i.item.style,"aria-label":n.label(),"aria-disabled":n.disabled(),"data-p-focused":n.isItemFocused(),"data-p-disabled":n.disabled()||!1,"data-p":n.dataP},n.getPTOptions("item")),[p("div",f({class:t.cx("itemContent"),onClick:e[0]||(e[0]=function(b){return n.onItemClick(b)}),onMousemove:e[1]||(e[1]=function(b){return n.onItemMouseMove(b)}),"data-p":n.dataP},n.getPTOptions("itemContent")),[i.templates.item?i.templates.item?(s(),w(N(i.templates.item),{key:1,item:i.item,label:n.label(),props:n.getMenuItemProps(i.item)},null,8,["item","label","props"])):y("",!0):Ae((s(),c("a",f({key:0,href:i.item.url,class:t.cx("itemLink"),target:i.item.target,tabindex:"-1"},n.getPTOptions("itemLink")),[i.templates.itemicon?(s(),w(N(i.templates.itemicon),{key:0,item:i.item,class:C(t.cx("itemIcon"))},null,8,["item","class"])):i.item.icon?(s(),c("span",f({key:1,class:[t.cx("itemIcon"),i.item.icon],"data-p":n.dataP},n.getPTOptions("itemIcon")),null,16,Ki)):y("",!0),p("span",f({class:t.cx("itemLabel"),"data-p":n.dataP},n.getPTOptions("itemLabel")),R(n.label()),17,Ei)],16,Oi)),[[m]])],16,xi)],16,Mi)):y("",!0)}Kt.render=Ai;function yt(t){return Vi(t)||zi(t)||Ti(t)||Di()}function Di(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ti(t,e){if(t){if(typeof t=="string")return xe(t,e);var i={}.toString.call(t).slice(8,-1);return i==="Object"&&t.constructor&&(i=t.constructor.name),i==="Map"||i==="Set"?Array.from(t):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?xe(t,e):void 0}}function zi(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Vi(t){if(Array.isArray(t))return xe(t)}function xe(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,r=Array(e);i<e;i++)r[i]=t[i];return r}var Et={name:"Menu",extends:Pi,inheritAttrs:!1,emits:["show","hide","focus","blur"],data:function(){return{overlayVisible:!1,focused:!1,focusedOptionIndex:-1,selectedOptionIndex:-1}},target:null,outsideClickListener:null,scrollHandler:null,resizeListener:null,container:null,list:null,mounted:function(){this.popup||(this.bindResizeListener(),this.bindOutsideClickListener())},beforeUnmount:function(){this.unbindResizeListener(),this.unbindOutsideClickListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.target=null,this.container&&this.autoZIndex&&F.clear(this.container),this.container=null},methods:{itemClick:function(e){var i=e.item;this.disabled(i)||(i.command&&i.command(e),this.overlayVisible&&this.hide(),!this.popup&&this.focusedOptionIndex!==e.id&&(this.focusedOptionIndex=e.id))},itemMouseMove:function(e){this.focused&&(this.focusedOptionIndex=e.id)},onListFocus:function(e){this.focused=!0,!this.popup&&this.changeFocusedOptionIndex(0),this.$emit("focus",e)},onListBlur:function(e){this.focused=!1,this.focusedOptionIndex=-1,this.$emit("blur",e)},onListKeyDown:function(e){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Enter":case"NumpadEnter":this.onEnterKey(e);break;case"Space":this.onSpaceKey(e);break;case"Escape":this.popup&&(O(this.target),this.hide());case"Tab":this.overlayVisible&&this.hide();break}},onArrowDownKey:function(e){var i=this.findNextOptionIndex(this.focusedOptionIndex);this.changeFocusedOptionIndex(i),e.preventDefault()},onArrowUpKey:function(e){if(e.altKey&&this.popup)O(this.target),this.hide(),e.preventDefault();else{var i=this.findPrevOptionIndex(this.focusedOptionIndex);this.changeFocusedOptionIndex(i),e.preventDefault()}},onHomeKey:function(e){this.changeFocusedOptionIndex(0),e.preventDefault()},onEndKey:function(e){this.changeFocusedOptionIndex(ye(this.container,'li[data-pc-section="item"][data-p-disabled="false"]').length-1),e.preventDefault()},onEnterKey:function(e){var i=te(this.list,'li[id="'.concat("".concat(this.focusedOptionIndex),'"]')),r=i&&te(i,'a[data-pc-section="itemlink"]');this.popup&&O(this.target),r?r.click():i&&i.click(),e.preventDefault()},onSpaceKey:function(e){this.onEnterKey(e)},findNextOptionIndex:function(e){var i=ye(this.container,'li[data-pc-section="item"][data-p-disabled="false"]'),r=yt(i).findIndex(function(o){return o.id===e});return r>-1?r+1:0},findPrevOptionIndex:function(e){var i=ye(this.container,'li[data-pc-section="item"][data-p-disabled="false"]'),r=yt(i).findIndex(function(o){return o.id===e});return r>-1?r-1:0},changeFocusedOptionIndex:function(e){var i=ye(this.container,'li[data-pc-section="item"][data-p-disabled="false"]'),r=e>=i.length?i.length-1:e<0?0:e;r>-1&&(this.focusedOptionIndex=i[r].getAttribute("id"))},toggle:function(e,i){this.overlayVisible?this.hide():this.show(e,i)},show:function(e,i){this.overlayVisible=!0,this.target=i??e.currentTarget},hide:function(){this.overlayVisible=!1,this.target=null},onEnter:function(e){Ct(e,{position:"absolute",top:"0"}),this.alignOverlay(),this.bindOutsideClickListener(),this.bindResizeListener(),this.bindScrollListener(),this.autoZIndex&&F.set("menu",e,this.baseZIndex+this.$primevue.config.zIndex.menu),this.popup&&O(this.list),this.$emit("show")},onLeave:function(){this.unbindOutsideClickListener(),this.unbindResizeListener(),this.unbindScrollListener(),this.$emit("hide")},onAfterLeave:function(e){this.autoZIndex&&F.clear(e)},alignOverlay:function(){Lt(this.container,this.target);var e=Y(this.target);e>Y(this.container)&&(this.container.style.minWidth=Y(this.target)+"px")},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){var r=e.container&&!e.container.contains(i.target),o=!(e.target&&(e.target===i.target||e.target.contains(i.target)));e.overlayVisible&&r&&o?e.hide():!e.popup&&r&&o&&(e.focusedOptionIndex=-1)},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new wt(this.target,function(){e.overlayVisible&&e.hide()})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(){e.overlayVisible&&!kt()&&e.hide()},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},visible:function(e){return typeof e.visible=="function"?e.visible():e.visible!==!1},disabled:function(e){return typeof e.disabled=="function"?e.disabled():e.disabled},label:function(e){return typeof e.label=="function"?e.label():e.label},onOverlayClick:function(e){Mt.emit("overlay-click",{originalEvent:e,target:this.target})},containerRef:function(e){this.container=e},listRef:function(e){this.list=e}},computed:{focusedOptionId:function(){return this.focusedOptionIndex!==-1?this.focusedOptionIndex:null},dataP:function(){return we({popup:this.popup})}},components:{PVMenuitem:Kt,Portal:Oe}},Ri=["id","data-p"],Bi=["id","tabindex","aria-activedescendant","aria-label","aria-labelledby"],_i=["id"];function Fi(t,e,i,r,o,n){var m=B("PVMenuitem"),b=B("Portal");return s(),w(b,{appendTo:t.appendTo,disabled:!t.popup},{default:P(function(){return[L(re,f({name:"p-connected-overlay",onEnter:n.onEnter,onLeave:n.onLeave,onAfterLeave:n.onAfterLeave},t.ptm("transition")),{default:P(function(){return[!t.popup||o.overlayVisible?(s(),c("div",f({key:0,ref:n.containerRef,id:t.$id,class:t.cx("root"),onClick:e[3]||(e[3]=function(){return n.onOverlayClick&&n.onOverlayClick.apply(n,arguments)}),"data-p":n.dataP},t.ptmi("root")),[t.$slots.start?(s(),c("div",f({key:0,class:t.cx("start")},t.ptm("start")),[E(t.$slots,"start")],16)):y("",!0),p("ul",f({ref:n.listRef,id:t.$id+"_list",class:t.cx("list"),role:"menu",tabindex:t.tabindex,"aria-activedescendant":o.focused?n.focusedOptionId:void 0,"aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledby,onFocus:e[0]||(e[0]=function(){return n.onListFocus&&n.onListFocus.apply(n,arguments)}),onBlur:e[1]||(e[1]=function(){return n.onListBlur&&n.onListBlur.apply(n,arguments)}),onKeydown:e[2]||(e[2]=function(){return n.onListKeyDown&&n.onListKeyDown.apply(n,arguments)})},t.ptm("list")),[(s(!0),c(x,null,$(t.model,function(l,a){return s(),c(x,{key:n.label(l)+a.toString()},[l.items&&n.visible(l)&&!l.separator?(s(),c(x,{key:0},[l.items?(s(),c("li",f({key:0,id:t.$id+"_"+a,class:[t.cx("submenuLabel"),l.class],role:"none"},{ref_for:!0},t.ptm("submenuLabel")),[E(t.$slots,t.$slots.submenulabel?"submenulabel":"submenuheader",{item:l},function(){return[Bt(R(n.label(l)),1)]})],16,_i)):y("",!0),(s(!0),c(x,null,$(l.items,function(d,h){return s(),c(x,{key:d.label+a+"_"+h},[n.visible(d)&&!d.separator?(s(),w(m,{key:0,id:t.$id+"_"+a+"_"+h,item:d,templates:t.$slots,focusedOptionId:n.focusedOptionId,unstyled:t.unstyled,onItemClick:n.itemClick,onItemMousemove:n.itemMouseMove,pt:t.pt},null,8,["id","item","templates","focusedOptionId","unstyled","onItemClick","onItemMousemove","pt"])):n.visible(d)&&d.separator?(s(),c("li",f({key:"separator"+a+h,class:[t.cx("separator"),l.class],style:d.style,role:"separator"},{ref_for:!0},t.ptm("separator")),null,16)):y("",!0)],64)}),128))],64)):n.visible(l)&&l.separator?(s(),c("li",f({key:"separator"+a.toString(),class:[t.cx("separator"),l.class],style:l.style,role:"separator"},{ref_for:!0},t.ptm("separator")),null,16)):(s(),w(m,{key:n.label(l)+a.toString(),id:t.$id+"_"+a,item:l,index:a,templates:t.$slots,focusedOptionId:n.focusedOptionId,unstyled:t.unstyled,onItemClick:n.itemClick,onItemMousemove:n.itemMouseMove,pt:t.pt},null,8,["id","item","index","templates","focusedOptionId","unstyled","onItemClick","onItemMousemove","pt"]))],64)}),128))],16,Bi),t.$slots.end?(s(),c("div",f({key:1,class:t.cx("end")},t.ptm("end")),[E(t.$slots,"end")],16)):y("",!0)],16,Ri)):y("",!0)]}),_:3},16,["onEnter","onLeave","onAfterLeave"])]}),_:3},8,["appendTo","disabled"])}Et.render=Fi;const Hi={class:"app-breadcrumbs"},Ni={class:"breadcrumb-container"},Ui=["onClick"],$i={key:0,class:"pi pi-angle-right separator"},Zi=Z({__name:"appBreadcrumbs",setup(t){const e=De(),i=Le(),{t:r}=Te(),o=K(()=>e.matched.filter(l=>{var a;return!((a=l.meta)!=null&&a.isHideBreadcrumb)})),n=l=>{l&&i.push({name:l})},m=l=>{var a,d;return((a=l==null?void 0:l.meta)==null?void 0:a.breadcrumbTitle)||r((d=l==null?void 0:l.meta)==null?void 0:d.i18nKey)||(l==null?void 0:l.name)},b=l=>{var a;return(l==null?void 0:l.name)||((a=l==null?void 0:l.redirect)==null?void 0:a.name)};return(l,a)=>(s(),c("div",Hi,[p("div",Ni,[(s(!0),c(x,null,$(o.value,(d,h)=>(s(),c("div",{key:h,class:"breadcrumb-item"},[p("h1",{class:C({clickable:b(d),active:h===o.value.length-1}),onClick:k=>d!=null&&d.name?n(d==null?void 0:d.name):null},R(m(d)),11,Ui),h<o.value.length-1?(s(),c("i",$i)):y("",!0)]))),128))])]))}}),Gi=Se(Zi,[["__scopeId","data-v-4633187f"]]),ji={class:"app-header"},qi={class:"header-start"},Wi={class:"header-end"},Xi={class:"flex items-center gap-2"},Yi={class:"user-notice"},Qi=["src"],Ji={class:"flex align-items-center gap-2 mb-4"},en=["for"],tn={class:"flex justify-end gap-4"},nn=Z({__name:"appHeader",setup(t){const e=De(),i=Le(),{t:r}=Te(),o=A(),n=St(),m=Ce(),{locale:b,rememberCompanyChangeConfirm:l}=U(m),{groupList:a,currentGroup:d,isLoadingGroupList:h,isShowSelectBid:k,activeBid:D}=U(n),G=A(["customersCreateInvite","customersCreateInvoice","planSubscriptionAdd","payMyInvoiceCreateInvoice","payMyInvoiceInvoiceDetailAndSend"]);_t(b,M=>{m.setLocale(M)});const ae=K(()=>{var M;return((M=n.user)==null?void 0:M.avatar)||ai}),se=ze(),{isSidebarSlim:j}=U(se),u=K(()=>[{label:r("user.profile"),icon:"pi pi-user",command:()=>i.push("/user/profile")},{label:r("user.settings.title"),icon:"pi pi-cog",command:()=>i.push("/user/settings")},{separator:!0},{label:r("user.logout"),icon:"pi pi-power-off",command:async()=>{await n.logout(),i.push("/login")}}]),v=()=>{se.setSidebarMode(j.value?"expanded":"slim")},T=()=>{m.toggleMobileMenu()},q=K(()=>{var M;return(M=a.value)==null?void 0:M.map(g=>({value:g.value,label:g.label,disabled:d.value.length===1&&d.value.includes(g.value)}))}),z=A(),_=A(!1),H=A(!1),oe=()=>{var M,g,V;if(l.value){(M=z.value)==null||M.show();return}if(H.value){(g=z.value)==null||g.hide(),_.value=!0,H.value=!1;return}(V=z.value)==null||V.hide(),H.value||(_.value=!0)},le=()=>{var M;H.value=!0,_.value=!1,(M=z.value)==null||M.show()},ue=()=>{_.value=!1,H.value=!1},de=M=>{var g;M.value.length===1&&(D.value=M.value[0]),(g=z.value)==null||g.hide(),m.refreshRouteView()};return(M,g)=>{const V=Pt,ce=ri,me=ni,fe=Et,pe=Ot,he=ii,J=Ft,be=ti;return s(),c("div",ji,[p("div",qi,[L(V,{class:"mobile-menu-toggle",severity:"secondary",onClick:T},{default:P(()=>g[6]||(g[6]=[p("i",{class:"pi pi-bars"},null,-1)])),_:1,__:[6]}),L(V,{class:"sidebar-toggle",severity:"secondary",onClick:v},{default:P(()=>[p("i",{class:C(["pi",I(j)?"pi-angle-right":"pi-angle-left"])},null,2)]),_:1}),L(ce,{layout:"vertical"}),L(Gi)]),p("div",Wi,[p("div",Xi,[g[7]||(g[7]=p("span",null,"Business :",-1)),L(me,{ref_key:"multiSelectRef",ref:z,modelValue:I(d),"onUpdate:modelValue":g[0]||(g[0]=S=>ee(d)?d.value=S:null),options:q.value,"option-disabled":S=>S.disabled,loading:I(h),"option-label":"label","option-value":"value",class:"w-60",placeholder:"Please select","show-toggle-all":!1,disabled:G.value.includes(I(e).name),onChange:de,onClick:oe},null,8,["modelValue","options","option-disabled","loading","disabled"])]),p("div",Yi,[p("img",{class:"notice-image",src:I(si),alt:"notice"},null,8,Qi)]),L(fe,{ref_key:"userSettingMenu",ref:o,model:u.value,popup:!0},null,8,["model"]),p("div",{class:"user-profile",onClick:g[1]||(g[1]=S=>o.value.toggle(S))},[L(pe,{image:ae.value,shape:"circle",class:"user-avatar"},null,8,["image"])])]),L(J,{visible:_.value,"onUpdate:visible":g[3]||(g[3]=S=>_.value=S),modal:!0,closable:!1,header:"Confirm",class:"w-96","pt:root:class":"!border-0 !bg-(--colors-pink) !rounded-[20px]","pt:mask:class":"backdrop-blur-sm"},{footer:P(()=>[L(V,{label:"Cancel",severity:"secondary",onClick:ue}),L(V,{label:"Continue",severity:"warning",onClick:le})]),default:P(()=>[g[9]||(g[9]=p("div",{class:"flex align-items-center gap-3 mb-4"},[p("i",{class:"pi pi-exclamation-triangle text-orange-500 text-2xl"}),p("span",null,"Changing the business will refresh the current page. Do you want to continue?")],-1)),p("div",Ji,[L(he,{modelValue:I(l),"onUpdate:modelValue":g[2]||(g[2]=S=>ee(l)?l.value=S:null),"input-id":"rememberCompanyChangeConfirm",binary:""},null,8,["modelValue"]),g[8]||(g[8]=p("label",{for:"rememberCompanyChangeConfirm"},"Remember my choice",-1))])]),_:1,__:[9]},8,["visible"]),L(J,{visible:I(k),"onUpdate:visible":g[5]||(g[5]=S=>ee(k)?k.value=S:null),modal:!0,header:"Select Business",class:"w-120",closable:!1,"pt:root:class":"!border-0 !bg-(--colors-pink) !rounded-[20px]","pt:mask:class":"backdrop-blur-sm"},{default:P(()=>[g[10]||(g[10]=p("div",{class:"flex align-items-center gap-3 mb-4"},[p("span",null,"Please choose a business to continue")],-1)),L(I(Ht),{class:"flex flex-col gap-4"},{default:P(()=>[L(I(Nt),{name:"selectBid",class:"flex flex-col gap-2"},{default:P(()=>[(s(!0),c(x,null,$(I(a),S=>(s(),c("div",{key:S.value,class:"flex items-center"},[L(be,{modelValue:I(D),"onUpdate:modelValue":g[4]||(g[4]=ve=>ee(D)?D.value=ve:null),"input-id":`selectBid-${S.value}`,name:"selectBid",value:S.value},null,8,["modelValue","input-id","value"]),p("label",{for:`selectBid-${S.value}`,class:"ml-2 cursor-pointer"},R(S.label)+" - "+R(S.value),9,en)]))),128))]),_:1}),p("div",tn,[L(V,{label:"Continue",severity:"warning",onClick:I(n).confirmSelectBid},null,8,["onClick"])])]),_:1})]),_:1,__:[10]},8,["visible"])])}}}),rn=Se(nn,[["__scopeId","data-v-2757c9eb"]]);var an=Ie`
    .p-tieredmenu {
        background: dt('tieredmenu.background');
        color: dt('tieredmenu.color');
        border: 1px solid dt('tieredmenu.border.color');
        border-radius: dt('tieredmenu.border.radius');
        min-width: 12.5rem;
    }

    .p-tieredmenu-root-list,
    .p-tieredmenu-submenu {
        margin: 0;
        padding: dt('tieredmenu.list.padding');
        list-style: none;
        outline: 0 none;
        display: flex;
        flex-direction: column;
        gap: dt('tieredmenu.list.gap');
    }

    .p-tieredmenu-submenu {
        position: absolute;
        min-width: 100%;
        z-index: 1;
        background: dt('tieredmenu.background');
        color: dt('tieredmenu.color');
        border: 1px solid dt('tieredmenu.border.color');
        border-radius: dt('tieredmenu.border.radius');
        box-shadow: dt('tieredmenu.shadow');
    }

    .p-tieredmenu-item {
        position: relative;
    }

    .p-tieredmenu-item-content {
        transition:
            background dt('tieredmenu.transition.duration'),
            color dt('tieredmenu.transition.duration');
        border-radius: dt('tieredmenu.item.border.radius');
        color: dt('tieredmenu.item.color');
    }

    .p-tieredmenu-item-link {
        cursor: pointer;
        display: flex;
        align-items: center;
        text-decoration: none;
        overflow: hidden;
        position: relative;
        color: inherit;
        padding: dt('tieredmenu.item.padding');
        gap: dt('tieredmenu.item.gap');
        user-select: none;
        outline: 0 none;
    }

    .p-tieredmenu-item-label {
        line-height: 1;
    }

    .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.color');
    }

    .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.color');
        margin-left: auto;
        font-size: dt('tieredmenu.submenu.icon.size');
        width: dt('tieredmenu.submenu.icon.size');
        height: dt('tieredmenu.submenu.icon.size');
    }

    .p-tieredmenu-submenu-icon:dir(rtl) {
        margin-left: 0;
        margin-right: auto;
    }

    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content {
        color: dt('tieredmenu.item.focus.color');
        background: dt('tieredmenu.item.focus.background');
    }

    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.focus.color');
    }

    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.focus.color');
    }

    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover {
        color: dt('tieredmenu.item.focus.color');
        background: dt('tieredmenu.item.focus.background');
    }

    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.focus.color');
    }

    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.focus.color');
    }

    .p-tieredmenu-item-active > .p-tieredmenu-item-content {
        color: dt('tieredmenu.item.active.color');
        background: dt('tieredmenu.item.active.background');
    }

    .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-item-icon {
        color: dt('tieredmenu.item.icon.active.color');
    }

    .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {
        color: dt('tieredmenu.submenu.icon.active.color');
    }

    .p-tieredmenu-separator {
        border-block-start: 1px solid dt('tieredmenu.separator.border.color');
    }

    .p-tieredmenu-overlay {
        box-shadow: dt('tieredmenu.shadow');
    }

    .p-tieredmenu-enter-from,
    .p-tieredmenu-leave-active {
        opacity: 0;
    }

    .p-tieredmenu-enter-active {
        transition: opacity 250ms;
    }

    .p-tieredmenu-mobile .p-tieredmenu-submenu {
        position: static;
        box-shadow: none;
        border: 0 none;
        padding-inline-start: dt('tieredmenu.submenu.mobile.indent');
        padding-inline-end: 0;
    }

    .p-tieredmenu-mobile .p-tieredmenu-submenu:dir(rtl) {
        padding-inline-start: 0;
        padding-inline-end: dt('tieredmenu.submenu.mobile.indent');
    }

    .p-tieredmenu-mobile .p-tieredmenu-submenu-icon {
        transition: transform 0.2s;
        transform: rotate(90deg);
    }

    .p-tieredmenu-mobile .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {
        transform: rotate(-90deg);
    }
`,sn={submenu:function(e){var i=e.instance,r=e.processedItem;return{display:i.isItemActive(r)?"flex":"none"}}},on={root:function(e){var i=e.props,r=e.instance;return["p-tieredmenu p-component",{"p-tieredmenu-overlay":i.popup,"p-tieredmenu-mobile":r.queryMatches}]},start:"p-tieredmenu-start",rootList:"p-tieredmenu-root-list",item:function(e){var i=e.instance,r=e.processedItem;return["p-tieredmenu-item",{"p-tieredmenu-item-active":i.isItemActive(r),"p-focus":i.isItemFocused(r),"p-disabled":i.isItemDisabled(r)}]},itemContent:"p-tieredmenu-item-content",itemLink:"p-tieredmenu-item-link",itemIcon:"p-tieredmenu-item-icon",itemLabel:"p-tieredmenu-item-label",submenuIcon:"p-tieredmenu-submenu-icon",submenu:"p-tieredmenu-submenu",separator:"p-tieredmenu-separator",end:"p-tieredmenu-end"},ln=ke.extend({name:"tieredmenu",style:an,classes:on,inlineStyles:sn}),un={name:"BaseTieredMenu",extends:Q,props:{popup:{type:Boolean,default:!1},model:{type:Array,default:null},appendTo:{type:[String,Object],default:"body"},breakpoint:{type:String,default:"960px"},autoZIndex:{type:Boolean,default:!0},baseZIndex:{type:Number,default:0},disabled:{type:Boolean,default:!1},tabindex:{type:Number,default:0},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:ln,provide:function(){return{$pcTieredMenu:this,$parentInstance:this}}},At={name:"TieredMenuSub",hostName:"TieredMenu",extends:Q,emits:["item-click","item-mouseenter","item-mousemove"],container:null,props:{menuId:{type:String,default:null},focusedItemId:{type:String,default:null},items:{type:Array,default:null},visible:{type:Boolean,default:!1},level:{type:Number,default:0},templates:{type:Object,default:null},activeItemPath:{type:Object,default:null},tabindex:{type:Number,default:0}},methods:{getItemId:function(e){return"".concat(this.menuId,"_").concat(e.key)},getItemKey:function(e){return this.getItemId(e)},getItemProp:function(e,i,r){return e&&e.item?Ke(e.item[i],r):void 0},getItemLabel:function(e){return this.getItemProp(e,"label")},getItemLabelId:function(e){return"".concat(this.menuId,"_").concat(e.key,"_label")},getPTOptions:function(e,i,r){return this.ptm(r,{context:{item:e.item,index:i,active:this.isItemActive(e),focused:this.isItemFocused(e),disabled:this.isItemDisabled(e)}})},isItemActive:function(e){return this.activeItemPath.some(function(i){return i.key===e.key})},isItemVisible:function(e){return this.getItemProp(e,"visible")!==!1},isItemDisabled:function(e){return this.getItemProp(e,"disabled")},isItemFocused:function(e){return this.focusedItemId===this.getItemId(e)},isItemGroup:function(e){return X(e.items)},onEnter:function(){$t(this.container,this.level)},onItemClick:function(e,i){this.getItemProp(i,"command",{originalEvent:e,item:i.item}),this.$emit("item-click",{originalEvent:e,processedItem:i,isFocus:!0})},onItemMouseEnter:function(e,i){this.$emit("item-mouseenter",{originalEvent:e,processedItem:i})},onItemMouseMove:function(e,i){this.$emit("item-mousemove",{originalEvent:e,processedItem:i})},getAriaSetSize:function(){var e=this;return this.items.filter(function(i){return e.isItemVisible(i)&&!e.getItemProp(i,"separator")}).length},getAriaPosInset:function(e){var i=this;return e-this.items.slice(0,e).filter(function(r){return i.isItemVisible(r)&&i.getItemProp(r,"separator")}).length+1},getMenuItemProps:function(e,i){return{action:f({class:this.cx("itemLink"),tabindex:-1},this.getPTOptions(e,i,"itemLink")),icon:f({class:[this.cx("itemIcon"),this.getItemProp(e,"icon")]},this.getPTOptions(e,i,"itemIcon")),label:f({class:this.cx("itemLabel")},this.getPTOptions(e,i,"itemLabel")),submenuicon:f({class:this.cx("submenuIcon")},this.getPTOptions(e,i,"submenuIcon"))}},containerRef:function(e){this.container=e}},components:{AngleRightIcon:di},directives:{ripple:It}},dn=["tabindex"],cn=["id","aria-label","aria-disabled","aria-expanded","aria-haspopup","aria-level","aria-setsize","aria-posinset","data-p-active","data-p-focused","data-p-disabled"],mn=["onClick","onMouseenter","onMousemove"],fn=["href","target"],pn=["id"],hn=["id"];function bn(t,e,i,r,o,n){var m=B("AngleRightIcon"),b=B("TieredMenuSub",!0),l=Ee("ripple");return s(),w(re,f({name:"p-tieredmenu",onEnter:n.onEnter},t.ptm("menu.transition")),{default:P(function(){return[i.level===0||i.visible?(s(),c("ul",{key:0,ref:n.containerRef,tabindex:i.tabindex},[(s(!0),c(x,null,$(i.items,function(a,d){return s(),c(x,{key:n.getItemKey(a)},[n.isItemVisible(a)&&!n.getItemProp(a,"separator")?(s(),c("li",f({key:0,id:n.getItemId(a),style:n.getItemProp(a,"style"),class:[t.cx("item",{processedItem:a}),n.getItemProp(a,"class")],role:"menuitem","aria-label":n.getItemLabel(a),"aria-disabled":n.isItemDisabled(a)||void 0,"aria-expanded":n.isItemGroup(a)?n.isItemActive(a):void 0,"aria-haspopup":n.isItemGroup(a)&&!n.getItemProp(a,"to")?"menu":void 0,"aria-level":i.level+1,"aria-setsize":n.getAriaSetSize(),"aria-posinset":n.getAriaPosInset(d)},{ref_for:!0},n.getPTOptions(a,d,"item"),{"data-p-active":n.isItemActive(a),"data-p-focused":n.isItemFocused(a),"data-p-disabled":n.isItemDisabled(a)}),[p("div",f({class:t.cx("itemContent"),onClick:function(k){return n.onItemClick(k,a)},onMouseenter:function(k){return n.onItemMouseEnter(k,a)},onMousemove:function(k){return n.onItemMouseMove(k,a)}},{ref_for:!0},n.getPTOptions(a,d,"itemContent")),[i.templates.item?(s(),w(N(i.templates.item),{key:1,item:a.item,hasSubmenu:n.getItemProp(a,"items"),label:n.getItemLabel(a),props:n.getMenuItemProps(a,d)},null,8,["item","hasSubmenu","label","props"])):Ae((s(),c("a",f({key:0,href:n.getItemProp(a,"url"),class:t.cx("itemLink"),target:n.getItemProp(a,"target"),tabindex:"-1"},{ref_for:!0},n.getPTOptions(a,d,"itemLink")),[i.templates.itemicon?(s(),w(N(i.templates.itemicon),{key:0,item:a.item,class:C(t.cx("itemIcon"))},null,8,["item","class"])):n.getItemProp(a,"icon")?(s(),c("span",f({key:1,class:[t.cx("itemIcon"),n.getItemProp(a,"icon")]},{ref_for:!0},n.getPTOptions(a,d,"itemIcon")),null,16)):y("",!0),p("span",f({id:n.getItemLabelId(a),class:t.cx("itemLabel")},{ref_for:!0},n.getPTOptions(a,d,"itemLabel")),R(n.getItemLabel(a)),17,pn),n.getItemProp(a,"items")?(s(),c(x,{key:2},[i.templates.submenuicon?(s(),w(N(i.templates.submenuicon),f({key:0,class:t.cx("submenuIcon"),active:n.isItemActive(a)},{ref_for:!0},n.getPTOptions(a,d,"submenuIcon")),null,16,["class","active"])):(s(),w(m,f({key:1,class:t.cx("submenuIcon")},{ref_for:!0},n.getPTOptions(a,d,"submenuIcon")),null,16,["class"]))],64)):y("",!0)],16,fn)),[[l]])],16,mn),n.isItemVisible(a)&&n.isItemGroup(a)?(s(),w(b,f({key:0,id:n.getItemId(a)+"_list",class:t.cx("submenu"),style:t.sx("submenu",!0,{processedItem:a}),"aria-labelledby":n.getItemLabelId(a),role:"menu",menuId:i.menuId,focusedItemId:i.focusedItemId,items:a.items,templates:i.templates,activeItemPath:i.activeItemPath,level:i.level+1,visible:n.isItemActive(a)&&n.isItemGroup(a),pt:t.pt,unstyled:t.unstyled,onItemClick:e[0]||(e[0]=function(h){return t.$emit("item-click",h)}),onItemMouseenter:e[1]||(e[1]=function(h){return t.$emit("item-mouseenter",h)}),onItemMousemove:e[2]||(e[2]=function(h){return t.$emit("item-mousemove",h)})},{ref_for:!0},t.ptm("submenu")),null,16,["id","class","style","aria-labelledby","menuId","focusedItemId","items","templates","activeItemPath","level","visible","pt","unstyled"])):y("",!0)],16,cn)):y("",!0),n.isItemVisible(a)&&n.getItemProp(a,"separator")?(s(),c("li",f({key:1,id:n.getItemId(a),style:n.getItemProp(a,"style"),class:[t.cx("separator"),n.getItemProp(a,"class")],role:"separator"},{ref_for:!0},t.ptm("separator")),null,16,hn)):y("",!0)],64)}),128))],8,dn)):y("",!0)]}),_:1},16,["onEnter"])}At.render=bn;var Dt={name:"TieredMenu",extends:un,inheritAttrs:!1,emits:["focus","blur","before-show","before-hide","hide","show"],outsideClickListener:null,matchMediaListener:null,scrollHandler:null,resizeListener:null,target:null,container:null,menubar:null,searchTimeout:null,searchValue:null,data:function(){return{focused:!1,focusedItemInfo:{index:-1,level:0,parentKey:""},activeItemPath:[],visible:!this.popup,submenuVisible:!1,dirty:!1,query:null,queryMatches:!1}},watch:{activeItemPath:function(e){this.popup||(X(e)?(this.bindOutsideClickListener(),this.bindResizeListener()):(this.unbindOutsideClickListener(),this.unbindResizeListener()))}},mounted:function(){this.bindMatchMediaListener()},beforeUnmount:function(){this.unbindOutsideClickListener(),this.unbindResizeListener(),this.unbindMatchMediaListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.container&&this.autoZIndex&&F.clear(this.container),this.target=null,this.container=null},methods:{getItemProp:function(e,i){return e?Ke(e[i]):void 0},getItemLabel:function(e){return this.getItemProp(e,"label")},isItemDisabled:function(e){return this.getItemProp(e,"disabled")},isItemVisible:function(e){return this.getItemProp(e,"visible")!==!1},isItemGroup:function(e){return X(this.getItemProp(e,"items"))},isItemSeparator:function(e){return this.getItemProp(e,"separator")},getProccessedItemLabel:function(e){return e?this.getItemLabel(e.item):void 0},isProccessedItemGroup:function(e){return e&&X(e.items)},toggle:function(e){this.visible?this.hide(e,!0):this.show(e)},show:function(e,i){this.popup&&(this.$emit("before-show"),this.visible=!0,this.target=this.target||e.currentTarget,this.relatedTarget=e.relatedTarget||null),i&&O(this.menubar)},hide:function(e,i){this.popup&&(this.$emit("before-hide"),this.visible=!1),this.activeItemPath=[],this.focusedItemInfo={index:-1,level:0,parentKey:""},i&&O(this.relatedTarget||this.target||this.menubar),this.dirty=!1},onFocus:function(e){this.focused=!0,this.popup||(this.focusedItemInfo=this.focusedItemInfo.index!==-1?this.focusedItemInfo:{index:this.findFirstFocusedItemIndex(),level:0,parentKey:""}),this.$emit("focus",e)},onBlur:function(e){this.focused=!1,this.focusedItemInfo={index:-1,level:0,parentKey:""},this.searchValue="",this.dirty=!1,this.$emit("blur",e)},onKeyDown:function(e){if(this.disabled){e.preventDefault();return}var i=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"ArrowLeft":this.onArrowLeftKey(e);break;case"ArrowRight":this.onArrowRightKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"Space":this.onSpaceKey(e);break;case"Enter":case"NumpadEnter":this.onEnterKey(e);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e);break;case"PageDown":case"PageUp":case"Backspace":case"ShiftLeft":case"ShiftRight":break;default:!i&&Ut(e.key)&&this.searchItems(e,e.key);break}},onItemChange:function(e,i){var r=e.processedItem,o=e.isFocus;if(!Pe(r)){var n=r.index,m=r.key,b=r.level,l=r.parentKey,a=r.items,d=X(a),h=this.activeItemPath.filter(function(k){return k.parentKey!==l&&k.parentKey!==m});d&&(h.push(r),this.submenuVisible=!0),this.focusedItemInfo={index:n,level:b,parentKey:l},d&&(this.dirty=!0),o&&O(this.menubar),!(i==="hover"&&this.queryMatches)&&(this.activeItemPath=h)}},onOverlayClick:function(e){Mt.emit("overlay-click",{originalEvent:e,target:this.target})},onItemClick:function(e){var i=e.originalEvent,r=e.processedItem,o=this.isProccessedItemGroup(r),n=Pe(r.parent),m=this.isSelected(r);if(m){var b=r.index,l=r.key,a=r.level,d=r.parentKey;this.activeItemPath=this.activeItemPath.filter(function(k){return l!==k.key&&l.startsWith(k.key)}),this.focusedItemInfo={index:b,level:a,parentKey:d},this.dirty=!n,O(this.menubar)}else if(o)this.onItemChange(e);else{var h=n?r:this.activeItemPath.find(function(k){return k.parentKey===""});this.hide(i),this.changeFocusedItemIndex(i,h?h.index:-1),O(this.menubar)}},onItemMouseEnter:function(e){this.dirty&&this.onItemChange(e,"hover")},onItemMouseMove:function(e){this.focused&&this.changeFocusedItemIndex(e,e.processedItem.index)},onArrowDownKey:function(e){var i=this.focusedItemInfo.index!==-1?this.findNextItemIndex(this.focusedItemInfo.index):this.findFirstFocusedItemIndex();this.changeFocusedItemIndex(e,i),e.preventDefault()},onArrowUpKey:function(e){if(e.altKey){if(this.focusedItemInfo.index!==-1){var i=this.visibleItems[this.focusedItemInfo.index],r=this.isProccessedItemGroup(i);!r&&this.onItemChange({originalEvent:e,processedItem:i})}this.popup&&this.hide(e,!0),e.preventDefault()}else{var o=this.focusedItemInfo.index!==-1?this.findPrevItemIndex(this.focusedItemInfo.index):this.findLastFocusedItemIndex();this.changeFocusedItemIndex(e,o),e.preventDefault()}},onArrowLeftKey:function(e){var i=this,r=this.visibleItems[this.focusedItemInfo.index],o=this.activeItemPath.find(function(m){return m.key===r.parentKey}),n=Pe(r.parent);n||(this.focusedItemInfo={index:-1,parentKey:o?o.parentKey:""},this.searchValue="",this.onArrowDownKey(e)),this.activeItemPath=this.activeItemPath.filter(function(m){return m.parentKey!==i.focusedItemInfo.parentKey}),e.preventDefault()},onArrowRightKey:function(e){var i=this.visibleItems[this.focusedItemInfo.index],r=this.isProccessedItemGroup(i);r&&(this.onItemChange({originalEvent:e,processedItem:i}),this.focusedItemInfo={index:-1,parentKey:i.key},this.searchValue="",this.onArrowDownKey(e)),e.preventDefault()},onHomeKey:function(e){this.changeFocusedItemIndex(e,this.findFirstItemIndex()),e.preventDefault()},onEndKey:function(e){this.changeFocusedItemIndex(e,this.findLastItemIndex()),e.preventDefault()},onEnterKey:function(e){if(this.focusedItemInfo.index!==-1){var i=te(this.menubar,'li[id="'.concat("".concat(this.focusedItemId),'"]')),r=i&&te(i,'[data-pc-section="itemlink"]');if(r?r.click():i&&i.click(),!this.popup){var o=this.visibleItems[this.focusedItemInfo.index],n=this.isProccessedItemGroup(o);!n&&(this.focusedItemInfo.index=this.findFirstFocusedItemIndex())}}e.preventDefault()},onSpaceKey:function(e){this.onEnterKey(e)},onEscapeKey:function(e){if(this.popup||this.focusedItemInfo.level!==0){var i=this.focusedItemInfo;this.hide(e,!1),this.focusedItemInfo={index:Number(i.parentKey.split("_")[0]),level:0,parentKey:""},this.popup&&O(this.target)}e.preventDefault()},onTabKey:function(e){if(this.focusedItemInfo.index!==-1){var i=this.visibleItems[this.focusedItemInfo.index],r=this.isProccessedItemGroup(i);!r&&this.onItemChange({originalEvent:e,processedItem:i})}this.hide()},onEnter:function(e){this.autoZIndex&&F.set("menu",e,this.baseZIndex+this.$primevue.config.zIndex.menu),Ct(e,{position:"absolute",top:"0"}),this.alignOverlay(),O(this.menubar),this.scrollInView()},onAfterEnter:function(){this.bindOutsideClickListener(),this.bindScrollListener(),this.bindResizeListener(),this.$emit("show")},onLeave:function(){this.unbindOutsideClickListener(),this.unbindScrollListener(),this.unbindResizeListener(),this.$emit("hide"),this.container=null,this.dirty=!1},onAfterLeave:function(e){this.autoZIndex&&F.clear(e)},alignOverlay:function(){Lt(this.container,this.target);var e=Y(this.target);e>Y(this.container)&&(this.container.style.minWidth=Y(this.target)+"px")},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){var r=e.container&&!e.container.contains(i.target),o=e.popup?!(e.target&&(e.target===i.target||e.target.contains(i.target))):!0;r&&o&&e.hide()},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new wt(this.target,function(i){e.hide(i,!0)})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(i){kt()||e.hide(i,!0)},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},bindMatchMediaListener:function(){var e=this;if(!this.matchMediaListener){var i=matchMedia("(max-width: ".concat(this.breakpoint,")"));this.query=i,this.queryMatches=i.matches,this.matchMediaListener=function(){e.queryMatches=i.matches},this.query.addEventListener("change",this.matchMediaListener)}},unbindMatchMediaListener:function(){this.matchMediaListener&&(this.query.removeEventListener("change",this.matchMediaListener),this.matchMediaListener=null)},isItemMatched:function(e){var i;return this.isValidItem(e)&&((i=this.getProccessedItemLabel(e))===null||i===void 0?void 0:i.toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase()))},isValidItem:function(e){return!!e&&!this.isItemDisabled(e.item)&&!this.isItemSeparator(e.item)&&this.isItemVisible(e.item)},isValidSelectedItem:function(e){return this.isValidItem(e)&&this.isSelected(e)},isSelected:function(e){return this.activeItemPath.some(function(i){return i.key===e.key})},findFirstItemIndex:function(){var e=this;return this.visibleItems.findIndex(function(i){return e.isValidItem(i)})},findLastItemIndex:function(){var e=this;return vt(this.visibleItems,function(i){return e.isValidItem(i)})},findNextItemIndex:function(e){var i=this,r=e<this.visibleItems.length-1?this.visibleItems.slice(e+1).findIndex(function(o){return i.isValidItem(o)}):-1;return r>-1?r+e+1:e},findPrevItemIndex:function(e){var i=this,r=e>0?vt(this.visibleItems.slice(0,e),function(o){return i.isValidItem(o)}):-1;return r>-1?r:e},findSelectedItemIndex:function(){var e=this;return this.visibleItems.findIndex(function(i){return e.isValidSelectedItem(i)})},findFirstFocusedItemIndex:function(){var e=this.findSelectedItemIndex();return e<0?this.findFirstItemIndex():e},findLastFocusedItemIndex:function(){var e=this.findSelectedItemIndex();return e<0?this.findLastItemIndex():e},searchItems:function(e,i){var r=this;this.searchValue=(this.searchValue||"")+i;var o=-1,n=!1;return this.focusedItemInfo.index!==-1?(o=this.visibleItems.slice(this.focusedItemInfo.index).findIndex(function(m){return r.isItemMatched(m)}),o=o===-1?this.visibleItems.slice(0,this.focusedItemInfo.index).findIndex(function(m){return r.isItemMatched(m)}):o+this.focusedItemInfo.index):o=this.visibleItems.findIndex(function(m){return r.isItemMatched(m)}),o!==-1&&(n=!0),o===-1&&this.focusedItemInfo.index===-1&&(o=this.findFirstFocusedItemIndex()),o!==-1&&this.changeFocusedItemIndex(e,o),this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(function(){r.searchValue="",r.searchTimeout=null},500),n},changeFocusedItemIndex:function(e,i){this.focusedItemInfo.index!==i&&(this.focusedItemInfo.index=i,this.scrollInView())},scrollInView:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:-1,i=e!==-1?"".concat(this.$id,"_").concat(e):this.focusedItemId,r=te(this.menubar,'li[id="'.concat(i,'"]'));r&&r.scrollIntoView&&r.scrollIntoView({block:"nearest",inline:"start"})},createProcessedItems:function(e){var i=this,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"",m=[];return e&&e.forEach(function(b,l){var a=(n!==""?n+"_":"")+l,d={item:b,index:l,level:r,key:a,parent:o,parentKey:n};d.items=i.createProcessedItems(b.items,r+1,d,a),m.push(d)}),m},containerRef:function(e){this.container=e},menubarRef:function(e){this.menubar=e?e.$el:void 0}},computed:{processedItems:function(){return this.createProcessedItems(this.model||[])},visibleItems:function(){var e=this,i=this.activeItemPath.find(function(r){return r.key===e.focusedItemInfo.parentKey});return i?i.items:this.processedItems},focusedItemId:function(){return this.focusedItemInfo.index!==-1?"".concat(this.$id).concat(X(this.focusedItemInfo.parentKey)?"_"+this.focusedItemInfo.parentKey:"","_").concat(this.focusedItemInfo.index):null}},components:{TieredMenuSub:At,Portal:Oe}},vn=["id"];function gn(t,e,i,r,o,n){var m=B("TieredMenuSub"),b=B("Portal");return s(),w(b,{appendTo:t.appendTo,disabled:!t.popup},{default:P(function(){return[L(re,f({name:"p-connected-overlay",onEnter:n.onEnter,onAfterEnter:n.onAfterEnter,onLeave:n.onLeave,onAfterLeave:n.onAfterLeave},t.ptm("transition")),{default:P(function(){return[o.visible?(s(),c("div",f({key:0,ref:n.containerRef,id:t.$id,class:t.cx("root"),onClick:e[0]||(e[0]=function(){return n.onOverlayClick&&n.onOverlayClick.apply(n,arguments)})},t.ptmi("root")),[t.$slots.start?(s(),c("div",f({key:0,class:t.cx("start")},t.ptm("start")),[E(t.$slots,"start")],16)):y("",!0),L(m,f({ref:n.menubarRef,id:t.$id+"_list",class:t.cx("rootList"),tabindex:t.disabled?-1:t.tabindex,role:"menubar","aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledby,"aria-disabled":t.disabled||void 0,"aria-orientation":"vertical","aria-activedescendant":o.focused?n.focusedItemId:void 0,menuId:t.$id,focusedItemId:o.focused?n.focusedItemId:void 0,items:n.processedItems,templates:t.$slots,activeItemPath:o.activeItemPath,level:0,visible:o.submenuVisible,pt:t.pt,unstyled:t.unstyled,onFocus:n.onFocus,onBlur:n.onBlur,onKeydown:n.onKeyDown,onItemClick:n.onItemClick,onItemMouseenter:n.onItemMouseEnter,onItemMousemove:n.onItemMouseMove},t.ptm("rootList")),null,16,["id","class","tabindex","aria-label","aria-labelledby","aria-disabled","aria-activedescendant","menuId","focusedItemId","items","templates","activeItemPath","visible","pt","unstyled","onFocus","onBlur","onKeydown","onItemClick","onItemMouseenter","onItemMousemove"]),t.$slots.end?(s(),c("div",f({key:1,class:t.cx("end")},t.ptm("end")),[E(t.$slots,"end")],16)):y("",!0)],16,vn)):y("",!0)]}),_:3},16,["onEnter","onAfterEnter","onLeave","onAfterLeave"])]}),_:3},8,["appendTo","disabled"])}Dt.render=gn;const yn=["onClick"],In=["src"],kn={class:"menu-item-label"},wn=["href"],Ln=["src"],Cn={class:"menu-item-label"},Sn=["src"],Pn={key:0,class:"menu-item-label"},Tt=Z({__name:"appMenuItem",props:{item:{},slim:{type:Boolean}},setup(t){const e=t,i=Ce(),r=Le(),o=De(),n=A(),m=A(o.fullPath.startsWith(e.item.path)),b=K(()=>e.item.path==="/support"?!0:typeof e.item.path=="string"&&e.item.path.startsWith("http")),l=K(()=>{var u,v,T;return!!((v=(u=e.item)==null?void 0:u.children)!=null&&v.length)&&((T=e.item)==null?void 0:T.children.length)>1}),a=K(()=>{var u;return((u=e.item)==null?void 0:u.children)&&e.item.children.length>1}),d=K(()=>{var u,v;return((v=(u=e.item)==null?void 0:u.children)==null?void 0:v.length)===1?e.item.children[0]:null}),h=K(()=>{if(!e.item.path)return!1;if(typeof e.item.name=="string"){if(o.name===e.item.name)return!0;const u=`/${e.item.name.toLowerCase()}`,v=o.path.toLowerCase();return v===u||v.startsWith(`${u}/`)}return!1}),k=K(()=>{var u;return(u=e.item.children)!=null&&u.length?e.item.children.some(v=>v.name?o.name===v.name||typeof v.name=="string"&&o.path.toLowerCase().startsWith(`/${v.name.toLowerCase()}`):!1):!1}),D=K(()=>{var u,v;return l.value?G(e.item.children||[]):[{...e.item,label:(u=e.item.meta)==null?void 0:u.label,icon:(v=e.item.meta)==null?void 0:v.icon,command:()=>{b.value?window.open(e.item.path,"_blank"):e.item.name&&r.push({name:e.item.name})}}]}),G=u=>u.map(v=>{var q,z;const T=typeof v.path=="string"&&v.path.startsWith("http");return{...v,label:(q=v.meta)==null?void 0:q.label,icon:(z=v.meta)==null?void 0:z.icon,items:v.children?G(v.children):void 0,command:()=>{T?window.open(v.path,"_blank"):v.name&&r.push({name:v.name})}}}),ae=(u,v)=>{var T;if(b.value){window.open(e.item.path,"_blank");return}if(e.slim){if(d.value){if(typeof d.value.path=="string"&&d.value.path.startsWith("http")){window.open(d.value.path,"_blank");return}else if(d.value.name){r.push({name:d.value.name});return}}if(a.value){se(u);return}}if(l.value){r.push({name:e.item.name}),m.value=!m.value;return}if(v){u.preventDefault();return}(T=e.item)!=null&&T.name&&(r.push({name:e.item.name}),i.closeMobileMenu())},se=u=>{n.value&&e.slim&&n.value.toggle(u)},j=u=>u.includes("/")?oi(u):u;return(u,v)=>{var _,H,oe,le,ue,de,M,g,V,ce,me,fe,pe,he,J,be,S,ve,Ve,Re,Be,_e,Fe,He,Ne,Ue,$e,Ze,Ge,je,qe,We,Xe,Ye;const T=B("router-link"),q=Dt,z=B("app-menu-item",!0);return s(),c("div",{class:C(["app-merchant-menu-item",{"has-submenu":l.value,"menu-item-slim":u.slim}])},[u.item.path&&!l.value&&!u.slim&&!b.value?(s(),w(T,{key:0,to:{name:u.item.name},custom:""},{default:P(({isActive:W})=>{var ge,Qe,Je,et,tt,it,nt,rt,at,st,ot,lt,ut,dt,ct,mt,ft,pt,ht,bt;return[p("div",{class:C(["menu-item-link",{active:u.item.path===I(o).fullPath||W}]),onClick:Vt=>ae(Vt,W)},[p("div",{class:C(["menu-item-icon-wrapper",{"is-background":(Je=(Qe=(ge=u.item)==null?void 0:ge.meta)==null?void 0:Qe.icon)==null?void 0:Je.includes("question.png")}])},[(tt=(et=u.item)==null?void 0:et.meta)!=null&&tt.icon&&((rt=String((nt=(it=u.item)==null?void 0:it.meta)==null?void 0:nt.icon))!=null&&rt.includes("pi"))?(s(),c("i",{key:0,class:C(["menu-item-icon",[u.item.meta.icon]])},null,2)):(st=(at=u.item)==null?void 0:at.meta)!=null&&st.icon&&((ut=String((lt=(ot=u.item)==null?void 0:ot.meta)==null?void 0:lt.icon))!=null&&ut.includes("/"))?(s(),c("img",{key:1,class:C(["menu-item-icon-img",{"is-background":(mt=(ct=(dt=u.item)==null?void 0:dt.meta)==null?void 0:ct.icon)==null?void 0:mt.includes("question.png")}]),src:j((pt=(ft=u.item)==null?void 0:ft.meta)==null?void 0:pt.icon)},null,10,In)):y("",!0)],2),p("span",kn,R((bt=(ht=u.item)==null?void 0:ht.meta)==null?void 0:bt.label),1)],10,yn)]}),_:1},8,["to"])):b.value&&!u.slim?(s(),c("a",{key:1,href:u.item.path,target:"_blank",class:"menu-item-link external-link"},[p("div",{class:C(["menu-item-icon-wrapper",{"is-background":(oe=(H=(_=u.item)==null?void 0:_.meta)==null?void 0:H.icon)==null?void 0:oe.includes("question.png")}])},[(ue=(le=u.item)==null?void 0:le.meta)!=null&&ue.icon&&((g=String((M=(de=u.item)==null?void 0:de.meta)==null?void 0:M.icon))!=null&&g.includes("pi"))?(s(),c("i",{key:0,class:C(["menu-item-icon",[u.item.meta.icon]])},null,2)):(ce=(V=u.item)==null?void 0:V.meta)!=null&&ce.icon&&((pe=String((fe=(me=u.item)==null?void 0:me.meta)==null?void 0:fe.icon))!=null&&pe.includes("/"))?(s(),c("img",{key:1,class:"menu-item-icon-img",src:j((J=(he=u.item)==null?void 0:he.meta)==null?void 0:J.icon)},null,8,Ln)):y("",!0)],2),p("span",Cn,R((S=(be=u.item)==null?void 0:be.meta)==null?void 0:S.label),1),v[1]||(v[1]=p("i",{class:"pi pi-external-link external-link-icon"},null,-1))],8,wn)):(s(),c("div",{key:2,class:C(["menu-item-link",{expanded:m.value&&!u.slim,active:h.value||k.value,"external-link":b.value}]),onClick:v[0]||(v[0]=W=>ae(W,h.value))},[p("div",{class:C(["menu-item-icon-wrapper",{"is-background":(Re=(Ve=(ve=u.item)==null?void 0:ve.meta)==null?void 0:Ve.icon)==null?void 0:Re.includes("question.png")}])},[(_e=(Be=u.item)==null?void 0:Be.meta)!=null&&_e.icon&&((Ne=String((He=(Fe=u.item)==null?void 0:Fe.meta)==null?void 0:He.icon))!=null&&Ne.includes("pi"))?(s(),c("i",{key:0,class:C(["menu-item-icon",[u.item.meta.icon]])},null,2)):($e=(Ue=u.item)==null?void 0:Ue.meta)!=null&&$e.icon&&((je=String((Ge=(Ze=u.item)==null?void 0:Ze.meta)==null?void 0:Ge.icon))!=null&&je.includes("/"))?(s(),c("img",{key:1,class:"menu-item-icon-img",src:j((We=(qe=u.item)==null?void 0:qe.meta)==null?void 0:We.icon)},null,8,Sn)):y("",!0)],2),u.slim?y("",!0):(s(),c("span",Pn,R((Ye=(Xe=u.item)==null?void 0:Xe.meta)==null?void 0:Ye.label),1)),l.value&&!u.slim?(s(),c("i",{key:1,class:C(["submenu-icon pi pi-angle-down",{expanded:m.value}])},null,2)):y("",!0)],2)),u.slim&&a.value?(s(),w(q,{key:3,ref_key:"menuPopover",ref:n,model:D.value,popup:""},null,8,["model"])):y("",!0),l.value&&!u.slim?(s(),c("div",{key:4,class:C(["submenu",{expanded:m.value}])},[(s(!0),c(x,null,$(u.item.children,(W,ge)=>(s(),w(z,{key:ge,item:W},null,8,["item"]))),128))],2)):y("",!0)],2)}}}),Mn={class:"app-menu-wrap"},xn={key:1,class:"menu-separator"},On=Z({__name:"appMenu",props:{slim:{type:Boolean,default:!1},items:{type:Array,required:!0,default:()=>[]}},setup(t){return(e,i)=>(s(),c("div",Mn,[p("nav",{class:C(["app-merchant-menu",{"menu-slim":t.slim}])},[(s(!0),c(x,null,$(t.items,(r,o)=>{var n;return s(),c(x,{key:o},[(n=r==null?void 0:r.meta)!=null&&n.isSeparator?(s(),c("div",xn)):(s(),w(Tt,{key:0,item:r,slim:t.slim},null,8,["item","slim"]))],64)}),128))],2)]))}});var Kn=Ie`
    .p-drawer {
        display: flex;
        flex-direction: column;
        transform: translate3d(0px, 0px, 0px);
        position: relative;
        transition: transform 0.3s;
        background: dt('drawer.background');
        color: dt('drawer.color');
        border: 1px solid dt('drawer.border.color');
        box-shadow: dt('drawer.shadow');
    }

    .p-drawer-content {
        overflow-y: auto;
        flex-grow: 1;
        padding: dt('drawer.content.padding');
    }

    .p-drawer-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-shrink: 0;
        padding: dt('drawer.header.padding');
    }

    .p-drawer-footer {
        padding: dt('drawer.footer.padding');
    }

    .p-drawer-title {
        font-weight: dt('drawer.title.font.weight');
        font-size: dt('drawer.title.font.size');
    }

    .p-drawer-full .p-drawer {
        transition: none;
        transform: none;
        width: 100vw !important;
        height: 100vh !important;
        max-height: 100%;
        top: 0px !important;
        left: 0px !important;
        border-width: 1px;
    }

    .p-drawer-left .p-drawer-enter-from,
    .p-drawer-left .p-drawer-leave-to {
        transform: translateX(-100%);
    }

    .p-drawer-right .p-drawer-enter-from,
    .p-drawer-right .p-drawer-leave-to {
        transform: translateX(100%);
    }

    .p-drawer-top .p-drawer-enter-from,
    .p-drawer-top .p-drawer-leave-to {
        transform: translateY(-100%);
    }

    .p-drawer-bottom .p-drawer-enter-from,
    .p-drawer-bottom .p-drawer-leave-to {
        transform: translateY(100%);
    }

    .p-drawer-full .p-drawer-enter-from,
    .p-drawer-full .p-drawer-leave-to {
        opacity: 0;
    }

    .p-drawer-full .p-drawer-enter-active,
    .p-drawer-full .p-drawer-leave-active {
        transition: opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    .p-drawer-left .p-drawer {
        width: 20rem;
        height: 100%;
        border-inline-end-width: 1px;
    }

    .p-drawer-right .p-drawer {
        width: 20rem;
        height: 100%;
        border-inline-start-width: 1px;
    }

    .p-drawer-top .p-drawer {
        height: 10rem;
        width: 100%;
        border-block-end-width: 1px;
    }

    .p-drawer-bottom .p-drawer {
        height: 10rem;
        width: 100%;
        border-block-start-width: 1px;
    }

    .p-drawer-left .p-drawer-content,
    .p-drawer-right .p-drawer-content,
    .p-drawer-top .p-drawer-content,
    .p-drawer-bottom .p-drawer-content {
        width: 100%;
        height: 100%;
    }

    .p-drawer-open {
        display: flex;
    }

    .p-drawer-mask:dir(rtl) {
        flex-direction: row-reverse;
    }
`,En={mask:function(e){var i=e.position,r=e.modal;return{position:"fixed",height:"100%",width:"100%",left:0,top:0,display:"flex",justifyContent:i==="left"?"flex-start":i==="right"?"flex-end":"center",alignItems:i==="top"?"flex-start":i==="bottom"?"flex-end":"center",pointerEvents:r?"auto":"none"}},root:{pointerEvents:"auto"}},An={mask:function(e){var i=e.instance,r=e.props,o=["left","right","top","bottom"],n=o.find(function(m){return m===r.position});return["p-drawer-mask",{"p-overlay-mask p-overlay-mask-enter":r.modal,"p-drawer-open":i.containerVisible,"p-drawer-full":i.fullScreen},n?"p-drawer-".concat(n):""]},root:function(e){var i=e.instance;return["p-drawer p-component",{"p-drawer-full":i.fullScreen}]},header:"p-drawer-header",title:"p-drawer-title",pcCloseButton:"p-drawer-close-button",content:"p-drawer-content",footer:"p-drawer-footer"},Dn=ke.extend({name:"drawer",style:Kn,classes:An,inlineStyles:En}),Tn={name:"BaseDrawer",extends:Q,props:{visible:{type:Boolean,default:!1},position:{type:String,default:"left"},header:{type:null,default:null},baseZIndex:{type:Number,default:0},autoZIndex:{type:Boolean,default:!0},dismissable:{type:Boolean,default:!0},showCloseIcon:{type:Boolean,default:!0},closeButtonProps:{type:Object,default:function(){return{severity:"secondary",text:!0,rounded:!0}}},closeIcon:{type:String,default:void 0},modal:{type:Boolean,default:!0},blockScroll:{type:Boolean,default:!1}},style:Dn,provide:function(){return{$pcDrawer:this,$parentInstance:this}}};function ne(t){"@babel/helpers - typeof";return ne=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ne(t)}function Me(t,e,i){return(e=zn(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function zn(t){var e=Vn(t,"string");return ne(e)=="symbol"?e:e+""}function Vn(t,e){if(ne(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var r=i.call(t,e);if(ne(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var zt={name:"Drawer",extends:Tn,inheritAttrs:!1,emits:["update:visible","show","after-show","hide","after-hide","before-hide"],data:function(){return{containerVisible:this.visible}},container:null,mask:null,content:null,headerContainer:null,footerContainer:null,closeButton:null,outsideClickListener:null,documentKeydownListener:null,watch:{dismissable:function(e){e?this.enableDocumentSettings():this.disableDocumentSettings()}},updated:function(){this.visible&&(this.containerVisible=this.visible)},beforeUnmount:function(){this.disableDocumentSettings(),this.mask&&this.autoZIndex&&F.clear(this.mask),this.container=null,this.mask=null},methods:{hide:function(){this.$emit("update:visible",!1)},onEnter:function(){this.$emit("show"),this.focus(),this.bindDocumentKeyDownListener(),this.autoZIndex&&F.set("modal",this.mask,this.baseZIndex||this.$primevue.config.zIndex.modal)},onAfterEnter:function(){this.enableDocumentSettings(),this.$emit("after-show")},onBeforeLeave:function(){this.modal&&!this.isUnstyled&&Wt(this.mask,"p-overlay-mask-leave"),this.$emit("before-hide")},onLeave:function(){this.$emit("hide")},onAfterLeave:function(){this.autoZIndex&&F.clear(this.mask),this.unbindDocumentKeyDownListener(),this.containerVisible=!1,this.disableDocumentSettings(),this.$emit("after-hide")},onMaskClick:function(e){this.dismissable&&this.modal&&this.mask===e.target&&this.hide()},focus:function(){var e=function(o){return o&&o.querySelector("[autofocus]")},i=this.$slots.header&&e(this.headerContainer);i||(i=this.$slots.default&&e(this.container),i||(i=this.$slots.footer&&e(this.footerContainer),i||(i=this.closeButton))),i&&O(i)},enableDocumentSettings:function(){this.dismissable&&!this.modal&&this.bindOutsideClickListener(),this.blockScroll&&qt()},disableDocumentSettings:function(){this.unbindOutsideClickListener(),this.blockScroll&&jt()},onKeydown:function(e){e.code==="Escape"&&this.hide()},containerRef:function(e){this.container=e},maskRef:function(e){this.mask=e},contentRef:function(e){this.content=e},headerContainerRef:function(e){this.headerContainer=e},footerContainerRef:function(e){this.footerContainer=e},closeButtonRef:function(e){this.closeButton=e?e.$el:void 0},bindDocumentKeyDownListener:function(){this.documentKeydownListener||(this.documentKeydownListener=this.onKeydown,document.addEventListener("keydown",this.documentKeydownListener))},unbindDocumentKeyDownListener:function(){this.documentKeydownListener&&(document.removeEventListener("keydown",this.documentKeydownListener),this.documentKeydownListener=null)},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){e.isOutsideClicked(i)&&e.hide()},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},isOutsideClicked:function(e){return this.container&&!this.container.contains(e.target)}},computed:{fullScreen:function(){return this.position==="full"},closeAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.close:void 0},dataP:function(){return we(Me(Me(Me({"full-screen":this.position==="full"},this.position,this.position),"open",this.containerVisible),"modal",this.modal))}},directives:{focustrap:Gt},components:{Button:Pt,Portal:Oe,TimesIcon:Zt}},Rn=["data-p"],Bn=["aria-modal","data-p"];function _n(t,e,i,r,o,n){var m=B("Button"),b=B("Portal"),l=Ee("focustrap");return s(),w(b,null,{default:P(function(){return[o.containerVisible?(s(),c("div",f({key:0,ref:n.maskRef,onMousedown:e[0]||(e[0]=function(){return n.onMaskClick&&n.onMaskClick.apply(n,arguments)}),class:t.cx("mask"),style:t.sx("mask",!0,{position:t.position,modal:t.modal}),"data-p":n.dataP},t.ptm("mask")),[L(re,f({name:"p-drawer",onEnter:n.onEnter,onAfterEnter:n.onAfterEnter,onBeforeLeave:n.onBeforeLeave,onLeave:n.onLeave,onAfterLeave:n.onAfterLeave,appear:""},t.ptm("transition")),{default:P(function(){return[t.visible?Ae((s(),c("div",f({key:0,ref:n.containerRef,class:t.cx("root"),style:t.sx("root"),role:"complementary","aria-modal":t.modal,"data-p":n.dataP},t.ptmi("root")),[t.$slots.container?E(t.$slots,"container",{key:0,closeCallback:n.hide}):(s(),c(x,{key:1},[p("div",f({ref:n.headerContainerRef,class:t.cx("header")},t.ptm("header")),[E(t.$slots,"header",{class:C(t.cx("title"))},function(){return[t.header?(s(),c("div",f({key:0,class:t.cx("title")},t.ptm("title")),R(t.header),17)):y("",!0)]}),t.showCloseIcon?E(t.$slots,"closebutton",{key:0,closeCallback:n.hide},function(){return[L(m,f({ref:n.closeButtonRef,type:"button",class:t.cx("pcCloseButton"),"aria-label":n.closeAriaLabel,unstyled:t.unstyled,onClick:n.hide},t.closeButtonProps,{pt:t.ptm("pcCloseButton"),"data-pc-group-section":"iconcontainer"}),{icon:P(function(a){return[E(t.$slots,"closeicon",{},function(){return[(s(),w(N(t.closeIcon?"span":"TimesIcon"),f({class:[t.closeIcon,a.class]},t.ptm("pcCloseButton").icon),null,16,["class"]))]})]}),_:3},16,["class","aria-label","unstyled","onClick","pt"])]}):y("",!0)],16),p("div",f({ref:n.contentRef,class:t.cx("content")},t.ptm("content")),[E(t.$slots,"default")],16),t.$slots.footer?(s(),c("div",f({key:0,ref:n.footerContainerRef,class:t.cx("footer")},t.ptm("footer")),[E(t.$slots,"footer")],16)):y("",!0)],64))],16,Bn)),[[l]]):y("",!0)]}),_:3},16,["onEnter","onAfterEnter","onBeforeLeave","onLeave","onAfterLeave"])],16,Rn)):y("",!0)]}),_:3})}zt.render=_n;const Fn={class:"mobile-menu-header"},Hn={class:"mobile-menu-content"},Nn={key:1,class:"menu-separator"},Un=Z({__name:"mobileMenu",props:{items:{type:Array,required:!0,default:()=>[]}},setup(t){const e=Le(),i=Ce(),{isMobileMenuVisible:r}=U(i),o=n=>{(n.children&&n.children.length===0||n.children===void 0||n.children===null)&&i.closeMobileMenu()};return(n,m)=>{const b=xt,l=zt;return s(),w(l,{visible:I(r),"onUpdate:visible":m[1]||(m[1]=a=>ee(r)?r.value=a:null),position:"left",class:"merchant-mobile-menu-drawer",modal:!0,dismissable:!0,"show-close-icon":!1},{default:P(()=>[p("div",Fn,[L(b,{src:I(ci),alt:"Logo",width:"160",onClick:m[0]||(m[0]=a=>{I(e).push("/merchant"),I(i).closeMobileMenu()})},null,8,["src"])]),p("div",Hn,[(s(!0),c(x,null,$(t.items,(a,d)=>{var h;return s(),c(x,{key:d},[(h=a==null?void 0:a.meta)!=null&&h.isSeparator?(s(),c("div",Nn)):(s(),w(Tt,{key:0,item:a,slim:!1,onClick:k=>o(a)},null,8,["item","onClick"]))],64)}),128))])]),_:1},8,["visible"])}}}),$n=Se(Un,[["__scopeId","data-v-41898e1d"]]),Zn={class:"sidebar-header"},Gn=Z({__name:"appSidebar",setup(t){const{t:e}=Te(),i=ze(),r=St(),{isSidebarSlim:o,isDarkTheme:n}=U(i),{userMenu:m}=U(r),b=a=>{var h,k,D;if(!a)return null;const d=(h=a==null?void 0:a.meta)!=null&&h.i18nKey?e(a.meta.i18nKey):(k=a==null?void 0:a.meta)==null?void 0:k.label;return{...a,meta:{...a.meta,label:d},children:(D=a.children)==null?void 0:D.map(b).filter(G=>G!==null)}},l=K(()=>m.value.map(a=>b(a)));return(a,d)=>{const h=xt;return s(),c("aside",{class:C(["app-merchant-sidebar",{"sidebar-slim":I(o),"theme-dark":I(n)}])},[p("div",Zn,[I(o)?(s(),w(h,{key:1,src:I(li),alt:"Image",width:"150px",onClick:d[1]||(d[1]=k=>a.$router.push({path:"/"}))},null,8,["src"])):(s(),w(h,{key:0,src:I(ui),alt:"Image",width:"100%",onClick:d[0]||(d[0]=k=>a.$router.push({path:"/"}))},null,8,["src"]))]),L(On,{items:l.value,slim:I(o)},null,8,["items","slim"]),L($n,{items:l.value},null,8,["items"])],2)}}}),jn={class:"layout-merchant-sidebar"},qn={class:"layout-merchant-content-wrapper"},Wn={class:"loading-container"},Xn=Z({__name:"index",setup(t){const e=A(null),i=Ce(),r=ze(),{isShowRouteView:o}=U(i),{isSidebarVisible:n,isSidebarSlim:m}=U(r),b=A(!1),l=()=>{b.value=window.innerWidth<992};return Xt(()=>{l(),window.addEventListener("resize",l)}),Yt(()=>{window.removeEventListener("resize",l)}),document.title="Bill Buddy HQ",(a,d)=>{const h=B("router-view");return s(),c("div",{class:C(["layout-merchant-wrapper",{"sidebar-hidden":!I(n),"sidebar-slim":I(m),"mobile-layout":b.value}])},[p("div",jn,[L(Gn)]),p("div",{class:C(["layout-merchant-header",{"sidebar-hidden":!I(n),"sidebar-slim":I(m)}])},[L(rn)],2),p("div",qn,[p("div",{ref_key:"contentRef",ref:e,class:"layout-merchant-content"},[I(o)?(s(),w(h,{key:0},{default:P(({Component:k,route:D})=>[k?(s(),w(re,{key:0,name:"fade",mode:"out-in"},{default:P(()=>[(s(),w(Qt,{include:I(i).keepAliveComponents,max:10},[(s(),w(Jt,null,{fallback:P(()=>[p("div",Wn,[L(I(ei),{style:{width:"50px",height:"50px"},"stroke-width":"4",fill:"var(--surface-ground)","animation-duration":".5s"}),d[0]||(d[0]=p("p",null,"Loading...",-1))])]),default:P(()=>[(s(),w(N(k),{key:D.fullPath}))]),_:2},1024))],1032,["include"]))]),_:2},1024)):y("",!0)]),_:1})):y("",!0)],512)])],2)}}}),kr=Se(Xn,[["__scopeId","data-v-ee7eb808"]]);export{kr as default};
