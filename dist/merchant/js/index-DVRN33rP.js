import{d as P,k as H,r as p,f as I,R as G,S as L,a as f,q as M,b as o,A as z,P as A,o as h}from"./index-BPwFJVSY.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const F={class:"recaptcha-wrap"},N={key:0,class:"recaptcha-loading"},B={key:1,class:"recaptcha-timeout"},q={key:2,class:"recaptcha-error"},K=P({__name:"index",props:{siteKey:{default:"6Lc7EhQrAAAAAIsN6MfROAMUqehxNI27h2gPHcG8"},theme:{default:"light"},size:{default:"normal"},tabindex:{default:0}},emits:["verify","expired","error"],setup(k,{expose:T,emit:x}){const d=k,n=x,{locale:y}=H(),c=p(null),t=p(null),s=p(!1),a=p(!0),v=p(!1);let i=null;const w=()=>new Promise((e,r)=>{if(window.grecaptcha){e();return}const u=document.createElement("script");u.src=`https://www.google.com/recaptcha/api.js?hl=${y.value}&render=explicit`,u.async=!0,u.defer=!0,u.onload=()=>e(),u.onerror=b=>r(b),document.head.appendChild(u)}),m=async()=>{if(await A(),!(!window.grecaptcha||!c.value)){if(t.value!==null){window.grecaptcha.reset(t.value);return}t.value=window.grecaptcha.render(c.value,{sitekey:d.siteKey,theme:d.theme==="auto"?void 0:d.theme,size:d.size,tabindex:d.tabindex,callback:e=>n("verify",e),"expired-callback":()=>n("expired"),"error-callback":()=>n("error")})}},_=()=>{window.grecaptcha&&t.value!==null&&window.grecaptcha.reset(t.value)},R=()=>window.grecaptcha&&t.value!==null?window.grecaptcha.getResponse(t.value):"",g=()=>{i!==null&&window.clearTimeout(i),i=window.setTimeout(()=>{a.value&&(a.value=!1,v.value=!0)},3e4)},l=()=>{i!==null&&(window.clearTimeout(i),i=null)},C=async()=>{try{a.value=!0,v.value=!1,s.value=!1,g(),await w();const e=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(e),m(),a.value=!1,l())},100)}catch(e){console.error("Failed to load reCAPTCHA on retry:",e),a.value=!1,s.value=!0,l(),n("error")}};return I(async()=>{try{a.value=!0,g(),await w();const e=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(e),m(),a.value=!1,l())},100)}catch(e){console.error("Failed to load reCAPTCHA:",e),a.value=!1,s.value=!0,l(),n("error")}}),G(()=>{if(window.grecaptcha&&t.value!==null){const e=c.value;e&&(e.innerHTML=""),t.value=null}l()}),L(y,()=>{t.value!==null&&(t.value=null,c.value&&(c.value.innerHTML=""),s.value=!1,v.value=!1,a.value=!0,g(),w().then(async()=>{await A(),m(),a.value=!1,l()}).catch(e=>{console.error("Failed to load reCAPTCHA after locale change:",e),a.value=!1,s.value=!0,l(),n("error")}))}),T({reset:_,execute:R}),(e,r)=>(h(),f("div",F,[a.value?(h(),f("div",N,r[0]||(r[0]=[o("div",{class:"loading-spinner"},null,-1),o("p",null,"Loading Google reCAPTCHA...",-1)]))):v.value?(h(),f("div",B,[r[1]||(r[1]=o("p",null,"Loading Google reCAPTCHA timed out after 30 seconds.",-1)),o("button",{class:"retry-button",onClick:C}," Retry ")])):s.value?(h(),f("div",q,[r[2]||(r[2]=o("p",null,"Failed to load Google reCAPTCHA. Please check your internet connection and try again.",-1)),o("button",{class:"retry-button",onClick:C}," Retry ")])):M("",!0),o("div",{ref_key:"recaptchaContainer",ref:c,class:z(e.$attrs.class),"data-theme":"dark"},null,2)]))}}),V=E(K,[["__scopeId","data-v-113c49e4"]]);export{V as G};
