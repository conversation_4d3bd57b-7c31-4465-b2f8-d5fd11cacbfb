import{Q as pe,c as ye,aZ as De,f as Ce,P as Fe,v as C,a_ as ae,a$ as oe,b0 as Ge,b1 as Me,b2 as me,r as Ae,S as Ne,ap as ke,E as Be,a1 as Ee,b3 as Ke,a as Ue,o as Ie,I as le,aJ as be,p as Le,w as He,a6 as We}from"./index-BPwFJVSY.js";function N(e){"@babel/helpers - typeof";return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},N(e)}function he(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,n)}return t}function ie(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?he(Object(t),!0).forEach(function(n){Ve(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):he(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Ve(e,r,t){return(r=qe(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function qe(e){var r=Je(e,"string");return N(r)=="symbol"?r:r+""}function Je(e,r){if(N(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(N(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}function I(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,t=typeof Symbol=="function"?Symbol:{},n=t.iterator||"@@iterator",i=t.toStringTag||"@@toStringTag";function l(j,E,R,J){var Q=E&&E.prototype instanceof f?E:f,L=Object.create(Q.prototype);return k(L,"_invoke",function(w,a,o){var s,u,c,h=0,P=o||[],p=!1,m={p:0,n:0,v:e,a:V,f:V.bind(e,4),d:function(S,F){return s=S,u=0,c=e,m.n=F,d}};function V(g,S){for(u=g,c=S,r=0;!p&&h&&!F&&r<P.length;r++){var F,O=P[r],T=m.p,A=O[2];g>3?(F=A===S)&&(u=O[4]||3,c=O[5]===e?O[3]:O[5],O[4]=3,O[5]=e):O[0]<=T&&((F=g<2&&T<O[1])?(u=0,m.v=S,m.n=O[1]):T<A&&(F=g<3||O[0]>S||S>A)&&(O[4]=g,O[5]=S,m.n=A,u=0))}if(F||g>1)return d;throw p=!0,S}return function(g,S,F){if(h>1)throw TypeError("Generator is already running");for(p&&S===1&&V(S,F),u=S,c=F;(r=u<2?e:c)||!p;){s||(u?u<3?(u>1&&(m.n=-1),V(u,c)):m.n=c:m.v=c);try{if(h=2,s){if(u||(g="next"),r=s[g]){if(!(r=r.call(s,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,u<2&&(u=0)}else u===1&&(r=s.return)&&r.call(s),u<2&&(c=TypeError("The iterator does not provide a '"+g+"' method"),u=1);s=e}else if((r=(p=m.n<0)?c:w.call(a,m))!==d)break}catch(O){s=e,u=1,c=O}finally{h=1}}return{value:r,done:p}}}(j,R,J),!0),L}var d={};function f(){}function v(){}function y(){}r=Object.getPrototypeOf;var b=[][n]?r(r([][n]())):(k(r={},n,function(){return this}),r),_=y.prototype=f.prototype=Object.create(b);function B(j){return Object.setPrototypeOf?Object.setPrototypeOf(j,y):(j.__proto__=y,k(j,i,"GeneratorFunction")),j.prototype=Object.create(_),j}return v.prototype=y,k(_,"constructor",y),k(y,"constructor",v),v.displayName="GeneratorFunction",k(y,i,"GeneratorFunction"),k(_),k(_,i,"Generator"),k(_,n,function(){return this}),k(_,"toString",function(){return"[object Generator]"}),(I=function(){return{w:l,m:B}})()}function k(e,r,t,n){var i=Object.defineProperty;try{i({},"",{})}catch{i=0}k=function(d,f,v,y){if(f)i?i(d,f,{value:v,enumerable:!y,configurable:!y,writable:!y}):d[f]=v;else{var b=function(B,j){k(d,B,function(E){return this._invoke(B,j,E)})};b("next",0),b("throw",1),b("return",2)}},k(e,r,t,n)}function ge(e,r,t,n,i,l,d){try{var f=e[l](d),v=f.value}catch(y){return void t(y)}f.done?r(v):Promise.resolve(v).then(n,i)}function M(e){return function(){var r=this,t=arguments;return new Promise(function(n,i){var l=e.apply(r,t);function d(v){ge(l,n,i,d,f,"next",v)}function f(v){ge(l,n,i,d,f,"throw",v)}d(void 0)})}}function se(e,r){return Xe(e)||ze(e,r)||Ze(e,r)||Qe()}function Qe(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ze(e,r){if(e){if(typeof e=="string")return Oe(e,r);var t={}.toString.call(e).slice(8,-1);return t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set"?Array.from(e):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Oe(e,r):void 0}}function Oe(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function ze(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n,i,l,d,f=[],v=!0,y=!1;try{if(l=(t=t.call(e)).next,r!==0)for(;!(v=(n=l.call(t)).done)&&(f.push(n.value),f.length!==r);v=!0);}catch(b){y=!0,i=b}finally{try{if(!v&&t.return!=null&&(d=t.return(),Object(d)!==d))return}finally{if(y)throw i}}return f}}function Xe(e){if(Array.isArray(e))return e}function Ye(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;De()?Ce(e):r?e():Fe(e)}function xe(e,r,t){var n=Ae(!0),i=Ne(e,function(l,d){n.value&&r(l,d)},t);return{stop:i,pause:function(){n.value=!1},resume:function(){n.value=!0}}}function ue(e){return Object.entries(e).reduce(function(r,t){var n=se(t,2),i=n[0],l=n[1];return i.split(/[\.\[\]]+/).filter(Boolean).reduce(function(d,f,v,y){var b;return(b=d[f])!==null&&b!==void 0?b:d[f]=isNaN(y[v+1])?v===y.length-1?l:{}:[]},r),r},{})}function je(e,r){if(!e||!r)return null;try{var t=e[r];if(ke(t))return t}catch{}var n=r.split(/[\.\[\]]+/).filter(Boolean);return n.reduce(function(i,l){return i&&i[l]!==void 0?i[l]:void 0},e)}var er=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=pe({}),n=pe({}),i=ye(function(){return Object.values(t).every(function(w){return!w.invalid})}),l=ye(function(){return ue(t)}),d=function(a,o){return{value:o??je(r.initialValues,a),touched:!1,dirty:!1,pristine:!0,valid:!0,invalid:!1,error:null,errors:[]}},f=function(a,o){var s=ae(o,a);return s===!0||oe(s)&&s.includes(a)},v=function(){var w=M(I().m(function a(o,s){var u,c,h,P,p;return I().w(function(m){for(;;)switch(m.n){case 0:if(c={},!oe(r[o])){m.n=2;break}return m.n=1,j(r[o]);case 1:c=m.v,m.n=4;break;case 2:if(P=(u=r[o])!==null&&u!==void 0?u:s,!P){m.n=4;break}return m.n=3,j();case 3:c=m.v;case 4:if(h=Object.keys(n).filter(function(V){var g;return(g=n[V])===null||g===void 0||(g=g.options)===null||g===void 0?void 0:g[o]})||[],p=ke(h),!p){m.n=6;break}return m.n=5,j(h);case 5:c=m.v;case 6:return m.a(2,c)}},a)}));return function(o,s){return w.apply(this,arguments)}}(),y=function(a,o,s,u){var c,h;((c=o==null?void 0:o[s])!==null&&c!==void 0?c:f(a,(h=r[s])!==null&&h!==void 0?h:u))&&j(a)},b=function(a,o){var s,u;if(!a)return[];(s=n[a])===null||s===void 0||s._watcher.stop(),t[a]||(t[a]=d(a,o==null?void 0:o.initialValue));var c=C((u=ae(o,t[a]))===null||u===void 0?void 0:u.props,ae(o==null?void 0:o.props,t[a]),{name:a,onBlur:function(){t[a].touched=!0,y(a,o,"validateOnBlur")},onInput:function(p){t[a].value=p&&Object.hasOwn(p,"value")?p.value:p.target.value},onChange:function(p){t[a].value=p&&Object.hasOwn(p,"value")?p.value:p.target.type==="checkbox"||p.target.type==="radio"?p.target.checked:p.target.value},onInvalid:function(p){var m;t[a].invalid=!0,t[a].errors=p,t[a].error=(m=p==null?void 0:p[0])!==null&&m!==void 0?m:null}}),h=xe(function(){return t[a].value},function(P,p){t[a].pristine&&(t[a].pristine=!1),P!==p&&(t[a].dirty=!0),y(a,o,"validateOnValueUpdate",!0)});return n[a]={props:c,states:t[a],options:o,_watcher:h},[t[a],c]},_=function(a){return function(){var o=M(I().m(function s(u){var c;return I().w(function(h){for(;;)switch(h.n){case 0:return h.n=1,v("validateOnSubmit",!0);case 1:return c=h.v,h.a(2,a(ie({originalEvent:u,valid:me(i),states:me(l),reset:E},c)))}},s)}));return function(s){return o.apply(this,arguments)}}()},B=function(a){return function(){var o=M(I().m(function s(u){return I().w(function(c){for(;;)switch(c.n){case 0:return E(),c.a(2,a({originalEvent:u}))}},s)}));return function(s){return o.apply(this,arguments)}}()},j=function(){var w=M(I().m(function a(o){var s,u,c,h,P,p,m,V,g,S,F,O,T,A,Z,z,X,Y,x,ee,H,G,D,W,ce,re,q,fe,te;return I().w(function($){for(;;)switch($.n){case 0:return P=Object.entries(t).reduce(function(ne,Te){var ve=se(Te,2),de=ve[0],Re=ve[1];return ne.names.push(de),ne.values[de]=Re.value,ne},{names:[],values:{}}),p=[P.names,ue(P.values)],m=p[0],V=p[1],$.n=1,(u=r.resolver)===null||u===void 0?void 0:u.call(r,{names:m,values:V});case 1:if(ce=s=$.v,W=ce!==null,!W){$.n=2;break}W=s!==void 0;case 2:if(!W){$.n=3;break}re=s,$.n=4;break;case 3:re={values:V};case 4:g=re,(h=(c=g).errors)!==null&&h!==void 0||(c.errors={}),S=[o].flat(),F=0,O=Object.entries(n);case 5:if(!(F<O.length)){$.n=12;break}if(T=se(O[F],2),A=T[0],Z=T[1],!(S.includes(A)||!o||Me(g.errors))){$.n=11;break}if(x=(z=Z.options)===null||z===void 0?void 0:z.resolver,!x){$.n=10;break}return H=Z.states.value,$.n=6,x({values:H,value:H,name:A});case 6:if(fe=ee=$.v,q=fe!==null,!q){$.n=7;break}q=ee!==void 0;case 7:if(!q){$.n=8;break}te=ee,$.n=9;break;case 8:te={values:H};case 9:G=te,oe(G.errors)&&(G.errors=Ve({},A,G.errors)),g=Ge(g,G);case 10:D=(X=je(g.errors,A))!==null&&X!==void 0?X:[],t[A].invalid=D.length>0,t[A].valid=!t[A].invalid,t[A].errors=D,t[A].error=(Y=D==null?void 0:D[0])!==null&&Y!==void 0?Y:null;case 11:F++,$.n=5;break;case 12:return $.a(2,ie(ie({},g),{},{errors:ue(g.errors)}))}},a)}));return function(o){return w.apply(this,arguments)}}(),E=function(){Object.keys(t).forEach(function(){var a=M(I().m(function o(s){var u,c;return I().w(function(h){for(;;)switch(h.n){case 0:return c=n[s]._watcher,c.pause(),n[s].states=t[s]=d(s,(u=n[s])===null||u===void 0||(u=u.options)===null||u===void 0?void 0:u.initialValue),h.n=1,Fe();case 1:c.resume();case 2:return h.a(2)}},o)}));return function(o){return a.apply(this,arguments)}}())},R=function(a,o){t[a]!==void 0&&(t[a].value=o)},J=function(a){var o;return(o=n[a])===null||o===void 0?void 0:o.states},Q=function(a){Object.keys(a).forEach(function(o){return R(o,a[o])})},L=function(){v("validateOnMount")};return Ye(L),{defineField:b,setFieldValue:R,getFieldState:J,handleSubmit:_,handleReset:B,validate:j,setValues:Q,reset:E,valid:i,states:l,fields:n}},rr={root:"p-form p-component"},tr=Be.extend({name:"form",classes:rr}),nr={name:"BaseForm",extends:Ee,style:tr,props:{resolver:{type:Function,default:null},initialValues:{type:Object,default:null},validateOnValueUpdate:{type:[Boolean,Array],default:!0},validateOnBlur:{type:[Boolean,Array],default:!1},validateOnMount:{type:[Boolean,Array],default:!1},validateOnSubmit:{type:[Boolean,Array],default:!0}},provide:function(){return{$pcForm:this,$parentInstance:this}}};function K(e){"@babel/helpers - typeof";return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},K(e)}function we(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,n)}return t}function ar(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?we(Object(t),!0).forEach(function(n){or(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):we(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function or(e,r,t){return(r=ir(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function ir(e){var r=ur(e,"string");return K(r)=="symbol"?r:r+""}function ur(e,r){if(K(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(K(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}function lr(e,r){return vr(e)||fr(e,r)||cr(e,r)||sr()}function sr(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cr(e,r){if(e){if(typeof e=="string")return Se(e,r);var t={}.toString.call(e).slice(8,-1);return t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set"?Array.from(e):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Se(e,r):void 0}}function Se(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function fr(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n,i,l,d,f=[],v=!0,y=!1;try{if(l=(t=t.call(e)).next,r!==0)for(;!(v=(n=l.call(t)).done)&&(f.push(n.value),f.length!==r);v=!0);}catch(b){y=!0,i=b}finally{try{if(!v&&t.return!=null&&(d=t.return(),Object(d)!==d))return}finally{if(y)throw i}}return f}}function vr(e){if(Array.isArray(e))return e}var dr={name:"Form",extends:nr,inheritAttrs:!1,emits:["submit","reset"],setup:function(r,t){var n=t.emit,i=Ae(null),l=er(r),d=function(){var _;(_=i.value)===null||_===void 0||_.requestSubmit()},f=function(_,B){if(!(B!=null&&B.novalidate)){var j=l.defineField(_,B),E=lr(j,2),R=E[1];return R}return{}},v=l.handleSubmit(function(b){n("submit",b)}),y=l.handleReset(function(b){n("reset",b)});return ar({formRef:i,submit:d,register:f,onSubmit:v,onReset:y},Ke(l,["handleSubmit","handleReset"]))}};function pr(e,r,t,n,i,l){return Ie(),Ue("form",C({ref:"formRef",onSubmit:r[0]||(r[0]=be(function(){return n.onSubmit&&n.onSubmit.apply(n,arguments)},["prevent"])),onReset:r[1]||(r[1]=be(function(){return n.onReset&&n.onReset.apply(n,arguments)},["prevent"])),class:e.cx("root")},e.ptmi("root")),[le(e.$slots,"default",C({register:n.register,valid:e.valid,reset:e.reset},e.states))],16)}dr.render=pr;var yr={root:"p-formfield p-component"},mr=Be.extend({name:"formfield",classes:yr}),br={name:"BaseFormField",extends:Ee,style:mr,props:{name:{type:String,default:void 0},resolver:{type:Function,default:void 0},initialValue:{type:null,default:void 0},validateOnValueUpdate:{type:Boolean,default:void 0},validateOnBlur:{type:Boolean,default:void 0},validateOnMount:{type:Boolean,default:void 0},validateOnSubmit:{type:Boolean,default:void 0},as:{type:[String,Object],default:"DIV"},asChild:{type:Boolean,default:!1}},provide:function(){return{$pcFormField:this,$parentInstance:this}}};function U(e){"@babel/helpers - typeof";return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},U(e)}function $e(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,n)}return t}function _e(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?$e(Object(t),!0).forEach(function(n){hr(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):$e(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function hr(e,r,t){return(r=gr(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function gr(e){var r=Or(e,"string");return U(r)=="symbol"?r:r+""}function Or(e,r){if(U(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(U(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}var jr={name:"FormField",extends:br,inheritAttrs:!1,inject:{$pcForm:{default:void 0}},watch:{formControl:{immediate:!0,handler:function(r){var t,n;(t=this.$pcForm)===null||t===void 0||(n=t.register)===null||n===void 0||n.call(t,this.name,r)}}},computed:{formControl:function(){return{name:this.name,resolver:this.resolver,initialValue:this.initialValue,validateOnValueUpdate:this.validateOnValueUpdate,validateOnBlur:this.validateOnBlur,validateOnMount:this.validateOnMount,validateOnSubmit:this.validateOnSubmit}},field:function(){var r;return((r=this.$pcForm)===null||r===void 0||(r=r.fields)===null||r===void 0?void 0:r[this.name])||{}},fieldAttrs:function(){return _e(_e({},this.field.props),this.field.states)}}};function wr(e,r,t,n,i,l){return e.asChild?le(e.$slots,"default",C({key:1,class:e.cx("root"),props:l.field.props},l.fieldAttrs)):(Ie(),Le(We(e.as),C({key:0,class:e.cx("root")},e.ptmi("root")),{default:He(function(){return[le(e.$slots,"default",C({props:l.field.props},l.fieldAttrs))]}),_:3},16,["class"]))}jr.render=wr;function Sr(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function $r(e){return!Sr(e)}function _r(e,r=!0){return e instanceof Object&&e.constructor===Object&&(r||Object.keys(e).length!==0)}var Pe=(e,r)=>_r(e)&&e.hasOwnProperty(r)?e:r?{[r]:e}:e,Fr=(e,r,t)=>async({values:n,name:i})=>{let{sync:l=!1,raw:d=!1}={};try{let f=await e[l?"parse":"parseAsync"](n,r);return{values:Pe(d?n:f,i),errors:{}}}catch(f){if(Array.isArray(f==null?void 0:f.errors))return{values:Pe(d?n:void 0,i),errors:f.errors.reduce((v,y)=>{let b=$r(y.path)?y.path.join("."):i;return b&&(v[b]||(v[b]=[]),v[b].push(y)),v},{})};throw f}};export{jr as a,Fr as h,dr as s};
