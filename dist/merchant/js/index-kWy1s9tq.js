import{D as x,E as $,ar as tt,a1 as et,a4 as it,aw as G,aL as k,aM as N,H as st,a as T,o as R,I as A,q as K,b as nt,y as q,z as J,v as M,e as rt}from"./index-BPwFJVSY.js";var lt=x`
    .p-virtualscroller-loader {
        background: dt('virtualscroller.loader.mask.background');
        color: dt('virtualscroller.loader.mask.color');
    }

    .p-virtualscroller-loading-icon {
        font-size: dt('virtualscroller.loader.icon.size');
        width: dt('virtualscroller.loader.icon.size');
        height: dt('virtualscroller.loader.icon.size');
    }
`,ot=`
.p-virtualscroller {
    position: relative;
    overflow: auto;
    contain: strict;
    transform: translateZ(0);
    will-change: scroll-position;
    outline: 0 none;
}

.p-virtualscroller-content {
    position: absolute;
    top: 0;
    left: 0;
    min-height: 100%;
    min-width: 100%;
    will-change: transform;
}

.p-virtualscroller-spacer {
    position: absolute;
    top: 0;
    left: 0;
    height: 1px;
    width: 1px;
    transform-origin: 0 0;
    pointer-events: none;
}

.p-virtualscroller-loader {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.p-virtualscroller-loader-mask {
    display: flex;
    align-items: center;
    justify-content: center;
}

.p-virtualscroller-horizontal > .p-virtualscroller-content {
    display: flex;
}

.p-virtualscroller-inline .p-virtualscroller-content {
    position: static;
}

.p-virtualscroller .p-virtualscroller-loading {
    transform: none !important;
    min-height: 0;
    position: sticky;
    inset-block-start: 0;
    inset-inline-start: 0;
}
`,Q=$.extend({name:"virtualscroller",css:ot,style:lt}),at={name:"BaseVirtualScroller",extends:et,props:{id:{type:String,default:null},style:null,class:null,items:{type:Array,default:null},itemSize:{type:[Number,Array],default:0},scrollHeight:null,scrollWidth:null,orientation:{type:String,default:"vertical"},numToleratedItems:{type:Number,default:null},delay:{type:Number,default:0},resizeDelay:{type:Number,default:10},lazy:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loaderDisabled:{type:Boolean,default:!1},columns:{type:Array,default:null},loading:{type:Boolean,default:!1},showSpacer:{type:Boolean,default:!0},showLoader:{type:Boolean,default:!1},tabindex:{type:Number,default:0},inline:{type:Boolean,default:!1},step:{type:Number,default:0},appendOnly:{type:Boolean,default:!1},autoSize:{type:Boolean,default:!1}},style:Q,provide:function(){return{$pcVirtualScroller:this,$parentInstance:this}},beforeMount:function(){var t;Q.loadCSS({nonce:(t=this.$primevueConfig)===null||t===void 0||(t=t.csp)===null||t===void 0?void 0:t.nonce})}};function E(e){"@babel/helpers - typeof";return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(e)}function U(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),i.push.apply(i,n)}return i}function j(e){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?U(Object(i),!0).forEach(function(n){X(e,n,i[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):U(Object(i)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(i,n))})}return e}function X(e,t,i){return(t=ht(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function ht(e){var t=ut(e,"string");return E(t)=="symbol"?t:t+""}function ut(e,t){if(E(e)!="object"||!e)return e;var i=e[Symbol.toPrimitive];if(i!==void 0){var n=i.call(e,t);if(E(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ct={name:"VirtualScroller",extends:at,inheritAttrs:!1,emits:["update:numToleratedItems","scroll","scroll-index-change","lazy-load"],data:function(){var t=this.isBoth();return{first:t?{rows:0,cols:0}:0,last:t?{rows:0,cols:0}:0,page:t?{rows:0,cols:0}:0,numItemsInViewport:t?{rows:0,cols:0}:0,lastScrollPos:t?{top:0,left:0}:0,d_numToleratedItems:this.numToleratedItems,d_loading:this.loading,loaderArr:[],spacerStyle:{},contentStyle:{}}},element:null,content:null,lastScrollPos:null,scrollTimeout:null,resizeTimeout:null,defaultWidth:0,defaultHeight:0,defaultContentWidth:0,defaultContentHeight:0,isRangeChanged:!1,lazyLoadState:{},resizeListener:null,resizeObserver:null,initialized:!1,watch:{numToleratedItems:function(t){this.d_numToleratedItems=t},loading:function(t,i){this.lazy&&t!==i&&t!==this.d_loading&&(this.d_loading=t)},items:{handler:function(t,i){(!i||i.length!==(t||[]).length)&&(this.init(),this.calculateAutoSize())},deep:!0},itemSize:function(){this.init(),this.calculateAutoSize()},orientation:function(){this.lastScrollPos=this.isBoth()?{top:0,left:0}:0},scrollHeight:function(){this.init(),this.calculateAutoSize()},scrollWidth:function(){this.init(),this.calculateAutoSize()}},mounted:function(){this.viewInit(),this.lastScrollPos=this.isBoth()?{top:0,left:0}:0,this.lazyLoadState=this.lazyLoadState||{}},updated:function(){!this.initialized&&this.viewInit()},unmounted:function(){this.unbindResizeListener(),this.initialized=!1},methods:{viewInit:function(){G(this.element)&&(this.setContentEl(this.content),this.init(),this.calculateAutoSize(),this.defaultWidth=k(this.element),this.defaultHeight=N(this.element),this.defaultContentWidth=k(this.content),this.defaultContentHeight=N(this.content),this.initialized=!0),this.element&&this.bindResizeListener()},init:function(){this.disabled||(this.setSize(),this.calculateOptions(),this.setSpacerSize())},isVertical:function(){return this.orientation==="vertical"},isHorizontal:function(){return this.orientation==="horizontal"},isBoth:function(){return this.orientation==="both"},scrollTo:function(t){this.element&&this.element.scrollTo(t)},scrollToIndex:function(t){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"auto",r=this.isBoth(),s=this.isHorizontal(),o=r?t.every(function(C){return C>-1}):t>-1;if(o){var a=this.first,h=this.element,u=h.scrollTop,l=u===void 0?0:u,c=h.scrollLeft,d=c===void 0?0:c,p=this.calculateNumItems(),v=p.numToleratedItems,g=this.getContentPosition(),y=this.itemSize,b=function(){var w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,O=arguments.length>1?arguments[1]:void 0;return w<=O?0:w},S=function(w,O,V){return w*O+V},P=function(){var w=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return i.scrollTo({left:w,top:O,behavior:n})},m=r?{rows:0,cols:0}:0,H=!1,L=!1;r?(m={rows:b(t[0],v[0]),cols:b(t[1],v[1])},P(S(m.cols,y[1],g.left),S(m.rows,y[0],g.top)),L=this.lastScrollPos.top!==l||this.lastScrollPos.left!==d,H=m.rows!==a.rows||m.cols!==a.cols):(m=b(t,v),s?P(S(m,y,g.left),l):P(d,S(m,y,g.top)),L=this.lastScrollPos!==(s?d:l),H=m!==a),this.isRangeChanged=H,L&&(this.first=m)}},scrollInView:function(t,i){var n=this,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"auto";if(i){var s=this.isBoth(),o=this.isHorizontal(),a=s?t.every(function(y){return y>-1}):t>-1;if(a){var h=this.getRenderedRange(),u=h.first,l=h.viewport,c=function(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return n.scrollTo({left:b,top:S,behavior:r})},d=i==="to-start",p=i==="to-end";if(d){if(s)l.first.rows-u.rows>t[0]?c(l.first.cols*this.itemSize[1],(l.first.rows-1)*this.itemSize[0]):l.first.cols-u.cols>t[1]&&c((l.first.cols-1)*this.itemSize[1],l.first.rows*this.itemSize[0]);else if(l.first-u>t){var v=(l.first-1)*this.itemSize;o?c(v,0):c(0,v)}}else if(p){if(s)l.last.rows-u.rows<=t[0]+1?c(l.first.cols*this.itemSize[1],(l.first.rows+1)*this.itemSize[0]):l.last.cols-u.cols<=t[1]+1&&c((l.first.cols+1)*this.itemSize[1],l.first.rows*this.itemSize[0]);else if(l.last-u<=t+1){var g=(l.first+1)*this.itemSize;o?c(g,0):c(0,g)}}}}else this.scrollToIndex(t,r)},getRenderedRange:function(){var t=function(c,d){return Math.floor(c/(d||c))},i=this.first,n=0;if(this.element){var r=this.isBoth(),s=this.isHorizontal(),o=this.element,a=o.scrollTop,h=o.scrollLeft;if(r)i={rows:t(a,this.itemSize[0]),cols:t(h,this.itemSize[1])},n={rows:i.rows+this.numItemsInViewport.rows,cols:i.cols+this.numItemsInViewport.cols};else{var u=s?h:a;i=t(u,this.itemSize),n=i+this.numItemsInViewport}}return{first:this.first,last:this.last,viewport:{first:i,last:n}}},calculateNumItems:function(){var t=this.isBoth(),i=this.isHorizontal(),n=this.itemSize,r=this.getContentPosition(),s=this.element?this.element.offsetWidth-r.left:0,o=this.element?this.element.offsetHeight-r.top:0,a=function(d,p){return Math.ceil(d/(p||d))},h=function(d){return Math.ceil(d/2)},u=t?{rows:a(o,n[0]),cols:a(s,n[1])}:a(i?s:o,n),l=this.d_numToleratedItems||(t?[h(u.rows),h(u.cols)]:h(u));return{numItemsInViewport:u,numToleratedItems:l}},calculateOptions:function(){var t=this,i=this.isBoth(),n=this.first,r=this.calculateNumItems(),s=r.numItemsInViewport,o=r.numToleratedItems,a=function(l,c,d){var p=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;return t.getLast(l+c+(l<d?2:3)*d,p)},h=i?{rows:a(n.rows,s.rows,o[0]),cols:a(n.cols,s.cols,o[1],!0)}:a(n,s,o);this.last=h,this.numItemsInViewport=s,this.d_numToleratedItems=o,this.$emit("update:numToleratedItems",this.d_numToleratedItems),this.showLoader&&(this.loaderArr=i?Array.from({length:s.rows}).map(function(){return Array.from({length:s.cols})}):Array.from({length:s})),this.lazy&&Promise.resolve().then(function(){var u;t.lazyLoadState={first:t.step?i?{rows:0,cols:n.cols}:0:n,last:Math.min(t.step?t.step:h,((u=t.items)===null||u===void 0?void 0:u.length)||0)},t.$emit("lazy-load",t.lazyLoadState)})},calculateAutoSize:function(){var t=this;this.autoSize&&!this.d_loading&&Promise.resolve().then(function(){if(t.content){var i=t.isBoth(),n=t.isHorizontal(),r=t.isVertical();t.content.style.minHeight=t.content.style.minWidth="auto",t.content.style.position="relative",t.element.style.contain="none";var s=[k(t.element),N(t.element)],o=s[0],a=s[1];(i||n)&&(t.element.style.width=o<t.defaultWidth?o+"px":t.scrollWidth||t.defaultWidth+"px"),(i||r)&&(t.element.style.height=a<t.defaultHeight?a+"px":t.scrollHeight||t.defaultHeight+"px"),t.content.style.minHeight=t.content.style.minWidth="",t.content.style.position="",t.element.style.contain=""}})},getLast:function(){var t,i,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,r=arguments.length>1?arguments[1]:void 0;return this.items?Math.min(r?((t=this.columns||this.items[0])===null||t===void 0?void 0:t.length)||0:((i=this.items)===null||i===void 0?void 0:i.length)||0,n):0},getContentPosition:function(){if(this.content){var t=getComputedStyle(this.content),i=parseFloat(t.paddingLeft)+Math.max(parseFloat(t.left)||0,0),n=parseFloat(t.paddingRight)+Math.max(parseFloat(t.right)||0,0),r=parseFloat(t.paddingTop)+Math.max(parseFloat(t.top)||0,0),s=parseFloat(t.paddingBottom)+Math.max(parseFloat(t.bottom)||0,0);return{left:i,right:n,top:r,bottom:s,x:i+n,y:r+s}}return{left:0,right:0,top:0,bottom:0,x:0,y:0}},setSize:function(){var t=this;if(this.element){var i=this.isBoth(),n=this.isHorizontal(),r=this.element.parentElement,s=this.scrollWidth||"".concat(this.element.offsetWidth||r.offsetWidth,"px"),o=this.scrollHeight||"".concat(this.element.offsetHeight||r.offsetHeight,"px"),a=function(u,l){return t.element.style[u]=l};i||n?(a("height",o),a("width",s)):a("height",o)}},setSpacerSize:function(){var t=this,i=this.items;if(i){var n=this.isBoth(),r=this.isHorizontal(),s=this.getContentPosition(),o=function(h,u,l){var c=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;return t.spacerStyle=j(j({},t.spacerStyle),X({},"".concat(h),(u||[]).length*l+c+"px"))};n?(o("height",i,this.itemSize[0],s.y),o("width",this.columns||i[1],this.itemSize[1],s.x)):r?o("width",this.columns||i,this.itemSize,s.x):o("height",i,this.itemSize,s.y)}},setContentPosition:function(t){var i=this;if(this.content&&!this.appendOnly){var n=this.isBoth(),r=this.isHorizontal(),s=t?t.first:this.first,o=function(l,c){return l*c},a=function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return i.contentStyle=j(j({},i.contentStyle),{transform:"translate3d(".concat(l,"px, ").concat(c,"px, 0)")})};if(n)a(o(s.cols,this.itemSize[1]),o(s.rows,this.itemSize[0]));else{var h=o(s,this.itemSize);r?a(h,0):a(0,h)}}},onScrollPositionChange:function(t){var i=this,n=t.target,r=this.isBoth(),s=this.isHorizontal(),o=this.getContentPosition(),a=function(f,z){return f?f>z?f-z:f:0},h=function(f,z){return Math.floor(f/(z||f))},u=function(f,z,W,F,I,B){return f<=I?I:B?W-F-I:z+I-1},l=function(f,z,W,F,I,B,_,Y){if(f<=B)return 0;var D=Math.max(0,_?f<z?W:f-B:f>z?W:f-2*B),Z=i.getLast(D,Y);return D>Z?Z-I:D},c=function(f,z,W,F,I,B){var _=z+F+2*I;return f>=I&&(_+=I+1),i.getLast(_,B)},d=a(n.scrollTop,o.top),p=a(n.scrollLeft,o.left),v=r?{rows:0,cols:0}:0,g=this.last,y=!1,b=this.lastScrollPos;if(r){var S=this.lastScrollPos.top<=d,P=this.lastScrollPos.left<=p;if(!this.appendOnly||this.appendOnly&&(S||P)){var m={rows:h(d,this.itemSize[0]),cols:h(p,this.itemSize[1])},H={rows:u(m.rows,this.first.rows,this.last.rows,this.numItemsInViewport.rows,this.d_numToleratedItems[0],S),cols:u(m.cols,this.first.cols,this.last.cols,this.numItemsInViewport.cols,this.d_numToleratedItems[1],P)};v={rows:l(m.rows,H.rows,this.first.rows,this.last.rows,this.numItemsInViewport.rows,this.d_numToleratedItems[0],S),cols:l(m.cols,H.cols,this.first.cols,this.last.cols,this.numItemsInViewport.cols,this.d_numToleratedItems[1],P,!0)},g={rows:c(m.rows,v.rows,this.last.rows,this.numItemsInViewport.rows,this.d_numToleratedItems[0]),cols:c(m.cols,v.cols,this.last.cols,this.numItemsInViewport.cols,this.d_numToleratedItems[1],!0)},y=v.rows!==this.first.rows||g.rows!==this.last.rows||v.cols!==this.first.cols||g.cols!==this.last.cols||this.isRangeChanged,b={top:d,left:p}}}else{var L=s?p:d,C=this.lastScrollPos<=L;if(!this.appendOnly||this.appendOnly&&C){var w=h(L,this.itemSize),O=u(w,this.first,this.last,this.numItemsInViewport,this.d_numToleratedItems,C);v=l(w,O,this.first,this.last,this.numItemsInViewport,this.d_numToleratedItems,C),g=c(w,v,this.last,this.numItemsInViewport,this.d_numToleratedItems),y=v!==this.first||g!==this.last||this.isRangeChanged,b=L}}return{first:v,last:g,isRangeChanged:y,scrollPos:b}},onScrollChange:function(t){var i=this.onScrollPositionChange(t),n=i.first,r=i.last,s=i.isRangeChanged,o=i.scrollPos;if(s){var a={first:n,last:r};if(this.setContentPosition(a),this.first=n,this.last=r,this.lastScrollPos=o,this.$emit("scroll-index-change",a),this.lazy&&this.isPageChanged(n)){var h,u,l={first:this.step?Math.min(this.getPageByFirst(n)*this.step,(((h=this.items)===null||h===void 0?void 0:h.length)||0)-this.step):n,last:Math.min(this.step?(this.getPageByFirst(n)+1)*this.step:r,((u=this.items)===null||u===void 0?void 0:u.length)||0)},c=this.lazyLoadState.first!==l.first||this.lazyLoadState.last!==l.last;c&&this.$emit("lazy-load",l),this.lazyLoadState=l}}},onScroll:function(t){var i=this;if(this.$emit("scroll",t),this.delay){if(this.scrollTimeout&&clearTimeout(this.scrollTimeout),this.isPageChanged()){if(!this.d_loading&&this.showLoader){var n=this.onScrollPositionChange(t),r=n.isRangeChanged,s=r||(this.step?this.isPageChanged():!1);s&&(this.d_loading=!0)}this.scrollTimeout=setTimeout(function(){i.onScrollChange(t),i.d_loading&&i.showLoader&&(!i.lazy||i.loading===void 0)&&(i.d_loading=!1,i.page=i.getPageByFirst())},this.delay)}}else this.onScrollChange(t)},onResize:function(){var t=this;this.resizeTimeout&&clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){if(G(t.element)){var i=t.isBoth(),n=t.isVertical(),r=t.isHorizontal(),s=[k(t.element),N(t.element)],o=s[0],a=s[1],h=o!==t.defaultWidth,u=a!==t.defaultHeight,l=i?h||u:r?h:n?u:!1;l&&(t.d_numToleratedItems=t.numToleratedItems,t.defaultWidth=o,t.defaultHeight=a,t.defaultContentWidth=k(t.content),t.defaultContentHeight=N(t.content),t.init())}},this.resizeDelay)},bindResizeListener:function(){var t=this;this.resizeListener||(this.resizeListener=this.onResize.bind(this),window.addEventListener("resize",this.resizeListener),window.addEventListener("orientationchange",this.resizeListener),this.resizeObserver=new ResizeObserver(function(){t.onResize()}),this.resizeObserver.observe(this.element))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),window.removeEventListener("orientationchange",this.resizeListener),this.resizeListener=null),this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)},getOptions:function(t){var i=(this.items||[]).length,n=this.isBoth()?this.first.rows+t:this.first+t;return{index:n,count:i,first:n===0,last:n===i-1,even:n%2===0,odd:n%2!==0}},getLoaderOptions:function(t,i){var n=this.loaderArr.length;return j({index:t,count:n,first:t===0,last:t===n-1,even:t%2===0,odd:t%2!==0},i)},getPageByFirst:function(t){return Math.floor(((t??this.first)+this.d_numToleratedItems*4)/(this.step||1))},isPageChanged:function(t){return this.step&&!this.lazy?this.page!==this.getPageByFirst(t??this.first):!0},setContentEl:function(t){this.content=t||this.content||it(this.element,'[data-pc-section="content"]')},elementRef:function(t){this.element=t},contentRef:function(t){this.content=t}},computed:{containerClass:function(){return["p-virtualscroller",this.class,{"p-virtualscroller-inline":this.inline,"p-virtualscroller-both p-both-scroll":this.isBoth(),"p-virtualscroller-horizontal p-horizontal-scroll":this.isHorizontal()}]},contentClass:function(){return["p-virtualscroller-content",{"p-virtualscroller-loading":this.d_loading}]},loaderClass:function(){return["p-virtualscroller-loader",{"p-virtualscroller-loader-mask":!this.$slots.loader}]},loadedItems:function(){var t=this;return this.items&&!this.d_loading?this.isBoth()?this.items.slice(this.appendOnly?0:this.first.rows,this.last.rows).map(function(i){return t.columns?i:i.slice(t.appendOnly?0:t.first.cols,t.last.cols)}):this.isHorizontal()&&this.columns?this.items:this.items.slice(this.appendOnly?0:this.first,this.last):[]},loadedRows:function(){return this.d_loading?this.loaderDisabled?this.loaderArr:[]:this.loadedItems},loadedColumns:function(){if(this.columns){var t=this.isBoth(),i=this.isHorizontal();if(t||i)return this.d_loading&&this.loaderDisabled?t?this.loaderArr[0]:this.loaderArr:this.columns.slice(t?this.first.cols:this.first,t?this.last.cols:this.last)}return this.columns}},components:{SpinnerIcon:tt}},dt=["tabindex"];function mt(e,t,i,n,r,s){var o=st("SpinnerIcon");return e.disabled?(R(),T(q,{key:1},[A(e.$slots,"default"),A(e.$slots,"content",{items:e.items,rows:e.items,columns:s.loadedColumns})],64)):(R(),T("div",M({key:0,ref:s.elementRef,class:s.containerClass,tabindex:e.tabindex,style:e.style,onScroll:t[0]||(t[0]=function(){return s.onScroll&&s.onScroll.apply(s,arguments)})},e.ptmi("root")),[A(e.$slots,"content",{styleClass:s.contentClass,items:s.loadedItems,getItemOptions:s.getOptions,loading:r.d_loading,getLoaderOptions:s.getLoaderOptions,itemSize:e.itemSize,rows:s.loadedRows,columns:s.loadedColumns,contentRef:s.contentRef,spacerStyle:r.spacerStyle,contentStyle:r.contentStyle,vertical:s.isVertical(),horizontal:s.isHorizontal(),both:s.isBoth()},function(){return[nt("div",M({ref:s.contentRef,class:s.contentClass,style:r.contentStyle},e.ptm("content")),[(R(!0),T(q,null,J(s.loadedItems,function(a,h){return A(e.$slots,"item",{key:h,item:a,options:s.getOptions(h)})}),128))],16)]}),e.showSpacer?(R(),T("div",M({key:0,class:"p-virtualscroller-spacer",style:r.spacerStyle},e.ptm("spacer")),null,16)):K("",!0),!e.loaderDisabled&&e.showLoader&&r.d_loading?(R(),T("div",M({key:1,class:s.loaderClass},e.ptm("loader")),[e.$slots&&e.$slots.loader?(R(!0),T(q,{key:0},J(r.loaderArr,function(a,h){return A(e.$slots,"loader",{key:h,options:s.getLoaderOptions(h,s.isBoth()&&{numCols:e.d_numItemsInViewport.cols})})}),128)):K("",!0),A(e.$slots,"loadingicon",{},function(){return[rt(o,M({spin:"",class:"p-virtualscroller-loading-icon"},e.ptm("loadingIcon")),null,16)]})],16)):K("",!0)],16,dt))}ct.render=mt;export{ct as s};
