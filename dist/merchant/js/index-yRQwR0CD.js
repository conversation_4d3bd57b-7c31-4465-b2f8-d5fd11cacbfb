import{d as q,l as M,r as c,B as $,f as E,a as g,o as h,e as r,y as H,w as R,b as t,t as u,j as T,X as F,A as j}from"./index-BPwFJVSY.js";import{d as N,x as U,s as V,r as A,e as O}from"./integrations-C_ojvdY3.js";import _ from"./hubSpot-CKUB9Yf4.js";import{_ as w}from"./myob.vue_vue_type_script_setup_true_lang-BqJjK1XS.js";import S from"./quickBooks-wjQ04Z-X.js";import W from"./xreo-CNKiLEoa.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./BaseTag-dd68UHCy.js";import"./index-CBr4Z_s8.js";import"./index-DDQWKtix.js";import"./index-CQ6UwYfx.js";import"./invoice-B6mg1HKE.js";import"./date-D3KeWVvs.js";import"./dayjs.min-Bar1rHDB.js";const P={key:0,class:"integrations__right flex-1 flex flex-col gap-4"},Q={class:"text-left"},Y={class:"text-lg sm:text-xl text-indigo-600 mb-6"},G={class:"text-sm sm:text-base text-gray-700 mb-6"},J={class:"text-sm sm:text-base text-gray-700 mb-6"},K={class:"text-sm sm:text-base text-gray-700"},L={class:"text-sm sm:text-base text-gray-700 mt-1"},Z=["href"],ee=q({__name:"index",setup(se){const m=M(),s=c({xero:!1,xeroSync:!1,xeroRefresh:!1,quickbooks:!1,hubspot:!1,myob:!1}),n=c({email:"",file_name:"",id:0,merchant_id:"",status:0,sync_records:[],business_id:""}),f=c(!1),a=c({statusCode:"500",errorMessageForHumans:"BillBuddy has encountered an unexpected error. Something out of the ordinary has happened.",whatShouldIDo:"This error is usually our fault. Something has gone wrong with the system or we don't quite understand what you're trying to do. Your best bet is double check that the data you're providing us, or the action you're taking is correct and try again. If you receive the error again, please see below.",moreThanOnceMessage:"We log all errors that occur, but to help us out please send an email to support with what you're trying to do and we'll take a look for you.",supportEmail:"<EMAIL>"}),x=$(),p=async()=>{var o,e,i,d,y,b,v;try{s.value.xero=!0;const{code:D,data:l}=await N();if(D===0){const C={email:((o=l.xero)==null?void 0:o.email)||"",file_name:((e=l.xero)==null?void 0:e.file_name)||"",id:((i=l.xero)==null?void 0:i.id)||0,merchant_id:((d=l.xero)==null?void 0:d.merchant_id)||"",status:((y=l.xero)==null?void 0:y.status)||0,sync_records:((b=l.xero)==null?void 0:b.sync_records)||[],business_id:((v=l.xero)==null?void 0:v.business_id)||""};n.value=C}}finally{s.value.xero=!1}},k=async()=>{try{s.value.xero=!0;const{code:o}=await U({headers:{"Business-Id":n.value.business_id}});o===0&&(p(),await m.getUserInfo(),m.getMenus())}finally{s.value.xero=!1}},I=async()=>{s.value.xeroSync=!0;try{const{code:o}=await V({},{headers:{"Business-Id":n.value.business_id}});o===0&&window.$toast.add({severity:"success",summary:"Success",detail:"Sync Xero data successfully"})}finally{s.value.xeroSync=!1}},B=async()=>{s.value.xeroRefresh=!0;try{const{code:o}=await A({business_id:n.value.business_id});o===0&&window.$toast.add({severity:"success",summary:"Success",detail:"Xero status refreshed successfully"})}finally{s.value.xeroRefresh=!1}},X=()=>{m.showSelectBid(async o=>{const{code:e,data:i}=await O({redirect_uri:`${window.location.origin}${window.location.pathname}`},{headers:{"Business-Id":o}});e===0&&(window.location.href=i.url)})};return E(()=>{p(),x.query.status==="0"&&(f.value=!0,a.value.statusCode=x.query.code,a.value.errorMessageForHumans=x.query.message)}),(o,e)=>{const i=F;return h(),g("div",{class:j(["integrations__wrapper",{"not-connected":n.value.status===0}])},[r(W,{info:n.value,loading:s.value.xero,"sync-loading":s.value.xeroSync,"refresh-loading":s.value.xeroRefresh,onDisconnect:k,onSync:I,onRefresh:B,onConnect:X},null,8,["info","loading","sync-loading","refresh-loading"]),n.value.status!==0?(h(),g("div",P,[r(S,{loading:s.value.quickbooks},null,8,["loading"]),r(_,{loading:s.value.hubspot},null,8,["loading"]),r(w,{loading:s.value.myob},null,8,["loading"])])):(h(),g(H,{key:1},[r(S,{loading:s.value.quickbooks},null,8,["loading"]),r(_,{loading:s.value.hubspot},null,8,["loading"]),r(w,{loading:s.value.myob},null,8,["loading"])],64)),r(i,{visible:f.value,"onUpdate:visible":e[0]||(e[0]=d=>f.value=d),modal:"",closable:!0,draggable:!1,class:"w-11/12 md:w-3/5 lg:w-1/2 xl:w-2/5",pt:{root:{class:"p-0 border-none shadow-xl"},header:{class:"hidden"},content:{class:"p-6 sm:p-8 bg-sky-50 rounded-lg"}},onAfterHide:e[1]||(e[1]=()=>{o.$router.replace({query:{}})})},{default:R(()=>[t("div",Q,[e[3]||(e[3]=t("h1",{class:"text-3xl sm:text-4xl font-bold text-indigo-700 mb-2"}," System Error ",-1)),t("p",Y," Status Code: "+u(a.value.statusCode),1),e[4]||(e[4]=t("h2",{class:"text-base sm:text-lg font-semibold text-indigo-700 mb-2"}," Error Message for Humans: ",-1)),t("p",G,u(a.value.errorMessageForHumans),1),e[5]||(e[5]=t("h2",{class:"text-base sm:text-lg font-semibold text-indigo-700 mb-2"}," What should I do?: ",-1)),t("p",J,u(a.value.whatShouldIDo),1),e[6]||(e[6]=t("h2",{class:"text-base sm:text-lg font-semibold text-indigo-700 mb-2"}," This is happening more than just once: ",-1)),t("p",K,u(a.value.moreThanOnceMessage),1),t("p",L,[e[2]||(e[2]=T(" Support: ")),t("a",{href:`mailto:${a.value.supportEmail}`,class:"text-red-500 hover:text-red-700 underline font-medium"},u(a.value.supportEmail),9,Z)])])]),_:1},8,["visible"])],2)}}}),he=z(ee,[["__scopeId","data-v-0dbabe56"]]);export{he as default};
