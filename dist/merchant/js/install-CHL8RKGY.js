import{_ as P,as as Te,p as b,at as Me,au as ar,aa as De,av as Gt,aw as ir,o as nt,ax as k,ay as or,az as sr,aA as at,an as q,aB as ur,aC as lr,aD as Ut,aq as ut,aE as cr,E as Ot,aF as fr,aG as Ie,aH as Pe,R as it,a0 as Rt,aI as hr,aJ as vr,aK as pr,aL as gr,aM as dr,aN as mr,aO as yr,aP as xr,aQ as B,aR as _r,aS as br,aT as Ar,aU as lt,a2 as Sr,q as Lt,ag as z,N as W,aV as Oe,aW as wr,aX as Cr,aY as Tr,aZ as ct,a_ as Mr,a$ as Re,b0 as Dr,b1 as Ir,aj as J,v as Y,b2 as Pr,b3 as ot,C as Le,W as ke,b4 as j,b5 as Or,b6 as Rr,b7 as Lr,b8 as kr,$ as Er,b9 as Br,ba as Ht,bb as Yt,g as _t,bc as Vr,Z as Xt,bd as Nr,al as zr,be as Wr,bf as Zr,j as bt,bg as Fr,f as Gr,h as Ur,bh as $t,ae as Hr,x as jt,a4 as Yr,bi as Ee,bj as Xr,bk as $r,u as jr}from"./installCanvasRenderer-p5Q0jy4r.js";var At=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return Te(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,r,a){var i=this.coordinateSystem;if(i&&i.clampData){var o=i.clampData(t),s=i.dataToPoint(o);if(a)b(i.getAxes(),function(h,p){if(h.type==="category"&&r!=null){var v=h.getTicksCoords(),g=h.getTickModel().get("alignWithLabel"),y=o[p],x=r[p]==="x1"||r[p]==="y1";if(x&&!g&&(y+=1),v.length<2)return;if(v.length===2){s[p]=h.toGlobalCoord(h.getExtent()[x?1:0]);return}for(var d=void 0,_=void 0,m=1,A=0;A<v.length;A++){var S=v[A].coord,w=A===v.length-1?v[A-1].tickValue+m:v[A].tickValue;if(w===y){_=S;break}else if(w<y)d=S;else if(d!=null&&w>y){_=(S+d)/2;break}A===1&&(m=w-v[0].tickValue)}_==null&&(d?d&&(_=v[v.length-1].coord):_=v[0].coord),s[p]=h.toGlobalCoord(_)}});else{var u=this.getData(),c=u.getLayout("offset"),l=u.getLayout("size"),f=i.getBaseAxis().isHorizontal()?0:1;s[f]+=c+l/2}return s}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(Me);Me.registerClass(At);var Kr=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(){return Te(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),r=this.get("largeThreshold");return r>t&&(t=r),t},e.prototype.brushSelector=function(t,r,a){return a.rect(r.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=ar(At.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),e}(At),qr=function(){function n(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return n}(),Kt=function(n){P(e,n);function e(t){var r=n.call(this,t)||this;return r.type="sausage",r}return e.prototype.getDefaultShape=function(){return new qr},e.prototype.buildPath=function(t,r){var a=r.cx,i=r.cy,o=Math.max(r.r0||0,0),s=Math.max(r.r,0),u=(s-o)*.5,c=o+u,l=r.startAngle,f=r.endAngle,h=r.clockwise,p=Math.PI*2,v=h?f-l<p:l-f<p;v||(l=f-(h?p:-p));var g=Math.cos(l),y=Math.sin(l),x=Math.cos(f),d=Math.sin(f);v?(t.moveTo(g*o+a,y*o+i),t.arc(g*c+a,y*c+i,u,-Math.PI+l,l,!h)):t.moveTo(g*s+a,y*s+i),t.arc(a,i,s,l,f,!h),t.arc(x*c+a,d*c+i,u,f-Math.PI*2,f-Math.PI,!h),o!==0&&t.arc(a,i,o,f,l,h)},e}(De);function Jr(n,e){e=e||{};var t=e.isRoundCap;return function(r,a,i){var o=a.position;if(!o||o instanceof Array)return Gt(r,a,i);var s=n(o),u=a.distance!=null?a.distance:5,c=this.shape,l=c.cx,f=c.cy,h=c.r,p=c.r0,v=(h+p)/2,g=c.startAngle,y=c.endAngle,x=(g+y)/2,d=t?Math.abs(h-p)/2:0,_=Math.cos,m=Math.sin,A=l+h*_(g),S=f+h*m(g),w="left",T="top";switch(s){case"startArc":A=l+(p-u)*_(x),S=f+(p-u)*m(x),w="center",T="top";break;case"insideStartArc":A=l+(p+u)*_(x),S=f+(p+u)*m(x),w="center",T="bottom";break;case"startAngle":A=l+v*_(g)+tt(g,u+d,!1),S=f+v*m(g)+et(g,u+d,!1),w="right",T="middle";break;case"insideStartAngle":A=l+v*_(g)+tt(g,-u+d,!1),S=f+v*m(g)+et(g,-u+d,!1),w="left",T="middle";break;case"middle":A=l+v*_(x),S=f+v*m(x),w="center",T="middle";break;case"endArc":A=l+(h+u)*_(x),S=f+(h+u)*m(x),w="center",T="bottom";break;case"insideEndArc":A=l+(h-u)*_(x),S=f+(h-u)*m(x),w="center",T="top";break;case"endAngle":A=l+v*_(y)+tt(y,u+d,!0),S=f+v*m(y)+et(y,u+d,!0),w="left",T="middle";break;case"insideEndAngle":A=l+v*_(y)+tt(y,-u+d,!0),S=f+v*m(y)+et(y,-u+d,!0),w="right",T="middle";break;default:return Gt(r,a,i)}return r=r||{},r.x=A,r.y=S,r.align=w,r.verticalAlign=T,r}}function Qr(n,e,t,r){if(ir(r)){n.setTextConfig({rotation:r});return}else if(nt(e)){n.setTextConfig({rotation:0});return}var a=n.shape,i=a.clockwise?a.startAngle:a.endAngle,o=a.clockwise?a.endAngle:a.startAngle,s=(i+o)/2,u,c=t(e);switch(c){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":u=s;break;case"startAngle":case"insideStartAngle":u=i;break;case"endAngle":case"insideEndAngle":u=o;break;default:n.setTextConfig({rotation:0});return}var l=Math.PI*1.5-u;c==="middle"&&l>Math.PI/2&&l<Math.PI*1.5&&(l-=Math.PI),n.setTextConfig({rotation:l})}function tt(n,e,t){return e*Math.sin(n)*(t?-1:1)}function et(n,e,t){return e*Math.cos(n)*(t?1:-1)}function tn(n,e,t){var r=n.get("borderRadius");if(r==null)return t?{cornerRadius:0}:null;nt(r)||(r=[r,r,r,r]);var a=Math.abs(e.r||0-e.r0||0);return{cornerRadius:k(r,function(i){return or(i,a)})}}var gt=Math.max,dt=Math.min;function en(n,e){var t=n.getArea&&n.getArea();if(Ie(n,"cartesian2d")){var r=n.getBaseAxis();if(r.type!=="category"||!r.onBand){var a=e.getLayout("bandWidth");r.isHorizontal()?(t.x-=a,t.width+=a*2):(t.y-=a,t.height+=a*2)}}return t}var rn=function(n){P(e,n);function e(){var t=n.call(this)||this;return t.type=e.type,t._isFirstFrame=!0,t}return e.prototype.render=function(t,r,a,i){this._model=t,this._removeOnRenderedListener(a),this._updateDrawMode(t);var o=t.get("coordinateSystem");(o==="cartesian2d"||o==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,r,a):this._renderNormal(t,r,a,i))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,r){this._progressiveEls=[],this._incrementalRenderLarge(t,r)},e.prototype.eachRendered=function(t){sr(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t,r,a,i){var o=this.group,s=t.getData(),u=this._data,c=t.coordinateSystem,l=c.getBaseAxis(),f;c.type==="cartesian2d"?f=l.isHorizontal():c.type==="polar"&&(f=l.dim==="angle");var h=t.isAnimationEnabled()?t:null,p=nn(t,c);p&&this._enableRealtimeSort(p,s,a);var v=t.get("clip",!0)||p,g=en(c,s);o.removeClipPath();var y=t.get("roundCap",!0),x=t.get("showBackground",!0),d=t.getModel("backgroundStyle"),_=d.get("borderRadius")||0,m=[],A=this._backgroundEls,S=i&&i.isInitSort,w=i&&i.type==="changeAxisOrder";function T(C){var O=rt[c.type](s,C),D=fn(c,f,O);return D.useStyle(d.getItemStyle()),c.type==="cartesian2d"?D.setShape("r",_):D.setShape("cornerRadius",_),m[C]=D,D}s.diff(u).add(function(C){var O=s.getItemModel(C),D=rt[c.type](s,C,O);if(x&&T(C),!(!s.hasValue(C)||!ee[c.type](D))){var E=!1;v&&(E=qt[c.type](g,D));var R=Jt[c.type](t,s,C,D,f,h,l.model,!1,y);p&&(R.forceLabelAnimation=!0),re(R,s,C,O,D,t,f,c.type==="polar"),S?R.attr({shape:D}):p?Qt(p,h,R,D,C,f,!1,!1):at(R,{shape:D},t,C),s.setItemGraphicEl(C,R),o.add(R),R.ignore=E}}).update(function(C,O){var D=s.getItemModel(C),E=rt[c.type](s,C,D);if(x){var R=void 0;A.length===0?R=T(O):(R=A[O],R.useStyle(d.getItemStyle()),c.type==="cartesian2d"?R.setShape("r",_):R.setShape("cornerRadius",_),m[C]=R);var rr=rt[c.type](s,C),nr=Ve(f,rr,c);q(R,{shape:nr},h,C)}var L=u.getItemGraphicEl(O);if(!s.hasValue(C)||!ee[c.type](E)){o.remove(L);return}var vt=!1;if(v&&(vt=qt[c.type](g,E),vt&&o.remove(L)),L?ur(L):L=Jt[c.type](t,s,C,E,f,h,l.model,!!L,y),p&&(L.forceLabelAnimation=!0),w){var Ft=L.getTextContent();if(Ft){var pt=lr(Ft);pt.prevValue!=null&&(pt.prevValue=pt.value)}}else re(L,s,C,D,E,t,f,c.type==="polar");S?L.attr({shape:E}):p?Qt(p,h,L,E,C,f,!0,w):q(L,{shape:E},t,C,null),s.setItemGraphicEl(C,L),L.ignore=vt,o.add(L)}).remove(function(C){var O=u.getItemGraphicEl(C);O&&Ut(O,t,C)}).execute();var M=this._backgroundGroup||(this._backgroundGroup=new ut);M.removeAll();for(var I=0;I<m.length;++I)M.add(m[I]);o.add(M),this._backgroundEls=m,this._data=s},e.prototype._renderLarge=function(t,r,a){this._clear(),ae(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,r){this._removeBackground(),ae(r,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var r=t.get("clip",!0)&&cr(t.coordinateSystem,!1,t),a=this.group;r?a.setClipPath(r):a.removeClipPath()},e.prototype._enableRealtimeSort=function(t,r,a){var i=this;if(r.count()){var o=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(r,t,a),this._isFirstFrame=!1;else{var s=function(u){var c=r.getItemGraphicEl(u),l=c&&c.shape;return l&&Math.abs(o.isHorizontal()?l.height:l.width)||0};this._onRendered=function(){i._updateSortWithinSameData(r,s,o,a)},a.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,r,a){var i=[];return t.each(t.mapDimension(r.dim),function(o,s){var u=a(s);u=u??NaN,i.push({dataIndex:s,mappedValue:u,ordinalNumber:o})}),i.sort(function(o,s){return s.mappedValue-o.mappedValue}),{ordinalNumbers:k(i,function(o){return o.ordinalNumber})}},e.prototype._isOrderChangedWithinSameData=function(t,r,a){for(var i=a.scale,o=t.mapDimension(a.dim),s=Number.MAX_VALUE,u=0,c=i.getOrdinalMeta().categories.length;u<c;++u){var l=t.rawIndexOf(o,i.getRawOrdinalNumber(u)),f=l<0?Number.MIN_VALUE:r(t.indexOfRawIndex(l));if(f>s)return!0;s=f}return!1},e.prototype._isOrderDifferentInView=function(t,r){for(var a=r.scale,i=a.getExtent(),o=Math.max(0,i[0]),s=Math.min(i[1],a.getOrdinalMeta().categories.length-1);o<=s;++o)if(t.ordinalNumbers[o]!==a.getRawOrdinalNumber(o))return!0},e.prototype._updateSortWithinSameData=function(t,r,a,i){if(this._isOrderChangedWithinSameData(t,r,a)){var o=this._dataSort(t,a,r);this._isOrderDifferentInView(o,a)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:a.dim+"Axis",axisId:a.index,sortInfo:o}))}},e.prototype._dispatchInitSort=function(t,r,a){var i=r.baseAxis,o=this._dataSort(t,i,function(s){return t.get(t.mapDimension(r.otherAxis.dim),s)});a.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:o})},e.prototype.remove=function(t,r){this._clear(this._model),this._removeOnRenderedListener(r)},e.prototype.dispose=function(t,r){this._removeOnRenderedListener(r)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var r=this.group,a=this._data;t&&t.isAnimationEnabled()&&a&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],a.eachItemGraphicEl(function(i){Ut(i,t,Ot(i).dataIndex)})):r.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(fr),qt={cartesian2d:function(n,e){var t=e.width<0?-1:1,r=e.height<0?-1:1;t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height);var a=n.x+n.width,i=n.y+n.height,o=gt(e.x,n.x),s=dt(e.x+e.width,a),u=gt(e.y,n.y),c=dt(e.y+e.height,i),l=s<o,f=c<u;return e.x=l&&o>a?s:o,e.y=f&&u>i?c:u,e.width=l?0:s-o,e.height=f?0:c-u,t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height),l||f},polar:function(n,e){var t=e.r0<=e.r?1:-1;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}var a=dt(e.r,n.r),i=gt(e.r0,n.r0);e.r=a,e.r0=i;var o=a-i<0;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}return o}},Jt={cartesian2d:function(n,e,t,r,a,i,o,s,u){var c=new it({shape:Rt({},r),z2:1});if(c.__dataIndex=t,c.name="item",i){var l=c.shape,f=a?"height":"width";l[f]=0}return c},polar:function(n,e,t,r,a,i,o,s,u){var c=!a&&u?Kt:Pe,l=new c({shape:r,z2:1});l.name="item";var f=Be(a);if(l.calculateTextPosition=Jr(f,{isRoundCap:c===Kt}),i){var h=l.shape,p=a?"r":"endAngle",v={};h[p]=a?r.r0:r.startAngle,v[p]=r[p],(s?q:at)(l,{shape:v},i)}return l}};function nn(n,e){var t=n.get("realtimeSort",!0),r=e.getBaseAxis();if(t&&r.type==="category"&&e.type==="cartesian2d")return{baseAxis:r,otherAxis:e.getOtherAxis(r)}}function Qt(n,e,t,r,a,i,o,s){var u,c;i?(c={x:r.x,width:r.width},u={y:r.y,height:r.height}):(c={y:r.y,height:r.height},u={x:r.x,width:r.width}),s||(o?q:at)(t,{shape:u},e,a,null);var l=e?n.baseAxis.model:null;(o?q:at)(t,{shape:c},l,a)}function te(n,e){for(var t=0;t<e.length;t++)if(!isFinite(n[e[t]]))return!0;return!1}var an=["x","y","width","height"],on=["cx","cy","r","startAngle","endAngle"],ee={cartesian2d:function(n){return!te(n,an)},polar:function(n){return!te(n,on)}},rt={cartesian2d:function(n,e,t){var r=n.getItemLayout(e),a=t?un(t,r):0,i=r.width>0?1:-1,o=r.height>0?1:-1;return{x:r.x+i*a/2,y:r.y+o*a/2,width:r.width-i*a,height:r.height-o*a}},polar:function(n,e,t){var r=n.getItemLayout(e);return{cx:r.cx,cy:r.cy,r0:r.r0,r:r.r,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}}};function sn(n){return n.startAngle!=null&&n.endAngle!=null&&n.startAngle===n.endAngle}function Be(n){return function(e){var t=e?"Arc":"Angle";return function(r){switch(r){case"start":case"insideStart":case"end":case"insideEnd":return r+t;default:return r}}}(n)}function re(n,e,t,r,a,i,o,s){var u=e.getItemVisual(t,"style");if(s){if(!i.get("roundCap")){var l=n.shape,f=tn(r.getModel("itemStyle"),l,!0);Rt(l,f),n.setShape(l)}}else{var c=r.get(["itemStyle","borderRadius"])||0;n.setShape("r",c)}n.useStyle(u);var h=r.getShallow("cursor");h&&n.attr("cursor",h);var p=s?o?a.r>=a.r0?"endArc":"startArc":a.endAngle>=a.startAngle?"endAngle":"startAngle":o?a.height>=0?"bottom":"top":a.width>=0?"right":"left",v=hr(r);vr(n,v,{labelFetcher:i,labelDataIndex:t,defaultText:pr(i.getData(),t),inheritColor:u.fill,defaultOpacity:u.opacity,defaultOutsidePosition:p});var g=n.getTextContent();if(s&&g){var y=r.get(["label","position"]);n.textConfig.inside=y==="middle"?!0:null,Qr(n,y==="outside"?p:y,Be(o),r.get(["label","rotate"]))}gr(g,v,i.getRawValue(t),function(d){return xr(e,d)});var x=r.getModel(["emphasis"]);dr(n,x.get("focus"),x.get("blurScope"),x.get("disabled")),mr(n,r),sn(a)&&(n.style.fill="none",n.style.stroke="none",b(n.states,function(d){d.style&&(d.style.fill=d.style.stroke="none")}))}function un(n,e){var t=n.get(["itemStyle","borderColor"]);if(!t||t==="none")return 0;var r=n.get(["itemStyle","borderWidth"])||0,a=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),i=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(r,a,i)}var ln=function(){function n(){}return n}(),ne=function(n){P(e,n);function e(t){var r=n.call(this,t)||this;return r.type="largeBar",r}return e.prototype.getDefaultShape=function(){return new ln},e.prototype.buildPath=function(t,r){for(var a=r.points,i=this.baseDimIdx,o=1-this.baseDimIdx,s=[],u=[],c=this.barWidth,l=0;l<a.length;l+=3)u[i]=c,u[o]=a[l+2],s[i]=a[l+i],s[o]=a[l+o],t.rect(s[0],s[1],u[0],u[1])},e}(De);function ae(n,e,t,r){var a=n.getData(),i=a.getLayout("valueAxisHorizontal")?1:0,o=a.getLayout("largeDataIndices"),s=a.getLayout("size"),u=n.getModel("backgroundStyle"),c=a.getLayout("largeBackgroundPoints");if(c){var l=new ne({shape:{points:c},incremental:!!r,silent:!0,z2:0});l.baseDimIdx=i,l.largeDataIndices=o,l.barWidth=s,l.useStyle(u.getItemStyle()),e.add(l),t&&t.push(l)}var f=new ne({shape:{points:a.getLayout("largePoints")},incremental:!!r,ignoreCoarsePointer:!0,z2:1});f.baseDimIdx=i,f.largeDataIndices=o,f.barWidth=s,e.add(f),f.useStyle(a.getVisual("style")),f.style.stroke=null,Ot(f).seriesIndex=n.seriesIndex,n.get("silent")||(f.on("mousedown",ie),f.on("mousemove",ie)),t&&t.push(f)}var ie=yr(function(n){var e=this,t=cn(e,n.offsetX,n.offsetY);Ot(e).dataIndex=t>=0?t:null},30,!1);function cn(n,e,t){for(var r=n.baseDimIdx,a=1-r,i=n.shape.points,o=n.largeDataIndices,s=[],u=[],c=n.barWidth,l=0,f=i.length/3;l<f;l++){var h=l*3;if(u[r]=c,u[a]=i[h+2],s[r]=i[h+r],s[a]=i[h+a],u[a]<0&&(s[a]+=u[a],u[a]=-u[a]),e>=s[0]&&e<=s[0]+u[0]&&t>=s[1]&&t<=s[1]+u[1])return o[l]}return-1}function Ve(n,e,t){if(Ie(t,"cartesian2d")){var r=e,a=t.getArea();return{x:n?r.x:a.x,y:n?a.y:r.y,width:n?r.width:a.width,height:n?a.height:r.height}}else{var a=t.getArea(),i=e;return{cx:a.cx,cy:a.cy,r0:n?a.r0:i.r0,r:n?a.r:i.r,startAngle:n?i.startAngle:0,endAngle:n?i.endAngle:Math.PI*2}}}function fn(n,e,t){var r=n.type==="polar"?Pe:it;return new r({shape:Ve(e,t,n),silent:!0,z2:0})}function wa(n){n.registerChartView(rn),n.registerSeriesModel(Kr),n.registerLayout(n.PRIORITY.VISUAL.LAYOUT,B(Ar,"bar")),n.registerLayout(n.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,_r("bar")),n.registerProcessor(n.PRIORITY.PROCESSOR.STATISTIC,br("bar")),n.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var r=e.componentType||"series";t.eachComponent({mainType:r,query:e},function(a){e.sortInfo&&a.axis.setCategorySortInfo(e.sortInfo)})})}var oe="\0_ec_interaction_mutex";function hn(n,e,t){var r=Ne(n);r[e]=t}function vn(n,e,t){var r=Ne(n),a=r[e];a===t&&(r[e]=null)}function Ne(n){return n[oe]||(n[oe]={})}lt({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},Sr);var pn={axisPointer:1,tooltip:1,brush:1};function gn(n,e,t){var r=e.getComponentByElement(n.topTarget),a=r&&r.coordinateSystem;return r&&r!==t&&!pn.hasOwnProperty(r.mainType)&&a&&a.model!==t}function ze(n,e,t,r,a,i){n=n||0;var o=t[1]-t[0];if(a!=null&&(a=G(a,[0,o])),i!=null&&(i=Math.max(i,a??0)),r==="all"){var s=Math.abs(e[1]-e[0]);s=G(s,[0,o]),a=i=G(s,[a,i]),r=0}e[0]=G(e[0],t),e[1]=G(e[1],t);var u=mt(e,r);e[r]+=n;var c=a||0,l=t.slice();u.sign<0?l[0]+=c:l[1]-=c,e[r]=G(e[r],l);var f;return f=mt(e,r),a!=null&&(f.sign!==u.sign||f.span<a)&&(e[1-r]=e[r]+u.sign*a),f=mt(e,r),i!=null&&f.span>i&&(e[1-r]=e[r]+f.sign*i),e}function mt(n,e){var t=n[e]-n[1-e];return{span:Math.abs(t),sign:t>0?-1:t<0?1:e?-1:1}}function G(n,e){return Math.min(e[1]!=null?e[1]:1/0,Math.max(e[0]!=null?e[0]:-1/0,n))}var Z=!0,Q=Math.min,X=Math.max,dn=Math.pow,mn=1e4,yn=6,xn=6,se="globalPan",_n={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},bn={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},ue={brushStyle:{lineWidth:2,stroke:"rgba(210,219,238,0.3)",fill:"#D2DBEE"},transformable:!0,brushMode:"single",removeOnClick:!1},An=0,Sn=function(n){P(e,n);function e(t){var r=n.call(this)||this;return r._track=[],r._covers=[],r._handlers={},r._zr=t,r.group=new ut,r._uid="brushController_"+An++,b(Pn,function(a,i){this._handlers[i]=Lt(a,this)},r),r}return e.prototype.enableBrush=function(t){return this._brushType&&this._doDisableBrush(),t.brushType&&this._doEnableBrush(t),this},e.prototype._doEnableBrush=function(t){var r=this._zr;this._enableGlobalPan||hn(r,se,this._uid),b(this._handlers,function(a,i){r.on(i,a)}),this._brushType=t.brushType,this._brushOption=z(W(ue),t,!0)},e.prototype._doDisableBrush=function(){var t=this._zr;vn(t,se,this._uid),b(this._handlers,function(r,a){t.off(a,r)}),this._brushType=this._brushOption=null},e.prototype.setPanels=function(t){if(t&&t.length){var r=this._panels={};b(t,function(a){r[a.panelId]=W(a)})}else this._panels=null;return this},e.prototype.mount=function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var r=this.group;return this._zr.add(r),r.attr({x:t.x||0,y:t.y||0,rotation:t.rotation||0,scaleX:t.scaleX||1,scaleY:t.scaleY||1}),this._transform=r.getLocalTransform(),this},e.prototype.updateCovers=function(t){t=k(t,function(h){return z(W(ue),h,!0)});var r="\0-brush-index-",a=this._covers,i=this._covers=[],o=this,s=this._creatingCover;return new Oe(a,t,c,u).add(l).update(l).remove(f).execute(),this;function u(h,p){return(h.id!=null?h.id:r+p)+"-"+h.brushType}function c(h,p){return u(h.__brushOption,p)}function l(h,p){var v=t[h];if(p!=null&&a[p]===s)i[h]=a[p];else{var g=i[h]=p!=null?(a[p].__brushOption=v,a[p]):Ze(o,We(o,v));kt(o,g)}}function f(h){a[h]!==s&&o.group.remove(a[h])}},e.prototype.unmount=function(){return this.enableBrush(!1),St(this),this._zr.remove(this.group),this},e.prototype.dispose=function(){this.unmount(),this.off()},e}(wr);function We(n,e){var t=ft[e.brushType].createCover(n,e);return t.__brushOption=e,Ge(t,e),n.group.add(t),t}function Ze(n,e){var t=Et(e);return t.endCreating&&(t.endCreating(n,e),Ge(e,e.__brushOption)),e}function Fe(n,e){var t=e.__brushOption;Et(e).updateCoverShape(n,e,t.range,t)}function Ge(n,e){var t=e.z;t==null&&(t=mn),n.traverse(function(r){r.z=t,r.z2=t})}function kt(n,e){Et(e).updateCommon(n,e),Fe(n,e)}function Et(n){return ft[n.__brushOption.brushType]}function Bt(n,e,t){var r=n._panels;if(!r)return Z;var a,i=n._transform;return b(r,function(o){o.isTargetByCursor(e,t,i)&&(a=o)}),a}function Ue(n,e){var t=n._panels;if(!t)return Z;var r=e.__brushOption.panelId;return r!=null?t[r]:Z}function St(n){var e=n._covers,t=e.length;return b(e,function(r){n.group.remove(r)},n),e.length=0,!!t}function F(n,e){var t=k(n._covers,function(r){var a=r.__brushOption,i=W(a.range);return{brushType:a.brushType,panelId:a.panelId,range:i}});n.trigger("brush",{areas:t,isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function wn(n){var e=n._track;if(!e.length)return!1;var t=e[e.length-1],r=e[0],a=t[0]-r[0],i=t[1]-r[1],o=dn(a*a+i*i,.5);return o>yn}function He(n){var e=n.length-1;return e<0&&(e=0),[n[0],n[e]]}function Ye(n,e,t,r){var a=new ut;return a.add(new it({name:"main",style:Vt(t),silent:!0,draggable:!0,cursor:"move",drift:B(le,n,e,a,["n","s","w","e"]),ondragend:B(F,e,{isEnd:!0})})),b(r,function(i){a.add(new it({name:i.join(""),style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:B(le,n,e,a,i),ondragend:B(F,e,{isEnd:!0})}))}),a}function Xe(n,e,t,r){var a=r.brushStyle.lineWidth||0,i=X(a,xn),o=t[0][0],s=t[1][0],u=o-a/2,c=s-a/2,l=t[0][1],f=t[1][1],h=l-i+a/2,p=f-i+a/2,v=l-o,g=f-s,y=v+a,x=g+a;N(n,e,"main",o,s,v,g),r.transformable&&(N(n,e,"w",u,c,i,x),N(n,e,"e",h,c,i,x),N(n,e,"n",u,c,y,i),N(n,e,"s",u,p,y,i),N(n,e,"nw",u,c,i,i),N(n,e,"ne",h,c,i,i),N(n,e,"sw",u,p,i,i),N(n,e,"se",h,p,i,i))}function wt(n,e){var t=e.__brushOption,r=t.transformable,a=e.childAt(0);a.useStyle(Vt(t)),a.attr({silent:!r,cursor:r?"move":"default"}),b([["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]],function(i){var o=e.childOfName(i.join("")),s=i.length===1?Ct(n,i[0]):Tn(n,i);o&&o.attr({silent:!r,invisible:!r,cursor:r?bn[s]+"-resize":null})})}function N(n,e,t,r,a,i,o){var s=e.childOfName(t);s&&s.setShape(Dn(Nt(n,e,[[r,a],[r+i,a+o]])))}function Vt(n){return ct({strokeNoScale:!0},n.brushStyle)}function $e(n,e,t,r){var a=[Q(n,t),Q(e,r)],i=[X(n,t),X(e,r)];return[[a[0],i[0]],[a[1],i[1]]]}function Cn(n){return Re(n.group)}function Ct(n,e){var t={w:"left",e:"right",n:"top",s:"bottom"},r={left:"w",right:"e",top:"n",bottom:"s"},a=Mr(t[e],Cn(n));return r[a]}function Tn(n,e){var t=[Ct(n,e[0]),Ct(n,e[1])];return(t[0]==="e"||t[0]==="w")&&t.reverse(),t.join("")}function le(n,e,t,r,a,i){var o=t.__brushOption,s=n.toRectRange(o.range),u=je(e,a,i);b(r,function(c){var l=_n[c];s[l[0]][l[1]]+=u[l[0]]}),o.range=n.fromRectRange($e(s[0][0],s[1][0],s[0][1],s[1][1])),kt(e,t),F(e,{isEnd:!1})}function Mn(n,e,t,r){var a=e.__brushOption.range,i=je(n,t,r);b(a,function(o){o[0]+=i[0],o[1]+=i[1]}),kt(n,e),F(n,{isEnd:!1})}function je(n,e,t){var r=n.group,a=r.transformCoordToLocal(e,t),i=r.transformCoordToLocal(0,0);return[a[0]-i[0],a[1]-i[1]]}function Nt(n,e,t){var r=Ue(n,e);return r&&r!==Z?r.clipPath(t,n._transform):W(t)}function Dn(n){var e=Q(n[0][0],n[1][0]),t=Q(n[0][1],n[1][1]),r=X(n[0][0],n[1][0]),a=X(n[0][1],n[1][1]);return{x:e,y:t,width:r-e,height:a-t}}function In(n,e,t){if(!(!n._brushType||On(n,e.offsetX,e.offsetY))){var r=n._zr,a=n._covers,i=Bt(n,e,t);if(!n._dragging)for(var o=0;o<a.length;o++){var s=a[o].__brushOption;if(i&&(i===Z||s.panelId===i.panelId)&&ft[s.brushType].contain(a[o],t[0],t[1]))return}i&&r.setCursorStyle("crosshair")}}function Tt(n){var e=n.event;e.preventDefault&&e.preventDefault()}function Mt(n,e,t){return n.childOfName("main").contain(e,t)}function Ke(n,e,t,r){var a=n._creatingCover,i=n._creatingPanel,o=n._brushOption,s;if(n._track.push(t.slice()),wn(n)||a){if(i&&!a){o.brushMode==="single"&&St(n);var u=W(o);u.brushType=ce(u.brushType,i),u.panelId=i===Z?null:i.panelId,a=n._creatingCover=We(n,u),n._covers.push(a)}if(a){var c=ft[ce(n._brushType,i)],l=a.__brushOption;l.range=c.getCreatingRange(Nt(n,a,n._track)),r&&(Ze(n,a),c.updateCommon(n,a)),Fe(n,a),s={isEnd:r}}}else r&&o.brushMode==="single"&&o.removeOnClick&&Bt(n,e,t)&&St(n)&&(s={isEnd:r,removeOnClick:!0});return s}function ce(n,e){return n==="auto"?e.defaultBrushType:n}var Pn={mousedown:function(n){if(this._dragging)fe(this,n);else if(!n.target||!n.target.draggable){Tt(n);var e=this.group.transformCoordToLocal(n.offsetX,n.offsetY);this._creatingCover=null;var t=this._creatingPanel=Bt(this,n,e);t&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(n){var e=n.offsetX,t=n.offsetY,r=this.group.transformCoordToLocal(e,t);if(In(this,n,r),this._dragging){Tt(n);var a=Ke(this,n,r,!1);a&&F(this,a)}},mouseup:function(n){fe(this,n)}};function fe(n,e){if(n._dragging){Tt(e);var t=e.offsetX,r=e.offsetY,a=n.group.transformCoordToLocal(t,r),i=Ke(n,e,a,!0);n._dragging=!1,n._track=[],n._creatingCover=null,i&&F(n,i)}}function On(n,e,t){var r=n._zr;return e<0||e>r.getWidth()||t<0||t>r.getHeight()}var ft={lineX:he(0),lineY:he(1),rect:{createCover:function(n,e){function t(r){return r}return Ye({toRectRange:t,fromRectRange:t},n,e,[["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]])},getCreatingRange:function(n){var e=He(n);return $e(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(n,e,t,r){Xe(n,e,t,r)},updateCommon:wt,contain:Mt},polygon:{createCover:function(n,e){var t=new ut;return t.add(new Tr({name:"main",style:Vt(e),silent:!0})),t},getCreatingRange:function(n){return n},endCreating:function(n,e){e.remove(e.childAt(0)),e.add(new Cr({name:"main",draggable:!0,drift:B(Mn,n,e),ondragend:B(F,n,{isEnd:!0})}))},updateCoverShape:function(n,e,t,r){e.childAt(0).setShape({points:Nt(n,e,t)})},updateCommon:wt,contain:Mt}};function he(n){return{createCover:function(e,t){return Ye({toRectRange:function(r){var a=[r,[0,100]];return n&&a.reverse(),a},fromRectRange:function(r){return r[n]}},e,t,[[["w"],["e"]],[["n"],["s"]]][n])},getCreatingRange:function(e){var t=He(e),r=Q(t[0][n],t[1][n]),a=X(t[0][n],t[1][n]);return[r,a]},updateCoverShape:function(e,t,r,a){var i,o=Ue(e,t);if(o!==Z&&o.getLinearBrushOtherExtent)i=o.getLinearBrushOtherExtent(n);else{var s=e._zr;i=[0,[s.getWidth(),s.getHeight()][1-n]]}var u=[r,i];n&&u.reverse(),Xe(e,t,u,a)},updateCommon:wt,contain:Mt}}function Rn(n){return n=zt(n),function(e){return Dr(e,n)}}function Ln(n,e){return n=zt(n),function(t){var r=e??t,a=r?n.width:n.height,i=r?n.x:n.y;return[i,i+(a||0)]}}function kn(n,e,t){var r=zt(n);return function(a,i){return r.contain(i[0],i[1])&&!gn(a,e,t)}}function zt(n){return Ir.create(n)}var ve=["x","y","radius","angle","single"],En=["cartesian2d","polar","singleAxis"];function Bn(n){var e=n.get("coordinateSystem");return Y(En,e)>=0}function H(n){return n+"Axis"}function Vn(n,e){var t=J(),r=[],a=J();n.eachComponent({mainType:"dataZoom",query:e},function(l){a.get(l.uid)||s(l)});var i;do i=!1,n.eachComponent("dataZoom",o);while(i);function o(l){!a.get(l.uid)&&u(l)&&(s(l),i=!0)}function s(l){a.set(l.uid,!0),r.push(l),c(l)}function u(l){var f=!1;return l.eachTargetAxis(function(h,p){var v=t.get(h);v&&v[p]&&(f=!0)}),f}function c(l){l.eachTargetAxis(function(f,h){(t.get(f)||t.set(f,[]))[h]=!0})}return r}var yt=function(){function n(){this.indexList=[],this.indexMap=[]}return n.prototype.add=function(e){this.indexMap[e]||(this.indexList.push(e),this.indexMap[e]=!0)},n}(),Nn=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t._autoThrottle=!0,t._noTarget=!0,t._rangePropMode=["percent","percent"],t}return e.prototype.init=function(t,r,a){var i=pe(t);this.settledOption=i,this.mergeDefaultAndTheme(t,a),this._doInit(i)},e.prototype.mergeOption=function(t){var r=pe(t);z(this.option,t,!0),z(this.settledOption,r,!0),this._doInit(r)},e.prototype._doInit=function(t){var r=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var a=this.settledOption;b([["start","startValue"],["end","endValue"]],function(i,o){this._rangePropMode[o]==="value"&&(r[i[0]]=a[i[0]]=null)},this),this._resetTarget()},e.prototype._resetTarget=function(){var t=this.get("orient",!0),r=this._targetAxisInfoMap=J(),a=this._fillSpecifiedTargetAxis(r);a?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(r,this._orient)),this._noTarget=!0,r.each(function(i){i.indexList.length&&(this._noTarget=!1)},this)},e.prototype._fillSpecifiedTargetAxis=function(t){var r=!1;return b(ve,function(a){var i=this.getReferringComponents(H(a),Pr);if(i.specified){r=!0;var o=new yt;b(i.models,function(s){o.add(s.componentIndex)}),t.set(a,o)}},this),r},e.prototype._fillAutoTargetAxisByOrient=function(t,r){var a=this.ecModel,i=!0;if(i){var o=r==="vertical"?"y":"x",s=a.findComponents({mainType:o+"Axis"});u(s,o)}if(i){var s=a.findComponents({mainType:"singleAxis",filter:function(l){return l.get("orient",!0)===r}});u(s,"single")}function u(c,l){var f=c[0];if(f){var h=new yt;if(h.add(f.componentIndex),t.set(l,h),i=!1,l==="x"||l==="y"){var p=f.getReferringComponents("grid",ot).models[0];p&&b(c,function(v){f.componentIndex!==v.componentIndex&&p===v.getReferringComponents("grid",ot).models[0]&&h.add(v.componentIndex)})}}}i&&b(ve,function(c){if(i){var l=a.findComponents({mainType:H(c),filter:function(h){return h.get("type",!0)==="category"}});if(l[0]){var f=new yt;f.add(l[0].componentIndex),t.set(c,f),i=!1}}},this)},e.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis(function(r){!t&&(t=r)},this),t==="y"?"vertical":"horizontal"},e.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var r=this.ecModel.option;this.option.throttle=r.animation&&r.animationDurationUpdate>0?100:20}},e.prototype._updateRangeUse=function(t){var r=this._rangePropMode,a=this.get("rangeMode");b([["start","startValue"],["end","endValue"]],function(i,o){var s=t[i[0]]!=null,u=t[i[1]]!=null;s&&!u?r[o]="percent":!s&&u?r[o]="value":a?r[o]=a[o]:s&&(r[o]="percent")})},e.prototype.noTarget=function(){return this._noTarget},e.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis(function(r,a){t==null&&(t=this.ecModel.getComponent(H(r),a))},this),t},e.prototype.eachTargetAxis=function(t,r){this._targetAxisInfoMap.each(function(a,i){b(a.indexList,function(o){t.call(r,i,o)})})},e.prototype.getAxisProxy=function(t,r){var a=this.getAxisModel(t,r);if(a)return a.__dzAxisProxy},e.prototype.getAxisModel=function(t,r){var a=this._targetAxisInfoMap.get(t);if(a&&a.indexMap[r])return this.ecModel.getComponent(H(t),r)},e.prototype.setRawRange=function(t){var r=this.option,a=this.settledOption;b([["start","startValue"],["end","endValue"]],function(i){(t[i[0]]!=null||t[i[1]]!=null)&&(r[i[0]]=a[i[0]]=t[i[0]],r[i[1]]=a[i[1]]=t[i[1]])},this),this._updateRangeUse(t)},e.prototype.setCalculatedRange=function(t){var r=this.option;b(["start","startValue","end","endValue"],function(a){r[a]=t[a]})},e.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},e.prototype.getValueRange=function(t,r){if(t==null&&r==null){var a=this.findRepresentativeAxisProxy();if(a)return a.getDataValueWindow()}else return this.getAxisProxy(t,r).getDataValueWindow()},e.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var r,a=this._targetAxisInfoMap.keys(),i=0;i<a.length;i++)for(var o=a[i],s=this._targetAxisInfoMap.get(o),u=0;u<s.indexList.length;u++){var c=this.getAxisProxy(o,s.indexList[u]);if(c.hostedBy(this))return c;r||(r=c)}return r},e.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},e.prototype.getOrient=function(){return this._orient},e.type="dataZoom",e.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],e.defaultOption={z:4,filterMode:"filter",start:0,end:100},e}(Le);function pe(n){var e={};return b(["start","end","startValue","endValue","throttle"],function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e}var zn=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(Nn),Wn=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,a,i){this.dataZoomModel=t,this.ecModel=r,this.api=a},e.type="dataZoom",e}(ke),Zn=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(Wn),U=b,ge=Or,Fn=function(){function n(e,t,r,a){this._dimName=e,this._axisIndex=t,this.ecModel=a,this._dataZoomModel=r}return n.prototype.hostedBy=function(e){return this._dataZoomModel===e},n.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},n.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},n.prototype.getTargetSeriesModels=function(){var e=[];return this.ecModel.eachSeries(function(t){if(Bn(t)){var r=H(this._dimName),a=t.getReferringComponents(r,ot).models[0];a&&this._axisIndex===a.componentIndex&&e.push(t)}},this),e},n.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},n.prototype.getMinMaxSpan=function(){return W(this._minMaxSpan)},n.prototype.calculateDataWindow=function(e){var t=this._dataExtent,r=this.getAxisModel(),a=r.axis.scale,i=this._dataZoomModel.getRangePropMode(),o=[0,100],s=[],u=[],c;U(["start","end"],function(h,p){var v=e[h],g=e[h+"Value"];i[p]==="percent"?(v==null&&(v=o[p]),g=a.parse(j(v,o,t))):(c=!0,g=g==null?t[p]:a.parse(g),v=j(g,t,o)),u[p]=g==null||isNaN(g)?t[p]:g,s[p]=v==null||isNaN(v)?o[p]:v}),ge(u),ge(s);var l=this._minMaxSpan;c?f(u,s,t,o,!1):f(s,u,o,t,!0);function f(h,p,v,g,y){var x=y?"Span":"ValueSpan";ze(0,h,v,"all",l["min"+x],l["max"+x]);for(var d=0;d<2;d++)p[d]=j(h[d],v,g,!0),y&&(p[d]=a.parse(p[d]))}return{valueWindow:u,percentWindow:s}},n.prototype.reset=function(e){if(e===this._dataZoomModel){var t=this.getTargetSeriesModels();this._dataExtent=Gn(this,this._dimName,t),this._updateMinMaxSpan();var r=this.calculateDataWindow(e.settledOption);this._valueWindow=r.valueWindow,this._percentWindow=r.percentWindow,this._setAxisModel()}},n.prototype.filterData=function(e,t){if(e!==this._dataZoomModel)return;var r=this._dimName,a=this.getTargetSeriesModels(),i=e.get("filterMode"),o=this._valueWindow;if(i==="none")return;U(a,function(u){var c=u.getData(),l=c.mapDimensionsAll(r);if(l.length){if(i==="weakFilter"){var f=c.getStore(),h=k(l,function(p){return c.getDimensionIndex(p)},c);c.filterSelf(function(p){for(var v,g,y,x=0;x<l.length;x++){var d=f.get(h[x],p),_=!isNaN(d),m=d<o[0],A=d>o[1];if(_&&!m&&!A)return!0;_&&(y=!0),m&&(v=!0),A&&(g=!0)}return y&&v&&g})}else U(l,function(p){if(i==="empty")u.setData(c=c.map(p,function(g){return s(g)?g:NaN}));else{var v={};v[p]=o,c.selectRange(v)}});U(l,function(p){c.setApproximateExtent(o,p)})}});function s(u){return u>=o[0]&&u<=o[1]}},n.prototype._updateMinMaxSpan=function(){var e=this._minMaxSpan={},t=this._dataZoomModel,r=this._dataExtent;U(["min","max"],function(a){var i=t.get(a+"Span"),o=t.get(a+"ValueSpan");o!=null&&(o=this.getAxisModel().axis.scale.parse(o)),o!=null?i=j(r[0]+o,r,[0,100],!0):i!=null&&(o=j(i,[0,100],r,!0)-r[0]),e[a+"Span"]=i,e[a+"ValueSpan"]=o},this)},n.prototype._setAxisModel=function(){var e=this.getAxisModel(),t=this._percentWindow,r=this._valueWindow;if(t){var a=Rr(r,[0,500]);a=Math.min(a,20);var i=e.axis.scale.rawExtentInfo;t[0]!==0&&i.setDeterminedMinMax("min",+r[0].toFixed(a)),t[1]!==100&&i.setDeterminedMinMax("max",+r[1].toFixed(a)),i.freeze()}},n}();function Gn(n,e,t){var r=[1/0,-1/0];U(t,function(o){Lr(r,o.getData(),e)});var a=n.getAxisModel(),i=kr(a.axis.scale,a,r).calculate();return[i.min,i.max]}var Un={getTargetSeries:function(n){function e(a){n.eachComponent("dataZoom",function(i){i.eachTargetAxis(function(o,s){var u=n.getComponent(H(o),s);a(o,s,u,i)})})}e(function(a,i,o,s){o.__dzAxisProxy=null});var t=[];e(function(a,i,o,s){o.__dzAxisProxy||(o.__dzAxisProxy=new Fn(a,i,s,n),t.push(o.__dzAxisProxy))});var r=J();return b(t,function(a){b(a.getTargetSeriesModels(),function(i){r.set(i.uid,i)})}),r},overallReset:function(n,e){n.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(r,a){t.getAxisProxy(r,a).reset(t)}),t.eachTargetAxis(function(r,a){t.getAxisProxy(r,a).filterData(t,e)})}),n.eachComponent("dataZoom",function(t){var r=t.findRepresentativeAxisProxy();if(r){var a=r.getDataPercentWindow(),i=r.getDataValueWindow();t.setCalculatedRange({start:a[0],end:a[1],startValue:i[0],endValue:i[1]})}})}};function Hn(n){n.registerAction("dataZoom",function(e,t){var r=Vn(t,e);b(r,function(a){a.setRawRange({start:e.start,end:e.end,startValue:e.startValue,endValue:e.endValue})})})}var de=!1;function Yn(n){de||(de=!0,n.registerProcessor(n.PRIORITY.PROCESSOR.FILTER,Un),Hn(n),n.registerSubTypeDefaulter("dataZoom",function(){return"slider"}))}function Xn(n){n.registerComponentModel(zn),n.registerComponentView(Zn),Yn(n)}var V=function(){function n(){}return n}(),qe={};function K(n,e){qe[n]=e}function Je(n){return qe[n]}var $n=function(n){P(e,n);function e(){var t=n!==null&&n.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){n.prototype.optionUpdated.apply(this,arguments);var t=this.ecModel;b(this.option.feature,function(r,a){var i=Je(a);i&&(i.getDefaultOption&&(i.defaultOption=i.getDefaultOption(t)),z(r,i.defaultOption))})},e.type="toolbox",e.layoutMode={type:"box",ignoreSize:!0},e.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},e}(Le),jn=function(n){P(e,n);function e(){return n!==null&&n.apply(this,arguments)||this}return e.prototype.render=function(t,r,a,i){var o=this.group;if(o.removeAll(),!t.get("show"))return;var s=+t.get("itemSize"),u=t.get("orient")==="vertical",c=t.get("feature")||{},l=this._features||(this._features={}),f=[];b(c,function(v,g){f.push(g)}),new Oe(this._featureNames||[],f).add(h).update(h).remove(B(h,null)).execute(),this._featureNames=f;function h(v,g){var y=f[v],x=f[g],d=c[y],_=new Er(d,t,t.ecModel),m;if(i&&i.newTitle!=null&&i.featureName===y&&(d.title=i.newTitle),y&&!x){if(Kn(y))m={onclick:_.option.onclick,featureName:y};else{var A=Je(y);if(!A)return;m=new A}l[y]=m}else if(m=l[x],!m)return;m.uid=Br("toolbox-feature"),m.model=_,m.ecModel=r,m.api=a;var S=m instanceof V;if(!y&&x){S&&m.dispose&&m.dispose(r,a);return}if(!_.get("show")||S&&m.unusable){S&&m.remove&&m.remove(r,a);return}p(_,m,y),_.setIconStatus=function(w,T){var M=this.option,I=this.iconPaths;M.iconStatus=M.iconStatus||{},M.iconStatus[w]=T,I[w]&&(T==="emphasis"?Ht:Yt)(I[w])},m instanceof V&&m.render&&m.render(_,r,a,i)}function p(v,g,y){var x=v.getModel("iconStyle"),d=v.getModel(["emphasis","iconStyle"]),_=g instanceof V&&g.getIcons?g.getIcons():v.get("icon"),m=v.get("title")||{},A,S;_t(_)?(A={},A[y]=_):A=_,_t(m)?(S={},S[y]=m):S=m;var w=v.iconPaths={};b(A,function(T,M){var I=Vr(T,{},{x:-s/2,y:-s/2,width:s,height:s});I.setStyle(x.getItemStyle());var C=I.ensureState("emphasis");C.style=d.getItemStyle();var O=new Xt({style:{text:S[M],align:d.get("textAlign"),borderRadius:d.get("textBorderRadius"),padding:d.get("textPadding"),fill:null,font:Nr({fontStyle:d.get("textFontStyle"),fontFamily:d.get("textFontFamily"),fontSize:d.get("textFontSize"),fontWeight:d.get("textFontWeight")},r)},ignore:!0});I.setTextContent(O),zr({el:I,componentModel:t,itemName:M,formatterParamsExtra:{title:S[M]}}),I.__title=S[M],I.on("mouseover",function(){var D=d.getItemStyle(),E=u?t.get("right")==null&&t.get("left")!=="right"?"right":"left":t.get("bottom")==null&&t.get("top")!=="bottom"?"bottom":"top";O.setStyle({fill:d.get("textFill")||D.fill||D.stroke||"#000",backgroundColor:d.get("textBackgroundColor")}),I.setTextConfig({position:d.get("textPosition")||E}),O.ignore=!t.get("showTitle"),a.enterEmphasis(this)}).on("mouseout",function(){v.get(["iconStatus",M])!=="emphasis"&&a.leaveEmphasis(this),O.hide()}),(v.get(["iconStatus",M])==="emphasis"?Ht:Yt)(I),o.add(I),I.on("click",Lt(g.onclick,g,r,a,M)),w[M]=I})}Wr(o,t,a),o.add(Zr(o.getBoundingRect(),t)),u||o.eachChild(function(v){var g=v.__title,y=v.ensureState("emphasis"),x=y.textConfig||(y.textConfig={}),d=v.getTextContent(),_=d&&d.ensureState("emphasis");if(_&&!bt(_)&&g){var m=_.style||(_.style={}),A=Fr(g,Xt.makeFont(m)),S=v.x+o.x,w=v.y+o.y+s,T=!1;w+A.height>a.getHeight()&&(x.position="top",T=!0);var M=T?-5-A.height:s+10;S+A.width/2>a.getWidth()?(x.position=["100%",M],m.align="right"):S-A.width/2<0&&(x.position=[0,M],m.align="left")}})},e.prototype.updateView=function(t,r,a,i){b(this._features,function(o){o instanceof V&&o.updateView&&o.updateView(o.model,r,a,i)})},e.prototype.remove=function(t,r){b(this._features,function(a){a instanceof V&&a.remove&&a.remove(t,r)}),this.group.removeAll()},e.prototype.dispose=function(t,r){b(this._features,function(a){a instanceof V&&a.dispose&&a.dispose(t,r)})},e.type="toolbox",e}(ke);function Kn(n){return n.indexOf("my")===0}var qn=function(n){P(e,n);function e(){return n!==null&&n.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){var a=this.model,i=a.get("name")||t.get("title.0.text")||"echarts",o=r.getZr().painter.getType()==="svg",s=o?"svg":a.get("type",!0)||"png",u=r.getConnectedDataURL({type:s,backgroundColor:a.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:a.get("connectedBackgroundColor"),excludeComponents:a.get("excludeComponents"),pixelRatio:a.get("pixelRatio")}),c=Gr.browser;if(typeof MouseEvent=="function"&&(c.newEdge||!c.ie&&!c.edge)){var l=document.createElement("a");l.download=i+"."+s,l.target="_blank",l.href=u;var f=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});l.dispatchEvent(f)}else if(window.navigator.msSaveOrOpenBlob||o){var h=u.split(","),p=h[0].indexOf("base64")>-1,v=o?decodeURIComponent(h[1]):h[1];p&&(v=window.atob(v));var g=i+"."+s;if(window.navigator.msSaveOrOpenBlob){for(var y=v.length,x=new Uint8Array(y);y--;)x[y]=v.charCodeAt(y);var d=new Blob([x]);window.navigator.msSaveOrOpenBlob(d,g)}else{var _=document.createElement("iframe");document.body.appendChild(_);var m=_.contentWindow,A=m.document;A.open("image/svg+xml","replace"),A.write(v),A.close(),m.focus(),A.execCommand("SaveAs",!0,g),document.body.removeChild(_)}}else{var S=a.get("lang"),w='<body style="margin:0;"><img src="'+u+'" style="max-width:100%;" title="'+(S&&S[0]||"")+'" /></body>',T=window.open();T.document.write(w),T.document.title=i}},e.getDefaultOption=function(t){var r={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:t.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:t.getLocaleModel().get(["toolbox","saveAsImage","lang"])};return r},e}(V),me="__ec_magicType_stack__",Jn=[["line","bar"],["stack"]],Qn=function(n){P(e,n);function e(){return n!==null&&n.apply(this,arguments)||this}return e.prototype.getIcons=function(){var t=this.model,r=t.get("icon"),a={};return b(t.get("type"),function(i){r[i]&&(a[i]=r[i])}),a},e.getDefaultOption=function(t){var r={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:t.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}};return r},e.prototype.onclick=function(t,r,a){var i=this.model,o=i.get(["seriesIndex",a]);if(ye[a]){var s={series:[]},u=function(f){var h=f.subType,p=f.id,v=ye[a](h,p,f,i);v&&(ct(v,f.option),s.series.push(v));var g=f.coordinateSystem;if(g&&g.type==="cartesian2d"&&(a==="line"||a==="bar")){var y=g.getAxesByScale("ordinal")[0];if(y){var x=y.dim,d=x+"Axis",_=f.getReferringComponents(d,ot).models[0],m=_.componentIndex;s[d]=s[d]||[];for(var A=0;A<=m;A++)s[d][m]=s[d][m]||{};s[d][m].boundaryGap=a==="bar"}}};b(Jn,function(f){Y(f,a)>=0&&b(f,function(h){i.setIconStatus(h,"normal")})}),i.setIconStatus(a,"emphasis"),t.eachComponent({mainType:"series",query:o==null?null:{seriesIndex:o}},u);var c,l=a;a==="stack"&&(c=z({stack:i.option.title.tiled,tiled:i.option.title.stack},i.option.title),i.get(["iconStatus",a])!=="emphasis"&&(l="tiled")),r.dispatchAction({type:"changeMagicType",currentType:l,newOption:s,newTitle:c,featureName:"magicType"})}},e}(V),ye={line:function(n,e,t,r){if(n==="bar")return z({id:e,type:"line",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","line"])||{},!0)},bar:function(n,e,t,r){if(n==="line")return z({id:e,type:"bar",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","bar"])||{},!0)},stack:function(n,e,t,r){var a=t.get("stack")===me;if(n==="line"||n==="bar")return r.setIconStatus("stack",a?"normal":"emphasis"),z({id:e,stack:a?"":me},r.get(["option","stack"])||{},!0)}};lt({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(n,e){e.mergeOption(n.newOption)});var ht=new Array(60).join("-"),$="	";function ta(n){var e={},t=[],r=[];return n.eachRawSeries(function(a){var i=a.coordinateSystem;if(i&&(i.type==="cartesian2d"||i.type==="polar")){var o=i.getBaseAxis();if(o.type==="category"){var s=o.dim+"_"+o.index;e[s]||(e[s]={categoryAxis:o,valueAxis:i.getOtherAxis(o),series:[]},r.push({axisDim:o.dim,axisIndex:o.index})),e[s].series.push(a)}else t.push(a)}else t.push(a)}),{seriesGroupByCategoryAxis:e,other:t,meta:r}}function ea(n){var e=[];return b(n,function(t,r){var a=t.categoryAxis,i=t.valueAxis,o=i.dim,s=[" "].concat(k(t.series,function(p){return p.name})),u=[a.model.getCategories()];b(t.series,function(p){var v=p.getRawData();u.push(p.getRawData().mapArray(v.mapDimension(o),function(g){return g}))});for(var c=[s.join($)],l=0;l<u[0].length;l++){for(var f=[],h=0;h<u.length;h++)f.push(u[h][l]);c.push(f.join($))}e.push(c.join(`
`))}),e.join(`

`+ht+`

`)}function ra(n){return k(n,function(e){var t=e.getRawData(),r=[e.name],a=[];return t.each(t.dimensions,function(){for(var i=arguments.length,o=arguments[i-1],s=t.getName(o),u=0;u<i-1;u++)a[u]=arguments[u];r.push((s?s+$:"")+a.join($))}),r.join(`
`)}).join(`

`+ht+`

`)}function na(n){var e=ta(n);return{value:Hr([ea(e.seriesGroupByCategoryAxis),ra(e.other)],function(t){return!!t.replace(/[\n\t\s]/g,"")}).join(`

`+ht+`

`),meta:e.meta}}function st(n){return n.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function aa(n){var e=n.slice(0,n.indexOf(`
`));if(e.indexOf($)>=0)return!0}var Dt=new RegExp("["+$+"]+","g");function ia(n){for(var e=n.split(/\n+/g),t=st(e.shift()).split(Dt),r=[],a=k(t,function(u){return{name:u,data:[]}}),i=0;i<e.length;i++){var o=st(e[i]).split(Dt);r.push(o.shift());for(var s=0;s<o.length;s++)a[s]&&(a[s].data[i]=o[s])}return{series:a,categories:r}}function oa(n){for(var e=n.split(/\n+/g),t=st(e.shift()),r=[],a=0;a<e.length;a++){var i=st(e[a]);if(i){var o=i.split(Dt),s="",u=void 0,c=!1;isNaN(o[0])?(c=!0,s=o[0],o=o.slice(1),r[a]={name:s,value:[]},u=r[a].value):u=r[a]=[];for(var l=0;l<o.length;l++)u.push(+o[l]);u.length===1&&(c?r[a].value=u[0]:r[a]=u[0])}}return{name:t,data:r}}function sa(n,e){var t=n.split(new RegExp(`
*`+ht+`
*`,"g")),r={series:[]};return b(t,function(a,i){if(aa(a)){var o=ia(a),s=e[i],u=s.axisDim+"Axis";s&&(r[u]=r[u]||[],r[u][s.axisIndex]={data:o.categories},r.series=r.series.concat(o.series))}else{var o=oa(a);r.series.push(o)}}),r}var ua=function(n){P(e,n);function e(){return n!==null&&n.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){setTimeout(function(){r.dispatchAction({type:"hideTip"})});var a=r.getDom(),i=this.model;this._dom&&a.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",o.style.backgroundColor=i.get("backgroundColor")||"#fff";var s=document.createElement("h4"),u=i.get("lang")||[];s.innerHTML=u[0]||i.get("title"),s.style.cssText="margin:10px 20px",s.style.color=i.get("textColor");var c=document.createElement("div"),l=document.createElement("textarea");c.style.cssText="overflow:auto";var f=i.get("optionToContent"),h=i.get("contentToOption"),p=na(t);if(bt(f)){var v=f(r.getOption());_t(v)?c.innerHTML=v:Ur(v)&&c.appendChild(v)}else{l.readOnly=i.get("readOnly");var g=l.style;g.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",g.color=i.get("textColor"),g.borderColor=i.get("textareaBorderColor"),g.backgroundColor=i.get("textareaColor"),l.value=p.value,c.appendChild(l)}var y=p.meta,x=document.createElement("div");x.style.cssText="position:absolute;bottom:5px;left:0;right:0";var d="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",_=document.createElement("div"),m=document.createElement("div");d+=";background-color:"+i.get("buttonColor"),d+=";color:"+i.get("buttonTextColor");var A=this;function S(){a.removeChild(o),A._dom=null}$t(_,"click",S),$t(m,"click",function(){if(h==null&&f!=null||h!=null&&f==null){S();return}var w;try{bt(h)?w=h(c,r.getOption()):w=sa(l.value,y)}catch(T){throw S(),new Error("Data view format error "+T)}w&&r.dispatchAction({type:"changeDataView",newOption:w}),S()}),_.innerHTML=u[1],m.innerHTML=u[2],m.style.cssText=_.style.cssText=d,!i.get("readOnly")&&x.appendChild(m),x.appendChild(_),o.appendChild(s),o.appendChild(c),o.appendChild(x),c.style.height=a.clientHeight-80+"px",a.appendChild(o),this._dom=o},e.prototype.remove=function(t,r){this._dom&&r.getDom().removeChild(this._dom)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.getDefaultOption=function(t){var r={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:t.getLocaleModel().get(["toolbox","dataView","title"]),lang:t.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"};return r},e}(V);function la(n,e){return k(n,function(t,r){var a=e&&e[r];if(jt(a)&&!nt(a)){var i=jt(t)&&!nt(t);i||(t={value:t});var o=a.name!=null&&t.name==null;return t=ct(t,a),o&&delete t.name,t}else return t})}lt({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(n,e){var t=[];b(n.newOption.series,function(r){var a=e.getSeriesByName(r.name)[0];if(!a)t.push(Rt({type:"scatter"},r));else{var i=a.get("data");t.push({name:r.name,data:la(r.data,i)})}}),e.mergeOption(ct({series:t},n.newOption))});var Qe=b,tr=Yr();function ca(n,e){var t=Wt(n);Qe(e,function(r,a){for(var i=t.length-1;i>=0;i--){var o=t[i];if(o[a])break}if(i<0){var s=n.queryComponents({mainType:"dataZoom",subType:"select",id:a})[0];if(s){var u=s.getPercentRange();t[0][a]={dataZoomId:a,start:u[0],end:u[1]}}}}),t.push(e)}function fa(n){var e=Wt(n),t=e[e.length-1];e.length>1&&e.pop();var r={};return Qe(t,function(a,i){for(var o=e.length-1;o>=0;o--)if(a=e[o][i],a){r[i]=a;break}}),r}function ha(n){tr(n).snapshots=null}function va(n){return Wt(n).length}function Wt(n){var e=tr(n);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var pa=function(n){P(e,n);function e(){return n!==null&&n.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){ha(t),r.dispatchAction({type:"restore",from:this.uid})},e.getDefaultOption=function(t){var r={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:t.getLocaleModel().get(["toolbox","restore","title"])};return r},e}(V);lt({type:"restore",event:"restore",update:"prepareAndUpdate"},function(n,e){e.resetOption("recreate")});var ga=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],er=function(){function n(e,t,r){var a=this;this._targetInfoList=[];var i=xe(t,e);b(da,function(o,s){(!r||!r.include||Y(r.include,s)>=0)&&o(i,a._targetInfoList)})}return n.prototype.setOutputRanges=function(e,t){return this.matchOutputRanges(e,t,function(r,a,i){if((r.coordRanges||(r.coordRanges=[])).push(a),!r.coordRange){r.coordRange=a;var o=xt[r.brushType](0,i,a);r.__rangeOffset={offset:Se[r.brushType](o.values,r.range,[1,1]),xyMinMax:o.xyMinMax}}}),e},n.prototype.matchOutputRanges=function(e,t,r){b(e,function(a){var i=this.findTargetInfo(a,t);i&&i!==!0&&b(i.coordSyses,function(o){var s=xt[a.brushType](1,o,a.range,!0);r(a,s.values,o,t)})},this)},n.prototype.setInputRanges=function(e,t){b(e,function(r){var a=this.findTargetInfo(r,t);if(r.range=r.range||[],a&&a!==!0){r.panelId=a.panelId;var i=xt[r.brushType](0,a.coordSys,r.coordRange),o=r.__rangeOffset;r.range=o?Se[r.brushType](i.values,o.offset,ma(i.xyMinMax,o.xyMinMax)):i.values}},this)},n.prototype.makePanelOpts=function(e,t){return k(this._targetInfoList,function(r){var a=r.getPanelRect();return{panelId:r.panelId,defaultBrushType:t?t(r):null,clipPath:Rn(a),isTargetByCursor:kn(a,e,r.coordSysModel),getLinearBrushOtherExtent:Ln(a)}})},n.prototype.controlSeries=function(e,t,r){var a=this.findTargetInfo(e,r);return a===!0||a&&Y(a.coordSyses,t.coordinateSystem)>=0},n.prototype.findTargetInfo=function(e,t){for(var r=this._targetInfoList,a=xe(t,e),i=0;i<r.length;i++){var o=r[i],s=e.panelId;if(s){if(o.panelId===s)return o}else for(var u=0;u<_e.length;u++)if(_e[u](a,o))return o}return!0},n}();function It(n){return n[0]>n[1]&&n.reverse(),n}function xe(n,e){return Ee(n,e,{includeMainTypes:ga})}var da={grid:function(n,e){var t=n.xAxisModels,r=n.yAxisModels,a=n.gridModels,i=J(),o={},s={};!t&&!r&&!a||(b(t,function(u){var c=u.axis.grid.model;i.set(c.id,c),o[c.id]=!0}),b(r,function(u){var c=u.axis.grid.model;i.set(c.id,c),s[c.id]=!0}),b(a,function(u){i.set(u.id,u),o[u.id]=!0,s[u.id]=!0}),i.each(function(u){var c=u.coordinateSystem,l=[];b(c.getCartesians(),function(f,h){(Y(t,f.getAxis("x").model)>=0||Y(r,f.getAxis("y").model)>=0)&&l.push(f)}),e.push({panelId:"grid--"+u.id,gridModel:u,coordSysModel:u,coordSys:l[0],coordSyses:l,getPanelRect:be.grid,xAxisDeclared:o[u.id],yAxisDeclared:s[u.id]})}))},geo:function(n,e){b(n.geoModels,function(t){var r=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:r,coordSyses:[r],getPanelRect:be.geo})})}},_e=[function(n,e){var t=n.xAxisModel,r=n.yAxisModel,a=n.gridModel;return!a&&t&&(a=t.axis.grid.model),!a&&r&&(a=r.axis.grid.model),a&&a===e.gridModel},function(n,e){var t=n.geoModel;return t&&t===e.geoModel}],be={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var n=this.coordSys,e=n.getBoundingRect().clone();return e.applyTransform(Re(n)),e}},xt={lineX:B(Ae,0),lineY:B(Ae,1),rect:function(n,e,t,r){var a=n?e.pointToData([t[0][0],t[1][0]],r):e.dataToPoint([t[0][0],t[1][0]],r),i=n?e.pointToData([t[0][1],t[1][1]],r):e.dataToPoint([t[0][1],t[1][1]],r),o=[It([a[0],i[0]]),It([a[1],i[1]])];return{values:o,xyMinMax:o}},polygon:function(n,e,t,r){var a=[[1/0,-1/0],[1/0,-1/0]],i=k(t,function(o){var s=n?e.pointToData(o,r):e.dataToPoint(o,r);return a[0][0]=Math.min(a[0][0],s[0]),a[1][0]=Math.min(a[1][0],s[1]),a[0][1]=Math.max(a[0][1],s[0]),a[1][1]=Math.max(a[1][1],s[1]),s});return{values:i,xyMinMax:a}}};function Ae(n,e,t,r){var a=t.getAxis(["x","y"][n]),i=It(k([0,1],function(s){return e?a.coordToData(a.toLocalCoord(r[s]),!0):a.toGlobalCoord(a.dataToCoord(r[s]))})),o=[];return o[n]=i,o[1-n]=[NaN,NaN],{values:i,xyMinMax:o}}var Se={lineX:B(we,0),lineY:B(we,1),rect:function(n,e,t){return[[n[0][0]-t[0]*e[0][0],n[0][1]-t[0]*e[0][1]],[n[1][0]-t[1]*e[1][0],n[1][1]-t[1]*e[1][1]]]},polygon:function(n,e,t){return k(n,function(r,a){return[r[0]-t[0]*e[a][0],r[1]-t[1]*e[a][1]]})}};function we(n,e,t,r){return[e[0]-r[n]*t[0],e[1]-r[n]*t[1]]}function ma(n,e){var t=Ce(n),r=Ce(e),a=[t[0]/r[0],t[1]/r[1]];return isNaN(a[0])&&(a[0]=1),isNaN(a[1])&&(a[1]=1),a}function Ce(n){return n?[n[0][1]-n[0][0],n[1][1]-n[1][0]]:[NaN,NaN]}var Pt=b,ya=$r("toolbox-dataZoom_"),xa=function(n){P(e,n);function e(){return n!==null&&n.apply(this,arguments)||this}return e.prototype.render=function(t,r,a,i){this._brushController||(this._brushController=new Sn(a.getZr()),this._brushController.on("brush",Lt(this._onBrush,this)).mount()),Aa(t,r,this,i,a),ba(t,r)},e.prototype.onclick=function(t,r,a){_a[a].call(this)},e.prototype.remove=function(t,r){this._brushController&&this._brushController.unmount()},e.prototype.dispose=function(t,r){this._brushController&&this._brushController.dispose()},e.prototype._onBrush=function(t){var r=t.areas;if(!t.isEnd||!r.length)return;var a={},i=this.ecModel;this._brushController.updateCovers([]);var o=new er(Zt(this.model),i,{include:["grid"]});o.matchOutputRanges(r,i,function(c,l,f){if(f.type==="cartesian2d"){var h=c.brushType;h==="rect"?(s("x",f,l[0]),s("y",f,l[1])):s({lineX:"x",lineY:"y"}[h],f,l)}}),ca(i,a),this._dispatchZoomAction(a);function s(c,l,f){var h=l.getAxis(c),p=h.model,v=u(c,p,i),g=v.findRepresentativeAxisProxy(p).getMinMaxSpan();(g.minValueSpan!=null||g.maxValueSpan!=null)&&(f=ze(0,f.slice(),h.scale.getExtent(),0,g.minValueSpan,g.maxValueSpan)),v&&(a[v.id]={dataZoomId:v.id,startValue:f[0],endValue:f[1]})}function u(c,l,f){var h;return f.eachComponent({mainType:"dataZoom",subType:"select"},function(p){var v=p.getAxisModel(c,l.componentIndex);v&&(h=p)}),h}},e.prototype._dispatchZoomAction=function(t){var r=[];Pt(t,function(a,i){r.push(W(a))}),r.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:r})},e.getDefaultOption=function(t){var r={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:t.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}};return r},e}(V),_a={zoom:function(){var n=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:n})},back:function(){this._dispatchZoomAction(fa(this.ecModel))}};function Zt(n){var e={xAxisIndex:n.get("xAxisIndex",!0),yAxisIndex:n.get("yAxisIndex",!0),xAxisId:n.get("xAxisId",!0),yAxisId:n.get("yAxisId",!0)};return e.xAxisIndex==null&&e.xAxisId==null&&(e.xAxisIndex="all"),e.yAxisIndex==null&&e.yAxisId==null&&(e.yAxisIndex="all"),e}function ba(n,e){n.setIconStatus("back",va(e)>1?"emphasis":"normal")}function Aa(n,e,t,r,a){var i=t._isZoomActive;r&&r.type==="takeGlobalCursor"&&(i=r.key==="dataZoomSelect"?r.dataZoomSelectActive:!1),t._isZoomActive=i,n.setIconStatus("zoom",i?"emphasis":"normal");var o=new er(Zt(n),e,{include:["grid"]}),s=o.makePanelOpts(a,function(u){return u.xAxisDeclared&&!u.yAxisDeclared?"lineX":!u.xAxisDeclared&&u.yAxisDeclared?"lineY":"rect"});t._brushController.setPanels(s).enableBrush(i&&s.length?{brushType:"auto",brushStyle:n.getModel("brushStyle").getItemStyle()}:!1)}Xr("dataZoom",function(n){var e=n.getComponent("toolbox",0),t=["feature","dataZoom"];if(!e||e.get(t)==null)return;var r=e.getModel(t),a=[],i=Zt(r),o=Ee(n,i);Pt(o.xAxisModels,function(u){return s(u,"xAxis","xAxisIndex")}),Pt(o.yAxisModels,function(u){return s(u,"yAxis","yAxisIndex")});function s(u,c,l){var f=u.componentIndex,h={type:"select",$fromToolbox:!0,filterMode:r.get("filterMode",!0)||"filter",id:ya+c+f};h[l]=f,a.push(h)}return a});function Ca(n){n.registerComponentModel($n),n.registerComponentView(jn),K("saveAsImage",qn),K("magicType",Qn),K("dataView",ua),K("dataZoom",xa),K("restore",pa),jr(Xn)}export{Ca as a,tn as g,wa as i};
