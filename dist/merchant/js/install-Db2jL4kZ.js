import{a3 as u,p,a4 as j,a0 as b,a5 as L,a6 as G,a7 as St,a8 as J,v as X,a9 as vt,aa as Ct,ab as ut,q as Tt,ac as At,ad as mt,o as lt,ae as dt,_ as ht,af as Rt,C as _t,ag as Bt,ah as wt,ai as Et,aj as I,ak as Y,Z as K,al as Dt,S as O,am as bt,an as Lt,W as Ft,ao as Wt,w as Gt,j as Ut,ap as zt,aq as Yt,ar as Ht,E as tt}from"./installCanvasRenderer-p5Q0jy4r.js";function Mt(e,t,a,r){return e&&(e.legacy||e.legacy!==!1&&!a&&!r&&t!=="tspan"&&(t==="text"||u(e,"text")))}function Vt(e,t,a){var r=e,n,i,o;if(t==="text")o=r;else{o={},u(r,"text")&&(o.text=r.text),u(r,"rich")&&(o.rich=r.rich),u(r,"textFill")&&(o.fill=r.textFill),u(r,"textStroke")&&(o.stroke=r.textStroke),u(r,"fontFamily")&&(o.fontFamily=r.fontFamily),u(r,"fontSize")&&(o.fontSize=r.fontSize),u(r,"fontStyle")&&(o.fontStyle=r.fontStyle),u(r,"fontWeight")&&(o.fontWeight=r.fontWeight),i={type:"text",style:o,silent:!0},n={};var v=u(r,"textPosition");n.position=v?r.textPosition:"inside",u(r,"textPosition")&&(n.position=r.textPosition),u(r,"textOffset")&&(n.offset=r.textOffset),u(r,"textRotation")&&(n.rotation=r.textRotation),u(r,"textDistance")&&(n.distance=r.textDistance)}return et(o,e),p(o.rich,function(s){et(s,s)}),{textConfig:n,textContent:i}}function et(e,t){t&&(t.font=t.textFont||t.font,u(t,"textStrokeWidth")&&(e.lineWidth=t.textStrokeWidth),u(t,"textAlign")&&(e.align=t.textAlign),u(t,"textVerticalAlign")&&(e.verticalAlign=t.textVerticalAlign),u(t,"textLineHeight")&&(e.lineHeight=t.textLineHeight),u(t,"textWidth")&&(e.width=t.textWidth),u(t,"textHeight")&&(e.height=t.textHeight),u(t,"textBackgroundColor")&&(e.backgroundColor=t.textBackgroundColor),u(t,"textPadding")&&(e.padding=t.textPadding),u(t,"textBorderColor")&&(e.borderColor=t.textBorderColor),u(t,"textBorderWidth")&&(e.borderWidth=t.textBorderWidth),u(t,"textBorderRadius")&&(e.borderRadius=t.textBorderRadius),u(t,"textBoxShadowColor")&&(e.shadowColor=t.textBoxShadowColor),u(t,"textBoxShadowBlur")&&(e.shadowBlur=t.textBoxShadowBlur),u(t,"textBoxShadowOffsetX")&&(e.shadowOffsetX=t.textBoxShadowOffsetX),u(t,"textBoxShadowOffsetY")&&(e.shadowOffsetY=t.textBoxShadowOffsetY))}var ct={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},at=L(ct);mt(G,function(e,t){return e[t]=1,e},{});G.join(", ");var V=["","style","shape","extra"],D=j();function Q(e,t,a,r,n){var i=e+"Animation",o=ut(e,r,n)||{},v=D(t).userDuring;return o.duration>0&&(o.during=v?Tt(Pt,{el:t,userDuring:v}):null,o.setToFinal=!0,o.scope=e),b(o,a[i]),o}function k(e,t,a,r){r=r||{};var n=r.dataIndex,i=r.isInit,o=r.clearStyle,v=a.isAnimationEnabled(),s=D(e),h=t.style;s.userDuring=t.during;var f={},d={};if(jt(e,t,d),nt("shape",t,d),nt("extra",t,d),!i&&v&&(qt(e,t,f),rt("shape",e,t,f),rt("extra",e,t,f),Jt(e,t,h,f)),d.style=h,Zt(e,d,o),kt(e,t),v)if(i){var l={};p(V,function(g){var x=g?t[g]:t;x&&x.enterFrom&&(g&&(l[g]=l[g]||{}),b(g?l[g]:l,x.enterFrom))});var c=Q("enter",e,t,a,n);c.duration>0&&e.animateFrom(l,c)}else $t(e,t,n||0,a,f);gt(e,t),h?e.dirty():e.markRedraw()}function gt(e,t){for(var a=D(e).leaveToProps,r=0;r<V.length;r++){var n=V[r],i=n?t[n]:t;i&&i.leaveTo&&(a||(a=D(e).leaveToProps={}),n&&(a[n]=a[n]||{}),b(n?a[n]:a,i.leaveTo))}}function Xt(e,t,a,r){if(e){var n=e.parent,i=D(e).leaveToProps;if(i){var o=Q("update",e,t,a,0);o.done=function(){n.remove(e)},e.animateTo(i,o)}else n.remove(e)}}function E(e){return e==="all"}function Zt(e,t,a){var r=t.style;if(!e.isGroup&&r){if(a){e.useStyle({});for(var n=e.animators,i=0;i<n.length;i++){var o=n[i];o.targetName==="style"&&o.changeTarget(e.style)}}e.setStyle(r)}t&&(t.style=null,t&&e.attr(t),t.style=r)}function $t(e,t,a,r,n){if(n){var i=Q("update",e,t,r,a);i.duration>0&&e.animateFrom(n,i)}}function kt(e,t){u(t,"silent")&&(e.silent=t.silent),u(t,"ignore")&&(e.ignore=t.ignore),e instanceof vt&&u(t,"invisible")&&(e.invisible=t.invisible),e instanceof Ct&&u(t,"autoBatch")&&(e.autoBatch=t.autoBatch)}var m={},Kt={setTransform:function(e,t){return m.el[e]=t,this},getTransform:function(e){return m.el[e]},setShape:function(e,t){var a=m.el,r=a.shape||(a.shape={});return r[e]=t,a.dirtyShape&&a.dirtyShape(),this},getShape:function(e){var t=m.el.shape;if(t)return t[e]},setStyle:function(e,t){var a=m.el,r=a.style;return r&&(r[e]=t,a.dirtyStyle&&a.dirtyStyle()),this},getStyle:function(e){var t=m.el.style;if(t)return t[e]},setExtra:function(e,t){var a=m.el.extra||(m.el.extra={});return a[e]=t,this},getExtra:function(e){var t=m.el.extra;if(t)return t[e]}};function Pt(){var e=this,t=e.el;if(t){var a=D(t).userDuring,r=e.userDuring;if(a!==r){e.el=e.userDuring=null;return}m.el=t,r(Kt)}}function rt(e,t,a,r){var n=a[e];if(n){var i=t[e],o;if(i){var v=a.transition,s=n.transition;if(s)if(!o&&(o=r[e]={}),E(s))b(o,i);else for(var h=J(s),f=0;f<h.length;f++){var d=h[f],l=i[d];o[d]=l}else if(E(v)||X(v,e)>=0){!o&&(o=r[e]={});for(var c=L(i),f=0;f<c.length;f++){var d=c[f],l=i[d];Qt(n[d],l)&&(o[d]=l)}}}}}function nt(e,t,a){var r=t[e];if(r)for(var n=a[e]={},i=L(r),o=0;o<i.length;o++){var v=i[o];n[v]=St(r[v])}}function qt(e,t,a){for(var r=t.transition,n=E(r)?G:J(r||[]),i=0;i<n.length;i++){var o=n[i];if(!(o==="style"||o==="shape"||o==="extra")){var v=e[o];a[o]=v}}}function jt(e,t,a){for(var r=0;r<at.length;r++){var n=at[r],i=ct[n],o=t[n];o&&(a[i[0]]=o[0],a[i[1]]=o[1])}for(var r=0;r<G.length;r++){var v=G[r];t[v]!=null&&(a[v]=t[v])}}function Jt(e,t,a,r){if(a){var n=e.style,i;if(n){var o=a.transition,v=t.transition;if(o&&!E(o)){var s=J(o);!i&&(i=r.style={});for(var h=0;h<s.length;h++){var f=s[h],d=n[f];i[f]=d}}else if(e.getAnimationStyleProps&&(E(v)||E(o)||X(v,"style")>=0)){var l=e.getAnimationStyleProps(),c=l?l.style:null;if(c){!i&&(i=r.style={});for(var g=L(a),h=0;h<g.length;h++){var f=g[h];if(c[f]){var d=n[f];i[f]=d}}}}}}}function Qt(e,t){return At(e)?e!==t:e!=null&&isFinite(e)}var xt=j(),Nt=["percent","easing","shape","style","extra"];function It(e){e.stopAnimation("keyframe"),e.attr(xt(e))}function P(e,t,a){if(!(!a.isAnimationEnabled()||!t)){if(lt(t)){p(t,function(v){P(e,v,a)});return}var r=t.keyframes,n=t.duration;if(a&&n==null){var i=ut("enter",a,0);n=i&&i.duration}if(!(!r||!n)){var o=xt(e);p(V,function(v){if(!(v&&!e[v])){var s;r.sort(function(h,f){return h.percent-f.percent}),p(r,function(h){var f=e.animators,d=v?h[v]:h;if(d){var l=L(d);if(v||(l=dt(l,function(x){return X(Nt,x)<0})),!!l.length){s||(s=e.animate(v,t.loop,!0),s.scope="keyframe");for(var c=0;c<f.length;c++)f[c]!==s&&f[c].targetName===s.targetName&&f[c].stopTracks(l);v&&(o[v]=o[v]||{});var g=v?o[v]:o;p(l,function(x){g[x]=((v?e[v]:e)||{})[x]}),s.whenWithKeys(n*h.percent,d,l,h.easing)}}}),s&&s.delay(t.delay||0).duration(n).start(t.easing)}})}}}function Ot(e,t){var a=e.existing;if(t.id=e.keyInfo.id,!t.type&&a&&(t.type=a.type),t.parentId==null){var r=t.parentOption;r?t.parentId=r.id:a&&(t.parentId=a.parentId)}t.parentOption=null}function it(e,t){var a;return p(t,function(r){e[r]!=null&&e[r]!=="auto"&&(a=!0)}),a}function te(e,t,a){var r=b({},a),n=e[t],i=a.$action||"merge";i==="merge"?n?(Bt(n,r,!0),wt(n,r,{ignoreSize:!0}),Et(a,n),H(a,n),H(a,n,"shape"),H(a,n,"style"),H(a,n,"extra"),a.clipPath=n.clipPath):e[t]=r:i==="replace"?e[t]=r:i==="remove"&&n&&(e[t]=null)}var yt=["transition","enterFrom","leaveTo"],ee=yt.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function H(e,t,a){if(a&&(!e[a]&&t[a]&&(e[a]={}),e=e[a],t=t[a]),!(!e||!t))for(var r=a?yt:ee,n=0;n<r.length;n++){var i=r[n];e[i]==null&&t[i]!=null&&(e[i]=t[i])}}function ae(e,t){if(e&&(e.hv=t.hv=[it(t,["left","right"]),it(t,["top","bottom"])],e.type==="group")){var a=e,r=t;a.width==null&&(a.width=r.width=0),a.height==null&&(a.height=r.height=0)}}var re=function(e){ht(t,e);function t(){var a=e!==null&&e.apply(this,arguments)||this;return a.type=t.type,a.preventAutoZ=!0,a}return t.prototype.mergeOption=function(a,r){var n=this.option.elements;this.option.elements=null,e.prototype.mergeOption.call(this,a,r),this.option.elements=n},t.prototype.optionUpdated=function(a,r){var n=this.option,i=(r?n:a).elements,o=n.elements=r?[]:n.elements,v=[];this._flatten(i,v,null);var s=Rt(o,v,"normalMerge"),h=this._elOptionsToUpdate=[];p(s,function(f,d){var l=f.newOption;l&&(h.push(l),Ot(f,l),te(o,d,l),ae(o[d],l))},this),n.elements=dt(o,function(f){return f&&delete f.$action,f!=null})},t.prototype._flatten=function(a,r,n){p(a,function(i){if(i){n&&(i.parentOption=n),r.push(i);var o=i.children;o&&o.length&&this._flatten(o,r,i),delete i.children}},this)},t.prototype.useElOptionsToUpdate=function(){var a=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,a},t.type="graphic",t.defaultOption={elements:[]},t}(_t),ot={path:null,compoundPath:null,group:Yt,image:zt,text:K},S=j(),ne=function(e){ht(t,e);function t(){var a=e!==null&&e.apply(this,arguments)||this;return a.type=t.type,a}return t.prototype.init=function(){this._elMap=I()},t.prototype.render=function(a,r,n){a!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=a,this._updateElements(a),this._relocate(a,n)},t.prototype._updateElements=function(a){var r=a.useElOptionsToUpdate();if(r){var n=this._elMap,i=this.group,o=a.get("z"),v=a.get("zlevel");p(r,function(s){var h=Y(s.id,null),f=h!=null?n.get(h):null,d=Y(s.parentId,null),l=d!=null?n.get(d):i,c=s.type,g=s.style;c==="text"&&g&&s.hv&&s.hv[1]&&(g.textVerticalAlign=g.textBaseline=g.verticalAlign=g.align=null);var x=s.textContent,R=s.textConfig;if(g&&Mt(g,c,!!R,!!x)){var T=Vt(g,c);!R&&T.textConfig&&(R=s.textConfig=T.textConfig),!x&&T.textContent&&(x=T.textContent)}var U=ie(s),B=s.$action||"merge",F=B==="merge",w=B==="replace";if(F){var A=!f,C=f;A?C=st(h,l,s.type,n):(C&&(S(C).isNew=!1),It(C)),C&&(k(C,U,a,{isInit:A}),ft(C,s,o,v))}else if(w){M(f,s,n,a);var _=st(h,l,s.type,n);_&&(k(_,U,a,{isInit:!0}),ft(_,s,o,v))}else B==="remove"&&(gt(f,s),M(f,s,n,a));var y=n.get(h);if(y&&x)if(F){var N=y.getTextContent();N?N.attr(x):y.setTextContent(new K(x))}else w&&y.setTextContent(new K(x));if(y){var z=s.clipPath;if(z){var Z=z.type,W=void 0,A=!1;if(F){var $=y.getClipPath();A=!$||S($).type!==Z,W=A?q(Z):$}else w&&(A=!0,W=q(Z));y.setClipPath(W),k(W,z,a,{isInit:A}),P(W,z.keyframeAnimation,a)}var pt=S(y);y.setTextConfig(R),pt.option=s,oe(y,a,s),Dt({el:y,componentModel:a,itemName:y.name,itemTooltipOption:s.tooltip}),P(y,s.keyframeAnimation,a)}})}},t.prototype._relocate=function(a,r){for(var n=a.option.elements,i=this.group,o=this._elMap,v=r.getWidth(),s=r.getHeight(),h=["x","y"],f=0;f<n.length;f++){var d=n[f],l=Y(d.id,null),c=l!=null?o.get(l):null;if(!(!c||!c.isGroup)){var g=c.parent,x=g===i,R=S(c),T=S(g);R.width=O(R.option.width,x?v:T.width)||0,R.height=O(R.option.height,x?s:T.height)||0}}for(var f=n.length-1;f>=0;f--){var d=n[f],l=Y(d.id,null),c=l!=null?o.get(l):null;if(c){var g=c.parent,T=S(g),U=g===i?{width:v,height:s}:{width:T.width,height:T.height},B={},F=bt(c,d,U,null,{hv:d.hv,boundingMode:d.bounding},B);if(!S(c).isNew&&F){for(var w=d.transition,A={},C=0;C<h.length;C++){var _=h[C],y=B[_];w&&(E(w)||X(w,_)>=0)?A[_]=y:c[_]=y}Lt(c,A,a,0)}else c.attr(B)}}},t.prototype._clear=function(){var a=this,r=this._elMap;r.each(function(n){M(n,S(n).option,r,a._lastGraphicModel)}),this._elMap=I()},t.prototype.dispose=function(){this._clear()},t.type="graphic",t}(Ft);function q(e){var t=u(ot,e)?ot[e]:Ht(e),a=new t({});return S(a).type=e,a}function st(e,t,a,r){var n=q(a);return t.add(n),r.set(e,n),S(n).id=e,S(n).isNew=!0,n}function M(e,t,a,r){var n=e&&e.parent;n&&(e.type==="group"&&e.traverse(function(i){M(i,t,a,r)}),Xt(e,t,r),a.removeKey(S(e).id))}function ft(e,t,a,r){e.isGroup||p([["cursor",vt.prototype.cursor],["zlevel",r||0],["z",a||0],["z2",0]],function(n){var i=n[0];u(t,i)?e[i]=Gt(t[i],n[1]):e[i]==null&&(e[i]=n[1])}),p(L(t),function(n){if(n.indexOf("on")===0){var i=t[n];e[n]=Ut(i)?i:null}}),u(t,"draggable")&&(e.draggable=t.draggable),t.name!=null&&(e.name=t.name),t.id!=null&&(e.id=t.id)}function ie(e){return e=b({},e),p(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(Wt),function(t){delete e[t]}),e}function oe(e,t,a){var r=tt(e).eventData;!e.silent&&!e.ignore&&!r&&(r=tt(e).eventData={componentType:"graphic",componentIndex:t.componentIndex,name:e.name}),r&&(r.info=a.info)}function fe(e){e.registerComponentModel(re),e.registerComponentView(ne),e.registerPreprocessor(function(t){var a=t.graphic;lt(a)?!a[0]||!a[0].elements?t.graphic=[{elements:a}]:t.graphic=[t.graphic[0]]:a&&!a.elements&&(t.graphic=[{elements:[a]}])})}export{fe as i};
