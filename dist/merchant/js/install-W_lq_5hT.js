import{_ as nt,C as vt,f as y,t as at,g as M,h as U,j as N,n as pt,k as gt,l as B,o as R,p as T,q as I,r as st,s as ct,v as mt,w as _t,x as ht,y as wt,Z as Tt,z as yt,A as Ct,B as j,D as xt,R as St,E as D,F as bt,G as Mt,H as q,I as kt,J as Dt,K as It,T as Y,L as Z,M as Q,N as G,O as Ot,P as Rt,Q as Ht,S as $,U as zt,V as At,W as Bt,X as Lt,Y as Pt,$ as A,a0 as J,u as Xt,a1 as Yt,a2 as K}from"./installCanvasRenderer-p5Q0jy4r.js";var Nt=function(r){nt(t,r);function t(){var i=r!==null&&r.apply(this,arguments)||this;return i.type=t.type,i}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(vt);function lt(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function ft(r){if(y.domSupported){for(var t=document.documentElement.style,i=0,e=r.length;i<e;i++)if(r[i]in t)return r[i]}}var ut=ft(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Wt=ft(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function dt(r,t){if(!r)return t;t=at(t,!0);var i=r.indexOf(t);return r=i===-1?t:"-"+r.slice(0,i)+"-"+t,r.toLowerCase()}function Ft(r,t){var i=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return i?i[t]:null}var Vt=dt(Wt,"transition"),W=dt(ut,"transform"),Ut="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(y.transform3dSupported?"will-change:transform;":"");function jt(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function qt(r,t,i){if(!M(i)||i==="inside")return"";var e=r.get("backgroundColor"),n=r.get("borderWidth");t=B(t);var o=jt(i),a=Math.max(Math.round(n)*1.5,6),h="",s=W+":",l;mt(["left","right"],o)>-1?(h+="top:50%",s+="translateY(-50%) rotate("+(l=o==="left"?-225:-45)+"deg)"):(h+="left:50%",s+="translateX(-50%) rotate("+(l=o==="top"?225:45)+"deg)");var f=l*Math.PI/180,u=a+n,d=u*Math.abs(Math.cos(f))+u*Math.abs(Math.sin(f)),v=Math.round(((d-Math.SQRT2*n)/2+Math.SQRT2*n-(d-u)/2)*100)/100;h+=";"+o+":-"+v+"px";var p=t+" solid "+n+"px;",g=["position:absolute;width:"+a+"px;height:"+a+"px;z-index:-1;",h+";"+s+";","border-bottom:"+p,"border-right:"+p,"background-color:"+e+";"];return'<div style="'+g.join("")+'"></div>'}function Zt(r,t){var i="cubic-bezier(0.23,1,0.32,1)",e=" "+r/2+"s "+i,n="opacity"+e+",visibility"+e;return t||(e=" "+r+"s "+i,n+=y.transformSupported?","+W+e:",left"+e+",top"+e),Vt+":"+n}function E(r,t,i){var e=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!y.transformSupported)return i?"top:"+n+";left:"+e+";":[["top",n],["left",e]];var o=y.transform3dSupported,a="translate"+(o?"3d":"")+"("+e+","+n+(o?",0":"")+")";return i?"top:0;left:0;"+W+":"+a+";":[["top",0],["left",0],[ut,a]]}function Qt(r){var t=[],i=r.get("fontSize"),e=r.getTextColor();e&&t.push("color:"+e),t.push("font:"+r.getFont());var n=_t(r.get("lineHeight"),Math.round(i*3/2));i&&t.push("line-height:"+n+"px");var o=r.get("textShadowColor"),a=r.get("textShadowBlur")||0,h=r.get("textShadowOffsetX")||0,s=r.get("textShadowOffsetY")||0;return o&&a&&t.push("text-shadow:"+h+"px "+s+"px "+a+"px "+o),T(["decoration","align"],function(l){var f=r.get(l);f&&t.push("text-"+l+":"+f)}),t.join(";")}function Gt(r,t,i){var e=[],n=r.get("transitionDuration"),o=r.get("backgroundColor"),a=r.get("shadowBlur"),h=r.get("shadowColor"),s=r.get("shadowOffsetX"),l=r.get("shadowOffsetY"),f=r.getModel("textStyle"),u=st(r,"html"),d=s+"px "+l+"px "+a+"px "+h;return e.push("box-shadow:"+d),t&&n&&e.push(Zt(n,i)),o&&e.push("background-color:"+o),T(["width","color","radius"],function(v){var p="border-"+v,g=at(p),_=r.get(g);_!=null&&e.push(p+":"+_+(v==="color"?"":"px"))}),e.push(Qt(f)),u!=null&&e.push("padding:"+ct(u).join("px ")+"px"),e.join(";")+";"}function tt(r,t,i,e,n){var o=t&&t.painter;if(i){var a=o&&o.getViewportRoot();a&&gt(r,a,i,e,n)}else{r[0]=e,r[1]=n;var h=o&&o.getViewportRootOffset();h&&(r[0]+=h.offsetLeft,r[1]+=h.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var $t=function(){function r(t,i){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,y.wxa)return null;var e=document.createElement("div");e.domBelongToZr=!0,this.el=e;var n=this._zr=t.getZr(),o=i.appendTo,a=o&&(M(o)?document.querySelector(o):U(o)?o:N(o)&&o(t.getDom()));tt(this._styleCoord,n,a,t.getWidth()/2,t.getHeight()/2),(a||t.getDom()).appendChild(e),this._api=t,this._container=a;var h=this;e.onmouseenter=function(){h._enterable&&(clearTimeout(h._hideTimeout),h._show=!0),h._inContent=!0},e.onmousemove=function(s){if(s=s||window.event,!h._enterable){var l=n.handler,f=n.painter.getViewportRoot();pt(f,s,!0),l.dispatch("mousemove",s)}},e.onmouseleave=function(){h._inContent=!1,h._enterable&&h._show&&h.hideLater(h._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var i=this._api.getDom(),e=Ft(i,"position"),n=i.style;n.position!=="absolute"&&e!=="absolute"&&(n.position="relative")}var o=t.get("alwaysShowContent");o&&this._moveIfResized(),this._alwaysShowContent=o,this.el.className=t.get("className")||""},r.prototype.show=function(t,i){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var e=this.el,n=e.style,o=this._styleCoord;e.innerHTML?n.cssText=Ut+Gt(t,!this._firstShow,this._longHide)+E(o[0],o[1],!0)+("border-color:"+B(i)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,i,e,n,o){var a=this.el;if(t==null){a.innerHTML="";return}var h="";if(M(o)&&e.get("trigger")==="item"&&!lt(e)&&(h=qt(e,n,o)),M(t))a.innerHTML=t+h;else if(t){a.innerHTML="",R(t)||(t=[t]);for(var s=0;s<t.length;s++)U(t[s])&&t[s].parentNode!==a&&a.appendChild(t[s]);if(h&&a.childNodes.length){var l=document.createElement("div");l.innerHTML=h,a.appendChild(l)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,i){if(this.el){var e=this._styleCoord;if(tt(e,this._zr,this._container,t,i),e[0]!=null&&e[1]!=null){var n=this.el.style,o=E(e[0],e[1]);T(o,function(a){n[a[0]]=a[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],i=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),i*this._zr.getHeight())},r.prototype.hide=function(){var t=this,i=this.el.style;i.visibility="hidden",i.opacity="0",y.transform3dSupported&&(i.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(I(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}(),Jt=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),it(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var i=t.get("alwaysShowContent");i&&this._moveIfResized(),this._alwaysShowContent=i},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,i,e,n,o){var a=this;ht(t)&&wt(""),this.el&&this._zr.remove(this.el);var h=e.getModel("textStyle");this.el=new Tt({style:{rich:i.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:h.get("textShadowColor"),fill:e.get(["textStyle","color"]),padding:st(e,"richText"),verticalAlign:"top",align:"left"},z:e.get("z")}),T(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(l){a.el.style[l]=e.get(l)}),T(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(l){a.el.style[l]=h.get(l)||0}),this._zr.add(this.el);var s=this;this.el.on("mouseover",function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0}),this.el.on("mouseout",function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,i=this.el.getBoundingRect(),e=et(t.style);return[i.width+e.left+e.right,i.height+e.top+e.bottom]},r.prototype.moveTo=function(t,i){var e=this.el;if(e){var n=this._styleCoord;it(n,this._zr,t,i),t=n[0],i=n[1];var o=e.style,a=b(o.borderWidth||0),h=et(o);e.x=t+a+h.left,e.y=i+a+h.top,e.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],i=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),i*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(I(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function b(r){return Math.max(0,r)}function et(r){var t=b(r.shadowBlur||0),i=b(r.shadowOffsetX||0),e=b(r.shadowOffsetY||0);return{left:b(t-i),right:b(t+i),top:b(t-e),bottom:b(t+e)}}function it(r,t,i,e){r[0]=i,r[1]=e,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var Kt=new St({shape:{x:-1,y:-1,width:2,height:2}}),Et=function(r){nt(t,r);function t(){var i=r!==null&&r.apply(this,arguments)||this;return i.type=t.type,i}return t.prototype.init=function(i,e){if(!(y.node||!e.getDom())){var n=i.getComponent("tooltip"),o=this._renderMode=yt(n.get("renderMode"));this._tooltipContent=o==="richText"?new Jt(e):new $t(e,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(i,e,n){if(!(y.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=i,this._ecModel=e,this._api=n;var o=this._tooltipContent;o.update(i),o.setEnterable(i.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&i.get("transitionDuration")?Ct(this,"_updatePosition",50,"fixRate"):j(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var i=this._tooltipModel,e=i.get("triggerOn");xt("itemTooltip",this._api,I(function(n,o,a){e!=="none"&&(e.indexOf(n)>=0?this._tryShow(o,a):n==="leave"&&this._hide(a))},this))},t.prototype._keepShow=function(){var i=this._tooltipModel,e=this._ecModel,n=this._api,o=i.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&o!=="none"&&o!=="click"){var a=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&a.manuallyShowTip(i,e,n,{x:a._lastX,y:a._lastY,dataByCoordSys:a._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(i,e,n,o){if(!(o.from===this.uid||y.node||!n.getDom())){var a=ot(o,n);this._ticket="";var h=o.dataByCoordSys,s=oe(o,e,n);if(s){var l=s.el.getBoundingRect().clone();l.applyTransform(s.el.transform),this._tryShow({offsetX:l.x+l.width/2,offsetY:l.y+l.height/2,target:s.el,position:o.position,positionDefault:"bottom"},a)}else if(o.tooltip&&o.x!=null&&o.y!=null){var f=Kt;f.x=o.x,f.y=o.y,f.update(),D(f).tooltipConfig={name:null,option:o.tooltip},this._tryShow({offsetX:o.x,offsetY:o.y,target:f},a)}else if(h)this._tryShow({offsetX:o.x,offsetY:o.y,position:o.position,dataByCoordSys:h,tooltipOption:o.tooltipOption},a);else if(o.seriesIndex!=null){if(this._manuallyAxisShowTip(i,e,n,o))return;var u=bt(o,e),d=u.point[0],v=u.point[1];d!=null&&v!=null&&this._tryShow({offsetX:d,offsetY:v,target:u.el,position:o.position,positionDefault:"bottom"},a)}else o.x!=null&&o.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:o.x,y:o.y}),this._tryShow({offsetX:o.x,offsetY:o.y,position:o.position,target:n.getZr().findHover(o.x,o.y).target},a))}},t.prototype.manuallyHideTip=function(i,e,n,o){var a=this._tooltipContent;this._tooltipModel&&a.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,o.from!==this.uid&&this._hide(ot(o,n))},t.prototype._manuallyAxisShowTip=function(i,e,n,o){var a=o.seriesIndex,h=o.dataIndex,s=e.getComponent("axisPointer").coordSysAxesInfo;if(!(a==null||h==null||s==null)){var l=e.getSeriesByIndex(a);if(l){var f=l.getData(),u=O([f.getItemModel(h),l,(l.coordinateSystem||{}).model],this._tooltipModel);if(u.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:a,dataIndex:h,position:o.position}),!0}}},t.prototype._tryShow=function(i,e){var n=i.target,o=this._tooltipModel;if(o){this._lastX=i.offsetX,this._lastY=i.offsetY;var a=i.dataByCoordSys;if(a&&a.length)this._showAxisTooltip(a,i);else if(n){var h=D(n);if(h.ssrType==="legend")return;this._lastDataByCoordSys=null;var s,l;Mt(n,function(f){if(D(f).dataIndex!=null)return s=f,!0;if(D(f).tooltipConfig!=null)return l=f,!0},!0),s?this._showSeriesItemTooltip(i,s,e):l?this._showComponentItemTooltip(i,l,e):this._hide(e)}else this._lastDataByCoordSys=null,this._hide(e)}},t.prototype._showOrMove=function(i,e){var n=i.get("showDelay");e=I(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},t.prototype._showAxisTooltip=function(i,e){var n=this._ecModel,o=this._tooltipModel,a=[e.offsetX,e.offsetY],h=O([e.tooltipOption],o),s=this._renderMode,l=[],f=q("section",{blocks:[],noHeader:!0}),u=[],d=new Y;T(i,function(w){T(w.dataByAxis,function(m){var C=n.getComponent(m.axisDim+"Axis",m.axisIndex),S=m.value;if(!(!C||S==null)){var k=kt(S,C.axis,n,m.seriesDataIndices,m.valueLabelOpt),H=q("section",{header:k,noHeader:!Dt(k),sortBlocks:!0,blocks:[]});f.blocks.push(H),T(m.seriesDataIndices,function(z){var L=n.getSeriesByIndex(z.seriesIndex),F=z.dataIndexInside,x=L.getDataParams(F);if(!(x.dataIndex<0)){x.axisDim=m.axisDim,x.axisIndex=m.axisIndex,x.axisType=m.axisType,x.axisId=m.axisId,x.axisValue=It(C.axis,{value:S}),x.axisValueLabel=k,x.marker=d.makeTooltipMarker("item",B(x.color),s);var P=Z(L.formatTooltip(F,!0,null)),X=P.frag;if(X){var V=O([L],o).get("valueFormatter");H.blocks.push(V?J({valueFormatter:V},X):X)}P.text&&u.push(P.text),l.push(x)}})}})}),f.blocks.reverse(),u.reverse();var v=e.position,p=h.get("order"),g=Q(f,d,s,p,n.get("useUTC"),h.get("textStyle"));g&&u.unshift(g);var _=s==="richText"?`

`:"<br/>",c=u.join(_);this._showOrMove(h,function(){this._updateContentNotChangedOnAxis(i,l)?this._updatePosition(h,v,a[0],a[1],this._tooltipContent,l):this._showTooltipContent(h,c,l,Math.random()+"",a[0],a[1],v,null,d)})},t.prototype._showSeriesItemTooltip=function(i,e,n){var o=this._ecModel,a=D(e),h=a.seriesIndex,s=o.getSeriesByIndex(h),l=a.dataModel||s,f=a.dataIndex,u=a.dataType,d=l.getData(u),v=this._renderMode,p=i.positionDefault,g=O([d.getItemModel(f),l,s&&(s.coordinateSystem||{}).model],this._tooltipModel,p?{position:p}:null),_=g.get("trigger");if(!(_!=null&&_!=="item")){var c=l.getDataParams(f,u),w=new Y;c.marker=w.makeTooltipMarker("item",B(c.color),v);var m=Z(l.formatTooltip(f,!1,u)),C=g.get("order"),S=g.get("valueFormatter"),k=m.frag,H=k?Q(S?J({valueFormatter:S},k):k,w,v,C,o.get("useUTC"),g.get("textStyle")):m.text,z="item_"+l.name+"_"+f;this._showOrMove(g,function(){this._showTooltipContent(g,H,c,z,i.offsetX,i.offsetY,i.position,i.target,w)}),n({type:"showTip",dataIndexInside:f,dataIndex:d.getRawIndex(f),seriesIndex:h,from:this.uid})}},t.prototype._showComponentItemTooltip=function(i,e,n){var o=this._renderMode==="html",a=D(e),h=a.tooltipConfig,s=h.option||{},l=s.encodeHTMLContent;if(M(s)){var f=s;s={content:f,formatter:f},l=!0}l&&o&&s.content&&(s=G(s),s.content=Ot(s.content));var u=[s],d=this._ecModel.getComponent(a.componentMainType,a.componentIndex);d&&u.push(d),u.push({formatter:s.content});var v=i.positionDefault,p=O(u,this._tooltipModel,v?{position:v}:null),g=p.get("content"),_=Math.random()+"",c=new Y;this._showOrMove(p,function(){var w=G(p.get("formatterParams")||{});this._showTooltipContent(p,g,w,_,i.offsetX,i.offsetY,i.position,e,c)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(i,e,n,o,a,h,s,l,f){if(this._ticket="",!(!i.get("showContent")||!i.get("show"))){var u=this._tooltipContent;u.setEnterable(i.get("enterable"));var d=i.get("formatter");s=s||i.get("position");var v=e,p=this._getNearestPoint([a,h],n,i.get("trigger"),i.get("borderColor")),g=p.color;if(d)if(M(d)){var _=i.ecModel.get("useUTC"),c=R(n)?n[0]:n,w=c&&c.axisType&&c.axisType.indexOf("time")>=0;v=d,w&&(v=Rt(c.axisValue,v,_)),v=Ht(v,n,!0)}else if(N(d)){var m=I(function(C,S){C===this._ticket&&(u.setContent(S,f,i,g,s),this._updatePosition(i,s,a,h,u,n,l))},this);this._ticket=o,v=d(n,o,m)}else v=d;u.setContent(v,f,i,g,s),u.show(i,g),this._updatePosition(i,s,a,h,u,n,l)}},t.prototype._getNearestPoint=function(i,e,n,o){if(n==="axis"||R(e))return{color:o||(this._renderMode==="html"?"#fff":"none")};if(!R(e))return{color:o||e.color||e.borderColor}},t.prototype._updatePosition=function(i,e,n,o,a,h,s){var l=this._api.getWidth(),f=this._api.getHeight();e=e||i.get("position");var u=a.getSize(),d=i.get("align"),v=i.get("verticalAlign"),p=s&&s.getBoundingRect().clone();if(s&&p.applyTransform(s.transform),N(e)&&(e=e([n,o],h,a.el,p,{viewSize:[l,f],contentSize:u.slice()})),R(e))n=$(e[0],l),o=$(e[1],f);else if(ht(e)){var g=e;g.width=u[0],g.height=u[1];var _=zt(g,{width:l,height:f});n=_.x,o=_.y,d=null,v=null}else if(M(e)&&s){var c=ie(e,p,u,i.get("borderWidth"));n=c[0],o=c[1]}else{var c=te(n,o,a,l,f,d?null:20,v?null:20);n=c[0],o=c[1]}if(d&&(n-=rt(d)?u[0]/2:d==="right"?u[0]:0),v&&(o-=rt(v)?u[1]/2:v==="bottom"?u[1]:0),lt(i)){var c=ee(n,o,a,l,f);n=c[0],o=c[1]}a.moveTo(n,o)},t.prototype._updateContentNotChangedOnAxis=function(i,e){var n=this._lastDataByCoordSys,o=this._cbParamsList,a=!!n&&n.length===i.length;return a&&T(n,function(h,s){var l=h.dataByAxis||[],f=i[s]||{},u=f.dataByAxis||[];a=a&&l.length===u.length,a&&T(l,function(d,v){var p=u[v]||{},g=d.seriesDataIndices||[],_=p.seriesDataIndices||[];a=a&&d.value===p.value&&d.axisType===p.axisType&&d.axisId===p.axisId&&g.length===_.length,a&&T(g,function(c,w){var m=_[w];a=a&&c.seriesIndex===m.seriesIndex&&c.dataIndex===m.dataIndex}),o&&T(d.seriesDataIndices,function(c){var w=c.seriesIndex,m=e[w],C=o[w];m&&C&&C.data!==m.data&&(a=!1)})})}),this._lastDataByCoordSys=i,this._cbParamsList=e,!!a},t.prototype._hide=function(i){this._lastDataByCoordSys=null,i({type:"hideTip",from:this.uid})},t.prototype.dispose=function(i,e){y.node||!e.getDom()||(j(this,"_updatePosition"),this._tooltipContent.dispose(),At("itemTooltip",e))},t.type="tooltip",t}(Bt);function O(r,t,i){var e=t.ecModel,n;i?(n=new A(i,e,e),n=new A(t.option,n,e)):n=t;for(var o=r.length-1;o>=0;o--){var a=r[o];a&&(a instanceof A&&(a=a.get("tooltip",!0)),M(a)&&(a={formatter:a}),a&&(n=new A(a,n,e)))}return n}function ot(r,t){return r.dispatchAction||I(t.dispatchAction,t)}function te(r,t,i,e,n,o,a){var h=i.getSize(),s=h[0],l=h[1];return o!=null&&(r+s+o+2>e?r-=s+o:r+=o),a!=null&&(t+l+a>n?t-=l+a:t+=a),[r,t]}function ee(r,t,i,e,n){var o=i.getSize(),a=o[0],h=o[1];return r=Math.min(r+a,e)-a,t=Math.min(t+h,n)-h,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function ie(r,t,i,e){var n=i[0],o=i[1],a=Math.ceil(Math.SQRT2*e)+8,h=0,s=0,l=t.width,f=t.height;switch(r){case"inside":h=t.x+l/2-n/2,s=t.y+f/2-o/2;break;case"top":h=t.x+l/2-n/2,s=t.y-o-a;break;case"bottom":h=t.x+l/2-n/2,s=t.y+f+a;break;case"left":h=t.x-n-a,s=t.y+f/2-o/2;break;case"right":h=t.x+l+a,s=t.y+f/2-o/2}return[h,s]}function rt(r){return r==="center"||r==="middle"}function oe(r,t,i){var e=Lt(r).queryOptionMap,n=e.keys()[0];if(!(!n||n==="series")){var o=Pt(t,n,e.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),a=o.models[0];if(a){var h=i.getViewOfComponentModel(a),s;if(h.group.traverse(function(l){var f=D(l).tooltipConfig;if(f&&f.name===r.name)return s=l,!0}),s)return{componentMainType:n,componentIndex:a.componentIndex,el:s}}}}function ne(r){Xt(Yt),r.registerComponentModel(Nt),r.registerComponentView(Et),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},K),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},K)}export{ne as i};
