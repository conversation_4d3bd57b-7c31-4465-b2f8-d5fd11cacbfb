/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Zs=function(r,t){return Zs=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},Zs(r,t)};function O(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Zs(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var Ju=12,$g="sans-serif",zr=Ju+"px "+$g,Zg=20,qg=100,Kg="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function Qg(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-Zg)/qg;t[i]=n}return t}var Jg=Qg(Kg),Di={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=Di.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||zr),r.measureText(e);e=e||"",i=i||zr;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||Ju,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var u=0;u<e.length;u++){var l=Jg[e[u]];s+=l==null?o:l*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},Ac=rr(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),Lc=rr(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),Ln=Object.prototype.toString,no=Array.prototype,jg=no.forEach,ty=no.filter,ju=no.slice,ey=no.map,uf=(function(){}).constructor,On=uf?uf.prototype:null,tl="__proto__",ry=2311;function Pc(){return ry++}function el(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function J(r){if(r==null||typeof r!="object")return r;var t=r,e=Ln.call(r);if(e==="[object Array]"){if(!nn(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=J(r[i])}}else if(Lc[e]){if(!nn(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!Ac[e]&&!nn(r)&&!Ks(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==tl&&(t[o]=J(r[o]))}return t}function et(r,t,e){if(!G(t)||!G(r))return e?J(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==tl){var n=r[i],a=t[i];G(a)&&G(n)&&!F(a)&&!F(n)&&!Ks(a)&&!Ks(n)&&!lf(a)&&!lf(n)&&!nn(a)&&!nn(n)?et(n,a,e):(e||!(i in r))&&(r[i]=J(t[i]))}return r}function k(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==tl&&(r[e]=t[e]);return r}function rt(r,t,e){for(var i=lt(t),n=0,a=i.length;n<a;n++){var o=i[n];r[o]==null&&(r[o]=t[o])}return r}function ot(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function iy(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function De(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&r[a]==null&&(r[a]=t[a])}else rt(r,t)}function Nt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function M(r,t,e){if(r&&t)if(r.forEach&&r.forEach===jg)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function z(r,t,e){if(!r)return[];if(!t)return rl(r);if(r.map&&r.map===ey)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function rr(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function wt(r,t,e){if(!r)return[];if(!t)return rl(r);if(r.filter&&r.filter===ty)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function lt(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function ny(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(ju.call(arguments)))}}var gt=On&&Y(On.bind)?On.call.bind(On.bind):ny;function yt(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(ju.call(arguments)))}}function F(r){return Array.isArray?Array.isArray(r):Ln.call(r)==="[object Array]"}function Y(r){return typeof r=="function"}function V(r){return typeof r=="string"}function qs(r){return Ln.call(r)==="[object String]"}function ft(r){return typeof r=="number"}function G(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function lf(r){return!!Ac[Ln.call(r)]}function Ft(r){return!!Lc[Ln.call(r)]}function Ks(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function ao(r){return r.colorStops!=null}function ay(r){return r.image!=null}function ka(r){return r!==r}function yn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function $(r,t){return r??t}function _a(r,t,e){return r??t??e}function rl(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return ju.apply(r,t)}function Ic(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function xe(r,t){if(!r)throw new Error(t)}function Re(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var Ec="__ec_primitive__";function Qs(r){r[Ec]=!0}function nn(r){return r[Ec]}var oy=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return lt(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),Rc=typeof Map=="function";function sy(){return Rc?new Map:new oy}var uy=function(){function r(t){var e=F(t);this.data=sy();var i=this;t instanceof r?t.each(n):t&&M(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return Rc?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function X(r){return new uy(r)}function ly(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function oo(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&k(e,t),e}function kc(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Gr(r,t){return r.hasOwnProperty(t)}function te(){}var fy=180/Math.PI,hy=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),vy=function(){function r(){this.browser=new hy,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),Q=new vy;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(Q.wxa=!0,Q.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?Q.worker=!0:!Q.hasGlobalWindow||"Deno"in window?(Q.node=!0,Q.svgSupported=!0):cy(navigator.userAgent,Q);function cy(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}var py=".",fr="___EC__COMPONENT__CONTAINER___",Oc="___EC__EXTENDED_CLASS___";function me(r){var t={main:"",sub:""};if(r){var e=r.split(py);t.main=e[0]||"",t.sub=e[1]||""}return t}function dy(r){xe(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function gy(r){return!!(r&&r[Oc])}function il(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return yy(i)?n=function(a){O(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},iy(n,this)),k(n.prototype,e),n[Oc]=!0,n.extend=this.extend,n.superCall=Sy,n.superApply=wy,n.superClass=i,n}}function yy(r){return Y(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function Bc(r,t){r.extend=t.extend}var my=Math.round(Math.random()*10);function _y(r){var t=["__\0is_clz",my++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function Sy(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function wy(r,t,e){return this.superClass.prototype[t].apply(r,e)}function so(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){dy(n),i.prototype.type=n;var a=me(n);if(!a.sub)t[a.main]=i;else if(a.sub!==fr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[fr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=me(i),a=[],o=t[n.main];return o&&o[fr]?M(o,function(s,u){u!==fr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=me(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return M(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=me(i),a=t[n.main];return a&&a[fr]};function e(i){var n=t[i.main];return(!n||!n[fr])&&(n=t[i.main]={},n[fr]=!0),n}}function mn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var u=r[s][1];if(!(n&&ot(n,u)>=0||a&&ot(a,u)<0)){var l=i.getShallow(u,t);l!=null&&(o[r[s][0]]=l)}}return o}}var by=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],xy=mn(by),Ty=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return xy(this,t,e)},r}(),Nc=function(){function r(t){this.value=t}return r}(),Cy=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new Nc(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),Pn=function(){function r(t){this._list=new Cy,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var u=i.head;i.remove(u),delete n[u.key],a=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new Nc(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}(),Js=new Pn(50);function Dy(r){if(typeof r=="string"){var t=Js.get(r);return t&&t.image}else return r}function Fc(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=Js.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!uo(t)&&a.pending.push(o)):(t=Di.loadImage(r,ff,ff),t.__zrImageSrc=r,Js.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function ff(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function uo(r){return r&&r.width&&r.height}function vi(){return[1,0,0,1,0,0]}function nl(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function My(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function ci(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],u=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=u,r}function js(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function al(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],u=t[3],l=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+u*f,r[3]=-a*f+h*u,r[4]=h*(o-i[0])+f*(l-i[1])+i[0],r[5]=h*(l-i[1])-f*(o-i[0])+i[1],r}function Ay(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function ol(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],u=e*o-a*i;return u?(u=1/u,r[0]=o*u,r[1]=-a*u,r[2]=-i*u,r[3]=e*u,r[4]=(i*s-o*n)*u,r[5]=(a*n-e*s)*u,r):null}var at=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}(),Bn=Math.min,Nn=Math.max,hr=new at,vr=new at,cr=new at,pr=new at,Ei=new at,Ri=new at,j=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=Bn(t.x,this.x),i=Bn(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Nn(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Nn(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=vi();return js(a,a,[-e.x,-e.y]),Ay(a,a,[i,n]),js(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,u=t.x,l=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<u||l<n||s<f||h<o);if(e){var v=1/0,p=0,y=Math.abs(a-u),d=Math.abs(l-n),g=Math.abs(s-f),m=Math.abs(h-o),_=Math.min(y,d),S=Math.min(g,m);a<u||l<n?_>p&&(p=_,y<d?at.set(Ri,-y,0):at.set(Ri,d,0)):_<v&&(v=_,y<d?at.set(Ei,y,0):at.set(Ei,-d,0)),s<f||h<o?S>p&&(p=S,g<m?at.set(Ri,0,-g):at.set(Ri,0,m)):_<v&&(v=_,g<m?at.set(Ei,0,g):at.set(Ei,0,-m))}return e&&at.copy(e,c?Ei:Ri),c},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}hr.x=cr.x=e.x,hr.y=pr.y=e.y,vr.x=pr.x=e.x+e.width,vr.y=cr.y=e.y+e.height,hr.transform(i),pr.transform(i),vr.transform(i),cr.transform(i),t.x=Bn(hr.x,vr.x,cr.x,pr.x),t.y=Bn(hr.y,vr.y,cr.y,pr.y);var u=Nn(hr.x,vr.x,cr.x,pr.x),l=Nn(hr.y,vr.y,cr.y,pr.y);t.width=u-t.x,t.height=l-t.y},r}(),hf={};function Ut(r,t){t=t||zr;var e=hf[t];e||(e=hf[t]=new Pn(500));var i=e.get(r);return i==null&&(i=Di.measureText(r,t).width,e.put(r,i)),i}function vf(r,t,e,i){var n=Ut(r,t),a=ul(t),o=Qi(0,n,e),s=si(0,a,i),u=new j(o,s,n,a);return u}function sl(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return vf(n[0],t,e,i);for(var o=new j(0,0,0,0),s=0;s<n.length;s++){var u=vf(n[s],t,e,i);s===0?o.copy(u):o.union(u)}return o}function Qi(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function si(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function ul(r){return Ut("国",r)}function Hr(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function zc(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,u=e.x,l=e.y,f="left",h="top";if(i instanceof Array)u+=Hr(i[0],e.width),l+=Hr(i[1],e.height),f=null,h=null;else switch(i){case"left":u-=n,l+=s,f="right",h="middle";break;case"right":u+=n+o,l+=s,h="middle";break;case"top":u+=o/2,l-=n,f="center",h="bottom";break;case"bottom":u+=o/2,l+=a+n,f="center";break;case"inside":u+=o/2,l+=s,f="center",h="middle";break;case"insideLeft":u+=n,l+=s,h="middle";break;case"insideRight":u+=o-n,l+=s,f="right",h="middle";break;case"insideTop":u+=o/2,l+=n,f="center";break;case"insideBottom":u+=o/2,l+=a-n,f="center",h="bottom";break;case"insideTopLeft":u+=n,l+=n;break;case"insideTopRight":u+=o-n,l+=n,f="right";break;case"insideBottomLeft":u+=n,l+=a-n,h="bottom";break;case"insideBottomRight":u+=o-n,l+=a-n,f="right",h="bottom";break}return r=r||{},r.x=u,r.y=l,r.align=f,r.verticalAlign=h,r}var Ro=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Ly(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=Gc(e,i,n,a);for(var s=!1,u={},l=0,f=o.length;l<f;l++)Hc(u,o[l],a),o[l]=u.textLine,s=s||u.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function Gc(r,t,e,i){i=i||{};var n=k({},i);n.font=t,e=$(e,"..."),n.maxIterations=$(i.maxIterations,2);var a=n.minChar=$(i.minChar,0);n.cnCharWidth=Ut("国",t);var o=n.ascCharWidth=Ut("a",t);n.placeholder=$(i.placeholder,"");for(var s=r=Math.max(0,r-1),u=0;u<a&&s>=o;u++)s-=o;var l=Ut(e,t);return l>s&&(e="",l=0),s=r-l,n.ellipsis=e,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=r,n}function Hc(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var o=Ut(t,n);if(o<=i){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var u=s===0?Py(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,u),o=Ut(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function Py(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function Iy(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=ul(n),s=$(t.lineHeight,o),u=!!t.backgroundColor,l=t.lineOverflow==="truncate",f=!1,h=t.width,c;h!=null&&(e==="break"||e==="breakAll")?c=r?Vc(r,t.font,h,e==="breakAll",0).lines:[]:c=r?r.split(`
`):[];var v=c.length*s,p=$(t.height,v);if(v>p&&l){var y=Math.floor(p/s);f=f||c.length>y,c=c.slice(0,y)}if(r&&a&&h!=null)for(var d=Gc(h,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},m=0;m<c.length;m++)Hc(g,c[m],d),c[m]=g.textLine,f=f||g.isTruncated;for(var _=p,S=0,m=0;m<c.length;m++)S=Math.max(Ut(c[m],n),S);h==null&&(h=S);var b=S;return i&&(_+=i[0]+i[2],b+=i[1]+i[3],h+=i[1]+i[3]),u&&(b=h),{lines:c,height:p,outerWidth:b,outerHeight:_,lineHeight:s,calculatedLineHeight:o,contentWidth:S,contentHeight:v,width:h,isTruncated:f}}var Ey=function(){function r(){}return r}(),cf=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),Ry=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function ky(r,t){var e=new Ry;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=Ro.lastIndex=0,u;(u=Ro.exec(r))!=null;){var l=u.index;l>s&&ko(e,r.substring(s,l),t,o),ko(e,u[2],t,o,u[1]),s=Ro.lastIndex}s<r.length&&ko(e,r.substring(s,r.length),t,o);var f=[],h=0,c=0,v=t.padding,p=a==="truncate",y=t.lineOverflow==="truncate",d={};function g(W,Z,q){W.width=Z,W.lineHeight=q,h+=q,c=Math.max(c,Z)}t:for(var m=0;m<e.lines.length;m++){for(var _=e.lines[m],S=0,b=0,w=0;w<_.tokens.length;w++){var x=_.tokens[w],T=x.styleName&&t.rich[x.styleName]||{},C=x.textPadding=T.padding,D=C?C[1]+C[3]:0,A=x.font=T.font||t.font;x.contentHeight=ul(A);var L=$(T.height,x.contentHeight);if(x.innerHeight=L,C&&(L+=C[0]+C[2]),x.height=L,x.lineHeight=_a(T.lineHeight,t.lineHeight,L),x.align=T&&T.align||t.align,x.verticalAlign=T&&T.verticalAlign||"middle",y&&n!=null&&h+x.lineHeight>n){var I=e.lines.length;w>0?(_.tokens=_.tokens.slice(0,w),g(_,b,S),e.lines=e.lines.slice(0,m+1)):e.lines=e.lines.slice(0,m),e.isTruncated=e.isTruncated||e.lines.length<I;break t}var P=T.width,E=P==null||P==="auto";if(typeof P=="string"&&P.charAt(P.length-1)==="%")x.percentWidth=P,f.push(x),x.contentWidth=Ut(x.text,A);else{if(E){var R=T.backgroundColor,H=R&&R.image;H&&(H=Dy(H),uo(H)&&(x.width=Math.max(x.width,H.width*L/H.height)))}var B=p&&i!=null?i-b:null;B!=null&&B<x.width?!E||B<D?(x.text="",x.width=x.contentWidth=0):(Ly(d,x.text,B-D,A,t.ellipsis,{minChar:t.truncateMinChar}),x.text=d.text,e.isTruncated=e.isTruncated||d.isTruncated,x.width=x.contentWidth=Ut(x.text,A)):x.contentWidth=Ut(x.text,A)}x.width+=D,b+=x.width,T&&(S=Math.max(S,x.lineHeight))}g(_,b,S)}e.outerWidth=e.width=$(i,c),e.outerHeight=e.height=$(n,h),e.contentHeight=h,e.contentWidth=c,v&&(e.outerWidth+=v[1]+v[3],e.outerHeight+=v[0]+v[2]);for(var m=0;m<f.length;m++){var x=f[m],N=x.percentWidth;x.width=parseInt(N,10)/100*e.width}return e}function ko(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,u=o.font||e.font,l=!1,f,h;if(i){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var p=Hr(o.width,i.width)+v;s.length>0&&p+i.accumWidth>i.width&&(f=t.split(`
`),l=!0),i.accumWidth=p}else{var y=Vc(t,u,i.width,i.breakAll,i.accumWidth);i.accumWidth=y.accumWidth+v,h=y.linesWidths,f=y.lines}}else f=t.split(`
`);for(var d=0;d<f.length;d++){var g=f[d],m=new Ey;if(m.styleName=n,m.text=g,m.isLineHolder=!g&&!a,typeof o.width=="number"?m.width=o.width:m.width=h?h[d]:Ut(g,u),!d&&!l){var _=(s[s.length-1]||(s[0]=new cf)).tokens,S=_.length;S===1&&_[0].isLineHolder?_[0]=m:(g||!S||a)&&_.push(m)}else s.push(new cf([m]))}}function Oy(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var By=rr(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function Ny(r){return Oy(r)?!!By[r]:!0}function Vc(r,t,e,i,n){for(var a=[],o=[],s="",u="",l=0,f=0,h=0;h<r.length;h++){var c=r.charAt(h);if(c===`
`){u&&(s+=u,f+=l),a.push(s),o.push(f),s="",u="",l=0,f=0;continue}var v=Ut(c,t),p=i?!1:!Ny(c);if(a.length?f+v>e:n+f+v>e){f?(s||u)&&(p?(s||(s=u,u="",l=0,f=l),a.push(s),o.push(f-l),u+=c,l+=v,s="",f=l):(u&&(s+=u,u="",l=0),a.push(s),o.push(f),s=c,f=v)):p?(a.push(u),o.push(l),u=c,l=v):(a.push(c),o.push(v));continue}f+=v,p?(u+=c,l+=v):(u&&(s+=u,u="",l=0),s+=c)}return!a.length&&!s&&(s=r,u="",l=0),u&&(s+=u),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}function Mi(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function Fy(r){return[r[0],r[1]]}function pf(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function zy(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function Gy(r){return Math.sqrt(Hy(r))}function Hy(r){return r[0]*r[0]+r[1]*r[1]}function Oo(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function Vy(r,t){var e=Gy(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function tu(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var Wy=tu;function Uy(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var pi=Uy;function OC(r,t,e,i){return r[0]=t[0]+i*(e[0]-t[0]),r[1]=t[1]+i*(e[1]-t[1]),r}function ee(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function ui(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function li(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var df=nl,Yy=5e-5;function dr(r){return r>Yy||r<-5e-5}var gr=[],Xr=[],Bo=vi(),No=Math.abs,ll=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return dr(this.rotation)||dr(this.x)||dr(this.y)||dr(this.scaleX-1)||dr(this.scaleY-1)||dr(this.skewX)||dr(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(df(i),this.invTransform=null);return}i=i||vi(),e?this.getLocalTransform(i):df(i),t&&(e?ci(i,t,i):My(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(gr);var i=gr[0]<0?-1:1,n=gr[1]<0?-1:1,a=((gr[0]-i)*e+i)/gr[0]||0,o=((gr[1]-n)*e+n)/gr[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||vi(),ol(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||vi(),ci(Xr,t.invTransform,e),e=Xr);var i=this.originX,n=this.originY;(i||n)&&(Bo[4]=i,Bo[5]=n,ci(Xr,e,Bo),Xr[4]-=i,Xr[5]-=n,e=Xr),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&ee(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&ee(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&No(t[0]-1)>1e-10&&No(t[3]-1)>1e-10?Math.sqrt(No(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){Xy(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||u){var p=i+s,y=n+u;e[4]=-p*a-c*y*o,e[5]=-y*o-v*p*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=v*a,e[2]=c*o,l&&al(e,e,l),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),_n=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Xy(r,t){for(var e=0;e<_n.length;e++){var i=_n[e];r[i]=t[i]}}var an={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-an.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?an.bounceIn(r*2)*.5:an.bounceOut(r*2-1)*.5+.5}},Fn=Math.pow,Je=Math.sqrt,Wc=1e-8,Uc=1e-4,gf=Je(3),zn=1/3,ye=Mi(),Kt=Mi(),di=Mi();function Ke(r){return r>-1e-8&&r<Wc}function Yc(r){return r>Wc||r<-1e-8}function bt(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function yf(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function Oa(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),u=3*(t-r),l=r-n,f=s*s-3*o*u,h=s*u-9*o*l,c=u*u-3*s*l,v=0;if(Ke(f)&&Ke(h))if(Ke(s))a[0]=0;else{var p=-u/s;p>=0&&p<=1&&(a[v++]=p)}else{var y=h*h-4*f*c;if(Ke(y)){var d=h/f,p=-s/o+d,g=-d/2;p>=0&&p<=1&&(a[v++]=p),g>=0&&g<=1&&(a[v++]=g)}else if(y>0){var m=Je(y),_=f*s+1.5*o*(-h+m),S=f*s+1.5*o*(-h-m);_<0?_=-Fn(-_,zn):_=Fn(_,zn),S<0?S=-Fn(-S,zn):S=Fn(S,zn);var p=(-s-(_+S))/(3*o);p>=0&&p<=1&&(a[v++]=p)}else{var b=(2*f*s-3*o*h)/(2*Je(f*f*f)),w=Math.acos(b)/3,x=Je(f),T=Math.cos(w),p=(-s-2*x*T)/(3*o),g=(-s+x*(T+gf*Math.sin(w)))/(3*o),C=(-s+x*(T-gf*Math.sin(w)))/(3*o);p>=0&&p<=1&&(a[v++]=p),g>=0&&g<=1&&(a[v++]=g),C>=0&&C<=1&&(a[v++]=C)}}return v}function Xc(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,u=0;if(Ke(o)){if(Yc(a)){var l=-s/a;l>=0&&l<=1&&(n[u++]=l)}}else{var f=a*a-4*o*s;if(Ke(f))n[0]=-a/(2*o);else if(f>0){var h=Je(f),l=(-a+h)/(2*o),c=(-a-h)/(2*o);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function Ba(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,u=(i-e)*n+e,l=(s-o)*n+o,f=(u-s)*n+s,h=(f-l)*n+l;a[0]=r,a[1]=o,a[2]=l,a[3]=h,a[4]=h,a[5]=f,a[6]=u,a[7]=i}function $y(r,t,e,i,n,a,o,s,u,l,f){var h,c=.005,v=1/0,p,y,d,g;ye[0]=u,ye[1]=l;for(var m=0;m<1;m+=.05)Kt[0]=bt(r,e,n,o,m),Kt[1]=bt(t,i,a,s,m),d=pi(ye,Kt),d<v&&(h=m,v=d);v=1/0;for(var _=0;_<32&&!(c<Uc);_++)p=h-c,y=h+c,Kt[0]=bt(r,e,n,o,p),Kt[1]=bt(t,i,a,s,p),d=pi(Kt,ye),p>=0&&d<v?(h=p,v=d):(di[0]=bt(r,e,n,o,y),di[1]=bt(t,i,a,s,y),g=pi(di,ye),y<=1&&g<v?(h=y,v=g):c*=.5);return Je(v)}function Zy(r,t,e,i,n,a,o,s,u){for(var l=r,f=t,h=0,c=1/u,v=1;v<=u;v++){var p=v*c,y=bt(r,e,n,o,p),d=bt(t,i,a,s,p),g=y-l,m=d-f;h+=Math.sqrt(g*g+m*m),l=y,f=d}return h}function Ot(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function mf(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function qy(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,u=0;if(Ke(a)){if(Yc(o)){var l=-s/o;l>=0&&l<=1&&(n[u++]=l)}}else{var f=o*o-4*a*s;if(Ke(f)){var l=-o/(2*a);l>=0&&l<=1&&(n[u++]=l)}else if(f>0){var h=Je(f),l=(-o+h)/(2*a),c=(-o-h)/(2*a);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function $c(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function Na(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function Ky(r,t,e,i,n,a,o,s,u){var l,f=.005,h=1/0;ye[0]=o,ye[1]=s;for(var c=0;c<1;c+=.05){Kt[0]=Ot(r,e,n,c),Kt[1]=Ot(t,i,a,c);var v=pi(ye,Kt);v<h&&(l=c,h=v)}h=1/0;for(var p=0;p<32&&!(f<Uc);p++){var y=l-f,d=l+f;Kt[0]=Ot(r,e,n,y),Kt[1]=Ot(t,i,a,y);var v=pi(Kt,ye);if(y>=0&&v<h)l=y,h=v;else{di[0]=Ot(r,e,n,d),di[1]=Ot(t,i,a,d);var g=pi(di,ye);d<=1&&g<h?(l=d,h=g):f*=.5}}return Je(h)}function Qy(r,t,e,i,n,a,o){for(var s=r,u=t,l=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=Ot(r,e,n,c),p=Ot(t,i,a,c),y=v-s,d=p-u;l+=Math.sqrt(y*y+d*d),s=v,u=p}return l}var Jy=/cubic-bezier\(([0-9,\.e ]+)\)/;function Zc(r){var t=r&&Jy.exec(r);if(t){var e=t[1].split(","),i=+Re(e[0]),n=+Re(e[1]),a=+Re(e[2]),o=+Re(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(u){return u<=0?0:u>=1?1:Oa(0,i,a,1,u,s)&&bt(0,n,o,1,s[0])}}}var jy=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||te,this.ondestroy=t.ondestroy||te,this.onrestart=t.onrestart||te,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var u=n%i;this._startTime=t-u,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=Y(t)?t:an[t]||Zc(t)},r}(),_f={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function je(r){return r=Math.round(r),r<0?0:r>255?255:r}function Fa(r){return r<0?0:r>1?1:r}function Fo(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?je(parseFloat(t)/100*255):je(parseInt(t,10))}function on(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?Fa(parseFloat(t)/100):Fa(parseFloat(t))}function zo(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Gn(r,t,e){return r+(t-r)*e}function $t(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function eu(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var qc=new Pn(20),Hn=null;function $r(r,t){Hn&&eu(Hn,t),Hn=qc.put(r,Hn||t.slice())}function _e(r,t){if(r){t=t||[];var e=qc.get(r);if(e)return eu(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in _f)return eu(t,_f[i]),$r(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){$t(t,0,0,0,1);return}return $t(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),$r(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){$t(t,0,0,0,1);return}return $t(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),$r(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var u=i.substr(0,o),l=i.substr(o+1,s-(o+1)).split(","),f=1;switch(u){case"rgba":if(l.length!==4)return l.length===3?$t(t,+l[0],+l[1],+l[2],1):$t(t,0,0,0,1);f=on(l.pop());case"rgb":if(l.length>=3)return $t(t,Fo(l[0]),Fo(l[1]),Fo(l[2]),l.length===3?f:on(l[3])),$r(r,t),t;$t(t,0,0,0,1);return;case"hsla":if(l.length!==4){$t(t,0,0,0,1);return}return l[3]=on(l[3]),Sf(l,t),$r(r,t),t;case"hsl":if(l.length!==3){$t(t,0,0,0,1);return}return Sf(l,t),$r(r,t),t;default:return}}$t(t,0,0,0,1)}}function Sf(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=on(r[1]),n=on(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],$t(t,je(zo(o,a,e+1/3)*255),je(zo(o,a,e)*255),je(zo(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function wf(r,t){var e=_e(r);if(e){for(var i=0;i<3;i++)e[i]=e[i]*(1-t)|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return In(e,e.length===4?"rgba":"rgb")}}function tm(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=_e(t[n]),s=_e(t[a]),u=i-n,l=In([je(Gn(o[0],s[0],u)),je(Gn(o[1],s[1],u)),je(Gn(o[2],s[2],u)),Fa(Gn(o[3],s[3],u))],"rgba");return e?{color:l,leftIndex:n,rightIndex:a,value:i}:l}}function BC(r,t){var e=_e(r);if(e&&t!=null)return e[3]=Fa(t),In(e,"rgba")}function In(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function za(r,t){var e=_e(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var bf=new Pn(100);function xf(r){if(V(r)){var t=bf.get(r);return t||(t=wf(r,-.1),bf.put(r,t)),t}else if(ao(r)){var e=k({},r);return e.colorStops=z(r.colorStops,function(i){return{offset:i.offset,color:wf(i.color,-.1)}}),e}return r}function em(r){return r.type==="linear"}function rm(r){return r.type==="radial"}(function(){return Q.hasGlobalWindow&&Y(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var ru=Array.prototype.slice;function Ee(r,t,e){return(t-r)*e+r}function Go(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=Ee(t[a],e[a],i);return r}function im(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=Ee(t[o][s],e[o][s],i)}return r}function Vn(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function Tf(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function nm(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function am(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var u=a;u<o;u++)i.push(e===1?n[u]:ru.call(n[u]))}for(var l=i[0]&&i[0].length,u=0;u<i.length;u++)if(e===1)isNaN(i[u])&&(i[u]=n[u]);else for(var f=0;f<l;f++)isNaN(i[u][f])&&(i[u][f]=n[u][f])}}function Sa(r){if(Nt(r)){var t=r.length;if(Nt(r[0])){for(var e=[],i=0;i<t;i++)e.push(ru.call(r[i]));return e}return ru.call(r)}return r}function wa(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function om(r){return Nt(r&&r[0])?2:1}var Wn=0,ba=1,Kc=2,Ji=3,iu=4,nu=5,Cf=6;function Df(r){return r===iu||r===nu}function Un(r){return r===ba||r===Kc}var ki=[0,0,0,0],sm=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=Cf,u=e;if(Nt(e)){var l=om(e);s=l,(l===1&&!ft(e[0])||l===2&&!ft(e[0][0]))&&(o=!0)}else if(ft(e)&&!ka(e))s=Wn;else if(V(e))if(!isNaN(+e))s=Wn;else{var f=_e(e);f&&(u=f,s=Ji)}else if(ao(e)){var h=k({},u);h.colorStops=z(e.colorStops,function(v){return{offset:v.offset,color:_e(v.color)}}),em(e)?s=iu:rm(e)&&(s=nu),u=h}a===0?this.valType=s:(s!==this.valType||s===Cf)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:u,rawValue:e,percent:0};return i&&(c.easing=i,c.easingFunc=Y(i)?i:an[i]||Zc(i)),n.push(c),c},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(y,d){return y.time-d.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,u=Un(n),l=Df(n),f=0;f<a;f++){var h=i[f],c=h.value,v=o.value;h.percent=h.time/t,s||(u&&f!==a-1?am(c,v,n):l&&nm(c.colorStops,v.colorStops))}if(!s&&n!==nu&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var p=i[0].value,f=0;f<a;f++)n===Wn?i[f].additiveValue=i[f].value-p:n===Ji?i[f].additiveValue=Vn([],i[f].value,p,-1):Un(n)&&(i[f].additiveValue=n===ba?Vn([],i[f].value,p,-1):Tf([],i[f].value,p,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,u=this.propName,l=a===Ji,f,h=this._lastFr,c=Math.min,v,p;if(s===1)v=p=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var y=c(h+1,s-1);for(f=y;f>=0&&!(o[f].percent<=e);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=c(f-1,s-2)}p=o[f+1],v=o[f]}if(v&&p){this._lastFr=f,this._lastFrP=e;var d=p.percent-v.percent,g=d===0?1:c((e-v.percent)/d,1);p.easingFunc&&(g=p.easingFunc(g));var m=i?this._additiveValue:l?ki:t[u];if((Un(a)||l)&&!m&&(m=this._additiveValue=[]),this.discrete)t[u]=g<1?v.rawValue:p.rawValue;else if(Un(a))a===ba?Go(m,v[n],p[n],g):im(m,v[n],p[n],g);else if(Df(a)){var _=v[n],S=p[n],b=a===iu;t[u]={type:b?"linear":"radial",x:Ee(_.x,S.x,g),y:Ee(_.y,S.y,g),colorStops:z(_.colorStops,function(x,T){var C=S.colorStops[T];return{offset:Ee(x.offset,C.offset,g),color:wa(Go([],x.color,C.color,g))}}),global:S.global},b?(t[u].x2=Ee(_.x2,S.x2,g),t[u].y2=Ee(_.y2,S.y2,g)):t[u].r=Ee(_.r,S.r,g)}else if(l)Go(m,v[n],p[n],g),i||(t[u]=wa(m));else{var w=Ee(v[n],p[n],g);i?this._additiveValue=w:t[u]=w}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===Wn?t[i]=t[i]+n:e===Ji?(_e(t[i],ki),Vn(ki,ki,n,1),t[i]=wa(ki)):e===ba?Vn(t[i],t[i],n,1):e===Kc&&Tf(t[i],t[i],n,1)},r}(),fl=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){el("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,lt(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],u=a[s];if(!u){u=a[s]=new sm(s);var l=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];l=c&&c.value,f.valType===Ji&&l&&(l=wa(l))}else l=this._target[s];if(l==null)continue;t>0&&u.addKeyframe(0,Sa(l),n),this._trackKeys.push(s)}u.addKeyframe(t,Sa(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,f=l.length;if(s.prepare(n,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var c=new jy({life:n,loop:this._loop,delay:this._delay||0,onframe:function(v){e._started=2;var p=e._additiveAnimators;if(p){for(var y=!1,d=0;d<p.length;d++)if(p[d]._clip){y=!0;break}y||(e._additiveAnimators=null)}for(var d=0;d<i.length;d++)i[d].step(e._target,v);var g=e._onframeCbs;if(g)for(var d=0;d<g.length;d++)g[d](e._target,v)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return z(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,u=s[i?0:s.length-1];u&&(t[a]=Sa(u.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||lt(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}(),Me=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var u={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},l=a[t].length-1,f=a[t][l];return f&&f.callAtLast?a[t].splice(l,0,u):a[t].push(u),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,u=0;u<s;u++){var l=n[u];if(!(a&&a.filter&&l.query!=null&&!a.filter(t,l.query)))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],u=n.length,l=0;l<u;l++){var f=n[l];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}(),Qc=1;Q.hasGlobalWindow&&(Qc=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ga=Qc,au=.4,ou="#333",su="#ccc",um="#eee",Se=1,xa=2,ji=4,Ho="__zr_normal__",Vo=_n.concat(["ignore"]),lm=rr(_n,function(r,t){return r[t]=!0,r},{ignore:!1}),Zr={},fm=new j(0,0,0,0),lo=function(){function r(t){this.id=Pc(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,u=!1;a.parent=n?this:null;var l=!1;if(a.copyTransform(e),i.position!=null){var f=fm;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Zr,i,f):zc(Zr,i,f),a.x=Zr.x,a.y=Zr.y,o=Zr.align,s=Zr.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=Hr(h[0],f.width),v=Hr(h[1],f.height)),l=!0,a.originX=-a.x+c+(n?0:f.x),a.originY=-a.y+v+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var p=i.offset;p&&(a.x+=p[0],a.y+=p[1],l||(a.originX=-p[0],a.originY=-p[1]));var y=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,m=void 0,_=void 0;y&&this.canBeInsideText()?(g=i.insideFill,m=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(m==null||m==="auto")&&(m=this.getInsideTextStroke(g),_=!0)):(g=i.outsideFill,m=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(m==null||m==="auto")&&(m=this.getOutsideStroke(g),_=!0)),g=g||"#000",(g!==d.fill||m!==d.stroke||_!==d.autoStroke||o!==d.align||s!==d.verticalAlign)&&(u=!0,d.fill=g,d.stroke=m,d.autoStroke=_,d.align=o,d.verticalAlign=s,e.setDefaultTextStyle(d)),e.__dirty|=Se,u&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?su:ou},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&_e(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,In(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},k(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(G(t))for(var i=t,n=lt(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==Ho)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Vo)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Ho,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===Ho,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,u=this.stateTransition;if(!(ot(s,t)>=0&&(e||s.length===1))){var l;if(this.stateProxy&&!a&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),!l&&!a){el("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(l);var f=!!(l&&l.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!i&&!this.__inHover&&u&&u.duration>0,u);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,e,i,f),c&&c.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),l}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var u=0;u<o;u++)if(t[u]!==a[u]){s=!1;break}}if(s)return;for(var u=0;u<o;u++){var l=t[u],f=void 0;this.stateProxy&&(f=this.stateProxy(l,t)),f||(f=this.states[l]),f&&n.push(f)}var h=n[o-1],c=!!(h&&h.hoverLayer||i);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(n),p=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!e&&!this.__inHover&&p&&p.duration>0,p);var y=this._textContent,d=this._textGuide;y&&y.useStates(t,e,c),d&&d.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=ot(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=ot(n,t),o=ot(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];k(e,a),a.textConfig&&(i=i||{},k(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=k({},n?this.textConfig:i.textConfig),k(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var u={},l=!1,f=0;f<Vo.length;f++){var h=Vo[f],c=a&&lm[h];e&&e[h]!=null?c?(l=!0,u[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(c?(l=!0,u[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],p=v.targetName;v.getLoop()||v.__changeFinalValue(p?(e||i)[p]:e||i)}l&&this._transitionState(t,u,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new ll,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),k(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=Se;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new fl(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=ot(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){Wo(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){Wo(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=Wo(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=Se;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var u=this[n]=[];s(this,u)}return this[n]},set:function(u){this[a]=u[0],this[o]=u[1],this[n]=u,s(this,u)}});function s(u,l){Object.defineProperty(l,0,{get:function(){return u[a]},set:function(f){u[a]=f}}),Object.defineProperty(l,1,{get:function(){return u[o]},set:function(f){u[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();De(lo,Me);De(lo,ll);function Wo(r,t,e,i,n){e=e||{};var a=[];Jc(r,"",r,t,e,i,a,n);var o=a.length,s=!1,u=e.done,l=e.aborted,f=function(){s=!0,o--,o<=0&&(s?u&&u():l&&l())},h=function(){o--,o<=0&&(s?u&&u():l&&l())};o||u&&u(),a.length>0&&e.during&&a[0].during(function(p,y){e.during(y)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),e.force&&v.duration(e.duration),v.start(e.easing)}return a}function Uo(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function hm(r){return Nt(r[0])}function vm(r,t,e){if(Nt(t[e]))if(Nt(r[e])||(r[e]=[]),Ft(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),Uo(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(hm(n))for(var s=n[0].length,u=0;u<o;u++)a[u]?Uo(a[u],n[u],s):a[u]=Array.prototype.slice.call(n[u]);else Uo(a,n,o);a.length=n.length}else r[e]=t[e]}function cm(r,t){return r===t||Nt(r)&&Nt(t)&&pm(r,t)}function pm(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function Jc(r,t,e,i,n,a,o,s){for(var u=lt(i),l=n.duration,f=n.delay,h=n.additive,c=n.setToFinal,v=!G(a),p=r.animators,y=[],d=0;d<u.length;d++){var g=u[d],m=i[g];if(m!=null&&e[g]!=null&&(v||a[g]))if(G(m)&&!Nt(m)&&!ao(m)){if(t){s||(e[g]=m,r.updateDuringAnimation(t));continue}Jc(r,g,e[g],m,n,a&&a[g],o,s)}else y.push(g);else s||(e[g]=m,r.updateDuringAnimation(t),y.push(g))}var _=y.length;if(!h&&_)for(var S=0;S<p.length;S++){var b=p[S];if(b.targetName===t){var w=b.stopTracks(y);if(w){var x=ot(p,b);p.splice(x,1)}}}if(n.force||(y=wt(y,function(A){return!cm(i[A],e[A])}),_=y.length),_>0||n.force&&!o.length){var T=void 0,C=void 0,D=void 0;if(s){C={},c&&(T={});for(var S=0;S<_;S++){var g=y[S];C[g]=e[g],c?T[g]=i[g]:e[g]=i[g]}}else if(c){D={};for(var S=0;S<_;S++){var g=y[S];D[g]=Sa(e[g]),vm(e,i,g)}}var b=new fl(e,!1,!1,h?wt(p,function(L){return L.targetName===t}):null);b.targetName=t,n.scope&&(b.scope=n.scope),c&&T&&b.whenWithKeys(0,T,y),D&&b.whenWithKeys(0,D,y),b.whenWithKeys(l??500,s?C:i,y).delay(f||0),r.addAnimator(b,t),o.push(b)}}var uu="__zr_style_"+Math.round(Math.random()*10),Br={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},fo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Br[uu]=!0;var Mf=["z","z2","invisible"],dm=["invisible"],En=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=lt(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&gm(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var u=this.parent;u;){if(u.ignore)return!1;u=u.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,u=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new j(0,0,0,0)),i?j.applyTransform(e,n,i):e.copy(n),(o||s||u)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(u),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+u-o));var l=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-l),e.y=Math.floor(e.y-l),e.width=Math.ceil(e.width+1+l*2),e.height=Math.ceil(e.height+1+l*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new j(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:k(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=xa,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&xa)},t.prototype.styleUpdated=function(){this.__dirty&=-3},t.prototype.createStyle=function(e){return oo(Br,e)},t.prototype.useStyle=function(e){e[uu]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[uu]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,Mf)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.style?o?a?l=i.style:(l=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(l,i.style)):(l=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(l,i.style)):u&&(l=n.style),l)if(o){var f=this.style;if(this.style=this.createStyle(u?{}:f),u)for(var h=lt(f),c=0;c<h.length;c++){var v=h[c];v in l&&(l[v]=l[v],this.style[v]=f[v])}for(var p=lt(l),c=0;c<p.length;c++){var v=p[c];this.style[v]=this.style[v]}this._transitionState(e,{style:l},s,this.getAnimationStyleProps())}else this.useStyle(l);for(var y=this.__inHover?dm:Mf,c=0;c<y.length;c++){var v=y[c];i&&i[v]!=null?this[v]=i[v]:u&&n[v]!=null&&(this[v]=n[v])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return k(e,i),e},t.prototype.getAnimationStyleProps=function(){return fo},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=Se|xa}(),t}(lo),Yo=new j(0,0,0,0),Xo=new j(0,0,0,0);function gm(r,t,e){return Yo.copy(r.getBoundingRect()),r.transform&&Yo.applyTransform(r.transform),Xo.width=t,Xo.height=e,!Yo.intersect(Xo)}var Qt=Math.min,Jt=Math.max,$o=Math.sin,Zo=Math.cos,yr=Math.PI*2,Yn=Mi(),Xn=Mi(),$n=Mi();function Af(r,t,e,i,n,a){n[0]=Qt(r,e),n[1]=Qt(t,i),a[0]=Jt(r,e),a[1]=Jt(t,i)}var Lf=[],Pf=[];function ym(r,t,e,i,n,a,o,s,u,l){var f=Xc,h=bt,c=f(r,e,n,o,Lf);u[0]=1/0,u[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var v=0;v<c;v++){var p=h(r,e,n,o,Lf[v]);u[0]=Qt(p,u[0]),l[0]=Jt(p,l[0])}c=f(t,i,a,s,Pf);for(var v=0;v<c;v++){var y=h(t,i,a,s,Pf[v]);u[1]=Qt(y,u[1]),l[1]=Jt(y,l[1])}u[0]=Qt(r,u[0]),l[0]=Jt(r,l[0]),u[0]=Qt(o,u[0]),l[0]=Jt(o,l[0]),u[1]=Qt(t,u[1]),l[1]=Jt(t,l[1]),u[1]=Qt(s,u[1]),l[1]=Jt(s,l[1])}function mm(r,t,e,i,n,a,o,s){var u=$c,l=Ot,f=Jt(Qt(u(r,e,n),1),0),h=Jt(Qt(u(t,i,a),1),0),c=l(r,e,n,f),v=l(t,i,a,h);o[0]=Qt(r,n,c),o[1]=Qt(t,a,v),s[0]=Jt(r,n,c),s[1]=Jt(t,a,v)}function _m(r,t,e,i,n,a,o,s,u){var l=ui,f=li,h=Math.abs(n-a);if(h%yr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,u[0]=r+e,u[1]=t+i;return}if(Yn[0]=Zo(n)*e+r,Yn[1]=$o(n)*i+t,Xn[0]=Zo(a)*e+r,Xn[1]=$o(a)*i+t,l(s,Yn,Xn),f(u,Yn,Xn),n=n%yr,n<0&&(n=n+yr),a=a%yr,a<0&&(a=a+yr),n>a&&!o?a+=yr:n<a&&o&&(n+=yr),o){var c=a;a=n,n=c}for(var v=0;v<a;v+=Math.PI/2)v>n&&($n[0]=Zo(v)*e+r,$n[1]=$o(v)*i+t,l(s,$n,s),f(u,$n,u))}var tt={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},mr=[],_r=[],ce=[],Ge=[],pe=[],de=[],qo=Math.min,Ko=Math.max,Sr=Math.cos,wr=Math.sin,Pe=Math.abs,lu=Math.PI,qe=lu*2,Qo=typeof Float32Array<"u",Oi=[];function Jo(r){var t=Math.round(r/lu*1e8)/1e8;return t%2*lu}function Sm(r,t){var e=Jo(r[0]);e<0&&(e+=qe);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=qe?n=e+qe:t&&e-n>=qe?n=e-qe:!t&&e>n?n=e+(qe-Jo(e-n)):t&&e<n&&(n=e-(qe-Jo(n-e))),r[0]=e,r[1]=n}var Vr=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=Pe(i/Ga/t)||0,this._uy=Pe(i/Ga/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(tt.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=Pe(t-this._xi),n=Pe(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(tt.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(tt.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(tt.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Oi[0]=n,Oi[1]=a,Sm(Oi,o),n=Oi[0],a=Oi[1];var s=a-n;return this.addData(tt.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=Sr(a)*i+t,this._yi=wr(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(tt.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(tt.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&Qo&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();Qo&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,u,l){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Qo&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){ce[0]=ce[1]=pe[0]=pe[1]=Number.MAX_VALUE,Ge[0]=Ge[1]=de[0]=de[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],u=o===1;switch(u&&(e=t[o],i=t[o+1],n=e,a=i),s){case tt.M:e=n=t[o++],i=a=t[o++],pe[0]=n,pe[1]=a,de[0]=n,de[1]=a;break;case tt.L:Af(e,i,t[o],t[o+1],pe,de),e=t[o++],i=t[o++];break;case tt.C:ym(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],pe,de),e=t[o++],i=t[o++];break;case tt.Q:mm(e,i,t[o++],t[o++],t[o],t[o+1],pe,de),e=t[o++],i=t[o++];break;case tt.A:var l=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],p=t[o++]+v;o+=1;var y=!t[o++];u&&(n=Sr(v)*h+l,a=wr(v)*c+f),_m(l,f,h,c,v,p,y,pe,de),e=Sr(p)*h+l,i=wr(p)*c+f;break;case tt.R:n=e=t[o++],a=i=t[o++];var d=t[o++],g=t[o++];Af(n,a,n+d,a+g,pe,de);break;case tt.Z:e=n,i=a;break}ui(ce,ce,pe),li(Ge,Ge,de)}return o===0&&(ce[0]=ce[1]=Ge[0]=Ge[1]=0),new j(ce[0],ce[1],Ge[0]-ce[0],Ge[1]-ce[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,f=0,h=0,c=0;c<e;){var v=t[c++],p=c===1;p&&(a=t[c],o=t[c+1],s=a,u=o);var y=-1;switch(v){case tt.M:a=s=t[c++],o=u=t[c++];break;case tt.L:{var d=t[c++],g=t[c++],m=d-a,_=g-o;(Pe(m)>i||Pe(_)>n||c===e-1)&&(y=Math.sqrt(m*m+_*_),a=d,o=g);break}case tt.C:{var S=t[c++],b=t[c++],d=t[c++],g=t[c++],w=t[c++],x=t[c++];y=Zy(a,o,S,b,d,g,w,x,10),a=w,o=x;break}case tt.Q:{var S=t[c++],b=t[c++],d=t[c++],g=t[c++];y=Qy(a,o,S,b,d,g,10),a=d,o=g;break}case tt.A:var T=t[c++],C=t[c++],D=t[c++],A=t[c++],L=t[c++],I=t[c++],P=I+L;c+=1,p&&(s=Sr(L)*D+T,u=wr(L)*A+C),y=Ko(D,A)*qo(qe,Math.abs(I)),a=Sr(P)*D+T,o=wr(P)*A+C;break;case tt.R:{s=a=t[c++],u=o=t[c++];var E=t[c++],R=t[c++];y=E*2+R*2;break}case tt.Z:{var m=s-a,_=u-o;y=Math.sqrt(m*m+_*_),a=s,o=u;break}}y>=0&&(l[h++]=y,f+=y)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,u,l,f,h,c,v=e<1,p,y,d=0,g=0,m,_=0,S,b;if(!(v&&(this._pathSegLen||this._calculateLength(),p=this._pathSegLen,y=this._pathLen,m=e*y,!m)))t:for(var w=0;w<o;){var x=i[w++],T=w===1;switch(T&&(l=i[w],f=i[w+1],s=l,u=f),x!==tt.L&&_>0&&(t.lineTo(S,b),_=0),x){case tt.M:s=l=i[w++],u=f=i[w++],t.moveTo(l,f);break;case tt.L:{h=i[w++],c=i[w++];var C=Pe(h-l),D=Pe(c-f);if(C>n||D>a){if(v){var A=p[g++];if(d+A>m){var L=(m-d)/A;t.lineTo(l*(1-L)+h*L,f*(1-L)+c*L);break t}d+=A}t.lineTo(h,c),l=h,f=c,_=0}else{var I=C*C+D*D;I>_&&(S=h,b=c,_=I)}break}case tt.C:{var P=i[w++],E=i[w++],R=i[w++],H=i[w++],B=i[w++],N=i[w++];if(v){var A=p[g++];if(d+A>m){var L=(m-d)/A;Ba(l,P,R,B,L,mr),Ba(f,E,H,N,L,_r),t.bezierCurveTo(mr[1],_r[1],mr[2],_r[2],mr[3],_r[3]);break t}d+=A}t.bezierCurveTo(P,E,R,H,B,N),l=B,f=N;break}case tt.Q:{var P=i[w++],E=i[w++],R=i[w++],H=i[w++];if(v){var A=p[g++];if(d+A>m){var L=(m-d)/A;Na(l,P,R,L,mr),Na(f,E,H,L,_r),t.quadraticCurveTo(mr[1],_r[1],mr[2],_r[2]);break t}d+=A}t.quadraticCurveTo(P,E,R,H),l=R,f=H;break}case tt.A:var W=i[w++],Z=i[w++],q=i[w++],it=i[w++],ut=i[w++],ht=i[w++],ie=i[w++],ur=!i[w++],Yr=q>it?q:it,Gt=Pe(q-it)>.001,_t=ut+ht,U=!1;if(v){var A=p[g++];d+A>m&&(_t=ut+ht*(m-d)/A,U=!0),d+=A}if(Gt&&t.ellipse?t.ellipse(W,Z,q,it,ie,ut,_t,ur):t.arc(W,Z,Yr,ut,_t,ur),U)break t;T&&(s=Sr(ut)*q+W,u=wr(ut)*it+Z),l=Sr(_t)*q+W,f=wr(_t)*it+Z;break;case tt.R:s=l=i[w],u=f=i[w+1],h=i[w++],c=i[w++];var K=i[w++],lr=i[w++];if(v){var A=p[g++];if(d+A>m){var Lt=m-d;t.moveTo(h,c),t.lineTo(h+qo(Lt,K),c),Lt-=K,Lt>0&&t.lineTo(h+K,c+qo(Lt,lr)),Lt-=lr,Lt>0&&t.lineTo(h+Ko(K-Lt,0),c+lr),Lt-=K,Lt>0&&t.lineTo(h,c+Ko(lr-Lt,0));break t}d+=A}t.rect(h,c,K,lr);break;case tt.Z:if(v){var A=p[g++];if(d+A>m){var L=(m-d)/A;t.lineTo(l*(1-L)+s*L,f*(1-L)+u*L);break t}d+=A}t.closePath(),l=s,f=u}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=tt,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function qr(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,u=0,l=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)u=(t-i)/(r-e),l=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=u*a-o+l,h=f*f/(u*u+1);return h<=s/2*s/2}function wm(r,t,e,i,n,a,o,s,u,l,f){if(u===0)return!1;var h=u;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||l>r+h&&l>e+h&&l>n+h&&l>o+h||l<r-h&&l<e-h&&l<n-h&&l<o-h)return!1;var c=$y(r,t,e,i,n,a,o,s,l,f);return c<=h/2}function bm(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;if(u>t+l&&u>i+l&&u>a+l||u<t-l&&u<i-l&&u<a-l||s>r+l&&s>e+l&&s>n+l||s<r-l&&s<e-l&&s<n-l)return!1;var f=Ky(r,t,e,i,n,a,s,u);return f<=l/2}var If=Math.PI*2;function Zn(r){return r%=If,r<0&&(r+=If),r}var Bi=Math.PI*2;function xm(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;s-=r,u-=t;var f=Math.sqrt(s*s+u*u);if(f-l>e||f+l<e)return!1;if(Math.abs(i-n)%Bi<1e-4)return!0;if(a){var h=i;i=Zn(n),n=Zn(h)}else i=Zn(i),n=Zn(n);i>n&&(n+=Bi);var c=Math.atan2(u,s);return c<0&&(c+=Bi),c>=i&&c<=n||c+Bi>=i&&c+Bi<=n}function br(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var u=o*(e-r)+r;return u===n?1/0:u>n?s:0}var He=Vr.CMD,xr=Math.PI*2,Tm=1e-4;function Cm(r,t){return Math.abs(r-t)<Tm}var Pt=[-1,-1,-1],qt=[-1,-1];function Dm(){var r=qt[0];qt[0]=qt[1],qt[1]=r}function Mm(r,t,e,i,n,a,o,s,u,l){if(l>t&&l>i&&l>a&&l>s||l<t&&l<i&&l<a&&l<s)return 0;var f=Oa(t,i,a,s,l,Pt);if(f===0)return 0;for(var h=0,c=-1,v=void 0,p=void 0,y=0;y<f;y++){var d=Pt[y],g=d===0||d===1?.5:1,m=bt(r,e,n,o,d);m<u||(c<0&&(c=Xc(t,i,a,s,qt),qt[1]<qt[0]&&c>1&&Dm(),v=bt(t,i,a,s,qt[0]),c>1&&(p=bt(t,i,a,s,qt[1]))),c===2?d<qt[0]?h+=v<t?g:-g:d<qt[1]?h+=p<v?g:-g:h+=s<p?g:-g:d<qt[0]?h+=v<t?g:-g:h+=s<v?g:-g)}return h}function Am(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var u=qy(t,i,a,s,Pt);if(u===0)return 0;var l=$c(t,i,a);if(l>=0&&l<=1){for(var f=0,h=Ot(t,i,a,l),c=0;c<u;c++){var v=Pt[c]===0||Pt[c]===1?.5:1,p=Ot(r,e,n,Pt[c]);p<o||(Pt[c]<l?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=Pt[0]===0||Pt[0]===1?.5:1,p=Ot(r,e,n,Pt[0]);return p<o?0:a<t?v:-v}}function Lm(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var u=Math.sqrt(e*e-s*s);Pt[0]=-u,Pt[1]=u;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=xr-1e-4){i=0,n=xr;var f=a?1:-1;return o>=Pt[0]+r&&o<=Pt[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=xr,n+=xr);for(var c=0,v=0;v<2;v++){var p=Pt[v];if(p+r>o){var y=Math.atan2(s,p),f=a?1:-1;y<0&&(y=xr+y),(y>=i&&y<=n||y+xr>=i&&y+xr<=n)&&(y>Math.PI/2&&y<Math.PI*1.5&&(f=-f),c+=f)}}return c}function jc(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,u=0,l=0,f=0,h=0,c,v,p=0;p<o;){var y=a[p++],d=p===1;switch(y===He.M&&p>1&&(e||(s+=br(u,l,f,h,i,n))),d&&(u=a[p],l=a[p+1],f=u,h=l),y){case He.M:f=a[p++],h=a[p++],u=f,l=h;break;case He.L:if(e){if(qr(u,l,a[p],a[p+1],t,i,n))return!0}else s+=br(u,l,a[p],a[p+1],i,n)||0;u=a[p++],l=a[p++];break;case He.C:if(e){if(wm(u,l,a[p++],a[p++],a[p++],a[p++],a[p],a[p+1],t,i,n))return!0}else s+=Mm(u,l,a[p++],a[p++],a[p++],a[p++],a[p],a[p+1],i,n)||0;u=a[p++],l=a[p++];break;case He.Q:if(e){if(bm(u,l,a[p++],a[p++],a[p],a[p+1],t,i,n))return!0}else s+=Am(u,l,a[p++],a[p++],a[p],a[p+1],i,n)||0;u=a[p++],l=a[p++];break;case He.A:var g=a[p++],m=a[p++],_=a[p++],S=a[p++],b=a[p++],w=a[p++];p+=1;var x=!!(1-a[p++]);c=Math.cos(b)*_+g,v=Math.sin(b)*S+m,d?(f=c,h=v):s+=br(u,l,c,v,i,n);var T=(i-g)*S/_+g;if(e){if(xm(g,m,S,b,b+w,x,t,T,n))return!0}else s+=Lm(g,m,S,b,b+w,x,T,n);u=Math.cos(b+w)*_+g,l=Math.sin(b+w)*S+m;break;case He.R:f=u=a[p++],h=l=a[p++];var C=a[p++],D=a[p++];if(c=f+C,v=h+D,e){if(qr(f,h,c,h,t,i,n)||qr(c,h,c,v,t,i,n)||qr(c,v,f,v,t,i,n)||qr(f,v,f,h,t,i,n))return!0}else s+=br(c,h,c,v,i,n),s+=br(f,v,f,h,i,n);break;case He.Z:if(e){if(qr(u,l,f,h,t,i,n))return!0}else s+=br(u,l,f,h,i,n);u=f,l=h;break}}return!e&&!Cm(l,h)&&(s+=br(u,l,f,h,i,n)||0),s!==0}function Pm(r,t,e){return jc(r,0,!1,t,e)}function Im(r,t,e,i){return jc(r,t,!0,e,i)}var tp=rt({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Br),Em={style:rt({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},fo.style)},jo=_n.concat(["invisible","culling","z","z2","zlevel","parent"]),st=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(u){e.buildPath(u,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<jo.length;++s)n[jo[s]]=this[jo[s]];n.__dirty|=Se}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=lt(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?k(this.style,s):this.useStyle(s):o==="shape"?k(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(V(e)){var i=za(e,0);return i>.5?ou:i>.2?um:su}else if(e)return su}return ou},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(V(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=za(e,0)<au;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=-5},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new Vr(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&ji)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var u=i.strokeNoScale?this.getLineScale():1,l=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;l=Math.max(l,f??4)}u>1e-10&&(s.width+=l/u,s.height+=l/u,s.x-=l/u/2,s.y-=l/u/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var u=o.lineWidth,l=o.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(this.hasFill()||(u=Math.max(u,this.strokeContainThreshold)),Im(s,u/l,e,i)))return!0}if(this.hasFill())return Pm(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=ji,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:k(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&ji)},t.prototype.createStyle=function(e){return oo(tp,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=k({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.shape?o?a?l=i.shape:(l=k({},n.shape),k(l,i.shape)):(l=k({},a?this.shape:n.shape),k(l,i.shape)):u&&(l=n.shape),l)if(o){this.shape=k({},this.shape);for(var f={},h=lt(l),c=0;c<h.length;c++){var v=h[c];typeof l[v]=="object"?this.shape[v]=l[v]:f[v]=l[v]}this._transitionState(e,{shape:f},s)}else this.shape=l,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return Em},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){O(o,a);function o(s){var u=a.call(this,s)||this;return e.init&&e.init.call(u,s),u}return o.prototype.getDefaultStyle=function(){return J(e.style)},o.prototype.getDefaultShape=function(){return J(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=Se|xa|ji}(),t}(En),Rm=rt({strokeFirst:!0,font:zr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},tp),Ha=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return oo(Rm,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=sl(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(En);Ha.prototype.type="tspan";var km=rt({x:0,y:0},Br),Om={style:rt({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},fo.style)};function Bm(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var sr=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return oo(km,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=Bm(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return Om},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new j(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(En);sr.prototype.type="image";function Nm(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,u,l,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=u=l=f=o:o instanceof Array?o.length===1?s=u=l=f=o[0]:o.length===2?(s=l=o[0],u=f=o[1]):o.length===3?(s=o[0],u=f=o[1],l=o[2]):(s=o[0],u=o[1],l=o[2],f=o[3]):s=u=l=f=0;var h;s+u>n&&(h=s+u,s*=n/h,u*=n/h),l+f>n&&(h=l+f,l*=n/h,f*=n/h),u+l>a&&(h=u+l,u*=a/h,l*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-u,i),u!==0&&r.arc(e+n-u,i+u,u,-Math.PI/2,0),r.lineTo(e+n,i+a-l),l!==0&&r.arc(e+n-l,i+a-l,l,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var fi=Math.round;function ep(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(fi(i*2)===fi(n*2)&&(r.x1=r.x2=Rr(i,s,!0)),fi(a*2)===fi(o*2)&&(r.y1=r.y2=Rr(a,s,!0))),r}}function rp(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Rr(i,s,!0),r.y=Rr(n,s,!0),r.width=Math.max(Rr(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Rr(n+o,s,!1)-r.y,o===0?0:1)),r}}function Rr(r,t,e){if(!t)return r;var i=fi(r*2);return(i+fi(t))%2===0?i/2:(i+(e?1:-1))/2}var Fm=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),zm={},xt=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Fm},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=rp(zm,i,this.style);n=u.x,a=u.y,o=u.width,s=u.height,u.r=i.r,i=u}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?Nm(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(st);xt.prototype.type="rect";var Ef={fill:"#000"},Rf=2,Gm={style:rt({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},fo.style)},Et=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=Ef,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,Ym(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new j(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],u=s.getBoundingRect(),l=s.getLocalTransform(n);l?(e.copy(u),e.applyTransform(l),a=a||e.clone(),a.union(e)):(a=a||u.clone(),a.union(u))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Ef},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return k(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=lt(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},k(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return Gm},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||zr,n=e.padding,a=Gf(e),o=Iy(a,e),s=ts(e),u=!!e.backgroundColor,l=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,p=this._defaultStyle;this.isTruncated=!!o.isTruncated;var y=e.x||0,d=e.y||0,g=e.align||p.align||"left",m=e.verticalAlign||p.verticalAlign||"top",_=y,S=si(d,o.contentHeight,m);if(s||n){var b=Qi(y,f,g),w=si(d,l,m);s&&this._renderBackground(e,e,b,w,f,l)}S+=v/2,n&&(_=zf(y,g,n),m==="top"?S+=n[0]:m==="bottom"&&(S-=n[2]));for(var x=0,T=!1,C=Ff("fill"in e?e.fill:(T=!0,p.fill)),D=Nf("stroke"in e?e.stroke:!u&&(!p.autoStroke||T)?(x=Rf,p.stroke):null),A=e.textShadowBlur>0,L=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),I=o.calculatedLineHeight,P=0;P<c.length;P++){var E=this._getOrCreateChild(Ha),R=E.createStyle();E.useStyle(R),R.text=c[P],R.x=_,R.y=S,R.textAlign=g,R.textBaseline="middle",R.opacity=e.opacity,R.strokeFirst=!0,A&&(R.shadowBlur=e.textShadowBlur||0,R.shadowColor=e.textShadowColor||"transparent",R.shadowOffsetX=e.textShadowOffsetX||0,R.shadowOffsetY=e.textShadowOffsetY||0),R.stroke=D,R.fill=C,D&&(R.lineWidth=e.lineWidth||x,R.lineDash=e.lineDash,R.lineDashOffset=e.lineDashOffset||0),R.font=i,Of(R,e),S+=v,L&&E.setBoundingRect(new j(Qi(R.x,h,R.textAlign),si(R.y,I,R.textBaseline),h,I))}},t.prototype._updateRichTexts=function(){var e=this.style,i=Gf(e),n=ky(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,u=e.padding,l=e.x||0,f=e.y||0,h=this._defaultStyle,c=e.align||h.align,v=e.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var p=Qi(l,o,c),y=si(f,s,v),d=p,g=y;u&&(d+=u[3],g+=u[0]);var m=d+a;ts(e)&&this._renderBackground(e,e,p,y,o,s);for(var _=!!e.backgroundColor,S=0;S<n.lines.length;S++){for(var b=n.lines[S],w=b.tokens,x=w.length,T=b.lineHeight,C=b.width,D=0,A=d,L=m,I=x-1,P=void 0;D<x&&(P=w[D],!P.align||P.align==="left");)this._placeToken(P,e,T,g,A,"left",_),C-=P.width,A+=P.width,D++;for(;I>=0&&(P=w[I],P.align==="right");)this._placeToken(P,e,T,g,L,"right",_),C-=P.width,L-=P.width,I--;for(A+=(a-(A-d)-(m-L)-C)/2;D<=I;)P=w[D],this._placeToken(P,e,T,g,A+P.width/2,"center",_),A+=P.width,D++;g+=T}},t.prototype._placeToken=function(e,i,n,a,o,s,u){var l=i.rich[e.styleName]||{};l.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var c=!e.isLineHolder&&ts(l);c&&this._renderBackground(l,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var v=!!l.backgroundColor,p=e.textPadding;p&&(o=zf(o,s,p),h-=e.height/2-p[0]-e.innerHeight/2);var y=this._getOrCreateChild(Ha),d=y.createStyle();y.useStyle(d);var g=this._defaultStyle,m=!1,_=0,S=Ff("fill"in l?l.fill:"fill"in i?i.fill:(m=!0,g.fill)),b=Nf("stroke"in l?l.stroke:"stroke"in i?i.stroke:!v&&!u&&(!g.autoStroke||m)?(_=Rf,g.stroke):null),w=l.textShadowBlur>0||i.textShadowBlur>0;d.text=e.text,d.x=o,d.y=h,w&&(d.shadowBlur=l.textShadowBlur||i.textShadowBlur||0,d.shadowColor=l.textShadowColor||i.textShadowColor||"transparent",d.shadowOffsetX=l.textShadowOffsetX||i.textShadowOffsetX||0,d.shadowOffsetY=l.textShadowOffsetY||i.textShadowOffsetY||0),d.textAlign=s,d.textBaseline="middle",d.font=e.font||zr,d.opacity=_a(l.opacity,i.opacity,1),Of(d,l),b&&(d.lineWidth=_a(l.lineWidth,i.lineWidth,_),d.lineDash=$(l.lineDash,i.lineDash),d.lineDashOffset=i.lineDashOffset||0,d.stroke=b),S&&(d.fill=S);var x=e.contentWidth,T=e.contentHeight;y.setBoundingRect(new j(Qi(d.x,x,d.textAlign),si(d.y,T,d.textBaseline),x,T))},t.prototype._renderBackground=function(e,i,n,a,o,s){var u=e.backgroundColor,l=e.borderWidth,f=e.borderColor,h=u&&u.image,c=u&&!h,v=e.borderRadius,p=this,y,d;if(c||e.lineHeight||l&&f){y=this._getOrCreateChild(xt),y.useStyle(y.createStyle()),y.style.fill=null;var g=y.shape;g.x=n,g.y=a,g.width=o,g.height=s,g.r=v,y.dirtyShape()}if(c){var m=y.style;m.fill=u||null,m.fillOpacity=$(e.fillOpacity,1)}else if(h){d=this._getOrCreateChild(sr),d.onload=function(){p.dirtyStyle()};var _=d.style;_.image=u.image,_.x=n,_.y=a,_.width=o,_.height=s}if(l&&f){var m=y.style;m.lineWidth=l,m.stroke=f,m.strokeOpacity=$(e.strokeOpacity,1),m.lineDash=e.borderDash,m.lineDashOffset=e.borderDashOffset||0,y.strokeContainThreshold=0,y.hasFill()&&y.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var S=(y||d).style;S.shadowBlur=e.shadowBlur||0,S.shadowColor=e.shadowColor||"transparent",S.shadowOffsetX=e.shadowOffsetX||0,S.shadowOffsetY=e.shadowOffsetY||0,S.opacity=_a(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return Um(e)&&(i=[e.fontStyle,e.fontWeight,Wm(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&Re(i)||e.textFont||e.font},t}(En),Hm={left:!0,right:1,center:1},Vm={top:1,bottom:1,middle:1},kf=["fontStyle","fontWeight","fontSize","fontFamily"];function Wm(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?Ju+"px":r+"px"}function Of(r,t){for(var e=0;e<kf.length;e++){var i=kf[e],n=t[i];n!=null&&(r[i]=n)}}function Um(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function Ym(r){return Bf(r),M(r.rich,Bf),r}function Bf(r){if(r){r.font=Et.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||Hm[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||Vm[e]?e:"top";var i=r.padding;i&&(r.padding=Ic(r.padding))}}function Nf(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function Ff(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function zf(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function Gf(r){var t=r.text;return t!=null&&(t+=""),t}function ts(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}var Xm=1e-4,ip=20;function $m(r){return r.replace(/^\s+|\s+$/g,"")}function Hf(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],u=a-n,l=s-o;if(u===0)return l===0?o:(o+s)/2;if(i)if(u>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/u*l+o}function Ht(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return V(r)?$m(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function pt(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),ip),r=(+r).toFixed(t),e?r:+r}function NC(r){return r.sort(function(t,e){return t-e}),r}function ke(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return Zm(r)}function Zm(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function qm(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function FC(r,t){var e=rr(r,function(v,p){return v+(isNaN(p)?0:p)},0);if(e===0)return[];for(var i=Math.pow(10,t),n=z(r,function(v){return(isNaN(v)?0:v)/e*i*100}),a=i*100,o=z(n,function(v){return Math.floor(v)}),s=rr(o,function(v,p){return v+p},0),u=z(n,function(v,p){return v-o[p]});s<a;){for(var l=Number.NEGATIVE_INFINITY,f=null,h=0,c=u.length;h<c;++h)u[h]>l&&(l=u[h],f=h);++o[f],u[f]=0,++s}return z(o,function(v){return v/i})}function Km(r,t){var e=Math.max(ke(r),ke(t)),i=r+t;return e>ip?i:pt(i,e)}function np(r){var t=Math.PI*2;return(r%t+t)%t}function Va(r){return r>-1e-4&&r<Xm}var Qm=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Fe(r){if(r instanceof Date)return r;if(V(r)){var t=Qm.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function Jm(r){return Math.pow(10,hl(r))}function hl(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function ap(r,t){var e=hl(r),i=Math.pow(10,e),n=r/i,a;return n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function Wa(r){var t=parseFloat(r);return t==r&&(t!==0||!V(r)||r.indexOf("x")<=0)?t:NaN}function jm(r){return!isNaN(Wa(r))}function op(){return Math.round(Math.random()*9)}function sp(r,t){return t===0?r:sp(t,r%t)}function Vf(r,t){return r==null?t:t==null?r:r*t/sp(r,t)}function Vt(r){throw new Error(r)}function Wf(r,t,e){return(t-r)*e+r}var up="series\0",lp="\0_ec_\0";function It(r){return r instanceof Array?r:r==null?[]:[r]}function Uf(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var Yf=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Rn(r){return G(r)&&!F(r)&&!(r instanceof Date)?r.value:r}function t0(r){return G(r)&&!(r instanceof Array)}function e0(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=X();M(t,function(u,l){if(!G(u)){t[l]=null;return}});var s=r0(r,o,e);return(i||n)&&i0(s,r,o,t),i&&n0(s,t),i||n?a0(s,t,n):a&&o0(s,t),s0(s),s}function r0(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||Sn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function i0(r,t,e,i){M(i,function(n,a){if(!(!n||n.id==null)){var o=sn(n.id),s=e.get(o);if(s!=null){var u=r[s];xe(!u.newOption,'Duplicated option on id "'+o+'".'),u.newOption=n,u.existing=t[s],i[a]=null}}})}function n0(r,t){M(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!Sn(e)&&!Sn(a)&&fp("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function a0(r,t,e){M(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||Sn(n.existing)||n.existing&&i.id!=null&&!fp("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function o0(r,t){M(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function s0(r){var t=X();M(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),M(r,function(e){var i=e.newOption;xe(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),M(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(G(a)){if(o.name=a.name!=null?sn(a.name):n?n.name:up+i,n)o.id=sn(n.id);else if(a.id!=null)o.id=sn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function fp(r,t,e){var i=we(t[r],null),n=we(e[r],null);return i!=null&&n!=null&&i===n}function sn(r){return we(r,"")}function we(r,t){return r==null?t:V(r)?r:ft(r)||qs(r)?r+"":t}function vl(r){var t=r.name;return!!(t&&t.indexOf(up))}function Sn(r){return r&&r.id!=null&&sn(r.id).indexOf(lp)===0}function zC(r){return lp+r}function u0(r,t,e){M(r,function(i){var n=i.newOption;G(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=l0(t,n,i.existing,e))})}function l0(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function Wr(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return F(t.dataIndex)?z(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return F(t.name)?z(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function dt(){var r="__ec_inner_"+f0++;return function(t){return t[r]||(t[r]={})}}var f0=op();function es(r,t,e){var i=hp(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,u=e?e.defaultMainType:null;return!n&&u&&a.set(u,{}),a.each(function(l,f){var h=ho(r,f,l,{useDefault:u===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function hp(r,t){var e;if(V(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=X(),a={},o=!1;return M(e,function(s,u){if(u==="dataIndex"||u==="dataIndexInside"){a[u]=s;return}var l=u.match(/^(\w+)(Index|Id|Name)$/)||[],f=l[1],h=(l[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&ot(t.includeMainTypes,f)<0)){o=o||!!f;var c=n.get(f)||n.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var fe={useDefault:!0,enableAll:!1,enableNone:!1},GC={useDefault:!1,enableAll:!0,enableNone:!0};function ho(r,t,e,i){i=i||fe;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var u=void 0;return s.models=i.useDefault&&(u=r.getComponent(t))?[u]:[],s}return n==="none"||n===!1?(xe(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(xe(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function vp(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function h0(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function HC(r){return r==="auto"?Q.domSupported?"html":"richText":r||"html"}function v0(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(ft(i)){var o=Wf(e||0,i,n);return pt(o,a?Math.max(ke(e||0),ke(i)):t)}else{if(V(i))return n<1?e:i;for(var s=[],u=e,l=i,f=Math.max(u?u.length:0,l.length),h=0;h<f;++h){var c=r.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(n<1&&u?u:l)[h];else{var v=u&&u[h]?u[h]:0,p=l[h],o=Wf(v,p,n);s[h]=pt(o,a?Math.max(ke(v),ke(p)):t)}}return s}}var vt=dt(),c0=function(r,t,e,i){if(i){var n=vt(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=vt(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},Xf=1,$f={},cp=dt(),cl=dt(),pl=0,vo=1,co=2,Te=["emphasis","blur","select"],Zf=["normal","emphasis","blur","select"],p0=10,d0=9,Nr="highlight",Ta="downplay",un="select",Ca="unselect",ln="toggleSelect";function Kr(r){return r!=null&&r!=="none"}function po(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function pp(r){po(r,"emphasis",co)}function dp(r){r.hoverState===co&&po(r,"normal",pl)}function dl(r){po(r,"blur",vo)}function gp(r){r.hoverState===vo&&po(r,"normal",pl)}function g0(r){r.selected=!0}function y0(r){r.selected=!1}function qf(r,t,e){t(r,e)}function ze(r,t,e){qf(r,t,e),r.isGroup&&r.traverse(function(i){qf(i,t,e)})}function Kf(r,t){switch(t){case"emphasis":r.hoverState=co;break;case"normal":r.hoverState=pl;break;case"blur":r.hoverState=vo;break;case"select":r.selected=!0}}function m0(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],u=n[s];a[s]=u??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var l=r.animators[o];l.__fromStateTransition&&l.__fromStateTransition.indexOf(e)<0&&l.targetName==="style"&&l.saveTo(a,t)}return a}function _0(r,t,e,i){var n=e&&ot(e,"select")>=0,a=!1;if(r instanceof st){var o=cp(r),s=n&&o.selectFill||o.normalFill,u=n&&o.selectStroke||o.normalStroke;if(Kr(s)||Kr(u)){i=i||{};var l=i.style||{};l.fill==="inherit"?(a=!0,i=k({},i),l=k({},l),l.fill=s):!Kr(l.fill)&&Kr(s)?(a=!0,i=k({},i),l=k({},l),l.fill=xf(s)):!Kr(l.stroke)&&Kr(u)&&(a||(i=k({},i),l=k({},l)),l.stroke=xf(u)),i.style=l}}if(i&&i.z2==null){a||(i=k({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??p0)}return i}function S0(r,t,e){if(e&&e.z2==null){e=k({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??d0)}return e}function w0(r,t,e){var i=ot(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:m0(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=k({},e),o=k({opacity:i?n:a.opacity*.1},o),e.style=o),e}function rs(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return _0(this,r,t,e);if(r==="blur")return w0(this,r,e);if(r==="select")return S0(this,r,e)}return e}function b0(r){r.stateProxy=rs;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=rs),e&&(e.stateProxy=rs)}function Qf(r,t){!Sp(r,t)&&!r.__highByOuter&&ze(r,pp)}function Jf(r,t){!Sp(r,t)&&!r.__highByOuter&&ze(r,dp)}function Ua(r,t){r.__highByOuter|=1<<(t||0),ze(r,pp)}function Ya(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&ze(r,dp)}function x0(r){ze(r,dl)}function yp(r){ze(r,gp)}function mp(r){ze(r,g0)}function _p(r){ze(r,y0)}function Sp(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function wp(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=cl(a),s=n==="series",u=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(u),o.isBlured&&(u.group.traverse(function(l){gp(l)}),s&&e.push(a)),o.isBlured=!1}),M(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function fu(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(l,f){for(var h=0;h<f.length;h++){var c=l.getItemGraphicEl(f[h]);c&&yp(c)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var u=[];n.eachSeries(function(l){var f=o===l,h=l.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!c||t==="series"&&f)){var v=i.getViewOfSeriesModel(l);if(v.group.traverse(function(d){d.__highByOuter&&f&&t==="self"||dl(d)}),Nt(t))a(l.getData(),t);else if(G(t))for(var p=lt(t),y=0;y<p.length;y++)a(l.getData(p[y]),t[p[y]]);u.push(l),cl(l).isBlured=!0}}),n.eachComponent(function(l,f){if(l!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(u,!0,n)}})}}function hu(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){cl(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){dl(a)})}}}function T0(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=Wr(n,t);a=(F(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),u=0;!o&&u<s;)o=n.getItemGraphicEl(u++);if(o){var l=vt(o);fu(i,l.focus,l.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&fu(i,f,h,e)}}}function gl(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),u,l=0;l<s.length;l++)if(vt(s[l]).focus==="self"){u=!0;break}return{focusSelf:u,dispatchers:s}}function C0(r,t,e){var i=vt(r),n=gl(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&hu(i.componentMainType,i.componentIndex,e),M(a,function(s){return Qf(s,t)})):(fu(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&hu(i.componentMainType,i.componentIndex,e),Qf(r,t))}function D0(r,t,e){wp(e);var i=vt(r),n=gl(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?M(n,function(a){return Jf(a,t)}):Jf(r,t)}function M0(r,t,e){if(du(t)){var i=t.dataType,n=r.getData(i),a=Wr(n,t);F(a)||(a=[a]),r[t.type===ln?"toggleSelect":t.type===un?"select":"unselect"](a,i)}}function jf(r){var t=r.getAllData();M(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?mp(a):_p(a)})})}function A0(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();M(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function vu(r,t,e){bp(r,!0),ze(r,b0),P0(r,t,e)}function L0(r){bp(r,!1)}function cu(r,t,e,i){i?L0(r):vu(r,t,e)}function P0(r,t,e){var i=vt(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var th=["emphasis","blur","select"],I0={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function eh(r,t,e,i){e=e||"itemStyle";for(var n=0;n<th.length;n++){var a=th[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=o[I0[e]]()}}function bp(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function pu(r){return!!(r&&r.__highDownDispatcher)}function E0(r){var t=$f[r];return t==null&&Xf<=32&&(t=$f[r]=Xf++),t}function du(r){var t=r.type;return t===un||t===Ca||t===ln}function rh(r){var t=r.type;return t===Nr||t===Ta}function R0(r){var t=cp(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var Qr=Vr.CMD,k0=[[],[],[]],ih=Math.sqrt,O0=Math.atan2;function B0(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,u,l,f=Qr.M,h=Qr.C,c=Qr.L,v=Qr.R,p=Qr.A,y=Qr.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case c:a=1;break;case h:a=3;break;case y:a=2;break;case p:var d=t[4],g=t[5],m=ih(t[0]*t[0]+t[1]*t[1]),_=ih(t[2]*t[2]+t[3]*t[3]),S=O0(-t[1]/_,t[0]/m);e[o]*=m,e[o++]+=d,e[o]*=_,e[o++]+=g,e[o++]*=m,e[o++]*=_,e[o++]+=S,e[o++]+=S,o+=2,s=o;break;case v:l[0]=e[o++],l[1]=e[o++],ee(l,l,t),e[s++]=l[0],e[s++]=l[1],l[0]+=e[o++],l[1]+=e[o++],ee(l,l,t),e[s++]=l[0],e[s++]=l[1]}for(u=0;u<a;u++){var b=k0[u];b[0]=e[o++],b[1]=e[o++],ee(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var is=Math.sqrt,qn=Math.sin,Kn=Math.cos,Ni=Math.PI;function nh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function gu(r,t){return(r[0]*t[0]+r[1]*t[1])/(nh(r)*nh(t))}function ah(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(gu(r,t))}function oh(r,t,e,i,n,a,o,s,u,l,f){var h=u*(Ni/180),c=Kn(h)*(r-e)/2+qn(h)*(t-i)/2,v=-1*qn(h)*(r-e)/2+Kn(h)*(t-i)/2,p=c*c/(o*o)+v*v/(s*s);p>1&&(o*=is(p),s*=is(p));var y=(n===a?-1:1)*is((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,d=y*o*v/s,g=y*-s*c/o,m=(r+e)/2+Kn(h)*d-qn(h)*g,_=(t+i)/2+qn(h)*d+Kn(h)*g,S=ah([1,0],[(c-d)/o,(v-g)/s]),b=[(c-d)/o,(v-g)/s],w=[(-1*c-d)/o,(-1*v-g)/s],x=ah(b,w);if(gu(b,w)<=-1&&(x=Ni),gu(b,w)>=1&&(x=0),x<0){var T=Math.round(x/Ni*1e6)/1e6;x=Ni*2+T%2*Ni}f.addData(l,m,_,o,s,S,x,h,a)}var N0=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,F0=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function z0(r){var t=new Vr;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=Vr.CMD,u=r.match(N0);if(!u)return t;for(var l=0;l<u.length;l++){for(var f=u[l],h=f.charAt(0),c=void 0,v=f.match(F0)||[],p=v.length,y=0;y<p;y++)v[y]=parseFloat(v[y]);for(var d=0;d<p;){var g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,T=e,C=i,D=void 0,A=void 0;switch(h){case"l":e+=v[d++],i+=v[d++],c=s.L,t.addData(c,e,i);break;case"L":e=v[d++],i=v[d++],c=s.L,t.addData(c,e,i);break;case"m":e+=v[d++],i+=v[d++],c=s.M,t.addData(c,e,i),n=e,a=i,h="l";break;case"M":e=v[d++],i=v[d++],c=s.M,t.addData(c,e,i),n=e,a=i,h="L";break;case"h":e+=v[d++],c=s.L,t.addData(c,e,i);break;case"H":e=v[d++],c=s.L,t.addData(c,e,i);break;case"v":i+=v[d++],c=s.L,t.addData(c,e,i);break;case"V":i=v[d++],c=s.L,t.addData(c,e,i);break;case"C":c=s.C,t.addData(c,v[d++],v[d++],v[d++],v[d++],v[d++],v[d++]),e=v[d-2],i=v[d-1];break;case"c":c=s.C,t.addData(c,v[d++]+e,v[d++]+i,v[d++]+e,v[d++]+i,v[d++]+e,v[d++]+i),e+=v[d-2],i+=v[d-1];break;case"S":g=e,m=i,D=t.len(),A=t.data,o===s.C&&(g+=e-A[D-4],m+=i-A[D-3]),c=s.C,T=v[d++],C=v[d++],e=v[d++],i=v[d++],t.addData(c,g,m,T,C,e,i);break;case"s":g=e,m=i,D=t.len(),A=t.data,o===s.C&&(g+=e-A[D-4],m+=i-A[D-3]),c=s.C,T=e+v[d++],C=i+v[d++],e+=v[d++],i+=v[d++],t.addData(c,g,m,T,C,e,i);break;case"Q":T=v[d++],C=v[d++],e=v[d++],i=v[d++],c=s.Q,t.addData(c,T,C,e,i);break;case"q":T=v[d++]+e,C=v[d++]+i,e+=v[d++],i+=v[d++],c=s.Q,t.addData(c,T,C,e,i);break;case"T":g=e,m=i,D=t.len(),A=t.data,o===s.Q&&(g+=e-A[D-4],m+=i-A[D-3]),e=v[d++],i=v[d++],c=s.Q,t.addData(c,g,m,e,i);break;case"t":g=e,m=i,D=t.len(),A=t.data,o===s.Q&&(g+=e-A[D-4],m+=i-A[D-3]),e+=v[d++],i+=v[d++],c=s.Q,t.addData(c,g,m,e,i);break;case"A":_=v[d++],S=v[d++],b=v[d++],w=v[d++],x=v[d++],T=e,C=i,e=v[d++],i=v[d++],c=s.A,oh(T,C,e,i,w,x,_,S,b,c,t);break;case"a":_=v[d++],S=v[d++],b=v[d++],w=v[d++],x=v[d++],T=e,C=i,e+=v[d++],i+=v[d++],c=s.A,oh(T,C,e,i,w,x,_,S,b,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),e=n,i=a),o=c}return t.toStatic(),t}var xp=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(st);function Tp(r){return r.setData!=null}function Cp(r,t){var e=z0(r),i=k({},t);return i.buildPath=function(n){if(Tp(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){B0(e,n),this.dirtyShape()},i}function G0(r,t){return new xp(Cp(r,t))}function H0(r,t){var e=Cp(r,t),i=function(n){O(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(xp);return i}function V0(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new st(t);return o.createPathProxy(),o.buildPath=function(s){if(Tp(s)){s.appendPath(e);var u=s.getContext();u&&s.rebuildPath(u,1)}},o}var At=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=ot(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=ot(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new j(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var u=n[s];if(!(u.ignore||u.invisible)){var l=u.getBoundingRect(),f=u.getLocalTransform(a);f?(j.applyTransform(i,l,f),o=o||i.clone(),o.union(i)):(o=o||l.clone(),o.union(l))}}return o||i},t}(lo);At.prototype.type="group";var W0=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),go=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new W0},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(st);go.prototype.type="circle";var U0=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),yl=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new U0},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,u=i.ry,l=s*n,f=u*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-l,o-u,a,o-u),e.bezierCurveTo(a+l,o-u,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+l,o+u,a,o+u),e.bezierCurveTo(a-l,o+u,a-s,o+f,a-s,o),e.closePath()},t}(st);yl.prototype.type="ellipse";var Dp=Math.PI,ns=Dp*2,Tr=Math.sin,Jr=Math.cos,Y0=Math.acos,Tt=Math.atan2,sh=Math.abs,fn=Math.sqrt,tn=Math.max,ge=Math.min,oe=1e-4;function X0(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=h*u-f*l;if(!(c*c<oe))return c=(f*(t-a)-h*(r-n))/c,[r+c*u,t+c*l]}function Qn(r,t,e,i,n,a,o){var s=r-e,u=t-i,l=(o?a:-a)/fn(s*s+u*u),f=l*u,h=-l*s,c=r+f,v=t+h,p=e+f,y=i+h,d=(c+p)/2,g=(v+y)/2,m=p-c,_=y-v,S=m*m+_*_,b=n-a,w=c*y-p*v,x=(_<0?-1:1)*fn(tn(0,b*b*S-w*w)),T=(w*_-m*x)/S,C=(-w*m-_*x)/S,D=(w*_+m*x)/S,A=(-w*m+_*x)/S,L=T-d,I=C-g,P=D-d,E=A-g;return L*L+I*I>P*P+E*E&&(T=D,C=A),{cx:T,cy:C,x0:-f,y0:-h,x1:T*(n/b-1),y1:C*(n/b-1)}}function $0(r){var t;if(F(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function Z0(r,t){var e,i=tn(t.r,0),n=tn(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var u=t.startAngle,l=t.endAngle;if(!(isNaN(u)||isNaN(l))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=sh(l-u),p=v>ns&&v%ns;if(p>oe&&(v=p),!(i>oe))r.moveTo(f,h);else if(v>ns-oe)r.moveTo(f+i*Jr(u),h+i*Tr(u)),r.arc(f,h,i,u,l,!c),n>oe&&(r.moveTo(f+n*Jr(l),h+n*Tr(l)),r.arc(f,h,n,l,u,c));else{var y=void 0,d=void 0,g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,T=void 0,C=void 0,D=void 0,A=void 0,L=void 0,I=void 0,P=void 0,E=i*Jr(u),R=i*Tr(u),H=n*Jr(l),B=n*Tr(l),N=v>oe;if(N){var W=t.cornerRadius;W&&(e=$0(W),y=e[0],d=e[1],g=e[2],m=e[3]);var Z=sh(i-n)/2;if(_=ge(Z,g),S=ge(Z,m),b=ge(Z,y),w=ge(Z,d),C=x=tn(_,S),D=T=tn(b,w),(x>oe||T>oe)&&(A=i*Jr(l),L=i*Tr(l),I=n*Jr(u),P=n*Tr(u),v<Dp)){var q=X0(E,R,I,P,A,L,H,B);if(q){var it=E-q[0],ut=R-q[1],ht=A-q[0],ie=L-q[1],ur=1/Tr(Y0((it*ht+ut*ie)/(fn(it*it+ut*ut)*fn(ht*ht+ie*ie)))/2),Yr=fn(q[0]*q[0]+q[1]*q[1]);C=ge(x,(i-Yr)/(ur+1)),D=ge(T,(n-Yr)/(ur-1))}}}if(!N)r.moveTo(f+E,h+R);else if(C>oe){var Gt=ge(g,C),_t=ge(m,C),U=Qn(I,P,E,R,i,Gt,c),K=Qn(A,L,H,B,i,_t,c);r.moveTo(f+U.cx+U.x0,h+U.cy+U.y0),C<x&&Gt===_t?r.arc(f+U.cx,h+U.cy,C,Tt(U.y0,U.x0),Tt(K.y0,K.x0),!c):(Gt>0&&r.arc(f+U.cx,h+U.cy,Gt,Tt(U.y0,U.x0),Tt(U.y1,U.x1),!c),r.arc(f,h,i,Tt(U.cy+U.y1,U.cx+U.x1),Tt(K.cy+K.y1,K.cx+K.x1),!c),_t>0&&r.arc(f+K.cx,h+K.cy,_t,Tt(K.y1,K.x1),Tt(K.y0,K.x0),!c))}else r.moveTo(f+E,h+R),r.arc(f,h,i,u,l,!c);if(!(n>oe)||!N)r.lineTo(f+H,h+B);else if(D>oe){var Gt=ge(y,D),_t=ge(d,D),U=Qn(H,B,A,L,n,-_t,c),K=Qn(E,R,I,P,n,-Gt,c);r.lineTo(f+U.cx+U.x0,h+U.cy+U.y0),D<T&&Gt===_t?r.arc(f+U.cx,h+U.cy,D,Tt(U.y0,U.x0),Tt(K.y0,K.x0),!c):(_t>0&&r.arc(f+U.cx,h+U.cy,_t,Tt(U.y0,U.x0),Tt(U.y1,U.x1),!c),r.arc(f,h,n,Tt(U.cy+U.y1,U.cx+U.x1),Tt(K.cy+K.y1,K.cx+K.x1),c),Gt>0&&r.arc(f+K.cx,h+K.cy,Gt,Tt(K.y1,K.x1),Tt(K.y0,K.x0),!c))}else r.lineTo(f+H,h+B),r.arc(f,h,n,l,u,c)}r.closePath()}}}var q0=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),yo=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new q0},t.prototype.buildPath=function(e,i){Z0(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(st);yo.prototype.type="sector";var K0=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),ml=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new K0},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(st);ml.prototype.type="ring";function Q0(r,t,e,i){var n=[],a=[],o=[],s=[],u,l,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=r.length;c<v;c++)ui(f,f,r[c]),li(h,h,r[c]);ui(f,f,i[0]),li(h,h,i[1])}for(var c=0,v=r.length;c<v;c++){var p=r[c];if(e)u=r[c?c-1:v-1],l=r[(c+1)%v];else if(c===0||c===v-1){n.push(Fy(r[c]));continue}else u=r[c-1],l=r[c+1];zy(a,l,u),Oo(a,a,t);var y=tu(p,u),d=tu(p,l),g=y+d;g!==0&&(y/=g,d/=g),Oo(o,a,-y),Oo(s,a,d);var m=pf([],p,o),_=pf([],p,s);i&&(li(m,m,f),ui(m,m,h),li(_,_,f),ui(_,_,h)),n.push(m),n.push(_)}return e&&n.push(n.shift()),n}function Mp(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=Q0(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var u=a[s*2],l=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(u[0],u[1],l[0],l[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var J0=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),_l=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new J0},t.prototype.buildPath=function(e,i){Mp(e,i,!0)},t}(st);_l.prototype.type="polygon";var j0=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),Sl=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new j0},t.prototype.buildPath=function(e,i){Mp(e,i,!1)},t}(st);Sl.prototype.type="polyline";var t_={},e_=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),ir=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new e_},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=ep(t_,i,this.style);n=u.x1,a=u.y1,o=u.x2,s=u.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var l=i.percent;l!==0&&(e.moveTo(n,a),l<1&&(o=n*(1-l)+o*l,s=a*(1-l)+s*l),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(st);ir.prototype.type="line";var Rt=[],r_=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function uh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?yf:bt)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?yf:bt)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?mf:Ot)(r.x1,r.cpx1,r.x2,t),(e?mf:Ot)(r.y1,r.cpy1,r.y2,t)]}var wl=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new r_},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,u=i.cpx1,l=i.cpy1,f=i.cpx2,h=i.cpy2,c=i.percent;c!==0&&(e.moveTo(n,a),f==null||h==null?(c<1&&(Na(n,u,o,c,Rt),u=Rt[1],o=Rt[2],Na(a,l,s,c,Rt),l=Rt[1],s=Rt[2]),e.quadraticCurveTo(u,l,o,s)):(c<1&&(Ba(n,u,f,o,c,Rt),u=Rt[1],f=Rt[2],o=Rt[3],Ba(a,l,h,s,c,Rt),l=Rt[1],h=Rt[2],s=Rt[3]),e.bezierCurveTo(u,l,f,h,o,s)))},t.prototype.pointAt=function(e){return uh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=uh(this.shape,e,!0);return Vy(i,i)},t}(st);wl.prototype.type="bezier-curve";var i_=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),mo=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new i_},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,u=i.endAngle,l=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,u,!l)},t}(st);mo.prototype.type="arc";var n_=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),st.prototype.getBoundingRect.call(this)},t}(st),Ap=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}(),Lp=function(r){O(t,r);function t(e,i,n,a,o,s){var u=r.call(this,o)||this;return u.x=e??0,u.y=i??0,u.x2=n??1,u.y2=a??0,u.type="linear",u.global=s||!1,u}return t}(Ap),a_=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(Ap),Cr=[0,0],Dr=[0,0],Jn=new at,jn=new at,Xa=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new at;for(var i=0;i<2;i++)this._axes[i]=new at;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,u=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,u),i[3].set(a,u),e)for(var l=0;l<4;l++)i[l].transform(e);at.sub(n[0],i[1],i[0]),at.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var l=0;l<2;l++)this._origin[l]=n[l].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return Jn.set(1/0,1/0),jn.set(0,0),!this._intersectCheckOneSide(this,t,Jn,jn,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,Jn,jn,n,-1)&&(i=!1,n)||n||at.copy(e,i?Jn:jn),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,u=0;u<2;u++){var l=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,Cr),this._getProjMinMaxOnAxis(u,e._corners,Dr),Cr[1]<Dr[0]||Cr[0]>Dr[1]){if(s=!1,a)return s;var f=Math.abs(Dr[0]-Cr[1]),h=Math.abs(Cr[0]-Dr[1]);Math.min(f,h)>n.len()&&(f<h?at.scale(n,l,-f*o):at.scale(n,l,h*o))}else if(i){var f=Math.abs(Dr[0]-Cr[1]),h=Math.abs(Cr[0]-Dr[1]);Math.min(f,h)<i.len()&&(f<h?at.scale(i,l,f*o):at.scale(i,l,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,u=o,l=1;l<e.length;l++){var f=e[l].dot(n)+a[t];s=Math.min(f,s),u=Math.max(f,u)}i[0]=s,i[1]=u},r}(),o_=[],s_=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new j(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(o_)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(En),u_=dt();function l_(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),u=r==="update";if(s){var l=void 0,f=void 0,h=void 0;i?(l=$(i.duration,200),f=$(i.easing,"cubicOut"),h=0):(l=t.getShallow(u?"animationDurationUpdate":"animationDuration"),f=t.getShallow(u?"animationEasingUpdate":"animationEasing"),h=t.getShallow(u?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(l=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),Y(h)&&(h=h(e,n)),Y(l)&&(l=l(e));var c={duration:l||0,delay:h,easing:f};return c}else return null}function bl(r,t,e,i,n,a,o){var s=!1,u;Y(n)?(o=a,a=n,n=null):G(n)&&(a=n.cb,o=n.during,s=n.isFrom,u=n.removeOpt,n=n.dataIndex);var l=r==="leave";l||t.stopAnimation("leave");var f=l_(r,i,n,l?u||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,p={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!l,scope:r,during:o};s?t.animateFrom(e,p):t.animateTo(e,p)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function nr(r,t,e,i,n,a){bl("update",r,t,e,i,n,a)}function kn(r,t,e,i,n,a){bl("enter",r,t,e,i,n,a)}function hn(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function $a(r,t,e,i,n,a){hn(r)||bl("leave",r,t,e,i,n,a)}function lh(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),$a(r,{style:{opacity:0}},t,e,i)}function f_(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||lh(n,t,e,i)}):lh(r,t,e,i)}function h_(r){u_(r).oldStyle=r.style}var Za=Math.max,qa=Math.min,yu={};function v_(r){return st.extend(r)}var c_=H0;function p_(r,t){return c_(r,t)}function he(r,t){yu[r]=t}function d_(r){if(yu.hasOwnProperty(r))return yu[r]}function xl(r,t,e,i){var n=G0(r,t);return e&&(i==="center"&&(e=Ip(e,n.getBoundingRect())),Ep(n,e)),n}function Pp(r,t,e){var i=new sr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle(Ip(t,a))}}});return i}function Ip(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var g_=V0;function Ep(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function wn(r,t){return ep(r,r,{lineWidth:t}),r}function y_(r){return rp(r.shape,r.shape,r.style),r}var m_=Rr;function __(r,t){for(var e=nl([]);r&&r!==t;)ci(e,r.getLocalTransform(),e),r=r.parent;return e}function Tl(r,t,e){return t&&!Nt(t)&&(t=ll.getLocalTransform(t)),e&&(t=ol([],t)),ee([],r,t)}function S_(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=Tl(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function fh(r){return!r.isGroup}function w_(r){return r.shape!=null}function Rp(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(u){fh(u)&&u.anid&&(s[u.anid]=u)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return w_(o)&&(s.shape=k({},o.shape)),s}var a=i(r);t.traverse(function(o){if(fh(o)&&o.anid){var s=a[o.anid];if(s){var u=n(o);o.attr(n(s)),nr(o,u,e,vt(o).dataIndex)}}})}function b_(r,t){return z(r,function(e){var i=e[0];i=Za(i,t.x),i=qa(i,t.x+t.width);var n=e[1];return n=Za(n,t.y),n=qa(n,t.y+t.height),[i,n]})}function x_(r,t){var e=Za(r.x,t.x),i=qa(r.x+r.width,t.x+t.width),n=Za(r.y,t.y),a=qa(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function Cl(r,t,e){var i=k({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),rt(n,e),new sr(i)):xl(r.replace("path://",""),i,e,"center")}function T_(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(kp(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function kp(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=as(f,h,u,l);if(C_(c))return!1;var v=r-n,p=t-a,y=as(v,p,u,l)/c;if(y<0||y>1)return!1;var d=as(v,p,f,h)/c;return!(d<0||d>1)}function as(r,t,e,i){return r*i-e*t}function C_(r){return r<=1e-6&&r>=-1e-6}function _o(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=V(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var u=r.formatterParamsExtra;u&&M(lt(u),function(f){Gr(s,f)||(s[f]=u[f],s.$vars.push(f))});var l=vt(r.el);l.componentMainType=a,l.componentIndex=o,l.tooltipConfig={name:i,option:rt({content:i,encodeHTMLContent:!0,formatterParams:s},n)}}function hh(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function Dl(r,t){if(r)if(F(r))for(var e=0;e<r.length;e++)hh(r[e],t);else hh(r,t)}he("circle",go);he("ellipse",yl);he("sector",yo);he("ring",ml);he("polygon",_l);he("polyline",Sl);he("rect",xt);he("line",ir);he("bezierCurve",wl);he("arc",mo);const D_=Object.freeze(Object.defineProperty({__proto__:null,Arc:mo,BezierCurve:wl,BoundingRect:j,Circle:go,CompoundPath:n_,Ellipse:yl,Group:At,Image:sr,IncrementalDisplayable:s_,Line:ir,LinearGradient:Lp,OrientedBoundingRect:Xa,Path:st,Point:at,Polygon:_l,Polyline:Sl,RadialGradient:a_,Rect:xt,Ring:ml,Sector:yo,Text:Et,applyTransform:Tl,clipPointsByRect:b_,clipRectByRect:x_,createIcon:Cl,extendPath:p_,extendShape:v_,getShapeClass:d_,getTransform:__,groupTransition:Rp,initProps:kn,isElementRemoved:hn,lineLineIntersect:kp,linePolygonIntersect:T_,makeImage:Pp,makePath:xl,mergePath:g_,registerShape:he,removeElement:$a,removeElementWithFadeOut:f_,resizePath:Ep,setTooltipConfig:_o,subPixelOptimize:m_,subPixelOptimizeLine:wn,subPixelOptimizeRect:y_,transformDirection:S_,traverseElements:Dl,updateProps:nr},Symbol.toStringTag,{value:"Module"}));var So={};function M_(r,t){for(var e=0;e<Te.length;e++){var i=Te[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function vh(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=Y(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var u={normal:s},l=0;l<Te.length;l++){var f=Te[l],h=t[f];u[f]=$(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return u}function Ml(r,t,e,i){e=e||So;for(var n=r instanceof Et,a=!1,o=0;o<Zf.length;o++){var s=t[Zf[o]];if(s&&s.getShallow("show")){a=!0;break}}var u=n?r:r.getTextContent();if(a){n||(u||(u=new Et,r.setTextContent(u)),r.stateProxy&&(u.stateProxy=r.stateProxy));var l=vh(e,t),f=t.normal,h=!!f.getShallow("show"),c=ar(f,i&&i.normal,e,!1,!n);c.text=l.normal,n||r.setTextConfig(ch(f,e,!1));for(var o=0;o<Te.length;o++){var v=Te[o],s=t[v];if(s){var p=u.ensureState(v),y=!!$(s.getShallow("show"),h);if(y!==h&&(p.ignore=!y),p.style=ar(s,i&&i[v],e,!0,!n),p.style.text=l[v],!n){var d=r.ensureState(v);d.textConfig=ch(s,e,!0)}}}u.silent=!!f.getShallow("silent"),u.style.x!=null&&(c.x=u.style.x),u.style.y!=null&&(c.y=u.style.y),u.ignore=!h,u.useStyle(c),u.dirty(),e.enableTextSetter&&(Ll(u).setLabelText=function(g){var m=vh(e,t,g);M_(u,m)})}else u&&(u.ignore=!0);r.dirty()}function Al(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<Te.length;i++){var n=Te[i];e[n]=r.getModel([n,t])}return e}function ar(r,t,e,i,n){var a={};return A_(a,r,e,i,n),t&&k(a,t),a}function ch(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=$(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function A_(r,t,e,i,n){e=e||So;var a=t.ecModel,o=a&&a.option.textStyle,s=L_(t),u;if(s){u={};for(var l in s)if(s.hasOwnProperty(l)){var f=t.getModel(["rich",l]);yh(u[l]={},f,o,e,i,n,!1,!0)}}u&&(r.rich=u);var h=t.get("overflow");h&&(r.overflow=h);var c=t.get("minMargin");c!=null&&(r.margin=c),yh(r,t,o,e,i,n,!0,!1)}function L_(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||So).rich;if(e){t=t||{};for(var i=lt(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var ph=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],dh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],gh=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function yh(r,t,e,i,n,a,o,s){e=!n&&e||So;var u=i&&i.inheritColor,l=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=$(t.getShallow("opacity"),e.opacity);(l==="inherit"||l==="auto")&&(u?l=u:l=null),(f==="inherit"||f==="auto")&&(u?f=u:f=null),a||(l=l||e.color,f=f||e.textBorderColor),l!=null&&(r.fill=l),f!=null&&(r.stroke=f);var c=$(t.getShallow("textBorderWidth"),e.textBorderWidth);c!=null&&(r.lineWidth=c);var v=$(t.getShallow("textBorderType"),e.textBorderType);v!=null&&(r.lineDash=v);var p=$(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);p!=null&&(r.lineDashOffset=p),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var y=0;y<ph.length;y++){var d=ph[y],g=$(t.getShallow(d),e[d]);g!=null&&(r[d]=g)}for(var y=0;y<dh.length;y++){var d=dh[y],g=t.getShallow(d);g!=null&&(r[d]=g)}if(r.verticalAlign==null){var m=t.getShallow("baseline");m!=null&&(r.verticalAlign=m)}if(!o||!i.disableBox){for(var y=0;y<gh.length;y++){var d=gh[y],g=t.getShallow(d);g!=null&&(r[d]=g)}var _=t.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&u&&(r.backgroundColor=u),(r.borderColor==="auto"||r.borderColor==="inherit")&&u&&(r.borderColor=u)}}function P_(r,t){var e=t&&t.getModel("textStyle");return Re([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var Ll=dt();function VC(r,t,e,i){if(r){var n=Ll(r);n.prevValue=n.value,n.value=e;var a=t.normal;n.valueAnimation=a.get("valueAnimation"),n.valueAnimation&&(n.precision=a.get("precision"),n.defaultInterpolatedText=i,n.statesModels=t)}}var I_=["textStyle","color"],os=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],ss=new Et,E_=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(I_):null)},r.prototype.getFont=function(){return P_({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<os.length;i++)e[os[i]]=this.getShallow(os[i]);return ss.useStyle(e),ss.update(),ss.getBoundingRect()},r}(),Op=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],R_=mn(Op),k_=function(){function r(){}return r.prototype.getLineStyle=function(t){return R_(this,t)},r}(),Bp=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],O_=mn(Bp),B_=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return O_(this,t,e)},r}(),mt=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){et(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(J(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!Q.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();il(mt);_y(mt);De(mt,k_);De(mt,B_);De(mt,Ty);De(mt,E_);function Fi(r){return r==null?0:r.length||1}function mh(r){return r}var N_=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||mh,this._newKeyGetter=n||mh,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],u=i[s],l=Fi(u);if(l>1){var f=u.shift();u.length===1&&(i[s]=u[0]),this._update&&this._update(f,o)}else l===1?(i[s]=null,this._update&&this._update(u,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var u=a[s],l=i[u],f=n[u],h=Fi(l),c=Fi(f);if(h>1&&c===1)this._updateManyToOne&&this._updateManyToOne(f,l),n[u]=null;else if(h===1&&c>1)this._updateOneToMany&&this._updateOneToMany(f,l),n[u]=null;else if(h===1&&c===1)this._update&&this._update(f,l),n[u]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(f,l),n[u]=null;else if(h>1)for(var v=0;v<h;v++)this._remove&&this._remove(l[v]);else this._remove&&this._remove(l)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=Fi(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var u=e[s],l=Fi(u);l===0?(e[s]=o,a&&i.push(s)):l===1?e[s]=[u,o]:u.push(o)}}},r}(),Np=X(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),re="original",zt="arrayRows",ve="objectRows",Ae="keyedColumns",tr="typedArray",Fp="unknown",Ne="column",Ai="row",St={Must:1,Might:2,Not:3},zp=dt();function F_(r){zp(r).datasetMap=X()}function z_(r,t,e){var i={},n=Pl(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,u=zp(s).datasetMap,l=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),M(r,function(y,d){var g=G(y)?y:r[d]={name:y};g.type==="ordinal"&&f==null&&(f=d,h=p(g)),i[g.name]=[]});var c=u.get(l)||u.set(l,{categoryWayDim:h,valueWayDim:0});M(r,function(y,d){var g=y.name,m=p(y);if(f==null){var _=c.valueWayDim;v(i[g],_,m),v(o,_,m),c.valueWayDim+=m}else if(f===d)v(i[g],0,m),v(a,0,m);else{var _=c.categoryWayDim;v(i[g],_,m),v(o,_,m),c.categoryWayDim+=m}});function v(y,d,g){for(var m=0;m<g;m++)y.push(d+m)}function p(y){var d=y.dimsDef;return d?d.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function WC(r,t,e){var i={},n=Pl(r);if(!n)return i;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===ve||a===Ae)&&M(o,function(f,h){(G(f)?f.name:f)==="name"&&(s=h)});var u=function(){for(var f={},h={},c=[],v=0,p=Math.min(5,e);v<p;v++){var y=Hp(t.data,a,t.seriesLayoutBy,o,t.startIndex,v);c.push(y);var d=y===St.Not;if(d&&f.v==null&&v!==s&&(f.v=v),(f.n==null||f.n===f.v||!d&&c[f.n]===St.Not)&&(f.n=v),g(f)&&c[f.n]!==St.Not)return f;d||(y===St.Might&&h.v==null&&v!==s&&(h.v=v),(h.n==null||h.n===h.v)&&(h.n=v))}function g(m){return m.v!=null&&m.n!=null}return g(f)?f:g(h)?h:null}();if(u){i.value=[u.v];var l=s??u.n;i.itemName=[l],i.seriesName=[l]}return i}function Pl(r){var t=r.get("data",!0);if(!t)return ho(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},fe).models[0]}function G_(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:ho(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},fe).models}function Gp(r,t){return Hp(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function Hp(r,t,e,i,n,a){var o,s=5;if(Ft(r))return St.Not;var u,l;if(i){var f=i[a];G(f)?(u=f.name,l=f.type):V(f)&&(u=f)}if(l!=null)return l==="ordinal"?St.Must:St.Not;if(t===zt){var h=r;if(e===Ai){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=S(c[n+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var p=h[n+v];if(p&&(o=S(p[a]))!=null)return o}}else if(t===ve){var y=r;if(!u)return St.Not;for(var v=0;v<y.length&&v<s;v++){var d=y[v];if(d&&(o=S(d[u]))!=null)return o}}else if(t===Ae){var g=r;if(!u)return St.Not;var c=g[u];if(!c||Ft(c))return St.Not;for(var v=0;v<c.length&&v<s;v++)if((o=S(c[v]))!=null)return o}else if(t===re)for(var m=r,v=0;v<m.length&&v<s;v++){var d=m[v],_=Rn(d);if(!F(_))return St.Not;if((o=S(_[a]))!=null)return o}function S(b){var w=V(b);if(b!=null&&Number.isFinite(Number(b))&&b!=="")return w?St.Might:St.Not;if(w&&b!=="-")return St.Must}return St.Not}var wo=function(){function r(t){this.data=t.data||(t.sourceFormat===Ae?{}:[]),this.sourceFormat=t.sourceFormat||Fp,this.seriesLayoutBy=t.seriesLayoutBy||Ne,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&Gp(this,i)===St.Must&&(n.type="ordinal")}}return r}();function Il(r){return r instanceof wo}function mu(r,t,e){e=e||Wp(r);var i=t.seriesLayoutBy,n=V_(r,e,i,t.sourceHeader,t.dimensions),a=new wo({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:J(t)});return a}function Vp(r){return new wo({data:r,sourceFormat:Ft(r)?tr:re})}function H_(r){return new wo({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:J(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function Wp(r){var t=Fp;if(Ft(r))t=tr;else if(F(r)){r.length===0&&(t=zt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(F(n)||Ft(n)){t=zt;break}else if(G(n)){t=ve;break}}}}else if(G(r)){for(var a in r)if(Gr(r,a)&&Nt(r[a])){t=Ae;break}}return t}function V_(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:_h(n),startIndex:o,dimensionsDetectedCount:a};if(t===zt){var s=r;i==="auto"||i==null?Sh(function(l){l!=null&&l!=="-"&&(V(l)?o==null&&(o=1):o=0)},e,s,10):o=ft(i)?i:i?1:0,!n&&o===1&&(n=[],Sh(function(l,f){n[f]=l!=null?l+"":""},e,s,1/0)),a=n?n.length:e===Ai?s.length:s[0]?s[0].length:null}else if(t===ve)n||(n=W_(r));else if(t===Ae)n||(n=[],M(r,function(l,f){n.push(f)}));else if(t===re){var u=Rn(r[0]);a=F(u)&&u.length||1}return{startIndex:o,dimensionsDefine:_h(n),dimensionsDetectedCount:a}}function W_(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return lt(e)}function _h(r){if(r){var t=X();return z(r,function(e,i){e=G(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function Sh(r,t,e,i){if(t===Ai)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function Up(r){var t=r.sourceFormat;return t===ve||t===Ae}var Mr,Ar,Lr,wh,bh,Yp=function(){function r(t,e){var i=Il(t)?t:Vp(t);this._source=i;var n=this._data=i.data;i.sourceFormat===tr&&(this._offset=0,this._dimSize=e,this._data=n),bh(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;bh=function(o,s,u){var l=u.sourceFormat,f=u.seriesLayoutBy,h=u.startIndex,c=u.dimensionsDefine,v=wh[El(l,f)];if(k(o,v),l===tr)o.getItem=e,o.count=n,o.fillStorage=i;else{var p=Xp(l,f);o.getItem=gt(p,null,s,h,c);var y=$p(l,f);o.count=gt(y,null,s,h,c)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var u=this._data,l=this._dimSize,f=l*o,h=0;h<l;h++)s[h]=u[f+h];return s},i=function(o,s,u,l){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=l[c],p=v[0]==null?1/0:v[0],y=v[1]==null?-1/0:v[1],d=s-o,g=u[c],m=0;m<d;m++){var _=f[m*h+c];g[o+m]=_,_<p&&(p=_),_>y&&(y=_)}v[0]=p,v[1]=y}},n=function(){return this._data?this._data.length/this._dimSize:0};wh=(t={},t[zt+"_"+Ne]={pure:!0,appendData:a},t[zt+"_"+Ai]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[ve]={pure:!0,appendData:a},t[Ae]={pure:!0,appendData:function(o){var s=this._data;M(o,function(u,l){for(var f=s[l]||(s[l]=[]),h=0;h<(u||[]).length;h++)f.push(u[h])})}},t[re]={appendData:a},t[tr]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),xh=function(r,t,e,i){return r[i]},U_=(Mr={},Mr[zt+"_"+Ne]=function(r,t,e,i){return r[i+t]},Mr[zt+"_"+Ai]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var u=o[s];a[s]=u?u[i]:null}return a},Mr[ve]=xh,Mr[Ae]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,u=r[s];a[o]=u?u[i]:null}return a},Mr[re]=xh,Mr);function Xp(r,t){var e=U_[El(r,t)];return e}var Th=function(r,t,e){return r.length},Y_=(Ar={},Ar[zt+"_"+Ne]=function(r,t,e){return Math.max(0,r.length-t)},Ar[zt+"_"+Ai]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},Ar[ve]=Th,Ar[Ae]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},Ar[re]=Th,Ar);function $p(r,t){var e=Y_[El(r,t)];return e}var us=function(r,t,e){return r[t]},X_=(Lr={},Lr[zt]=us,Lr[ve]=function(r,t,e){return r[e]},Lr[Ae]=us,Lr[re]=function(r,t,e){var i=Rn(r);return i instanceof Array?i[t]:i},Lr[tr]=us,Lr);function Zp(r){var t=X_[r];return t}function El(r,t){return r===zt?r+"_"+t:r}function Si(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return Zp(a)(i,o,s)}else{var u=i;return a===re&&(u=Rn(i)),u}}}}var $_=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function Z_(r,t){var e={},i=e.encode={},n=X(),a=[],o=[],s={};M(r.dimensions,function(c){var v=r.getDimensionInfo(c),p=v.coordDim;if(p){var y=v.coordDimIndex;ls(i,p)[y]=c,v.isExtraCoord||(n.set(p,1),K_(v.type)&&(a[0]=c),ls(s,p)[y]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(c)}Np.each(function(d,g){var m=ls(i,g),_=v.otherDims[g];_!=null&&_!==!1&&(m[_]=v.name)})});var u=[],l={};n.each(function(c,v){var p=i[v];l[v]=p[0],u=u.concat(p)}),e.dataDimsOnCoord=u,e.dataDimIndicesOnCoord=z(u,function(c){return r.getDimensionInfo(c).storeDimIndex}),e.encodeFirstDimNotExtra=l;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new $_(s,t),e}function ls(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function q_(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function K_(r){return!(r==="ordinal"||r==="time")}var Da=function(){function r(t){this.otherDims={},t!=null&&k(this,t)}return r}();function Ma(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!ft(r)&&r!=null&&r!=="-"&&(r=+Fe(r)),r==null||r===""?NaN:Number(r))}X({number:function(r){return parseFloat(r)},time:function(r){return+Fe(r)},trim:function(r){return V(r)?Re(r):r}});var Q_=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=ft(t)?t:Wa(t),n=ft(e)?e:Wa(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=V(t),u=V(e);s&&(i=u?t:0),u&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),bo="undefined",J_=typeof Uint32Array===bo?Array:Uint32Array,j_=typeof Uint16Array===bo?Array:Uint16Array,qp=typeof Int32Array===bo?Array:Int32Array,Ch=typeof Float64Array===bo?Array:Float64Array,Kp={float:Ch,int:qp,ordinal:Array,number:Array,time:Ch},fs;function jr(r){return r>65535?J_:j_}function ti(){return[1/0,-1/0]}function t1(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function Dh(r,t,e,i,n){var a=Kp[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var u=new a(i),l=0;l<s;l++)u[l]=o[l];r[t]=u}}else r[t]=new a(i)}var _u=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=X()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=fs[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],Up(n),this._dimensions=z(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new Kp[e||"float"](this._rawCount),this._rawExtent[a]=ti(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=ti());for(var u=a[t],l=o;l<s;l++){var f=i[l]=e.parseAndCollect(i[l]);isNaN(f)||(u[0]=Math.min(f,u[0]),u[1]=Math.max(f,u[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<a;l++){var f=n[l];Dh(i,l,f.type,u,!0)}for(var h=[],c=s;c<u;c++)for(var v=c-s,p=0;p<a;p++){var f=n[p],y=fs.arrayRows.call(this,t[v]||h,f.property,v,p);i[p][c]=y;var d=o[p];y<d[0]&&(d[0]=y),y>d[1]&&(d[1]=y)}return this._rawCount=this._count=u,{start:s,end:u}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,u=this._rawExtent,l=z(o,function(m){return m.property}),f=0;f<s;f++){var h=o[f];u[f]||(u[f]=ti()),Dh(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,u);else for(var c=[],v=t;v<e;v++){c=n.getItem(v,c);for(var p=0;p<s;p++){var y=a[p],d=this._dimValueGetter(c,l[p],v,p);y[v]=d;var g=u[p];d<g[0]&&(g[0]=d),d>g[1]&&(g[1]=d)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,u=-1,l=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=e-a[c],p=Math.abs(v);p<=i&&((p<s||p===s&&v>=0&&u<0)&&(s=p,u=v,l=0),v===u&&(o[l++]=f))}return o.length=l,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=jr(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=jr(i._rawCount),o=new a(n),s=[],u=t.length,l=0,f=t[0],h=i._chunks,c=0;c<n;c++){var v=void 0,p=i.getRawIndex(c);if(u===0)v=e(c);else if(u===1){var y=h[f][p];v=e(y,c)}else{for(var d=0;d<u;d++)s[d]=h[t[d]][p];s[d]=c,v=e.apply(null,s)}v&&(o[l++]=p)}return l<n&&(i._indices=o),i._count=l,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=lt(t),a=n.length;if(!a)return this;var o=e.count(),s=jr(e._rawCount),u=new s(o),l=0,f=n[0],h=t[f][0],c=t[f][1],v=e._chunks,p=!1;if(!e._indices){var y=0;if(a===1){for(var d=v[n[0]],g=0;g<i;g++){var m=d[g];(m>=h&&m<=c||isNaN(m))&&(u[l++]=y),y++}p=!0}else if(a===2){for(var d=v[n[0]],_=v[n[1]],S=t[n[1]][0],b=t[n[1]][1],g=0;g<i;g++){var m=d[g],w=_[g];(m>=h&&m<=c||isNaN(m))&&(w>=S&&w<=b||isNaN(w))&&(u[l++]=y),y++}p=!0}}if(!p)if(a===1)for(var g=0;g<o;g++){var x=e.getRawIndex(g),m=v[n[0]][x];(m>=h&&m<=c||isNaN(m))&&(u[l++]=x)}else for(var g=0;g<o;g++){for(var T=!0,x=e.getRawIndex(g),C=0;C<a;C++){var D=n[C],m=v[D][x];(m<t[D][0]||m>t[D][1])&&(T=!1)}T&&(u[l++]=e.getRawIndex(g))}return l<o&&(e._indices=u),e._count=l,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),u=[],l=t._rawExtent,f=0;f<e.length;f++)l[e[f]]=ti();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)u[v]=n[e[v]][c];u[o]=h;var p=i&&i.apply(null,u);if(p!=null){typeof p!="object"&&(a[0]=p,p=a);for(var f=0;f<p.length;f++){var y=e[f],d=p[f],g=l[y],m=n[y];m&&(m[c]=d),d<g[0]&&(g[0]=d),d>g[1]&&(g[1]=d)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,u=Math.floor(1/e),l=this.getRawIndex(0),f,h,c,v=new(jr(this._rawCount))(Math.min((Math.ceil(o/u)+2)*2,o));v[s++]=l;for(var p=1;p<o-1;p+=u){for(var y=Math.min(p+u,o-1),d=Math.min(p+u*2,o),g=(d+y)/2,m=0,_=y;_<d;_++){var S=this.getRawIndex(_),b=a[S];isNaN(b)||(m+=b)}m/=d-y;var w=p,x=Math.min(p+u,o),T=p-1,C=a[l];f=-1,c=w;for(var D=-1,A=0,_=w;_<x;_++){var S=this.getRawIndex(_),b=a[S];if(isNaN(b)){A++,D<0&&(D=S);continue}h=Math.abs((T-g)*(b-C)-(T-_)*(m-C)),h>f&&(f=h,c=S)}A>0&&A<x-w&&(v[s++]=Math.min(D,c),c=Math.max(D,c)),v[s++]=c,l=c}return v[s++]=this.getRawIndex(o-1),i._count=s,i._indices=v,i.getRawIndex=this._getRawIdx,i},r.prototype.minmaxDownSample=function(t,e){for(var i=this.clone([t],!0),n=i._chunks,a=Math.floor(1/e),o=n[t],s=this.count(),u=new(jr(this._rawCount))(Math.ceil(s/a)*2),l=0,f=0;f<s;f+=a){var h=f,c=o[this.getRawIndex(h)],v=f,p=o[this.getRawIndex(v)],y=a;f+a>s&&(y=s-f);for(var d=0;d<y;d++){var g=this.getRawIndex(f+d),m=o[g];m<c&&(c=m,h=f+d),m>p&&(p=m,v=f+d)}var _=this.getRawIndex(h),S=this.getRawIndex(v);h<v?(u[l++]=_,u[l++]=S):(u[l++]=S,u[l++]=_)}return i._count=l,i._indices=u,i._updateGetRawIdx(),i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],u=Math.floor(1/e),l=o[t],f=this.count(),h=a._rawExtent[t]=ti(),c=new(jr(this._rawCount))(Math.ceil(f/u)),v=0,p=0;p<f;p+=u){u>f-p&&(u=f-p,s.length=u);for(var y=0;y<u;y++){var d=this.getRawIndex(p+y);s[y]=l[d]}var g=i(s),m=this.getRawIndex(Math.min(p+n(s,g)||0,f-1));l[m]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),c[v++]=m}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var u=0,l=[];u<i;u++)l[u]=n[t[u]][s];l[u]=a,e.apply(null,l)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=ti();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],u=o[1],l=0;l<n;l++){var f=this.getRawIndex(l),h=e[f];h<s&&(s=h),h>u&&(u=h)}return o=[s,u],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&rr(t,function(s,u){return s[u]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?t1(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=J(this._extent),t._rawExtent=J(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return Ma(e[a],this._dimensions[a])}fs={arrayRows:t,objectRows:function(e,i,n,a){return Ma(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return Ma(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),e1=dt(),r1={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Qp=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=td(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return $(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Up(this.source),i=!ed(t),n="",a=[],o=0,s=0;o<t;o++){var u=void 0,l=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)u=e?h.name:null,l=h.type,f=h.ordinalMeta,s++;else{var c=this.getSourceDimension(o);c&&(u=e?c.name:null,l=c.type)}a.push({property:u,type:l,ordinalMeta:f}),e&&u!=null&&(!h||!h.isCalculationCoord)&&(n+=i?u.replace(/\`/g,"`1").replace(/\$/g,"`2"):u),n+="$",n+=r1[l]||"f",f&&(n+=f.uid),n+="$"}var v=this.source,p=[v.seriesLayoutBy,v.startIndex,n].join("$$");return{dimensions:a,hash:p}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function Jp(r){return r instanceof Qp}function jp(r){for(var t=X(),e=0;e<(r||[]).length;e++){var i=r[e],n=G(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function td(r){var t=e1(r);return t.dimNameMap||(t.dimNameMap=jp(r.dimensionsDefine))}function ed(r){return r>30}var zi=G,Ve=z,i1=typeof Int32Array>"u"?Array:Int32Array,n1="e\0\0",Mh=-1,a1=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],o1=["_approximateExtent"],Ah,ta,Gi,Hi,hs,Vi,vs,s1=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i,n=!1;Jp(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},u=!1,l={},f=0;f<i.length;f++){var h=i[f],c=V(h)?new Da({name:h}):h instanceof Da?h:new Da(h),v=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=v,c.coordDimIndex=0);var p=c.otherDims=c.otherDims||{};o.push(v),a[v]=c,l[v]!=null&&(u=!0),c.createInvertedIndices&&(s[v]=[]),p.itemName===0&&(this._nameDimIdx=f),p.itemId===0&&(this._idDimIdx=f),n&&(c.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(u),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var y=this._dimIdxToName=X();M(o,function(d){y.set(a[d].storeDimIndex,d)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(ft(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof _u&&(a=t),!a){var o=this.dimensions,s=Il(t)||Nt(t)?new Yp(t,o.length):t;a=new _u;var u=Ve(o,function(l){return{type:n._dimInfos[l].type,property:l}});a.initData(s,u,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=Z_(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e&&e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var u=s-n;this._nameList[s]=e[u],o&&vs(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==tr&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,u=s===re;if(u&&!n.pure)for(var l=[],f=t;f<e;f++){var h=n.getItem(f,l);if(!this.hasItemOption&&t0(h)&&(this.hasItemOption=!0),h){var c=h.name;a[f]==null&&c!=null&&(a[f]=we(c,null));var v=h.id;o[f]==null&&v!=null&&(o[f]=we(v,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)vs(this,f);Ah(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){zi(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=Gi(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return ta(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return F(t)?n.getValues(Ve(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i&&i[e];return n==null||isNaN(n)?Mh:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){Y(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ve(Hi(t),this._getStoreDimIndex,this);this._store.each(a,n?gt(e,n):e)},r.prototype.filterSelf=function(t,e,i){Y(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ve(Hi(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?gt(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=lt(t);return M(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){Y(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=Ve(Hi(t),this._getStoreDimIndex,this),s=Vi(this);return s._store=this._store.map(o,a?gt(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=Ve(Hi(t),this._getStoreDimIndex,this);this._store.modify(o,a?gt(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=Vi(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.minmaxDownSample=function(t,e){var i=Vi(this);return i._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),i},r.prototype.lttbDownSample=function(t,e){var i=Vi(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new mt(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new N_(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return ta(t,i)},function(i){return ta(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},zi(t)?k(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),F(a)?a=a.slice():zi(a)&&(a=k({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,zi(e)?k(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){zi(t)?k(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?k(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;c0(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){M(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:Ve(this.dimensions,this._getDimInfo,this),this.hostModel)),hs(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];Y(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(rl(arguments)))})},r.internalField=function(){Ah=function(t){var e=t._invertedIndicesMap;M(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new i1(o.categories.length);for(var u=0;u<i.length;u++)i[u]=Mh;for(var u=0;u<s.count();u++)i[s.get(a.storeDimIndex,u)]=u}})},Gi=function(t,e,i){return we(t._getCategory(e,i),null)},ta=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=Gi(t,t._idDimIdx,e)),i==null&&(i=n1+e),i},Hi=function(t){return F(t)||(t=t!=null?[t]:[]),t},Vi=function(t){var e=new r(t._schema?t._schema:Ve(t.dimensions,t._getDimInfo,t),t.hostModel);return hs(e,t),e},hs=function(t,e){M(a1.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,M(o1,function(i){t[i]=J(e[i])}),t._calculationInfo=k({},e._calculationInfo)},vs=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],u=n[e];if(s==null&&a!=null&&(i[e]=s=Gi(t,a,e)),u==null&&o!=null&&(n[e]=u=Gi(t,o,e)),u==null&&s!=null){var l=t._nameRepeatCount,f=l[s]=(l[s]||0)+1;u=s,f>1&&(u+="__ec__"+f),n[e]=u}}}(),r}();function u1(r,t){Il(r)||(r=Vp(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=X(),a=[],o=f1(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&ed(o),u=i===r.dimensionsDefine,l=u?td(r):jp(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=X(f),c=new qp(o),v=0;v<c.length;v++)c[v]=-1;function p(C){var D=c[C];if(D<0){var A=i[C],L=G(A)?A:{name:A},I=new Da,P=L.name;P!=null&&l.get(P)!=null&&(I.name=I.displayName=P),L.type!=null&&(I.type=L.type),L.displayName!=null&&(I.displayName=L.displayName);var E=a.length;return c[C]=E,I.storeDimIndex=C,a.push(I),I}return a[D]}if(!s)for(var v=0;v<o;v++)p(v);h.each(function(C,D){var A=It(C).slice();if(A.length===1&&!V(A[0])&&A[0]<0){h.set(D,!1);return}var L=h.set(D,[]);M(A,function(I,P){var E=V(I)?l.get(I):I;E!=null&&E<o&&(L[P]=E,d(p(E),D,P))})});var y=0;M(e,function(C){var D,A,L,I;if(V(C))D=C,I={};else{I=C,D=I.name;var P=I.ordinalMeta;I.ordinalMeta=null,I=k({},I),I.ordinalMeta=P,A=I.dimsDef,L=I.otherDims,I.name=I.coordDim=I.coordDimIndex=I.dimsDef=I.otherDims=null}var E=h.get(D);if(E!==!1){if(E=It(E),!E.length)for(var R=0;R<(A&&A.length||1);R++){for(;y<o&&p(y).coordDim!=null;)y++;y<o&&E.push(y++)}M(E,function(H,B){var N=p(H);if(u&&I.type!=null&&(N.type=I.type),d(rt(N,I),D,B),N.name==null&&A){var W=A[B];!G(W)&&(W={name:W}),N.name=N.displayName=W.name,N.defaultTooltip=W.defaultTooltip}L&&rt(N.otherDims,L)})}});function d(C,D,A){Np.get(D)!=null?C.otherDims[D]=A:(C.coordDim=D,C.coordDimIndex=A,n.set(D,!0))}var g=t.generateCoord,m=t.generateCoordCount,_=m!=null;m=g?m||1:0;var S=g||"value";function b(C){C.name==null&&(C.name=C.coordDim)}if(s)M(a,function(C){b(C)}),a.sort(function(C,D){return C.storeDimIndex-D.storeDimIndex});else for(var w=0;w<o;w++){var x=p(w),T=x.coordDim;T==null&&(x.coordDim=h1(S,n,_),x.coordDimIndex=0,(!g||m<=0)&&(x.isExtraCoord=!0),m--),b(x),x.type==null&&(Gp(r,w)===St.Must||x.isExtraCoord&&(x.otherDims.itemName!=null||x.otherDims.seriesName!=null))&&(x.type="ordinal")}return l1(a),new Qp({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function l1(r){for(var t=X(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function f1(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return M(t,function(a){var o;G(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function h1(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}var cs={},Rl=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];M(cs,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){M(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){cs[t]=e},r.get=function(t){return cs[t]},r}(),v1=function(){function r(t){this.coordSysDims=[],this.axisMap=X(),this.categoryAxisMap=X(),this.coordSysName=t}return r}();function c1(r){var t=r.get("coordinateSystem"),e=new v1(t),i=p1[t];if(i)return i(r,e,e.axisMap,e.categoryAxisMap),e}var p1={cartesian2d:function(r,t,e,i){var n=r.getReferringComponents("xAxis",fe).models[0],a=r.getReferringComponents("yAxis",fe).models[0];t.coordSysDims=["x","y"],e.set("x",n),e.set("y",a),ei(n)&&(i.set("x",n),t.firstCategoryDimIndex=0),ei(a)&&(i.set("y",a),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},singleAxis:function(r,t,e,i){var n=r.getReferringComponents("singleAxis",fe).models[0];t.coordSysDims=["single"],e.set("single",n),ei(n)&&(i.set("single",n),t.firstCategoryDimIndex=0)},polar:function(r,t,e,i){var n=r.getReferringComponents("polar",fe).models[0],a=n.findAxisModel("radiusAxis"),o=n.findAxisModel("angleAxis");t.coordSysDims=["radius","angle"],e.set("radius",a),e.set("angle",o),ei(a)&&(i.set("radius",a),t.firstCategoryDimIndex=0),ei(o)&&(i.set("angle",o),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},geo:function(r,t,e,i){t.coordSysDims=["lng","lat"]},parallel:function(r,t,e,i){var n=r.ecModel,a=n.getComponent("parallel",r.get("parallelIndex")),o=t.coordSysDims=a.dimensions.slice();M(a.parallelAxisIndex,function(s,u){var l=n.getComponent("parallelAxis",s),f=o[u];e.set(f,l),ei(l)&&(i.set(f,l),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=u))})}};function ei(r){return r.get("type")==="category"}function d1(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;g1(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var u=!!(r&&r.get("stack")),l,f,h,c;if(M(a,function(m,_){V(m)&&(a[_]=m={name:m}),u&&!m.isExtraCoord&&(!i&&!l&&m.ordinalMeta&&(l=m),!f&&m.type!=="ordinal"&&m.type!=="time"&&(!n||n===m.coordDim)&&(f=m))}),f&&!i&&!l&&(i=!0),f){h="__\0ecstackresult_"+r.id,c="__\0ecstackedover_"+r.id,l&&(l.createInvertedIndices=!0);var v=f.coordDim,p=f.type,y=0;M(a,function(m){m.coordDim===v&&y++});var d={name:h,coordDim:v,coordDimIndex:y,type:p,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},g={name:c,coordDim:c,coordDimIndex:y+1,type:p,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(d.storeDimIndex=s.ensureCalculationDimension(c,p),g.storeDimIndex=s.ensureCalculationDimension(h,p)),o.appendCalculationDimension(d),o.appendCalculationDimension(g)):(a.push(d),a.push(g))}return{stackedDimension:f&&f.name,stackedByDimension:l&&l.name,isStackedByIndex:i,stackedOverDimension:c,stackResultDimension:h}}function g1(r){return!Jp(r.schema)}function wi(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function y1(r,t){return wi(r,t)?r.getCalculationInfo("stackResultDimension"):t}function m1(r,t){var e=r.get("coordinateSystem"),i=Rl.get(e),n;return t&&t.coordSysDims&&(n=z(t.coordSysDims,function(a){var o={name:a},s=t.axisMap.get(a);if(s){var u=s.get("type");o.type=q_(u)}return o})),n||(n=i&&(i.getDimensionsInfo?i.getDimensionsInfo():i.dimensions.slice())||["x","y"]),n}function _1(r,t,e){var i,n;return e&&M(r,function(a,o){var s=a.coordDim,u=e.categoryAxisMap.get(s);u&&(i==null&&(i=o),a.ordinalMeta=u.getOrdinalMeta(),t&&(a.createInvertedIndices=!0)),a.otherDims.itemName!=null&&(n=!0)}),!n&&i!=null&&(r[i].otherDims.itemName=0),i}function S1(r,t,e){e=e||{};var i=t.getSourceManager(),n,a=!1;n=i.getSource(),a=n.sourceFormat===re;var o=c1(t),s=m1(t,o),u=e.useEncodeDefaulter,l=Y(u)?u:u?yt(z_,s,t):null,f={coordDimensions:s,generateCoord:e.generateCoord,encodeDefine:t.getEncode(),encodeDefaulter:l,canOmitUnusedDimensions:!a},h=u1(n,f),c=_1(h.dimensions,e.createInvertedIndices,o),v=a?null:i.getSharedDataStore(h),p=d1(t,{schema:h,store:v}),y=new s1(h,t);y.setCalculationInfo(p);var d=c!=null&&w1(n)?function(g,m,_,S){return S===c?_:this.defaultDimValueGetter(g,m,_,S)}:null;return y.hasItemOption=!1,y.initData(a?n:v,null,d),y}function w1(r){if(r.sourceFormat===re){var t=b1(r.data||[]);return!F(Rn(t))}}function b1(r){for(var t=0;t<r.length&&r[t]==null;)t++;return r[t]}var x1=Math.round(Math.random()*10);function xo(r){return[r||"",x1++].join("_")}function T1(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=me(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=me(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function C1(r,t){r.topologicalTravel=function(a,o,s,u){if(!a.length)return;var l=e(o),f=l.graph,h=l.noEntryList,c={};for(M(a,function(m){c[m]=!0});h.length;){var v=h.pop(),p=f[v],y=!!c[v];y&&(s.call(u,v,p.originalDeps.slice()),delete c[v]),M(p.successor,y?g:d)}M(c,function(){var m="";throw new Error(m)});function d(m){f[m].entryCount--,f[m].entryCount===0&&h.push(m)}function g(m){c[m]=!0,d(m)}};function e(a){var o={},s=[];return M(a,function(u){var l=i(o,u),f=l.originalDeps=t(u),h=n(f,a);l.entryCount=h.length,l.entryCount===0&&s.push(u),M(h,function(c){ot(l.predecessor,c)<0&&l.predecessor.push(c);var v=i(o,c);ot(v.successor,c)<0&&v.successor.push(u)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return M(a,function(u){ot(o,u)>=0&&s.push(u)}),s}}function D1(r,t){return et(et({},r,!0),t,!0)}var M1=Math.log(2);function Su(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var u=Math.round(Math.log((1<<s)-1&~n)/M1);return r[e][u]}for(var l=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var p=1<<c;p&n||(h+=(v%2?-1:1)*r[e][c]*Su(r,t-1,f,l,n|p,a),v++)}return a[o]=h,h}function Lh(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=Su(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*Su(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(u,l,f){var h=l*a[6]+f*a[7]+1;u[0]=(l*a[0]+f*a[1]+a[2])/h,u[1]=(l*a[3]+f*a[4]+a[5])/h}}}var Ph="___zrEVENTSAVED",ps=[];function UC(r,t,e,i,n){return wu(ps,t,i,n,!0)&&wu(r,e,ps[0],ps[1])}function wu(r,t,e,i,n){if(t.getBoundingClientRect&&Q.domSupported&&!rd(t)){var a=t[Ph]||(t[Ph]={}),o=A1(t,a),s=L1(o,a,n);if(s)return s(r,e,i),!0}return!1}function A1(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,u=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[u]+":0",n[l]+":0",i[1-u]+":auto",n[1-l]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function L1(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],u=!0,l=0;l<4;l++){var f=r[l].getBoundingClientRect(),h=2*l,c=f.left,v=f.top;o.push(c,v),u=u&&a&&c===a[h]&&v===a[h+1],s.push(r[l].offsetLeft,r[l].offsetTop)}return u&&n?n:(t.srcCoords=o,t[i]=e?Lh(s,o):Lh(o,s))}function rd(r){return r.nodeName.toUpperCase()==="CANVAS"}var P1=/([&<>"'])/g,I1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Wt(r){return r==null?"":(r+"").replace(P1,function(t,e){return I1[e]})}const E1={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},R1={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var Ka="ZH",kl="EN",gi=kl,Aa={},Ol={},id=Q.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||gi).toUpperCase();return r.indexOf(Ka)>-1?Ka:gi}():gi;function nd(r,t){r=r.toUpperCase(),Ol[r]=new mt(t),Aa[r]=t}function k1(r){if(V(r)){var t=Aa[r.toUpperCase()]||{};return r===Ka||r===kl?J(t):et(J(t),J(Aa[gi]),!1)}else return et(J(r),J(Aa[gi]),!1)}function O1(r){return Ol[r]}function B1(){return Ol[gi]}nd(kl,E1);nd(Ka,R1);var Bl=1e3,Nl=Bl*60,vn=Nl*60,jt=vn*24,Ih=jt*365,en={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ea="{yyyy}-{MM}-{dd}",Eh={year:"{yyyy}",month:"{yyyy}-{MM}",day:ea,hour:ea+" "+en.hour,minute:ea+" "+en.minute,second:ea+" "+en.second,millisecond:en.none},ds=["year","month","day","hour","minute","second","millisecond"],ad=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function We(r,t){return r+="","0000".substr(0,t-r.length)+r}function yi(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function N1(r){return r===yi(r)}function F1(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function Fl(r,t,e,i){var n=Fe(r),a=n[zl(e)](),o=n[mi(e)]()+1,s=Math.floor((o-1)/3)+1,u=n[To(e)](),l=n["get"+(e?"UTC":"")+"Day"](),f=n[bn(e)](),h=(f-1)%12+1,c=n[Co(e)](),v=n[Do(e)](),p=n[Mo(e)](),y=f>=12?"pm":"am",d=y.toUpperCase(),g=i instanceof mt?i:O1(i||id)||B1(),m=g.getModel("time"),_=m.get("month"),S=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,y+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,We(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,We(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,We(u,2)).replace(/{d}/g,u+"").replace(/{eeee}/g,b[l]).replace(/{ee}/g,w[l]).replace(/{e}/g,l+"").replace(/{HH}/g,We(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,We(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,We(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,We(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,We(p,3)).replace(/{S}/g,p+"")}function z1(r,t,e,i,n){var a=null;if(V(e))a=e;else if(Y(e))a=e(r.value,t,{level:r.level});else{var o=k({},en);if(r.level>0)for(var s=0;s<ds.length;++s)o[ds[s]]="{primary|"+o[ds[s]]+"}";var u=e?e.inherit===!1?e:rt(e,o):o,l=od(r.value,n);if(u[l])a=u[l];else if(u.inherit){for(var f=ad.indexOf(l),s=f-1;s>=0;--s)if(u[l]){a=u[l];break}a=a||o.none}if(F(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return Fl(new Date(r.value),a,n,i)}function od(r,t){var e=Fe(r),i=e[mi(t)]()+1,n=e[To(t)](),a=e[bn(t)](),o=e[Co(t)](),s=e[Do(t)](),u=e[Mo(t)](),l=u===0,f=l&&s===0,h=f&&o===0,c=h&&a===0,v=c&&n===1,p=v&&i===1;return p?"year":v?"month":c?"day":h?"hour":f?"minute":l?"second":"millisecond"}function Rh(r,t,e){var i=ft(r)?Fe(r):r;switch(t=t||od(r,e),t){case"year":return i[zl(e)]();case"half-year":return i[mi(e)]()>=6?1:0;case"quarter":return Math.floor((i[mi(e)]()+1)/4);case"month":return i[mi(e)]();case"day":return i[To(e)]();case"half-day":return i[bn(e)]()/24;case"hour":return i[bn(e)]();case"minute":return i[Co(e)]();case"second":return i[Do(e)]();case"millisecond":return i[Mo(e)]()}}function zl(r){return r?"getUTCFullYear":"getFullYear"}function mi(r){return r?"getUTCMonth":"getMonth"}function To(r){return r?"getUTCDate":"getDate"}function bn(r){return r?"getUTCHours":"getHours"}function Co(r){return r?"getUTCMinutes":"getMinutes"}function Do(r){return r?"getUTCSeconds":"getSeconds"}function Mo(r){return r?"getUTCMilliseconds":"getMilliseconds"}function G1(r){return r?"setUTCFullYear":"setFullYear"}function sd(r){return r?"setUTCMonth":"setMonth"}function ud(r){return r?"setUTCDate":"setDate"}function ld(r){return r?"setUTCHours":"setHours"}function fd(r){return r?"setUTCMinutes":"setMinutes"}function hd(r){return r?"setUTCSeconds":"setSeconds"}function vd(r){return r?"setUTCMilliseconds":"setMilliseconds"}function cd(r){if(!jm(r))return V(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function YC(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var Gl=Ic;function bu(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&Re(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var u=o?Fe(r):r;if(isNaN(+u)){if(s)return"-"}else return Fl(u,i,e)}if(t==="ordinal")return qs(r)?n(r):ft(r)&&a(r)?r+"":"-";var l=Wa(r);return a(l)?cd(l):qs(r)?n(r):typeof r=="boolean"?r+"":"-"}var kh=["a","b","c","d","e","f","g"],gs=function(r,t){return"{"+r+(t??"")+"}"};function H1(r,t,e){F(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=kh[a];r=r.replace(gs(o),gs(o,0))}for(var s=0;s<i;s++)for(var u=0;u<n.length;u++){var l=t[s][n[u]];r=r.replace(gs(kh[u],s),e?Wt(l):l)}return r}function V1(r,t){var e=V(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Wt(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Wt(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function xu(r,t){return t=t||"transparent",V(r)?r:G(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function Oh(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var La=M,W1=["left","right","top","bottom","width","height"],ra=[["width","left","right"],["height","top","bottom"]];function Hl(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(u,l){var f=u.getBoundingRect(),h=t.childAt(l+1),c=h&&h.getBoundingRect(),v,p;if(r==="horizontal"){var y=f.width+(c?-c.x+f.x:0);v=a+y,v>i||u.newline?(a=0,v=y,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var d=f.height+(c?-c.y+f.y:0);p=o+d,p>n||u.newline?(a+=s+e,o=0,p=d,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=a,u.y=o,u.markRedraw(),r==="horizontal"?a=v+e:o=p+e)})}var _i=Hl;yt(Hl,"vertical");yt(Hl,"horizontal");function bi(r,t,e){e=Gl(e||0);var i=t.width,n=t.height,a=Ht(r.left,i),o=Ht(r.top,n),s=Ht(r.right,i),u=Ht(r.bottom,n),l=Ht(r.width,i),f=Ht(r.height,n),h=e[2]+e[0],c=e[1]+e[3],v=r.aspect;switch(isNaN(l)&&(l=i-s-c-a),isNaN(f)&&(f=n-u-h-o),v!=null&&(isNaN(l)&&isNaN(f)&&(v>i/n?l=i*.8:f=n*.8),isNaN(l)&&(l=v*f),isNaN(f)&&(f=l/v)),isNaN(a)&&(a=i-s-l-c),isNaN(o)&&(o=n-u-f-h),r.left||r.right){case"center":a=i/2-l/2-e[3];break;case"right":a=i-l-c;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(l)&&(l=i-c-a-(s||0)),isNaN(f)&&(f=n-h-o-(u||0));var p=new j(a+e[3],o+e[0],l,f);return p.margin=e,p}function U1(r,t,e,i,n,a){var o=!n||!n.hv||n.hv[0],s=!n||!n.hv||n.hv[1],u=n&&n.boundingMode||"all";if(a=a||r,a.x=r.x,a.y=r.y,!o&&!s)return!1;var l;if(u==="raw")l=r.type==="group"?new j(0,0,+t.width||0,+t.height||0):r.getBoundingRect();else if(l=r.getBoundingRect(),r.needLocalTransform()){var f=r.getLocalTransform();l=l.clone(),l.applyTransform(f)}var h=bi(rt({width:l.width,height:l.height},t),e,i),c=o?h.x-l.x:0,v=s?h.y-l.y:0;return u==="raw"?(a.x=c,a.y=v):(a.x+=c,a.y+=v),a===r&&r.markRedraw(),!0}function xn(r){var t=r.layoutMode||r.constructor.layoutMode;return G(t)?t:t?{type:t}:null}function xi(r,t,e){var i=e&&e.ignoreSize;!F(i)&&(i=[i,i]);var n=o(ra[0],0),a=o(ra[1],1);l(ra[0],r,n),l(ra[1],r,a);function o(f,h){var c={},v=0,p={},y=0,d=2;if(La(f,function(_){p[_]=r[_]}),La(f,function(_){s(t,_)&&(c[_]=p[_]=t[_]),u(c,_)&&v++,u(p,_)&&y++}),i[h])return u(t,f[1])?p[f[2]]=null:u(t,f[2])&&(p[f[1]]=null),p;if(y===d||!v)return p;if(v>=d)return c;for(var g=0;g<f.length;g++){var m=f[g];if(!s(c,m)&&s(r,m)){c[m]=r[m];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function u(f,h){return f[h]!=null&&f[h]!=="auto"}function l(f,h,c){La(f,function(v){h[v]=c[v]})}}function Ao(r){return Y1({},r)}function Y1(r,t){return t&&r&&La(W1,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var X1=dt(),nt=function(r){O(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=xo("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=xn(this),a=n?Ao(e):{},o=i.getTheme();et(e,o.get(this.mainType)),et(e,this.getDefaultOption()),n&&xi(e,a,n)},t.prototype.mergeOption=function(e,i){et(this.option,e,!0);var n=xn(this);n&&xi(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!gy(e))return e.defaultOption;var i=X1(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},u=n.length-1;u>=0;u--)s=et(s,n[u],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return ho(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(mt);Bc(nt,mt);so(nt);T1(nt);C1(nt,$1);function $1(r){var t=[];return M(nt.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=z(t,function(e){return me(e).main}),r!=="dataset"&&ot(t,"dataset")<=0&&t.unshift("dataset"),t}var Bh=dt();dt();var Vl=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=It(this.get("color",!0)),a=this.get("colorLayer",!0);return q1(this,Bh,n,a,t,e,i)},r.prototype.clearColorPalette=function(){K1(this,Bh)},r}();function Z1(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function q1(r,t,e,i,n,a,o){a=a||r;var s=t(a),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(n))return l[n];var f=o==null||!i?e:Z1(i,o);if(f=f||e,!(!f||!f.length)){var h=f[u];return n&&(l[n]=h),s.paletteIdx=(u+1)%f.length,h}}function K1(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var Q1=/\{@(.+?)\}/g,J1=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),u=i.getItemVisual(t,"style"),l=u&&u[i.getItemVisual(t,"drawType")||"fill"],f=u&&u.stroke,h=this.mainType,c=h==="series",v=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:l,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),u=this.getDataParams(t,i);if(o&&(u.value=o.interpolatedValue),n!=null&&F(u.value)&&(u.value=u.value[n]),!a){var l=s.getItemModel(t);a=l.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(Y(a))return u.status=e,u.dimensionIndex=n,a(u);if(V(a)){var f=H1(a,u);return f.replace(Q1,function(h,c){var v=c.length,p=c;p.charAt(0)==="["&&p.charAt(v-1)==="]"&&(p=+p.slice(1,v-1));var y=Si(s,t,p);if(o&&F(o.interpolatedValue)){var d=s.getDimensionIndex(p);d>=0&&(y=o.interpolatedValue[d])}return y!=null?y+"":""})}},r.prototype.getRawValue=function(t,e){return Si(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function XC(r){var t,e;return G(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function cn(r){return new j1(r)}var j1=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,u=f(t&&t.modBy),l=t&&t.modDataCount||0;(o!==u||s!==l)&&(a="reset");function f(m){return!(m>=1)&&(m=1),m}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=u,this._modDataCount=l;var c=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,p=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||v<p)){var y=this._progress;if(F(y))for(var d=0;d<y.length;d++)this._doProgress(y[d],v,p,u,l);else this._doProgress(y,v,p,u,l)}this._dueIndex=p;var g=this._settedOutputEnd!=null?this._settedOutputEnd:p;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){Nh.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:Nh.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),F(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),Nh=function(){var r,t,e,i,n,a={reset:function(u,l,f,h){t=u,r=l,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var u=t%n*e+Math.ceil(t/n),l=t>=r?null:u<i?u:t;return t++,l}}(),tS=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return Ma(t,e)},r}();function eS(r,t){var e=new tS,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==Ne&&Vt(o);var s=[],u={},l=r.dimensionsDefine;if(l)M(l,function(y,d){var g=y.name,m={index:d,name:g,displayName:y.displayName};if(s.push(m),g!=null){var _="";Gr(u,g)&&Vt(_),u[g]=m}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=Xp(n,Ne);t.__isBuiltIn&&(e.getRawDataItem=function(y){return h(i,a,s,y)},e.getRawData=gt(rS,null,r)),e.cloneRawData=gt(iS,null,r);var c=$p(n,Ne);e.count=gt(c,null,i,a,s);var v=Zp(n);e.retrieveValue=function(y,d){var g=h(i,a,s,y);return p(g,d)};var p=e.retrieveValueFromItem=function(y,d){if(y!=null){var g=s[d];if(g)return v(y,d,g.name)}};return e.getDimensionInfo=gt(nS,null,s,u),e.cloneAllDimensionInfo=gt(aS,null,s),e}function rS(r){var t=r.sourceFormat;if(!Wl(t)){var e="";Vt(e)}return r.data}function iS(r){var t=r.sourceFormat,e=r.data;if(!Wl(t)){var i="";Vt(i)}if(t===zt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===ve){for(var n=[],a=0,o=e.length;a<o;a++)n.push(k({},e[a]));return n}}function nS(r,t,e){if(e!=null){if(ft(e)||!isNaN(e)&&!Gr(t,e))return r[e];if(Gr(t,e))return t[e]}}function aS(r){return J(r)}var pd=X();function oS(r){r=J(r);var t=r.type,e="";t||Vt(e);var i=t.split(":");i.length!==2&&Vt(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,pd.set(t,r)}function sS(r,t,e){var i=It(r),n=i.length,a="";n||Vt(a);for(var o=0,s=n;o<s;o++){var u=i[o];t=uS(u,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function uS(r,t,e,i){var n="";t.length||Vt(n),G(r)||Vt(n);var a=r.type,o=pd.get(a);o||Vt(n);var s=z(t,function(l){return eS(l,o)}),u=It(o.transform({upstream:s[0],upstreamList:s,config:J(r.config)}));return z(u,function(l,f){var h="";G(l)||Vt(h),l.data||Vt(h);var c=Wp(l.data);Wl(c)||Vt(h);var v,p=t[0];if(p&&f===0&&!l.dimensions){var y=p.startIndex;y&&(l.data=p.data.slice(0,y).concat(l.data)),v={seriesLayoutBy:Ne,sourceHeader:y,dimensions:p.metaRawOption.dimensions}}else v={seriesLayoutBy:Ne,sourceHeader:0,dimensions:l.dimensions};return mu(l.data,v,null)})}function Wl(r){return r===zt||r===ve}var lS=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(ia(t)){var o=t,s=void 0,u=void 0,l=void 0;if(i){var f=e[0];f.prepareSource(),l=f.getSource(),s=l.data,u=l.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),u=Ft(s)?tr:re,a=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},v=$(h.seriesLayoutBy,c.seriesLayoutBy)||null,p=$(h.sourceHeader,c.sourceHeader),y=$(h.dimensions,c.dimensions),d=v!==c.seriesLayoutBy||!!p!=!!c.sourceHeader||y;n=d?[mu(s,{seriesLayoutBy:v,sourceHeader:p,dimensions:y},u)]:[]}else{var g=t;if(i){var m=this._applyTransform(e);n=m.sourceList,a=m.upstreamSignList}else{var _=g.get("source",!0);n=[mu(_,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&Fh(a)}var o,s=[],u=[];return M(t,function(l){l.prepareSource();var f=l.getSource(n||0),h="";n!=null&&!f&&Fh(h),s.push(f),u.push(l._getVersionSign())}),i?o=sS(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[H_(s[0])]),{sourceList:o,upstreamSignList:u}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var u=this._getUpstreamSourceManagers()[0];ia(this._sourceHost)&&u?s=u._innerGetDataStore(t,e,i):(s=new _u,s.initData(new Yp(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(ia(t)){var e=Pl(t);return e?[e.getSourceManager()]:[]}else return z(G_(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(ia(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function ia(r){return r.mainType==="series"}function Fh(r){throw new Error(r)}var fS="line-height:1";function dd(r){var t=r.lineHeight;return t==null?fS:"line-height:"+Wt(t+"")+"px"}function gd(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Wt(i+"")+"px;color:"+Wt(e)+";font-weight:"+Wt(n+""),valueStyle:"font-size:"+Wt(o+"")+"px;color:"+Wt(a)+";font-weight:"+Wt(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var hS=[0,10,20,30],vS=["",`
`,`

`,`


`];function Tu(r,t){return t.type=r,t}function Cu(r){return r.type==="section"}function yd(r){return Cu(r)?cS:pS}function md(r){if(Cu(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return M(r.blocks,function(n){var a=md(n);a>=t&&(t=a+ +(i&&(!a||Cu(n)&&!n.noHeader)))}),t}return 0}function cS(r,t,e,i){var n=t.noHeader,a=dS(md(t)),o=[],s=t.blocks||[];xe(!s||F(s)),s=s||[];var u=r.orderMode;if(t.sortBlocks&&u){s=s.slice();var l={valueAsc:"asc",valueDesc:"desc"};if(Gr(l,u)){var f=new Q_(l[u],null);s.sort(function(y,d){return f.evaluate(y.sortParam,d.sortParam)})}else u==="seriesDesc"&&s.reverse()}M(s,function(y,d){var g=t.valueFormatter,m=yd(y)(g?k(k({},r),{valueFormatter:g}):r,y,d>0?a.html:0,i);m!=null&&o.push(m)});var h=r.renderMode==="richText"?o.join(a.richText):Du(i,o.join(""),n?e:a.html);if(n)return h;var c=bu(t.header,"ordinal",r.useUTC),v=gd(i,r.renderMode).nameStyle,p=dd(i);return r.renderMode==="richText"?_d(r,c,v)+a.richText+h:Du(i,'<div style="'+v+";"+p+';">'+Wt(c)+"</div>"+h,e)}function pS(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,u=t.name,l=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(S){return S=F(S)?S:[S],z(S,function(b,w){return bu(b,F(v)?v[w]:v,l)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),c=a?"":bu(u,"ordinal",l),v=t.valueType,p=o?[]:f(t.value,t.dataIndex),y=!s||!a,d=!s&&a,g=gd(i,n),m=g.nameStyle,_=g.valueStyle;return n==="richText"?(s?"":h)+(a?"":_d(r,c,m))+(o?"":mS(r,p,y,d,_)):Du(i,(s?"":h)+(a?"":gS(c,!s,m))+(o?"":yS(p,y,d,_)),e)}}function $C(r,t,e,i,n,a){if(r){var o=yd(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function dS(r){return{html:hS[r],richText:vS[r]}}function Du(r,t,e){var i='<div style="clear:both"></div>',n="margin: "+e+"px 0 0",a=dd(r);return'<div style="'+n+";"+a+';">'+t+i+"</div>"}function gS(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Wt(r)+"</span>"}function yS(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=F(r)?r:[r],'<span style="'+a+";"+i+'">'+z(r,function(o){return Wt(o)}).join("&nbsp;&nbsp;")+"</span>"}function _d(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function mS(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(F(t)?t.join("  "):t,a)}function _S(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return xu(i)}function ZC(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var qC=function(){function r(){this.richTextStyles={},this._nextStyleNameId=op()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=V1({color:e,type:t,renderMode:i,markerId:n});return V(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};F(e)?M(e,function(a){return k(i,a)}):k(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function SS(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),u=F(s),l=_S(t,e),f,h,c,v;if(o>1||u&&!o){var p=wS(s,t,e,a,l);f=p.inlineValues,h=p.inlineValueTypes,c=p.blocks,v=p.inlineValues[0]}else if(o){var y=n.getDimensionInfo(a[0]);v=f=Si(n,e,a[0]),h=y.type}else v=f=u?s[0]:s;var d=vl(t),g=d&&t.name||"",m=n.getName(e),_=i?g:m;return Tu("section",{header:g,noHeader:i||!d,sortParam:v,blocks:[Tu("nameValue",{markerType:"item",markerColor:l,name:_,noName:!Re(_),value:f,valueType:h,dataIndex:e})].concat(c||[])})}function wS(r,t,e,i,n){var a=t.getData(),o=rr(r,function(h,c,v){var p=a.getDimensionInfo(v);return h=h||p&&p.tooltip!==!1&&p.displayName!=null},!1),s=[],u=[],l=[];i.length?M(i,function(h){f(Si(a,e,h),h)}):M(r,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?l.push(Tu("nameValue",{markerType:"subItem",markerColor:n,name:v.displayName,value:h,valueType:v.type})):(s.push(h),u.push(v.type)))}return{inlineValues:s,inlineValueTypes:u,blocks:l}}var Ue=dt();function na(r,t){return r.getName(t)||r.getId(t)}var bS="__universalTransitionEnabled",or=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=cn({count:TS,reset:CS}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=Ue(this).sourceManager=new lS(this);a.prepareSource();var o=this.getInitialData(e,n);Gh(o,this),this.dataTask.context.data=o,Ue(this).dataBeforeProcessed=o,zh(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=xn(this),a=n?Ao(e):{},o=this.subType;nt.hasClass(o)&&(o+="Series"),et(e,i.getTheme().get(this.subType)),et(e,this.getDefaultOption()),Uf(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&xi(e,a,n)},t.prototype.mergeOption=function(e,i){e=et(this.option,e,!0),this.fillDataTextStyle(e.data);var n=xn(this);n&&xi(this.option,e,n);var a=Ue(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);Gh(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,Ue(this).dataBeforeProcessed=o,zh(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!Ft(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&Uf(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=Mu(this);if(i){var n=i.context.data;return e==null||!n.getLinkedData?n:n.getLinkedData(e)}else return Ue(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=Mu(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}Ue(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return X(e)},t.prototype.getSourceManager=function(){return Ue(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return Ue(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return SS({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if(Q.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=Vl.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var u=e[s],l=na(o,u);n[l]=!1,this._selectedDataIndicesMap[l]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=lt(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[na(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[bS])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,u=i.length;if(!(!s||!u)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){G(o.selectedMap)||(o.selectedMap={});for(var l=o.selectedMap,f=0;f<u;f++){var h=i[f],c=na(e,h);l[c]=!0,this._selectedDataIndicesMap[c]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var v=i[u-1],c=na(e,v);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(a={},a[c]=e.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return nt.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(nt);De(or,J1);De(or,Vl);Bc(or,nt);function zh(r){var t=r.name;vl(r)||(r.name=xS(r)||t)}function xS(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return M(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function TS(r){return r.model.getRawData().count()}function CS(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),DS}function DS(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function Gh(r,t){M(ly(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,yt(MS,t))})}function MS(r,t){var e=Mu(r);return e&&e.setOutputEnd((t||this).count()),t}function Mu(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}var AS=st.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),LS=st.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),PS=st.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),u=i-a+o+s,l=Math.asin(s/o),f=Math.cos(l)*o,h=Math.sin(l),c=Math.cos(l),v=o*.6,p=o*.7;r.moveTo(e-f,u+s),r.arc(e,u,o,Math.PI-l,Math.PI*2+l),r.bezierCurveTo(e+f-h*v,u+s+c*v,e,i-p,e,i),r.bezierCurveTo(e,i-p,e-f+h*v,u+s+c*v,e-f,u+s),r.closePath()}}),IS=st.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),ES={line:ir,rect:xt,roundRect:xt,square:xt,circle:go,diamond:LS,pin:PS,arrow:IS,triangle:AS},RS={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},Au={};M(ES,function(r,t){Au[t]=new r});var kS=st.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=zc(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=Au[i];n||(i="rect",n=Au[i]),RS[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function OS(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Ti(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var u;return r.indexOf("image://")===0?u=Pp(r.slice(8),new j(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?u=xl(r.slice(7),{},new j(t,e,i,n),o?"center":"cover"):u=new kS({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),u.__isEmptyBrush=s,u.setColor=OS,a&&u.setColor(a),u}function BS(r){return F(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function Sd(r,t){if(r!=null)return F(r)||(r=[r,r]),[Ht(r[0],t[0])||0,Ht($(r[1],r[0]),t[1])||0]}var NS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.hasSymbolVisual=!0,e}return t.prototype.getInitialData=function(e){return S1(null,this,{useEncodeDefaulter:!0})},t.prototype.getLegendIcon=function(e){var i=new At,n=Ti("line",0,e.itemHeight/2,e.itemWidth,0,e.lineStyle.stroke,!1);i.add(n),n.setStyle(e.lineStyle);var a=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=a==="none"?"circle":a,u=e.itemHeight*.8,l=Ti(s,(e.itemWidth-u)/2,(e.itemHeight-u)/2,u,u,e.itemStyle.fill);i.add(l),l.setStyle(e.itemStyle);var f=e.iconRotate==="inherit"?o:e.iconRotate||0;return l.rotation=f*Math.PI/180,l.setOrigin([e.itemWidth/2,e.itemHeight/2]),s.indexOf("empty")>-1&&(l.style.stroke=l.style.fill,l.style.fill="#fff",l.style.lineWidth=2),i},t.type="series.line",t.dependencies=["grid","polar"],t.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},t}(or);function wd(r,t){var e=r.mapDimensionsAll("defaultedLabel"),i=e.length;if(i===1){var n=Si(r,t,e[0]);return n!=null?n+"":null}else if(i){for(var a=[],o=0;o<e.length;o++)a.push(Si(r,t,e[o]));return a.join(" ")}}function FS(r,t){var e=r.mapDimensionsAll("defaultedLabel");if(!F(t))return t+"";for(var i=[],n=0;n<e.length;n++){var a=r.getDimensionIndex(e[n]);a>=0&&i.push(t[a])}return i.join(" ")}var Ul=function(r){O(t,r);function t(e,i,n,a){var o=r.call(this)||this;return o.updateData(e,i,n,a),o}return t.prototype._createSymbol=function(e,i,n,a,o){this.removeAll();var s=Ti(e,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:a[0]/2,scaleY:a[1]/2}),s.drift=zS,this._symbolType=e,this.add(s)},t.prototype.stopSymbolAnimation=function(e){this.childAt(0).stopAnimation(null,e)},t.prototype.getSymbolType=function(){return this._symbolType},t.prototype.getSymbolPath=function(){return this.childAt(0)},t.prototype.highlight=function(){Ua(this.childAt(0))},t.prototype.downplay=function(){Ya(this.childAt(0))},t.prototype.setZ=function(e,i){var n=this.childAt(0);n.zlevel=e,n.z=i},t.prototype.setDraggable=function(e,i){var n=this.childAt(0);n.draggable=e,n.cursor=!i&&e?"move":n.cursor},t.prototype.updateData=function(e,i,n,a){this.silent=!1;var o=e.getItemVisual(i,"symbol")||"circle",s=e.hostModel,u=t.getSymbolSize(e,i),l=o!==this._symbolType,f=a&&a.disableAnimation;if(l){var h=e.getItemVisual(i,"symbolKeepAspect");this._createSymbol(o,e,i,u,h)}else{var c=this.childAt(0);c.silent=!1;var v={scaleX:u[0]/2,scaleY:u[1]/2};f?c.attr(v):nr(c,v,s,i),h_(c)}if(this._updateCommon(e,i,u,n,a),l){var c=this.childAt(0);if(!f){var v={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:c.style.opacity}};c.scaleX=c.scaleY=0,c.style.opacity=0,kn(c,v,s,i)}}f&&this.childAt(0).stopAnimation("leave")},t.prototype._updateCommon=function(e,i,n,a,o){var s=this.childAt(0),u=e.hostModel,l,f,h,c,v,p,y,d,g;if(a&&(l=a.emphasisItemStyle,f=a.blurItemStyle,h=a.selectItemStyle,c=a.focus,v=a.blurScope,y=a.labelStatesModels,d=a.hoverScale,g=a.cursorStyle,p=a.emphasisDisabled),!a||e.hasItemOption){var m=a&&a.itemModel?a.itemModel:e.getItemModel(i),_=m.getModel("emphasis");l=_.getModel("itemStyle").getItemStyle(),h=m.getModel(["select","itemStyle"]).getItemStyle(),f=m.getModel(["blur","itemStyle"]).getItemStyle(),c=_.get("focus"),v=_.get("blurScope"),p=_.get("disabled"),y=Al(m),d=_.getShallow("scale"),g=m.getShallow("cursor")}var S=e.getItemVisual(i,"symbolRotate");s.attr("rotation",(S||0)*Math.PI/180||0);var b=Sd(e.getItemVisual(i,"symbolOffset"),n);b&&(s.x=b[0],s.y=b[1]),g&&s.attr("cursor",g);var w=e.getItemVisual(i,"style"),x=w.fill;if(s instanceof sr){var T=s.style;s.useStyle(k({image:T.image,x:T.x,y:T.y,width:T.width,height:T.height},w))}else s.__isEmptyBrush?s.useStyle(k({},w)):s.useStyle(w),s.style.decal=null,s.setColor(x,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var C=e.getItemVisual(i,"liftZ"),D=this._z2;C!=null?D==null&&(this._z2=s.z2,s.z2+=C):D!=null&&(s.z2=D,this._z2=null);var A=o&&o.useNameLabel;Ml(s,y,{labelFetcher:u,labelDataIndex:i,defaultText:L,inheritColor:x,defaultOpacity:w.opacity});function L(E){return A?e.getName(E):wd(e,E)}this._sizeX=n[0]/2,this._sizeY=n[1]/2;var I=s.ensureState("emphasis");I.style=l,s.ensureState("select").style=h,s.ensureState("blur").style=f;var P=d==null||d===!0?Math.max(1.1,3/this._sizeY):isFinite(d)&&d>0?+d:1;I.scaleX=this._sizeX*P,I.scaleY=this._sizeY*P,this.setSymbolScale(1),cu(this,c,v,p)},t.prototype.setSymbolScale=function(e){this.scaleX=this.scaleY=e},t.prototype.fadeOut=function(e,i,n){var a=this.childAt(0),o=vt(this).dataIndex,s=n&&n.animation;if(this.silent=a.silent=!0,n&&n.fadeLabel){var u=a.getTextContent();u&&$a(u,{style:{opacity:0}},i,{dataIndex:o,removeOpt:s,cb:function(){a.removeTextContent()}})}else a.removeTextContent();$a(a,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:o,cb:e,removeOpt:s})},t.getSymbolSize=function(e,i){return BS(e.getItemVisual(i,"symbolSize"))},t}(At);function zS(r,t){this.parent.drift(r,t)}function ys(r,t,e,i){return t&&!isNaN(t[0])&&!isNaN(t[1])&&!(i.isIgnore&&i.isIgnore(e))&&!(i.clipShape&&!i.clipShape.contain(t[0],t[1]))&&r.getItemVisual(e,"symbol")!=="none"}function Hh(r){return r!=null&&!G(r)&&(r={isIgnore:r}),r||{}}function Vh(r){var t=r.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:Al(t),cursorStyle:t.get("cursor")}}var GS=function(){function r(t){this.group=new At,this._SymbolCtor=t||Ul}return r.prototype.updateData=function(t,e){this._progressiveEls=null,e=Hh(e);var i=this.group,n=t.hostModel,a=this._data,o=this._SymbolCtor,s=e.disableAnimation,u=Vh(t),l={disableAnimation:s},f=e.getSymbolPoint||function(h){return t.getItemLayout(h)};a||i.removeAll(),t.diff(a).add(function(h){var c=f(h);if(ys(t,c,h,e)){var v=new o(t,h,u,l);v.setPosition(c),t.setItemGraphicEl(h,v),i.add(v)}}).update(function(h,c){var v=a.getItemGraphicEl(c),p=f(h);if(!ys(t,p,h,e)){i.remove(v);return}var y=t.getItemVisual(h,"symbol")||"circle",d=v&&v.getSymbolType&&v.getSymbolType();if(!v||d&&d!==y)i.remove(v),v=new o(t,h,u,l),v.setPosition(p);else{v.updateData(t,h,u,l);var g={x:p[0],y:p[1]};s?v.attr(g):nr(v,g,n)}i.add(v),t.setItemGraphicEl(h,v)}).remove(function(h){var c=a.getItemGraphicEl(h);c&&c.fadeOut(function(){i.remove(c)},n)}).execute(),this._getSymbolPoint=f,this._data=t},r.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl(function(i,n){var a=t._getSymbolPoint(n);i.setPosition(a),i.markRedraw()})},r.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=Vh(t),this._data=null,this.group.removeAll()},r.prototype.incrementalUpdate=function(t,e,i){this._progressiveEls=[],i=Hh(i);function n(u){u.isGroup||(u.incremental=!0,u.ensureState("emphasis").hoverLayer=!0)}for(var a=t.start;a<t.end;a++){var o=e.getItemLayout(a);if(ys(e,o,a,i)){var s=new this._SymbolCtor(e,a,this._seriesScope);s.traverse(n),s.setPosition(o),this.group.add(s),e.setItemGraphicEl(a,s),this._progressiveEls.push(s)}}},r.prototype.eachRendered=function(t){Dl(this._progressiveEls||this.group,t)},r.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(n){n.fadeOut(function(){e.remove(n)},i.hostModel)}):e.removeAll()},r}();function bd(r,t,e){var i=r.getBaseAxis(),n=r.getOtherAxis(i),a=HS(n,e),o=i.dim,s=n.dim,u=t.mapDimension(s),l=t.mapDimension(o),f=s==="x"||s==="radius"?1:0,h=z(r.dimensions,function(p){return t.mapDimension(p)}),c=!1,v=t.getCalculationInfo("stackResultDimension");return wi(t,h[0])&&(c=!0,h[0]=v),wi(t,h[1])&&(c=!0,h[1]=v),{dataDimsForPoint:h,valueStart:a,valueAxisDim:s,baseAxisDim:o,stacked:!!c,valueDim:u,baseDim:l,baseDataOffset:f,stackedOverDimension:t.getCalculationInfo("stackedOverDimension")}}function HS(r,t){var e=0,i=r.scale.getExtent();return t==="start"?e=i[0]:t==="end"?e=i[1]:ft(t)&&!isNaN(t)?e=t:i[0]>0?e=i[0]:i[1]<0&&(e=i[1]),e}function xd(r,t,e,i){var n=NaN;r.stacked&&(n=e.get(e.getCalculationInfo("stackedOverDimension"),i)),isNaN(n)&&(n=r.valueStart);var a=r.baseDataOffset,o=[];return o[a]=e.get(r.baseDim,i),o[1-a]=n,t.dataToPoint(o)}var Td=typeof Float32Array<"u",VS=Td?Float32Array:Array;function Oe(r){return F(r)?Td?new Float32Array(r):r:new VS(r)}function WS(r,t){var e=[];return t.diff(r).add(function(i){e.push({cmd:"+",idx:i})}).update(function(i,n){e.push({cmd:"=",idx:n,idx1:i})}).remove(function(i){e.push({cmd:"-",idx:i})}).execute(),e}function US(r,t,e,i,n,a,o,s){for(var u=WS(r,t),l=[],f=[],h=[],c=[],v=[],p=[],y=[],d=bd(n,t,o),g=r.getLayout("points")||[],m=t.getLayout("points")||[],_=0;_<u.length;_++){var S=u[_],b=!0,w=void 0,x=void 0;switch(S.cmd){case"=":w=S.idx*2,x=S.idx1*2;var T=g[w],C=g[w+1],D=m[x],A=m[x+1];(isNaN(T)||isNaN(C))&&(T=D,C=A),l.push(T,C),f.push(D,A),h.push(e[w],e[w+1]),c.push(i[x],i[x+1]),y.push(t.getRawIndex(S.idx1));break;case"+":var L=S.idx,I=d.dataDimsForPoint,P=n.dataToPoint([t.get(I[0],L),t.get(I[1],L)]);x=L*2,l.push(P[0],P[1]),f.push(m[x],m[x+1]);var E=xd(d,n,t,L);h.push(E[0],E[1]),c.push(i[x],i[x+1]),y.push(t.getRawIndex(L));break;case"-":b=!1}b&&(v.push(S),p.push(p.length))}p.sort(function(ht,ie){return y[ht]-y[ie]});for(var R=l.length,H=Oe(R),B=Oe(R),N=Oe(R),W=Oe(R),Z=[],_=0;_<p.length;_++){var q=p[_],it=_*2,ut=q*2;H[it]=l[ut],H[it+1]=l[ut+1],B[it]=f[ut],B[it+1]=f[ut+1],N[it]=h[ut],N[it+1]=h[ut+1],W[it]=c[ut],W[it+1]=c[ut+1],Z[_]=v[q]}return{current:H,next:B,stackedOnCurrent:N,stackedOnNext:W,status:Z}}var Ye=Math.min,Xe=Math.max;function Fr(r,t){return isNaN(r)||isNaN(t)}function Lu(r,t,e,i,n,a,o,s,u){for(var l,f,h,c,v,p,y=e,d=0;d<i;d++){var g=t[y*2],m=t[y*2+1];if(y>=n||y<0)break;if(Fr(g,m)){if(u){y+=a;continue}break}if(y===e)r[a>0?"moveTo":"lineTo"](g,m),h=g,c=m;else{var _=g-l,S=m-f;if(_*_+S*S<.5){y+=a;continue}if(o>0){for(var b=y+a,w=t[b*2],x=t[b*2+1];w===g&&x===m&&d<i;)d++,b+=a,y+=a,w=t[b*2],x=t[b*2+1],g=t[y*2],m=t[y*2+1],_=g-l,S=m-f;var T=d+1;if(u)for(;Fr(w,x)&&T<i;)T++,b+=a,w=t[b*2],x=t[b*2+1];var C=.5,D=0,A=0,L=void 0,I=void 0;if(T>=i||Fr(w,x))v=g,p=m;else{D=w-l,A=x-f;var P=g-l,E=w-g,R=m-f,H=x-m,B=void 0,N=void 0;if(s==="x"){B=Math.abs(P),N=Math.abs(E);var W=D>0?1:-1;v=g-W*B*o,p=m,L=g+W*N*o,I=m}else if(s==="y"){B=Math.abs(R),N=Math.abs(H);var Z=A>0?1:-1;v=g,p=m-Z*B*o,L=g,I=m+Z*N*o}else B=Math.sqrt(P*P+R*R),N=Math.sqrt(E*E+H*H),C=N/(N+B),v=g-D*o*(1-C),p=m-A*o*(1-C),L=g+D*o*C,I=m+A*o*C,L=Ye(L,Xe(w,g)),I=Ye(I,Xe(x,m)),L=Xe(L,Ye(w,g)),I=Xe(I,Ye(x,m)),D=L-g,A=I-m,v=g-D*B/N,p=m-A*B/N,v=Ye(v,Xe(l,g)),p=Ye(p,Xe(f,m)),v=Xe(v,Ye(l,g)),p=Xe(p,Ye(f,m)),D=g-v,A=m-p,L=g+D*N/B,I=m+A*N/B}r.bezierCurveTo(h,c,v,p,g,m),h=L,c=I}else r.lineTo(g,m)}l=g,f=m,y+=a}return d}var Cd=function(){function r(){this.smooth=0,this.smoothConstraint=!0}return r}(),YS=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polyline",i}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Cd},t.prototype.buildPath=function(e,i){var n=i.points,a=0,o=n.length/2;if(i.connectNulls){for(;o>0&&Fr(n[o*2-2],n[o*2-1]);o--);for(;a<o&&Fr(n[a*2],n[a*2+1]);a++);}for(;a<o;)a+=Lu(e,n,a,o,o,1,i.smooth,i.smoothMonotone,i.connectNulls)+1},t.prototype.getPointOn=function(e,i){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n=this.path,a=n.data,o=Vr.CMD,s,u,l=i==="x",f=[],h=0;h<a.length;){var c=a[h++],v=void 0,p=void 0,y=void 0,d=void 0,g=void 0,m=void 0,_=void 0;switch(c){case o.M:s=a[h++],u=a[h++];break;case o.L:if(v=a[h++],p=a[h++],_=l?(e-s)/(v-s):(e-u)/(p-u),_<=1&&_>=0){var S=l?(p-u)*_+u:(v-s)*_+s;return l?[e,S]:[S,e]}s=v,u=p;break;case o.C:v=a[h++],p=a[h++],y=a[h++],d=a[h++],g=a[h++],m=a[h++];var b=l?Oa(s,v,y,g,e,f):Oa(u,p,d,m,e,f);if(b>0)for(var w=0;w<b;w++){var x=f[w];if(x<=1&&x>=0){var S=l?bt(u,p,d,m,x):bt(s,v,y,g,x);return l?[e,S]:[S,e]}}s=g,u=m;break}}},t}(st),XS=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Cd),$S=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polygon",i}return t.prototype.getDefaultShape=function(){return new XS},t.prototype.buildPath=function(e,i){var n=i.points,a=i.stackedOnPoints,o=0,s=n.length/2,u=i.smoothMonotone;if(i.connectNulls){for(;s>0&&Fr(n[s*2-2],n[s*2-1]);s--);for(;o<s&&Fr(n[o*2],n[o*2+1]);o++);}for(;o<s;){var l=Lu(e,n,o,s,s,1,i.smooth,u,i.connectNulls);Lu(e,a,o+l-1,l,s,-1,i.stackedOnSmooth,u,i.connectNulls),o+=l+1,e.closePath()}},t}(st);function Yl(){var r=dt();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var Dd=dt(),ZS=Yl(),be=function(){function r(){this.group=new At,this.uid=xo("viewChart"),this.renderTask=cn({plan:qS,reset:KS}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Uh(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Uh(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){Dl(this.group,t)},r.markUpdateMethod=function(t,e){Dd(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function Wh(r,t,e){r&&pu(r)&&(t==="emphasis"?Ua:Ya)(r,e)}function Uh(r,t,e){var i=Wr(r,t),n=t&&t.highlightKey!=null?E0(t.highlightKey):null;i!=null?M(It(i),function(a){Wh(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){Wh(a,e,n)})}il(be);so(be);function qS(r){return ZS(r.model)}function KS(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&Dd(n).updateMethod,u=a?"incrementalPrepareRender":s&&o[s]?s:"render";return u!=="render"&&o[u](t,e,i,n),QS[u]}var QS={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}};function Md(r,t,e,i,n){var a=r.getArea(),o=a.x,s=a.y,u=a.width,l=a.height,f=e.get(["lineStyle","width"])||0;o-=f/2,s-=f/2,u+=f,l+=f,u=Math.ceil(u),o!==Math.floor(o)&&(o=Math.floor(o),u++);var h=new xt({shape:{x:o,y:s,width:u,height:l}});if(t){var c=r.getBaseAxis(),v=c.isHorizontal(),p=c.inverse;v?(p&&(h.shape.x+=u),h.shape.width=0):(p||(h.shape.y+=l),h.shape.height=0);var y=Y(n)?function(d){n(d,h)}:null;kn(h,{shape:{width:u,height:l,x:o,y:s}},e,null,i,y)}return h}function Ad(r,t,e){var i=r.getArea(),n=pt(i.r0,1),a=pt(i.r,1),o=new yo({shape:{cx:pt(r.cx,1),cy:pt(r.cy,1),r0:n,r:a,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});if(t){var s=r.getBaseAxis().dim==="angle";s?o.shape.endAngle=i.startAngle:o.shape.r=n,kn(o,{shape:{endAngle:i.endAngle,r:a}},e)}return o}function KC(r,t,e,i,n){if(r){if(r.type==="polar")return Ad(r,t,e);if(r.type==="cartesian2d")return Md(r,t,e,i,n)}else return null;return null}function JS(r,t){return r.type===t}function Yh(r,t){if(r.length===t.length){for(var e=0;e<r.length;e++)if(r[e]!==t[e])return;return!0}}function Xh(r){for(var t=1/0,e=1/0,i=-1/0,n=-1/0,a=0;a<r.length;){var o=r[a++],s=r[a++];isNaN(o)||(t=Math.min(o,t),i=Math.max(o,i)),isNaN(s)||(e=Math.min(s,e),n=Math.max(s,n))}return[[t,e],[i,n]]}function $h(r,t){var e=Xh(r),i=e[0],n=e[1],a=Xh(t),o=a[0],s=a[1];return Math.max(Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]),Math.abs(n[0]-s[0]),Math.abs(n[1]-s[1]))}function Zh(r){return ft(r)?r:r?.5:0}function jS(r,t,e){if(!e.valueDim)return[];for(var i=t.count(),n=Oe(i*2),a=0;a<i;a++){var o=xd(e,r,t,a);n[a*2]=o[0],n[a*2+1]=o[1]}return n}function $e(r,t,e,i,n){var a=e.getBaseAxis(),o=a.dim==="x"||a.dim==="radius"?0:1,s=[],u=0,l=[],f=[],h=[],c=[];if(n){for(u=0;u<r.length;u+=2){var v=t||r;!isNaN(v[u])&&!isNaN(v[u+1])&&c.push(r[u],r[u+1])}r=c}for(u=0;u<r.length-2;u+=2)switch(h[0]=r[u+2],h[1]=r[u+3],f[0]=r[u],f[1]=r[u+1],s.push(f[0],f[1]),i){case"end":l[o]=h[o],l[1-o]=f[1-o],s.push(l[0],l[1]);break;case"middle":var p=(f[o]+h[o])/2,y=[];l[o]=y[o]=p,l[1-o]=f[1-o],y[1-o]=h[1-o],s.push(l[0],l[1]),s.push(y[0],y[1]);break;default:l[o]=f[o],l[1-o]=h[1-o],s.push(l[0],l[1])}return s.push(r[u++],r[u++]),s}function tw(r,t){var e=[],i=r.length,n,a;function o(f,h,c){var v=f.coord,p=(c-v)/(h.coord-v),y=tm(p,[f.color,h.color]);return{coord:c,color:y}}for(var s=0;s<i;s++){var u=r[s],l=u.coord;if(l<0)n=u;else if(l>t){a?e.push(o(a,u,t)):n&&e.push(o(n,u,0),o(n,u,t));break}else n&&(e.push(o(n,u,0)),n=null),e.push(u),a=u}return e}function ew(r,t,e){var i=r.getVisual("visualMeta");if(!(!i||!i.length||!r.count())&&t.type==="cartesian2d"){for(var n,a,o=i.length-1;o>=0;o--){var s=r.getDimensionInfo(i[o].dimension);if(n=s&&s.coordDim,n==="x"||n==="y"){a=i[o];break}}if(a){var u=t.getAxis(n),l=z(a.stops,function(_){return{coord:u.toGlobalCoord(u.dataToCoord(_.value)),color:_.color}}),f=l.length,h=a.outerColors.slice();f&&l[0].coord>l[f-1].coord&&(l.reverse(),h.reverse());var c=tw(l,n==="x"?e.getWidth():e.getHeight()),v=c.length;if(!v&&f)return l[0].coord<0?h[1]?h[1]:l[f-1].color:h[0]?h[0]:l[0].color;var p=10,y=c[0].coord-p,d=c[v-1].coord+p,g=d-y;if(g<.001)return"transparent";M(c,function(_){_.offset=(_.coord-y)/g}),c.push({offset:v?c[v-1].offset:.5,color:h[1]||"transparent"}),c.unshift({offset:v?c[0].offset:.5,color:h[0]||"transparent"});var m=new Lp(0,0,0,0,c,!0);return m[n]=y,m[n+"2"]=d,m}}}function rw(r,t,e){var i=r.get("showAllSymbol"),n=i==="auto";if(!(i&&!n)){var a=e.getAxesByScale("ordinal")[0];if(a&&!(n&&iw(a,t))){var o=t.mapDimension(a.dim),s={};return M(a.getViewLabels(),function(u){var l=a.scale.getRawOrdinalNumber(u.tickValue);s[l]=1}),function(u){return!s.hasOwnProperty(t.get(o,u))}}}}function iw(r,t){var e=r.getExtent(),i=Math.abs(e[1]-e[0])/r.scale.count();isNaN(i)&&(i=0);for(var n=t.count(),a=Math.max(1,Math.round(n/5)),o=0;o<n;o+=a)if(Ul.getSymbolSize(t,o)[r.isHorizontal()?1:0]*1.5>i)return!1;return!0}function nw(r,t){return isNaN(r)||isNaN(t)}function aw(r){for(var t=r.length/2;t>0&&nw(r[t*2-2],r[t*2-1]);t--);return t-1}function qh(r,t){return[r[t*2],r[t*2+1]]}function ow(r,t,e){for(var i=r.length/2,n=e==="x"?0:1,a,o,s=0,u=-1,l=0;l<i;l++)if(o=r[l*2+n],!(isNaN(o)||isNaN(r[l*2+1-n]))){if(l===0){a=o;continue}if(a<=t&&o>=t||a>=t&&o<=t){u=l;break}s=l,a=o}return{range:[s,u],t:(t-a)/(o-a)}}function Ld(r){if(r.get(["endLabel","show"]))return!0;for(var t=0;t<Te.length;t++)if(r.get([Te[t],"endLabel","show"]))return!0;return!1}function ms(r,t,e,i){if(JS(t,"cartesian2d")){var n=i.getModel("endLabel"),a=n.get("valueAnimation"),o=i.getData(),s={lastFrameIndex:0},u=Ld(i)?function(v,p){r._endLabelOnDuring(v,p,o,s,a,n,t)}:null,l=t.getBaseAxis().isHorizontal(),f=Md(t,e,i,function(){var v=r._endLabel;v&&e&&s.originalX!=null&&v.attr({x:s.originalX,y:s.originalY})},u);if(!i.get("clip",!0)){var h=f.shape,c=Math.max(h.width,h.height);l?(h.y-=c,h.height+=c*2):(h.x-=c,h.width+=c*2)}return u&&u(1,f),f}else return Ad(t,e,i)}function sw(r,t){var e=t.getBaseAxis(),i=e.isHorizontal(),n=e.inverse,a=i?n?"right":"left":"center",o=i?"middle":n?"top":"bottom";return{normal:{align:r.get("align")||a,verticalAlign:r.get("verticalAlign")||o}}}var uw=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(){var e=new At,i=new GS;this.group.add(i.group),this._symbolDraw=i,this._lineGroup=e,this._changePolyState=gt(this._changePolyState,this)},t.prototype.render=function(e,i,n){var a=e.coordinateSystem,o=this.group,s=e.getData(),u=e.getModel("lineStyle"),l=e.getModel("areaStyle"),f=s.getLayout("points")||[],h=a.type==="polar",c=this._coordSys,v=this._symbolDraw,p=this._polyline,y=this._polygon,d=this._lineGroup,g=!i.ssr&&e.get("animation"),m=!l.isEmpty(),_=l.get("origin"),S=bd(a,s,_),b=m&&jS(a,s,S),w=e.get("showSymbol"),x=e.get("connectNulls"),T=w&&!h&&rw(e,s,a),C=this._data;C&&C.eachItemGraphicEl(function(ht,ie){ht.__temp&&(o.remove(ht),C.setItemGraphicEl(ie,null))}),w||v.remove(),o.add(d);var D=h?!1:e.get("step"),A;a&&a.getArea&&e.get("clip",!0)&&(A=a.getArea(),A.width!=null?(A.x-=.1,A.y-=.1,A.width+=.2,A.height+=.2):A.r0&&(A.r0-=.5,A.r+=.5)),this._clipShapeForSymbol=A;var L=ew(s,a,n)||s.getVisual("style")[s.getVisual("drawType")];if(!(p&&c.type===a.type&&D===this._step))w&&v.updateData(s,{isIgnore:T,clipShape:A,disableAnimation:!0,getSymbolPoint:function(ht){return[f[ht*2],f[ht*2+1]]}}),g&&this._initSymbolLabelAnimation(s,a,A),D&&(b&&(b=$e(b,f,a,D,x)),f=$e(f,null,a,D,x)),p=this._newPolyline(f),m?y=this._newPolygon(f,b):y&&(d.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,xu(L)),d.setClipPath(ms(this,a,!0,e));else{m&&!y?y=this._newPolygon(f,b):y&&!m&&(d.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,xu(L));var I=d.getClipPath();if(I){var P=ms(this,a,!1,e);kn(I,{shape:P.shape},e)}else d.setClipPath(ms(this,a,!0,e));w&&v.updateData(s,{isIgnore:T,clipShape:A,disableAnimation:!0,getSymbolPoint:function(ht){return[f[ht*2],f[ht*2+1]]}}),(!Yh(this._stackedOnPoints,b)||!Yh(this._points,f))&&(g?this._doUpdateAnimation(s,b,a,n,D,_,x):(D&&(b&&(b=$e(b,f,a,D,x)),f=$e(f,null,a,D,x)),p.setShape({points:f}),y&&y.setShape({points:f,stackedOnPoints:b})))}var E=e.getModel("emphasis"),R=E.get("focus"),H=E.get("blurScope"),B=E.get("disabled");if(p.useStyle(rt(u.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),eh(p,e,"lineStyle"),p.style.lineWidth>0&&e.get(["emphasis","lineStyle","width"])==="bolder"){var N=p.getState("emphasis").style;N.lineWidth=+p.style.lineWidth+1}vt(p).seriesIndex=e.seriesIndex,cu(p,R,H,B);var W=Zh(e.get("smooth")),Z=e.get("smoothMonotone");if(p.setShape({smooth:W,smoothMonotone:Z,connectNulls:x}),y){var q=s.getCalculationInfo("stackedOnSeries"),it=0;y.useStyle(rt(l.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),q&&(it=Zh(q.get("smooth"))),y.setShape({smooth:W,stackedOnSmooth:it,smoothMonotone:Z,connectNulls:x}),eh(y,e,"areaStyle"),vt(y).seriesIndex=e.seriesIndex,cu(y,R,H,B)}var ut=this._changePolyState;s.eachItemGraphicEl(function(ht){ht&&(ht.onHoverStateChange=ut)}),this._polyline.onHoverStateChange=ut,this._data=s,this._coordSys=a,this._stackedOnPoints=b,this._points=f,this._step=D,this._valueOrigin=_,e.get("triggerLineEvent")&&(this.packEventData(e,p),y&&this.packEventData(e,y))},t.prototype.packEventData=function(e,i){vt(i).eventData={componentType:"series",componentSubType:"line",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"line"}},t.prototype.highlight=function(e,i,n,a){var o=e.getData(),s=Wr(o,a);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var u=o.getLayout("points"),l=o.getItemGraphicEl(s);if(!l){var f=u[s*2],h=u[s*2+1];if(isNaN(f)||isNaN(h)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(f,h))return;var c=e.get("zlevel")||0,v=e.get("z")||0;l=new Ul(o,s),l.x=f,l.y=h,l.setZ(c,v);var p=l.getSymbolPath().getTextContent();p&&(p.zlevel=c,p.z=v,p.z2=this._polyline.z2+1),l.__temp=!0,o.setItemGraphicEl(s,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}else be.prototype.highlight.call(this,e,i,n,a)},t.prototype.downplay=function(e,i,n,a){var o=e.getData(),s=Wr(o,a);if(this._changePolyState("normal"),s!=null&&s>=0){var u=o.getItemGraphicEl(s);u&&(u.__temp?(o.setItemGraphicEl(s,null),this.group.remove(u)):u.downplay())}else be.prototype.downplay.call(this,e,i,n,a)},t.prototype._changePolyState=function(e){var i=this._polygon;Kf(this._polyline,e),i&&Kf(i,e)},t.prototype._newPolyline=function(e){var i=this._polyline;return i&&this._lineGroup.remove(i),i=new YS({shape:{points:e},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(i),this._polyline=i,i},t.prototype._newPolygon=function(e,i){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new $S({shape:{points:e,stackedOnPoints:i},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},t.prototype._initSymbolLabelAnimation=function(e,i,n){var a,o,s=i.getBaseAxis(),u=s.inverse;i.type==="cartesian2d"?(a=s.isHorizontal(),o=!1):i.type==="polar"&&(a=s.dim==="angle",o=!0);var l=e.hostModel,f=l.get("animationDuration");Y(f)&&(f=f(null));var h=l.get("animationDelay")||0,c=Y(h)?h(null):h;e.eachItemGraphicEl(function(v,p){var y=v;if(y){var d=[v.x,v.y],g=void 0,m=void 0,_=void 0;if(n)if(o){var S=n,b=i.pointToCoord(d);a?(g=S.startAngle,m=S.endAngle,_=-b[1]/180*Math.PI):(g=S.r0,m=S.r,_=b[0])}else{var w=n;a?(g=w.x,m=w.x+w.width,_=v.x):(g=w.y+w.height,m=w.y,_=v.y)}var x=m===g?0:(_-g)/(m-g);u&&(x=1-x);var T=Y(h)?h(p):f*x+c,C=y.getSymbolPath(),D=C.getTextContent();y.attr({scaleX:0,scaleY:0}),y.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:T}),D&&D.animateFrom({style:{opacity:0}},{duration:300,delay:T}),C.disableLabelAnimation=!0}})},t.prototype._initOrUpdateEndLabel=function(e,i,n){var a=e.getModel("endLabel");if(Ld(e)){var o=e.getData(),s=this._polyline,u=o.getLayout("points");if(!u){s.removeTextContent(),this._endLabel=null;return}var l=this._endLabel;l||(l=this._endLabel=new Et({z2:200}),l.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var f=aw(u);f>=0&&(Ml(s,Al(e,"endLabel"),{inheritColor:n,labelFetcher:e,labelDataIndex:f,defaultText:function(h,c,v){return v!=null?FS(o,v):wd(o,h)},enableTextSetter:!0},sw(a,i)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},t.prototype._endLabelOnDuring=function(e,i,n,a,o,s,u){var l=this._endLabel,f=this._polyline;if(l){e<1&&a.originalX==null&&(a.originalX=l.x,a.originalY=l.y);var h=n.getLayout("points"),c=n.hostModel,v=c.get("connectNulls"),p=s.get("precision"),y=s.get("distance")||0,d=u.getBaseAxis(),g=d.isHorizontal(),m=d.inverse,_=i.shape,S=m?g?_.x:_.y+_.height:g?_.x+_.width:_.y,b=(g?y:0)*(m?-1:1),w=(g?0:-y)*(m?-1:1),x=g?"x":"y",T=ow(h,S,x),C=T.range,D=C[1]-C[0],A=void 0;if(D>=1){if(D>1&&!v){var L=qh(h,C[0]);l.attr({x:L[0]+b,y:L[1]+w}),o&&(A=c.getRawValue(C[0]))}else{var L=f.getPointOn(S,x);L&&l.attr({x:L[0]+b,y:L[1]+w});var I=c.getRawValue(C[0]),P=c.getRawValue(C[1]);o&&(A=v0(n,p,I,P,T.t))}a.lastFrameIndex=C[0]}else{var E=e===1||a.lastFrameIndex>0?C[0]:0,L=qh(h,E);o&&(A=c.getRawValue(E)),l.attr({x:L[0]+b,y:L[1]+w})}if(o){var R=Ll(l);typeof R.setLabelText=="function"&&R.setLabelText(A)}}},t.prototype._doUpdateAnimation=function(e,i,n,a,o,s,u){var l=this._polyline,f=this._polygon,h=e.hostModel,c=US(this._data,e,this._stackedOnPoints,i,this._coordSys,n,this._valueOrigin),v=c.current,p=c.stackedOnCurrent,y=c.next,d=c.stackedOnNext;if(o&&(p=$e(c.stackedOnCurrent,c.current,n,o,u),v=$e(c.current,null,n,o,u),d=$e(c.stackedOnNext,c.next,n,o,u),y=$e(c.next,null,n,o,u)),$h(v,y)>3e3||f&&$h(p,d)>3e3){l.stopAnimation(),l.setShape({points:y}),f&&(f.stopAnimation(),f.setShape({points:y,stackedOnPoints:d}));return}l.shape.__points=c.current,l.shape.points=v;var g={shape:{points:y}};c.current!==v&&(g.shape.__points=c.next),l.stopAnimation(),nr(l,g,h),f&&(f.setShape({points:v,stackedOnPoints:p}),f.stopAnimation(),nr(f,{shape:{stackedOnPoints:d}},h),l.shape.points!==f.shape.points&&(f.shape.points=l.shape.points));for(var m=[],_=c.status,S=0;S<_.length;S++){var b=_[S].cmd;if(b==="="){var w=e.getItemGraphicEl(_[S].idx1);w&&m.push({el:w,ptIdx:S})}}l.animators&&l.animators.length&&l.animators[0].during(function(){f&&f.dirtyShape();for(var x=l.shape.__points,T=0;T<m.length;T++){var C=m[T].el,D=m[T].ptIdx*2;C.x=x[D],C.y=x[D+1],C.markRedraw()}})},t.prototype.remove=function(e){var i=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(a,o){a.__temp&&(i.remove(a),n.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},t.type="line",t}(be);function lw(r,t){return{seriesType:r,plan:Yl(),reset:function(e){var i=e.getData(),n=e.coordinateSystem;if(e.pipelineContext,!!n){var a=z(n.dimensions,function(h){return i.mapDimension(h)}).slice(0,2),o=a.length,s=i.getCalculationInfo("stackResultDimension");wi(i,a[0])&&(a[0]=s),wi(i,a[1])&&(a[1]=s);var u=i.getStore(),l=i.getDimensionIndex(a[0]),f=i.getDimensionIndex(a[1]);return o&&{progress:function(h,c){for(var v=h.end-h.start,p=Oe(v*o),y=[],d=[],g=h.start,m=0;g<h.end;g++){var _=void 0;if(o===1){var S=u.get(l,g);_=n.dataToPoint(S,null,d)}else y[0]=u.get(l,g),y[1]=u.get(f,g),_=n.dataToPoint(y,null,d);p[m++]=_[0],p[m++]=_[1]}c.setLayout("points",p)}}}}}}var fw={average:function(r){for(var t=0,e=0,i=0;i<r.length;i++)isNaN(r[i])||(t+=r[i],e++);return e===0?NaN:t/e},sum:function(r){for(var t=0,e=0;e<r.length;e++)t+=r[e]||0;return t},max:function(r){for(var t=-1/0,e=0;e<r.length;e++)r[e]>t&&(t=r[e]);return isFinite(t)?t:NaN},min:function(r){for(var t=1/0,e=0;e<r.length;e++)r[e]<t&&(t=r[e]);return isFinite(t)?t:NaN},nearest:function(r){return r[0]}},hw=function(r){return Math.round(r.length/2)};function vw(r){return{seriesType:r,reset:function(t,e,i){var n=t.getData(),a=t.get("sampling"),o=t.coordinateSystem,s=n.count();if(s>10&&o.type==="cartesian2d"&&a){var u=o.getBaseAxis(),l=o.getOtherAxis(u),f=u.getExtent(),h=i.getDevicePixelRatio(),c=Math.abs(f[1]-f[0])*(h||1),v=Math.round(s/c);if(isFinite(v)&&v>1){a==="lttb"?t.setData(n.lttbDownSample(n.mapDimension(l.dim),1/v)):a==="minmax"&&t.setData(n.minmaxDownSample(n.mapDimension(l.dim),1/v));var p=void 0;V(a)?p=fw[a]:Y(a)&&(p=a),p&&t.setData(n.downSample(n.mapDimension(l.dim),1/v,p,hw))}}}}}function QC(r){r.registerChartView(uw),r.registerSeriesModel(NS),r.registerLayout(lw("line")),r.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,vw("line"))}var cw="__ec_stack_";function Pd(r){return r.get("stack")||cw+r.seriesIndex}function Xl(r){return r.dim+r.index}function Id(r,t){var e=[];return t.eachSeriesByType(r,function(i){Rd(i)&&e.push(i)}),e}function pw(r){var t={};M(r,function(u){var l=u.coordinateSystem,f=l.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=u.getData(),c=f.dim+"_"+f.index,v=h.getDimensionIndex(h.mapDimension(f.dim)),p=h.getStore(),y=0,d=p.count();y<d;++y){var g=p.get(v,y);t[c]?t[c].push(g):t[c]=[g]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(u,l){return u-l});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function Ed(r){var t=pw(r),e=[];return M(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var u=a.dim+"_"+a.index,l=t[u],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);s=l?f/c*l:f}else{var v=i.getData();s=Math.abs(o[1]-o[0])/v.count()}var p=Ht(i.get("barWidth"),s),y=Ht(i.get("barMaxWidth"),s),d=Ht(i.get("barMinWidth")||(kd(i)?.5:1),s),g=i.get("barGap"),m=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:p,barMaxWidth:y,barMinWidth:d,barGap:g,barCategoryGap:m,axisKey:Xl(a),stackId:Pd(i)})}),dw(e)}function dw(r){var t={};M(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},u=s.stacks;t[a]=s;var l=i.stackId;u[l]||s.autoWidthCount++,u[l]=u[l]||{width:0,maxWidth:0};var f=i.barWidth;f&&!u[l].width&&(u[l].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(u[l].maxWidth=h);var c=i.barMinWidth;c&&(u[l].minWidth=c);var v=i.barGap;v!=null&&(s.gap=v);var p=i.barCategoryGap;p!=null&&(s.categoryGap=p)});var e={};return M(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var u=lt(a).length;s=Math.max(35-u*4,15)+"%"}var l=Ht(s,o),f=Ht(i.gap,1),h=i.remainedWidth,c=i.autoWidthCount,v=(h-l)/(c+(c-1)*f);v=Math.max(v,0),M(a,function(g){var m=g.maxWidth,_=g.minWidth;if(g.width){var S=g.width;m&&(S=Math.min(S,m)),_&&(S=Math.max(S,_)),g.width=S,h-=S+f*S,c--}else{var S=v;m&&m<S&&(S=Math.min(m,h)),_&&_>S&&(S=_),S!==v&&(g.width=S,h-=S+f*S,c--)}}),v=(h-l)/(c+(c-1)*f),v=Math.max(v,0);var p=0,y;M(a,function(g,m){g.width||(g.width=v),y=g,p+=g.width*(1+f)}),y&&(p-=y.width*f);var d=-p/2;M(a,function(g,m){e[n][m]=e[n][m]||{bandWidth:o,offset:d,width:g.width},d+=g.width*(1+f)})}),e}function gw(r,t,e){if(r&&t){var i=r[Xl(t)];return i}}function JC(r,t){var e=Id(r,t),i=Ed(e);M(e,function(n){var a=n.getData(),o=n.coordinateSystem,s=o.getBaseAxis(),u=Pd(n),l=i[Xl(s)][u],f=l.offset,h=l.width;a.setLayout({bandWidth:l.bandWidth,offset:f,size:h})})}function jC(r){return{seriesType:r,plan:Yl(),reset:function(t){if(Rd(t)){var e=t.getData(),i=t.coordinateSystem,n=i.getBaseAxis(),a=i.getOtherAxis(n),o=e.getDimensionIndex(e.mapDimension(a.dim)),s=e.getDimensionIndex(e.mapDimension(n.dim)),u=t.get("showBackground",!0),l=e.mapDimension(a.dim),f=e.getCalculationInfo("stackResultDimension"),h=wi(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=a.isHorizontal(),v=yw(n,a),p=kd(t),y=t.get("barMinHeight")||0,d=f&&e.getDimensionIndex(f),g=e.getLayout("size"),m=e.getLayout("offset");return{progress:function(_,S){for(var b=_.count,w=p&&Oe(b*3),x=p&&u&&Oe(b*3),T=p&&Oe(b),C=i.master.getRect(),D=c?C.width:C.height,A,L=S.getStore(),I=0;(A=_.next())!=null;){var P=L.get(h?d:o,A),E=L.get(s,A),R=v,H=void 0;h&&(H=+P-L.get(o,A));var B=void 0,N=void 0,W=void 0,Z=void 0;if(c){var q=i.dataToPoint([P,E]);if(h){var it=i.dataToPoint([H,E]);R=it[0]}B=R,N=q[1]+m,W=q[0]-R,Z=g,Math.abs(W)<y&&(W=(W<0?-1:1)*y)}else{var q=i.dataToPoint([E,P]);if(h){var it=i.dataToPoint([E,H]);R=it[1]}B=q[0]+m,N=R,W=g,Z=q[1]-R,Math.abs(Z)<y&&(Z=(Z<=0?-1:1)*y)}p?(w[I]=B,w[I+1]=N,w[I+2]=c?W:Z,x&&(x[I]=c?C.x:B,x[I+1]=c?N:C.y,x[I+2]=D),T[A]=A):S.setItemLayout(A,{x:B,y:N,width:W,height:Z}),I+=3}p&&S.setLayout({largePoints:w,largeDataIndices:T,largeBackgroundPoints:x,valueAxisHorizontal:c})}}}}}}function Rd(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function kd(r){return r.pipelineContext&&r.pipelineContext.large}function yw(r,t){var e=t.model.get("startValue");return e||(e=0),t.toGlobalCoord(t.dataToCoord(t.type==="log"?e>0?e:1:e))}var Qa="\0__throttleOriginMethod",Kh="\0__throttleRate",Qh="\0__throttleType";function Od(r,t,e){var i,n=0,a=0,o=null,s,u,l,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(u,l||[])}var c=function(){for(var v=[],p=0;p<arguments.length;p++)v[p]=arguments[p];i=new Date().getTime(),u=this,l=v;var y=f||t,d=f||e;f=null,s=i-(d?n:a)-y,clearTimeout(o),d?o=setTimeout(h,y):s>=0?h():o=setTimeout(h,-s),n=i};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function mw(r,t,e,i){var n=r[t];if(n){var a=n[Qa]||n,o=n[Qh],s=n[Kh];if(s!==e||o!==i){if(e==null)return r[t]=a;n=r[t]=Od(a,e,i==="debounce"),n[Qa]=a,n[Qh]=i,n[Kh]=e}return n}}function _w(r,t){var e=r[t];e&&e[Qa]&&(e.clear&&e.clear(),r[t]=e[Qa])}function tD(r,t){function e(i,n){var a=[];return i.eachComponent({mainType:"series",subType:r,query:n},function(o){a.push(o.seriesIndex)}),a}M([[r+"ToggleSelect","toggleSelect"],[r+"Select","select"],[r+"UnSelect","unselect"]],function(i){t(i[0],function(n,a,o){n=k({},n),o.dispatchAction(k(n,{type:i[1],seriesIndex:e(a,n)}))})})}function ri(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,u=o.option.selectedMap,l=n.selected,f=0;f<l.length;f++)if(l[f].seriesIndex===s){var h=o.getData(),c=Wr(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:F(c)?h.getName(c[0]):h.getName(c),selected:V(u)?u:k({},u)})}})}function Sw(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(ri("map","selectchanged",t,n,i),ri("pie","selectchanged",t,n,i)):i.fromAction==="select"?(ri("map","selected",t,n,i),ri("pie","selected",t,n,i)):i.fromAction==="unselect"&&(ri("map","unselected",t,n,i),ri("pie","unselected",t,n,i))})}function ww(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,u=n.style.margin||0,l=o.clone();l.applyTransform(a),l.x-=u/2,l.y-=u/2,l.width+=u,l.height+=u;var f=s?new Xa(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:l,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function bw(r,t,e,i,n,a){var o=r.length;if(o<2)return;r.sort(function(w,x){return w.rect[t]-x.rect[t]});for(var s=0,u,l=!1,f=0;f<o;f++){var h=r[f],c=h.rect;u=c[t]-s,u<0&&(c[t]-=u,h.label[t]-=u,l=!0),s=c[t]+c[e]}var v=r[0],p=r[o-1],y,d;g(),y<0&&S(-y,.8),d<0&&S(d,.8),g(),m(y,d,1),m(d,y,-1),g(),y<0&&b(-y),d<0&&b(d);function g(){y=v.rect[t]-i,d=n-p.rect[t]-p.rect[e]}function m(w,x,T){if(w<0){var C=Math.min(x,-w);if(C>0){_(C*T,0,o);var D=C+w;D<0&&S(-D*T,1)}else S(-w*T,1)}}function _(w,x,T){w!==0&&(l=!0);for(var C=x;C<T;C++){var D=r[C],A=D.rect;A[t]+=w,D.label[t]+=w}}function S(w,x){for(var T=[],C=0,D=1;D<o;D++){var A=r[D-1].rect,L=Math.max(r[D].rect[t]-A[t]-A[e],0);T.push(L),C+=L}if(C){var I=Math.min(Math.abs(w)/C,x);if(w>0)for(var D=0;D<o-1;D++){var P=T[D]*I;_(P,0,D+1)}else for(var D=o-1;D>0;D--){var P=T[D-1]*I;_(-P,D,o)}}}function b(w){var x=w<0?-1:1;w=Math.abs(w);for(var T=Math.ceil(w/(o-1)),C=0;C<o-1;C++)if(x>0?_(T,0,C+1):_(-T,o-C-1,o),w-=T,w<=0)return}return l}function eD(r,t,e,i){return bw(r,"y","height",t,e)}function xw(r){var t=[];r.sort(function(y,d){return d.priority-y.priority});var e=new j(0,0,0,0);function i(y){if(!y.ignore){var d=y.ensureState("emphasis");d.ignore==null&&(d.ignore=!1)}y.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,u=a.transform,l=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,c=!1,v=0;v<t.length;v++){var p=t[v];if(e.intersect(p.rect)){if(o&&p.axisAligned){c=!0;break}if(p.obb||(p.obb=new Xa(p.localRect,p.transform)),h||(h=new Xa(s,u)),h.intersect(p.obb)){c=!0;break}}}c?(i(l),f&&i(f)):(l.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}var ii=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),Tw=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new ii(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new ii(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,u=this._dropTarget;this._dropTarget=s,e!==s&&(u&&s!==u&&this.handler.dispatchToElement(new ii(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new ii(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new ii(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new ii(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}(),Cw=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,_s=[],Dw=Q.browser.firefox&&+Q.browser.version.split(".")[0]<39;function Pu(r,t,e,i){return e=e||{},i?Jh(r,t,e):Dw&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):Jh(r,t,e),e}function Jh(r,t,e){if(Q.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(rd(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(wu(_s,r,i,n)){e.zrX=_s[0],e.zrY=_s[1];return}}e.zrX=e.zrY=0}function $l(r){return r||window.event}function se(r,t,e){if(t=$l(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&Pu(r,o,t,e)}else{Pu(r,t,t,e);var a=Mw(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&Cw.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function Mw(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function Aw(r,t,e,i){r.addEventListener(t,e,i)}function Lw(r,t,e,i){r.removeEventListener(t,e,i)}var Bd=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0},Pw=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var u=n[o],l=Pu(i,u,{});a.points.push([l.zrX,l.zrY]),a.touches.push(u)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in Ss)if(Ss.hasOwnProperty(e)){var i=Ss[e](this._track,t);if(i)return i}},r}();function jh(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function Iw(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var Ss={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=jh(i)/jh(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=Iw(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}},Nd="silent";function Ew(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:Rw}}function Rw(){Bd(this.event)}var kw=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(Me),Wi=function(){function r(t,e){this.x=t,this.y=e}return r}(),Ow=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],ws=new j(0,0,0,0),Fd=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new Wi(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new kw,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new Tw(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(M(Ow,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=zd(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var u=this._hovered=a?new Wi(i,n):this.findHover(i,n),l=u.target,f=this.proxy;f.setCursor&&f.setCursor(l?l.cursor:"default"),s&&l!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(u,"mousemove",e),l&&l!==s&&this.dispatchToElement(u,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new Wi(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=Ew(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(u){typeof u[o]=="function"&&u[o].call(u,s),u.trigger&&u.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new Wi(e,i);if(tv(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],u=this._pointerSize,l=u/2,f=new j(e-l,i-l,u,u),h=a.length-1;h>=0;h--){var c=a[h];c!==n&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(ws.copy(c.getBoundingRect()),c.transform&&ws.applyTransform(c.transform),ws.intersect(f)&&s.push(c))}if(s.length)for(var v=4,p=Math.PI/12,y=Math.PI*2,d=0;d<l;d+=v)for(var g=0;g<y;g+=p){var m=e+d*Math.cos(g),_=i+d*Math.sin(g);if(tv(s,o,m,_,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new Pw);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new Wi;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(Me);M(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){Fd.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=zd(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||Wy(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function Bw(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?Nd:!0}return!1}function tv(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=Bw(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==Nd)){t.target=o;break}}}function zd(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}var Gd=32,Ui=7;function Nw(r){for(var t=0;r>=Gd;)t|=r&1,r>>=1;return r+t}function ev(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;Fw(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function Fw(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function rv(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,u;o<s;)u=o+s>>>1,n(a,r[u])<0?s=u:o=u+1;var l=i-o;switch(l){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;l>0;)r[o+l]=r[o+l-1],l--}r[o]=a}}function bs(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])>0){for(s=i-n;u<s&&a(r,t[e+n+u])>0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}else{for(s=n+1;u<s&&a(r,t[e+n-u])<=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])>0?o=f+1:u=f}return u}function xs(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])<0){for(s=n+1;u<s&&a(r,t[e+n-u])<0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}else{for(s=i-n;u<s&&a(r,t[e+n+u])>=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])<0?u=f:o=f+1}return u}function zw(r,t){var e=Ui,i,n,a=0,o=[];i=[],n=[];function s(v,p){i[a]=v,n[a]=p,a+=1}function u(){for(;a>1;){var v=a-2;if(v>=1&&n[v-1]<=n[v]+n[v+1]||v>=2&&n[v-2]<=n[v]+n[v-1])n[v-1]<n[v+1]&&v--;else if(n[v]>n[v+1])break;f(v)}}function l(){for(;a>1;){var v=a-2;v>0&&n[v-1]<n[v+1]&&v--,f(v)}}function f(v){var p=i[v],y=n[v],d=i[v+1],g=n[v+1];n[v]=y+g,v===a-3&&(i[v+1]=i[v+2],n[v+1]=n[v+2]),a--;var m=xs(r[d],r,p,y,0,t);p+=m,y-=m,y!==0&&(g=bs(r[p+y-1],r,d,g,g-1,t),g!==0&&(y<=g?h(p,y,d,g):c(p,y,d,g)))}function h(v,p,y,d){var g=0;for(g=0;g<p;g++)o[g]=r[v+g];var m=0,_=y,S=v;if(r[S++]=r[_++],--d===0){for(g=0;g<p;g++)r[S+g]=o[m+g];return}if(p===1){for(g=0;g<d;g++)r[S+g]=r[_+g];r[S+d]=o[m];return}for(var b=e,w,x,T;;){w=0,x=0,T=!1;do if(t(r[_],o[m])<0){if(r[S++]=r[_++],x++,w=0,--d===0){T=!0;break}}else if(r[S++]=o[m++],w++,x=0,--p===1){T=!0;break}while((w|x)<b);if(T)break;do{if(w=xs(r[_],o,m,p,0,t),w!==0){for(g=0;g<w;g++)r[S+g]=o[m+g];if(S+=w,m+=w,p-=w,p<=1){T=!0;break}}if(r[S++]=r[_++],--d===0){T=!0;break}if(x=bs(o[m],r,_,d,0,t),x!==0){for(g=0;g<x;g++)r[S+g]=r[_+g];if(S+=x,_+=x,d-=x,d===0){T=!0;break}}if(r[S++]=o[m++],--p===1){T=!0;break}b--}while(w>=Ui||x>=Ui);if(T)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),p===1){for(g=0;g<d;g++)r[S+g]=r[_+g];r[S+d]=o[m]}else{if(p===0)throw new Error;for(g=0;g<p;g++)r[S+g]=o[m+g]}}function c(v,p,y,d){var g=0;for(g=0;g<d;g++)o[g]=r[y+g];var m=v+p-1,_=d-1,S=y+d-1,b=0,w=0;if(r[S--]=r[m--],--p===0){for(b=S-(d-1),g=0;g<d;g++)r[b+g]=o[g];return}if(d===1){for(S-=p,m-=p,w=S+1,b=m+1,g=p-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_];return}for(var x=e;;){var T=0,C=0,D=!1;do if(t(o[_],r[m])<0){if(r[S--]=r[m--],T++,C=0,--p===0){D=!0;break}}else if(r[S--]=o[_--],C++,T=0,--d===1){D=!0;break}while((T|C)<x);if(D)break;do{if(T=p-xs(o[_],r,v,p,p-1,t),T!==0){for(S-=T,m-=T,p-=T,w=S+1,b=m+1,g=T-1;g>=0;g--)r[w+g]=r[b+g];if(p===0){D=!0;break}}if(r[S--]=o[_--],--d===1){D=!0;break}if(C=d-bs(r[m],o,0,d,d-1,t),C!==0){for(S-=C,_-=C,d-=C,w=S+1,b=_+1,g=0;g<C;g++)r[w+g]=o[b+g];if(d<=1){D=!0;break}}if(r[S--]=r[m--],--p===0){D=!0;break}x--}while(T>=Ui||C>=Ui);if(D)break;x<0&&(x=0),x+=2}if(e=x,e<1&&(e=1),d===1){for(S-=p,m-=p,w=S+1,b=m+1,g=p-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_]}else{if(d===0)throw new Error;for(b=S-(d-1),g=0;g<d;g++)r[b+g]=o[g]}}return{mergeRuns:u,forceMergeRuns:l,pushRun:s}}function Pa(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<Gd){a=ev(r,e,i,t),rv(r,e,i,e+a,t);return}var o=zw(r,t),s=Nw(n);do{if(a=ev(r,e,i,t),a<s){var u=n;u>s&&(u=s),rv(r,e,e+u,e+a,t),a=u}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var iv=!1;function Ts(){iv||(iv=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function nv(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var Gw=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=nv}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Pa(i,nv)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),u=0;u<s.length;u++){var l=s[u];t.__dirty&&(l.__dirty|=Se),this._updateAndAddDisplayable(l,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Ts(),f.z=0),isNaN(f.z2)&&(Ts(),f.z2=0),isNaN(f.zlevel)&&(Ts(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,i);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=ot(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}(),Ja;Ja=Q.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};function hi(){return new Date().getTime()}var Hw=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=hi()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(Ja(i),!e._paused&&e.update())}Ja(i)},t.prototype.start=function(){this._running||(this._time=hi(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=hi(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=hi()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new fl(e,i.loop);return this.addAnimator(n),n},t}(Me),Vw=300,Cs=Q.domSupported,Ds=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=z(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),av={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},ov=!1;function Iu(r){var t=r.pointerType;return t==="pen"||t==="touch"}function Ww(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function Ms(r){r&&(r.zrByTouch=!0)}function Uw(r,t){return se(r.dom,new Yw(r,t),!0)}function Hd(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var Yw=function(){function r(t,e){this.stopPropagation=te,this.stopImmediatePropagation=te,this.preventDefault=te,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),ue={mousedown:function(r){r=se(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=se(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=se(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=se(this.dom,r);var t=r.toElement||r.relatedTarget;Hd(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){ov=!0,r=se(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){ov||(r=se(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=se(this.dom,r),Ms(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),ue.mousemove.call(this,r),ue.mousedown.call(this,r)},touchmove:function(r){r=se(this.dom,r),Ms(r),this.handler.processGesture(r,"change"),ue.mousemove.call(this,r)},touchend:function(r){r=se(this.dom,r),Ms(r),this.handler.processGesture(r,"end"),ue.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<Vw&&ue.click.call(this,r)},pointerdown:function(r){ue.mousedown.call(this,r)},pointermove:function(r){Iu(r)||ue.mousemove.call(this,r)},pointerup:function(r){ue.mouseup.call(this,r)},pointerout:function(r){Iu(r)||ue.mouseout.call(this,r)}};M(["click","dblclick","contextmenu"],function(r){ue[r]=function(t){t=se(this.dom,t),this.trigger(r,t)}});var Eu={pointermove:function(r){Iu(r)||Eu.mousemove.call(this,r)},pointerup:function(r){Eu.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function Xw(r,t){var e=t.domHandlers;Q.pointerEventsSupported?M(Ds.pointer,function(i){Ia(t,i,function(n){e[i].call(r,n)})}):(Q.touchEventsSupported&&M(Ds.touch,function(i){Ia(t,i,function(n){e[i].call(r,n),Ww(t)})}),M(Ds.mouse,function(i){Ia(t,i,function(n){n=$l(n),t.touching||e[i].call(r,n)})}))}function $w(r,t){Q.pointerEventsSupported?M(av.pointer,e):Q.touchEventsSupported||M(av.mouse,e);function e(i){function n(a){a=$l(a),Hd(r,a.target)||(a=Uw(r,a),t.domHandlers[i].call(r,a))}Ia(t,i,n,{capture:!0})}}function Ia(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,Aw(r.domTarget,t,e,i)}function As(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&Lw(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var sv=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),Zw=function(r){O(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new sv(e,ue),Cs&&(n._globalHandlerScope=new sv(document,Eu)),Xw(n,n._localHandlerScope),n}return t.prototype.dispose=function(){As(this._localHandlerScope),Cs&&As(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,Cs&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?$w(this,i):As(i)}},t}(Me);/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Ea={},Vd={};function qw(r){delete Vd[r]}function Kw(r){if(!r)return!1;if(typeof r=="string")return za(r,1)<au;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=za(t[n].color,1);return e/=i,e<au}return!1}var Qw=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new Gw,o=i.renderer||"canvas";Ea[o]||(o=lt(Ea)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Ea[o](e,a,i,t),u=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var l=!Q.node&&!Q.worker&&!u?new Zw(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?Q.touchEventsSupported:!!f,c=44,v;h&&(v=$(i.pointerSize,c)),this.handler=new Fd(a,s,l,s.root,v),this.animation=new Hw({stage:{update:u?null:function(){return n._flush(!0)}}}),u||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=Kw(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=hi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=hi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof At&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,qw(this.id))},r}();function uv(r,t){var e=new Qw(Pc(),r,t);return Vd[e.id]=e,e}function Jw(r,t){Ea[r]=t}var Wd="";typeof navigator<"u"&&(Wd=navigator.platform||"");var ni="rgba(0, 0, 0, 0.2)";const jw={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:ni,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:ni,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:ni,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:ni,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:ni,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:ni,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Wd.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var Ru=X();function rD(r,t){xe(Ru.get(r)==null&&t),Ru.set(r,t)}function tb(r,t,e){var i=Ru.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var aa,Yi,lv,fv="\0_ec_inner",eb=1,Zl=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new mt(a),this._locale=new mt(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=cv(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,cv(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?lv(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var u=a.getMediaOption(this);u.length&&M(u,function(l){n=!0,this._mergeOption(l,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],u=X(),l=i&&i.replaceMergeMainTypeMap;F_(this),M(e,function(h,c){h!=null&&(nt.hasClass(c)?c&&(s.push(c),u.set(c,!0)):n[c]=n[c]==null?J(h):et(n[c],h,!0))}),l&&l.each(function(h,c){nt.hasClass(c)&&!u.get(c)&&(s.push(c),u.set(c,!0))}),nt.topologicalTravel(s,nt.getAllClassMainTypes(),f,this);function f(h){var c=tb(this,h,It(e[h])),v=a.get(h),p=v?l&&l.get(h)?"replaceMerge":"normalMerge":"replaceAll",y=e0(v,c,p);u0(y,h,nt),n[h]=null,a.set(h,null),o.set(h,0);var d=[],g=[],m=0,_;M(y,function(S,b){var w=S.existing,x=S.newOption;if(!x)w&&(w.mergeOption({},this),w.optionUpdated({},!1));else{var T=h==="series",C=nt.getClass(h,S.keyInfo.subType,!T);if(!C)return;if(h==="tooltip"){if(_)return;_=!0}if(w&&w.constructor===C)w.name=S.keyInfo.name,w.mergeOption(x,this),w.optionUpdated(x,!1);else{var D=k({componentIndex:b},S.keyInfo);w=new C(x,this,this,D),k(w,D),S.brandNew&&(w.__requireNewView=!0),w.init(x,this,this),w.optionUpdated(null,!0)}}w?(d.push(w.option),g.push(w),m++):(d.push(void 0),g.push(void 0))},this),n[h]=d,a.set(h,g),o.set(h,m),h==="series"&&aa(this)}this._seriesIndices||aa(this)},t.prototype.getOption=function(){var e=J(this.option);return M(e,function(i,n){if(nt.hasClass(n)){for(var a=It(i),o=a.length,s=!1,u=o-1;u>=0;u--)a[u]&&!Sn(a[u])?s=!0:(a[u]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[fv],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var u;return n!=null?(u=[],M(It(n),function(l){s[l]&&u.push(s[l])})):a!=null?u=hv("id",a,s):o!=null?u=hv("name",o,s):u=wt(s,function(l){return!!l}),vv(u,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):wt(this._componentsMap.get(n),function(l){return!!l});return u(vv(o,e));function s(l){var f=n+"Index",h=n+"Id",c=n+"Name";return l&&(l[f]!=null||l[h]!=null||l[c]!=null)?{mainType:n,index:l[f],id:l[h],name:l[c]}:null}function u(l){return e.filter?wt(l,e.filter):l}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if(Y(e)){var o=i,s=e;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var p=h[v];p&&s.call(o,c,p,p.componentIndex)}})}else for(var u=V(e)?a.get(e):G(e)?this.findComponents(e):null,l=0;u&&l<u.length;l++){var f=u[l];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=we(e,null);return wt(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return wt(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return wt(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){Yi(this),M(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){M(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){Yi(this),M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return M(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return Yi(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){Yi(this);var n=[];M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=X(n)},t.prototype.restoreData=function(e){aa(this);var i=this._componentsMap,n=[];i.each(function(a,o){nt.hasClass(o)&&n.push(o)}),nt.topologicalTravel(n,nt.getAllClassMainTypes(),function(a){M(i.get(a),function(o){o&&(a!=="series"||!rb(o,e))&&o.restoreData()})})},t.internalField=function(){aa=function(e){var i=e._seriesIndices=[];M(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=X(i)},Yi=function(e){},lv=function(e,i){e.option={},e.option[fv]=eb,e._componentsMap=X({series:[]}),e._componentsCount=X();var n=i.aria;G(n)&&n.enabled==null&&(n.enabled=!0),ib(i,e._theme.option),et(i,jw,!1),e._mergeOption(i,null)}}(),t}(mt);function rb(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function ib(r,t){var e=r.color&&!r.colorLayer;M(t,function(i,n){n==="colorLayer"&&e||nt.hasClass(n)||(typeof i=="object"?r[n]=r[n]?et(r[n],i,!1):J(i):r[n]==null&&(r[n]=i))})}function hv(r,t,e){if(F(t)){var i=X();return M(t,function(a){if(a!=null){var o=we(a,null);o!=null&&i.set(a,!0)}}),wt(e,function(a){return a&&i.get(a[r])})}else{var n=we(t,null);return wt(e,function(a){return a&&n!=null&&a[r]===n})}}function vv(r,t){return t.hasOwnProperty("subType")?wt(r,function(e){return e&&e.subType===t.subType}):r}function cv(r){var t=X();return r&&M(It(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}De(Zl,Vl);var nb=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],Ud=function(){function r(t){M(nb,function(e){this[e]=gt(t[e],t)},this)}return r}(),ab=/^(min|max)?(.+)$/,ob=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(M(It(t.series),function(o){o&&o.data&&Ft(o.data)&&Qs(o.data)}),M(It(t.dataset),function(o){o&&o.source&&Ft(o.source)&&Qs(o.source)})),t=J(t);var n=this._optionBackup,a=sb(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],J(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=J(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var u=0,l=n.length;u<l;u++)ub(n[u].query,e,i)&&o.push(u);return!o.length&&a&&(o=[-1]),o.length&&!fb(o,this._currentMediaIndices)&&(s=z(o,function(f){return J(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function sb(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,u=r.options,l=r.media,f=!!r.media,h=!!(u||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&F(l)&&M(l,function(v){v&&v.option&&(v.query?i.push(v):n||(n=v))}),c(a),M(u,function(v){return c(v)}),M(i,function(v){return c(v.option)});function c(v){M(t,function(p){p(v,e)})}return{baseOption:a,timelineOptions:u||[],mediaDefault:n,mediaList:i}}function ub(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return M(r,function(a,o){var s=o.match(ab);if(!(!s||!s[1]||!s[2])){var u=s[1],l=s[2].toLowerCase();lb(i[l],a,u)||(n=!1)}}),n}function lb(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function fb(r,t){return r.join(",")===t.join(",")}var ne=M,Tn=G,pv=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ls(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=pv.length;e<i;e++){var n=pv[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?et(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?et(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function Mt(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,rt(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function rn(r){Mt(r,"itemStyle"),Mt(r,"lineStyle"),Mt(r,"areaStyle"),Mt(r,"label"),Mt(r,"labelLine"),Mt(r,"upperLabel"),Mt(r,"edgeLabel")}function ct(r,t){var e=Tn(r)&&r[t],i=Tn(e)&&e.textStyle;if(i)for(var n=0,a=Yf.length;n<a;n++){var o=Yf[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function Zt(r){r&&(rn(r),ct(r,"label"),r.emphasis&&ct(r.emphasis,"label"))}function hb(r){if(Tn(r)){Ls(r),rn(r),ct(r,"label"),ct(r,"upperLabel"),ct(r,"edgeLabel"),r.emphasis&&(ct(r.emphasis,"label"),ct(r.emphasis,"upperLabel"),ct(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Ls(t),Zt(t));var e=r.markLine;e&&(Ls(e),Zt(e));var i=r.markArea;i&&Zt(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!Ft(a))for(var o=0;o<a.length;o++)Zt(a[o]);M(r.categories,function(l){rn(l)})}if(n&&!Ft(n))for(var o=0;o<n.length;o++)Zt(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)Zt(s[o]);if(e=r.markLine,e&&e.data)for(var u=e.data,o=0;o<u.length;o++)F(u[o])?(Zt(u[o][0]),Zt(u[o][1])):Zt(u[o]);r.type==="gauge"?(ct(r,"axisLabel"),ct(r,"title"),ct(r,"detail")):r.type==="treemap"?(Mt(r.breadcrumb,"itemStyle"),M(r.levels,function(l){rn(l)})):r.type==="tree"&&rn(r.leaves)}}function Ie(r){return F(r)?r:r?[r]:[]}function dv(r){return(F(r)?r[0]:r)||{}}function vb(r,t){ne(Ie(r.series),function(i){Tn(i)&&hb(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),ne(e,function(i){ne(Ie(r[i]),function(n){n&&(ct(n,"axisLabel"),ct(n.axisPointer,"label"))})}),ne(Ie(r.parallel),function(i){var n=i&&i.parallelAxisDefault;ct(n,"axisLabel"),ct(n&&n.axisPointer,"label")}),ne(Ie(r.calendar),function(i){Mt(i,"itemStyle"),ct(i,"dayLabel"),ct(i,"monthLabel"),ct(i,"yearLabel")}),ne(Ie(r.radar),function(i){ct(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),ne(Ie(r.geo),function(i){Tn(i)&&(Zt(i),ne(Ie(i.regions),function(n){Zt(n)}))}),ne(Ie(r.timeline),function(i){Zt(i),Mt(i,"label"),Mt(i,"itemStyle"),Mt(i,"controlStyle",!0);var n=i.data;F(n)&&M(n,function(a){G(a)&&(Mt(a,"label"),Mt(a,"itemStyle"))})}),ne(Ie(r.toolbox),function(i){Mt(i,"iconStyle"),ne(i.feature,function(n){Mt(n,"iconStyle")})}),ct(dv(r.axisPointer),"label"),ct(dv(r.tooltip).axisPointer,"label")}function cb(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function pb(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];a[n[s]]==null&&(a[n[s]]=e)}function gv(r){r&&M(db,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var db=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],gb=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Ps=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Xi(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<Ps.length;e++){var i=Ps[e][1],n=Ps[e][0];t[i]!=null&&(t[n]=t[i])}}function yv(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function mv(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function yb(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function Yd(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&Yd(r[e].children,t)}function Xd(r,t){vb(r,t),r.series=It(r.series),M(r.series,function(e){if(G(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),yv(e.label);var n=e.data;if(n&&!Ft(n))for(var a=0;a<n.length;a++)yv(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=cb(e,"pointer.color");o!=null&&pb(e,"itemStyle.color",o)}else if(i==="bar"){Xi(e),Xi(e.backgroundStyle),Xi(e.emphasis);var n=e.data;if(n&&!Ft(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(Xi(n[a]),Xi(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),mv(e),Yd(e.data,mv)}else i==="graph"||i==="sankey"?yb(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&rt(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),gv(e)}}),r.dataRange&&(r.visualMap=r.dataRange),M(gb,function(e){var i=r[e];i&&(F(i)||(i=[i]),M(i,function(n){gv(n)}))})}function mb(r){var t=X();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(_b)}function _b(r){M(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,u=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(l,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return n;var v,p;s?p=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var y=NaN,d=e-1;d>=0;d--){var g=r[d];if(s||(p=g.data.rawIndexOf(g.stackedByDimension,v)),p>=0){var m=g.data.getByRawIndex(g.stackResultDimension,p);if(u==="all"||u==="positive"&&m>0||u==="negative"&&m<0||u==="samesign"&&c>=0&&m>0||u==="samesign"&&c<=0&&m<0){c=Km(c,m),y=m;break}}}return i[0]=c,i[1]=y,i})})}var Ce=function(){function r(){this.group=new At,this.uid=xo("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();il(Ce);so(Ce);var _v=dt(),Sv={itemStyle:mn(Bp,!0),lineStyle:mn(Op,!0)},Sb={lineStyle:"stroke",itemStyle:"fill"};function $d(r,t){var e=r.visualStyleMapper||Sv[t];return e||(console.warn("Unknown style type '"+t+"'."),Sv.itemStyle)}function Zd(r,t){var e=r.visualDrawType||Sb[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var wb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=$d(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var u=Zd(r,i),l=o[u],f=Y(l)?l:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[u]||f||h){var c=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[u]||(o[u]=c,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||Y(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||Y(o.stroke)?c:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",u),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(v,p){var y=r.getDataParams(p),d=k({},o);d[u]=f(y),v.setItemVisual(p,"style",d)}}}},$i=new mt,bb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=$d(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var u=o.getRawDataItem(s);if(u&&u[i]){$i.option=u[i];var l=n($i),f=o.ensureUniqueItemVisual(s,"style");k(f,l),$i.option.decal&&(o.setItemVisual(s,"decal",$i.option.decal),$i.option.decal.dirty=!0),a in l&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},xb={performRawSeries:!0,overallReset:function(r){var t=X();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),_v(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=_v(e).scope,s=e.visualStyleAccessPath||"itemStyle",u=Zd(e,s);a.each(function(l){var f=a.getRawIndex(l);n[f]=l}),i.each(function(l){var f=n[l],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=i.getName(l)||l+"",p=i.count();c[u]=e.getColorFromPalette(v,o,p)}})}})}},oa=Math.PI;function Tb(r,t){t=t||{},rt(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new At,i=new xt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new Et({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new xt({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new mo({shape:{startAngle:-oa/2,endAngle:-oa/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:oa*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:oa*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,u=t.showSpinner?t.spinnerRadius:0,l=(r.getWidth()-u*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:u),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:l,cy:f}),a.setShape({x:l-u,y:f-u,width:u*2,height:u*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var qd=function(){function r(t,e,i,n){this._stageTaskMap=X(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,u=s!=null?Math.ceil(s/o):null;return{step:o,modBy:u,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),u=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:u,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=X();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;M(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";xe(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;M(t,function(u,l){if(!(n.visualType&&n.visualType!==u.visualType)){var f=o._stageTaskMap.get(u.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,p=c.agentStubMap;p.each(function(d){s(n,d)&&(d.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,i);var y=o.getPerformArgs(c,n.block);p.each(function(d){d.perform(y)}),c.perform(y)&&(a=!0)}else h&&h.each(function(d,g){s(n,d)&&d.dirty();var m=o.getPerformArgs(d,n.block);m.skip=!u.performRawSeries&&e.isSeriesFiltered(d.context.model),o.updatePayload(d,i),d.perform(m)&&(a=!0)})}});function s(u,l){return u.setDirty&&(!u.dirtyMap||u.dirtyMap.get(l.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=X(),u=t.seriesType,l=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):u?i.eachRawSeriesByType(u,f):l&&l(i,n).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||cn({plan:Lb,reset:Pb,count:Eb}));v.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||cn({reset:Cb});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,u=o.agentStubMap=X(),l=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";xe(!t.createOnAllSeries,v),l?i.eachRawSeriesByType(l,p):f?f(i,n).each(p):(h=!1,M(i.getSeries(),p));function p(y){var d=y.uid,g=u.set(d,s&&s.get(d)||(c=!0,cn({reset:Db,onDirty:Ab})));g.context={model:y,overallProgress:h},g.agent=o,g.__block=h,a._pipe(y,g)}c&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return Y(t)&&(t={overallReset:t,seriesType:Rb(t)}),t.uid=xo("stageHandler"),e&&(t.visualType=e),t},r}();function Cb(r){r.overallReset(r.ecModel,r.api,r.payload)}function Db(r){return r.overallProgress&&Mb}function Mb(){this.agent.dirty(),this.getDownstream().dirty()}function Ab(){this.agent&&this.agent.dirty()}function Lb(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function Pb(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=It(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?z(t,function(e,i){return Kd(i)}):Ib}var Ib=Kd(0);function Kd(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function Eb(r){return r.data.count()}function Rb(r){ja=null;try{r(Cn,Qd)}catch{}return ja}var Cn={},Qd={},ja;Jd(Cn,Zl);Jd(Qd,Ud);Cn.eachSeriesByType=Cn.eachRawSeriesByType=function(r){ja=r};Cn.eachComponent=function(r){r.mainType==="series"&&r.subType&&(ja=r.subType)};function Jd(r,t){for(var e in t.prototype)r[e]=te}var wv=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const kb={color:wv,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],wv]};var Dt="#B9B8CE",bv="#100C2A",sa=function(){return{axisLine:{lineStyle:{color:Dt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},xv=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],jd={darkMode:!0,color:xv,backgroundColor:bv,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Dt},pageTextStyle:{color:Dt}},textStyle:{color:Dt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Dt}},dataZoom:{borderColor:"#71708A",textStyle:{color:Dt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Dt}},timeline:{lineStyle:{color:Dt},label:{color:Dt},controlStyle:{color:Dt,borderColor:Dt}},calendar:{itemStyle:{color:bv},dayLabel:{color:Dt},monthLabel:{color:Dt},yearLabel:{color:Dt}},timeAxis:sa(),logAxis:sa(),valueAxis:sa(),categoryAxis:sa(),line:{symbol:"circle"},graph:{color:xv},gauge:{title:{color:Dt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Dt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};jd.categoryAxis.splitLine.show=!1;var Ob=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(V(t)){var a=me(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};M(t,function(u,l){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=l.lastIndexOf(c);if(v>0&&v===l.length-c.length){var p=l.slice(0,v);p!=="data"&&(e.mainType=p,e[c.toLowerCase()]=u,f=!0)}}s.hasOwnProperty(l)&&(i[l]=u,f=!0),f||(n[l]=u)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var u=e.cptQuery,l=e.dataQuery;return f(u,o,"mainType")&&f(u,o,"subType")&&f(u,o,"index","componentIndex")&&f(u,o,"name")&&f(u,o,"id")&&f(l,a,"name")&&f(l,a,"dataIndex")&&f(l,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,c,v,p){return h[v]==null||c[p||v]===h[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),ku=["symbol","symbolSize","symbolRotate","symbolOffset"],Tv=ku.concat(["symbolKeepAspect"]),Bb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<ku.length;o++){var s=ku[o],u=r.get(s);Y(u)?(a=!0,n[s]=u):i[s]=u}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(k({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var l=lt(n);function f(h,c){for(var v=r.getRawValue(c),p=r.getDataParams(c),y=0;y<l.length;y++){var d=l[y];h.setItemVisual(c,d,n[d](v,p))}}return{dataEach:a?f:null}}},Nb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<Tv.length;s++){var u=Tv[s],l=o.getShallow(u,!0);l!=null&&n.setItemVisual(a,u,l)}}return{dataEach:e.hasItemOption?i:null}}};function Fb(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function zb(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function ua(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var Gb=Math.round(Math.random()*9),Hb=typeof Object.defineProperty=="function",Vb=function(){function r(){this._id="__ec_inner_"+Gb++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return Hb?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}();function kr(r){return isFinite(r)}function Wb(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=kr(i)?i:0,n=kr(n)?n:1,a=kr(a)?a:0,o=kr(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function Ub(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,u=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,u=u*a),o=kr(o)?o:.5,s=kr(s)?s:.5,u=u>=0&&kr(u)?u:.5;var l=r.createRadialGradient(o,s,0,o,s,u);return l}function Ou(r,t,e){for(var i=t.type==="radial"?Ub(r,t,e):Wb(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function Yb(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function la(r){return parseInt(r,10)}function fa(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||la(s[i])||la(r.style[i]))-(la(s[a])||0)-(la(s[o])||0)|0}function Xb(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:ft(r)?[r]:F(r)?r:null}function tg(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&Xb(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=z(e,function(a){return a/n}),i/=n)}return[e,i]}var $b=new Vr(!0);function to(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function Cv(r){return typeof r=="string"&&r!=="none"}function eo(r){var t=r.fill;return t!=null&&t!=="none"}function Dv(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function Mv(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function Bu(r,t,e){var i=Fc(t.image,t.__image,e);if(uo(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*fy),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function Zb(r,t,e,i){var n,a=to(e),o=eo(e),s=e.strokePercent,u=s<1,l=!t.path;(!t.silent||u)&&l&&t.createPathProxy();var f=t.path||$b,h=t.__dirty;if(!i){var c=e.fill,v=e.stroke,p=o&&!!c.colorStops,y=a&&!!v.colorStops,d=o&&!!c.image,g=a&&!!v.image,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0;(p||y)&&(w=t.getBoundingRect()),p&&(m=h?Ou(r,c,w):t.__canvasFillGradient,t.__canvasFillGradient=m),y&&(_=h?Ou(r,v,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=_),d&&(S=h||!t.__canvasFillPattern?Bu(r,c,t):t.__canvasFillPattern,t.__canvasFillPattern=S),g&&(b=h||!t.__canvasStrokePattern?Bu(r,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=S),p?r.fillStyle=m:d&&(S?r.fillStyle=S:o=!1),y?r.strokeStyle=_:g&&(b?r.strokeStyle=b:a=!1)}var x=t.getGlobalScale();f.setScale(x[0],x[1],t.segmentIgnoreThreshold);var T,C;r.setLineDash&&e.lineDash&&(n=tg(t),T=n[0],C=n[1]);var D=!0;(l||h&ji)&&(f.setDPR(r.dpr),u?f.setContext(null):(f.setContext(r),D=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),D&&f.rebuildPath(r,u?s:1),T&&(r.setLineDash(T),r.lineDashOffset=C),i||(e.strokeFirst?(a&&Mv(r,e),o&&Dv(r,e)):(o&&Dv(r,e),a&&Mv(r,e))),T&&r.setLineDash([])}function qb(r,t,e){var i=t.__image=Fc(e.image,t.__image,t,t.onload);if(!(!i||!uo(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),u=i.width/i.height;if(o==null&&s!=null?o=s*u:s==null&&o!=null?s=o/u:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var l=e.sx||0,f=e.sy||0;r.drawImage(i,l,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var l=e.sx,f=e.sy,h=o-l,c=s-f;r.drawImage(i,l,f,h,c,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function Kb(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||zr,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=tg(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(to(e)&&r.strokeText(n,e.x,e.y),eo(e)&&r.fillText(n,e.x,e.y)):(eo(e)&&r.fillText(n,e.x,e.y),to(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var Av=["shadowBlur","shadowOffsetX","shadowOffsetY"],Lv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function eg(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){Bt(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?Br.opacity:o}(i||t.blend!==e.blend)&&(a||(Bt(r,n),a=!0),r.globalCompositeOperation=t.blend||Br.blend);for(var s=0;s<Av.length;s++){var u=Av[s];(i||t[u]!==e[u])&&(a||(Bt(r,n),a=!0),r[u]=r.dpr*(t[u]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(Bt(r,n),a=!0),r.shadowColor=t.shadowColor||Br.shadowColor),a}function Pv(r,t,e,i,n){var a=Dn(t,n.inHover),o=i?null:e&&Dn(e,n.inHover)||{};if(a===o)return!1;var s=eg(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(Bt(r,n),s=!0),Cv(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(Bt(r,n),s=!0),Cv(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(Bt(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var u=a.lineWidth,l=u/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==l&&(s||(Bt(r,n),s=!0),r.lineWidth=l)}for(var f=0;f<Lv.length;f++){var h=Lv[f],c=h[0];(i||a[c]!==o[c])&&(s||(Bt(r,n),s=!0),r[c]=a[c]||h[1])}return s}function Qb(r,t,e,i,n){return eg(r,Dn(t,n.inHover),e&&Dn(e,n.inHover),i,n)}function rg(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function Jb(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),rg(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function jb(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var Iv=1,Ev=2,Rv=3,kv=4;function tx(r){var t=eo(r),e=to(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function Bt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Dn(r,t){return t&&r.__hoverStyle||r.style}function ig(r,t){Or(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Or(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=-2,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,u=!1;if((!o||Yb(a,o))&&(o&&o.length&&(Bt(r,e),r.restore(),u=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(Bt(r,e),r.save(),Jb(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var l=e.prevEl;l||(u=s=!0);var f=t instanceof st&&t.autoBatch&&tx(t.style);s||jb(n,l.transform)?(Bt(r,e),rg(r,t)):f||Bt(r,e);var h=Dn(t,e.inHover);t instanceof st?(e.lastDrawType!==Iv&&(u=!0,e.lastDrawType=Iv),Pv(r,t,l,u,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),Zb(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Ha?(e.lastDrawType!==Rv&&(u=!0,e.lastDrawType=Rv),Pv(r,t,l,u,e),Kb(r,t,h)):t instanceof sr?(e.lastDrawType!==Ev&&(u=!0,e.lastDrawType=Ev),Qb(r,t,l,u,e),qb(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==kv&&(u=!0,e.lastDrawType=kv),ex(r,t,e)),f&&i&&Bt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function ex(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var u=i[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Or(r,u,a,o===s-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}for(var l=0,f=n.length;l<f;l++){var u=n[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Or(r,u,a,l===f-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Is=new Vb,Ov=new Pn(100),Bv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Nu(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&Is.delete(r);var a=Is.get(r);if(a)return a;var o=rt(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return u(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,Is.set(r,s),r.dirty=!1,s;function u(l){for(var f=[e],h=!0,c=0;c<Bv.length;++c){var v=o[Bv[c]];if(v!=null&&!F(v)&&!V(v)&&!ft(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var p;if(h){p=f.join(",")+(n?"-svg":"");var y=Ov.get(p);y&&(n?l.svgElement=y:l.image=y)}var d=ag(o.dashArrayX),g=rx(o.dashArrayY),m=ng(o.symbol),_=ix(d),S=og(g),b=!n&&Di.createCanvas(),w=n&&{tag:"g",attrs:{},key:"dcl",children:[]},x=C(),T;b&&(b.width=x.width*e,b.height=x.height*e,T=b.getContext("2d")),D(),h&&Ov.put(p,b||w),l.image=b,l.svgElement=w,l.svgWidth=x.width,l.svgHeight=x.height;function C(){for(var A=1,L=0,I=_.length;L<I;++L)A=Vf(A,_[L]);for(var P=1,L=0,I=m.length;L<I;++L)P=Vf(P,m[L].length);A*=P;var E=S*_.length*m.length;return{width:Math.max(1,Math.min(A,o.maxTileWidth)),height:Math.max(1,Math.min(E,o.maxTileHeight))}}function D(){T&&(T.clearRect(0,0,b.width,b.height),o.backgroundColor&&(T.fillStyle=o.backgroundColor,T.fillRect(0,0,b.width,b.height)));for(var A=0,L=0;L<g.length;++L)A+=g[L];if(A<=0)return;for(var I=-S,P=0,E=0,R=0;I<x.height;){if(P%2===0){for(var H=E/2%m.length,B=0,N=0,W=0;B<x.width*2;){for(var Z=0,L=0;L<d[R].length;++L)Z+=d[R][L];if(Z<=0)break;if(N%2===0){var q=(1-o.symbolSize)*.5,it=B+d[R][N]*q,ut=I+g[P]*q,ht=d[R][N]*o.symbolSize,ie=g[P]*o.symbolSize,ur=W/2%m[H].length;Yr(it,ut,ht,ie,m[H][ur])}B+=d[R][N],++W,++N,N===d[R].length&&(N=0)}++R,R===d.length&&(R=0)}I+=g[P],++E,++P,P===g.length&&(P=0)}function Yr(Gt,_t,U,K,lr){var Lt=n?1:e,of=Ti(lr,Gt*Lt,_t*Lt,U*Lt,K*Lt,o.color,o.symbolKeepAspect);if(n){var sf=i.painter.renderOneToVNode(of);sf&&w.children.push(sf)}else ig(T,of)}}}}function ng(r){if(!r||r.length===0)return[["rect"]];if(V(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!V(r[e])){t=!1;break}if(t)return ng([r]);for(var i=[],e=0;e<r.length;++e)V(r[e])?i.push([r[e]]):i.push(r[e]);return i}function ag(r){if(!r||r.length===0)return[[0,0]];if(ft(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!ft(r[i])){e=!1;break}if(e)return ag([r]);for(var n=[],i=0;i<r.length;++i)if(ft(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=z(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function rx(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(ft(r)){var t=Math.ceil(r);return[t,t]}var e=z(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function ix(r){return z(r,function(t){return og(t)})}function og(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function nx(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var u=i.ensureUniqueItemVisual(o,"style");u.decal=Nu(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=Nu(n,t)}}})}var le=new Me,sg={};function ax(r,t){sg[r]=t}function ox(r){return sg[r]}var sx=1,ux=800,lx=900,fx=1e3,hx=2e3,vx=5e3,ug=1e3,cx=1100,ql=2e3,lg=3e3,px=4e3,Lo=4500,dx=4600,gx=5e3,yx=6e3,fg=7e3,mx={PROCESSOR:{FILTER:fx,SERIES_FILTER:ux,STATISTIC:vx},VISUAL:{LAYOUT:ug,PROGRESSIVE_LAYOUT:cx,GLOBAL:ql,CHART:lg,POST_CHART_LAYOUT:dx,COMPONENT:px,BRUSH:gx,CHART_ITEM:Lo,ARIA:yx,DECAL:fg}},Ct="__flagInMainProcess",kt="__pendingUpdate",Es="__needsUpdateStatus",Nv=/^[a-zA-Z0-9_]+$/,Rs="__connectUpdateStatus",Fv=0,_x=1,Sx=2;function hg(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return cg(this,r,t)}}function vg(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return cg(this,r,t)}}function cg(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),Me.prototype[t].apply(r,e)}var pg=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Me),dg=pg.prototype;dg.on=vg("on");dg.off=vg("off");var ai,ks,ha,Ze,Os,Bs,Ns,Zi,qi,zv,Gv,Fs,Hv,va,Vv,gg,Yt,Wv,yg=function(r){O(t,r);function t(e,i,n){var a=r.call(this,new Ob)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},V(i)&&(i=mg[i]),a._dom=e;var o="canvas",s="auto",u=!1;n.ssr;var l=a._zr=uv(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:$(n.useDirtyRect,u),useCoarsePointer:$(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=Od(gt(l.flush,l),17),i=J(i),i&&Xd(i,!0),a._theme=i,a._locale=k1(n.locale||id),a._coordSysMgr=new Rl;var f=a._api=Vv(a);function h(c,v){return c.__prio-v.__prio}return Pa(io,h),Pa(Fu,h),a._scheduler=new qd(a,f,Fu,io),a._messageCenter=new pg,a._initEvents(),a.resize=gt(a.resize,a),l.animation.on("frame",a._onframe,a),zv(l,a),Gv(l,a),Qs(a),a}return t.prototype._onframe=function(){if(!this._disposed){Wv(this);var e=this._scheduler;if(this[kt]){var i=this[kt].silent;this[Ct]=!0;try{ai(this),Ze.update.call(this,null,this[kt].updateParams)}catch(u){throw this[Ct]=!1,this[kt]=null,u}this._zr.flush(),this[Ct]=!1,this[kt]=null,Zi.call(this,i),qi.call(this,i)}else if(e.unfinished){var n=sx,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),Bs(this,a),e.performVisualTasks(a),va(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[Ct]){if(this._disposed){this.id;return}var a,o,s;if(G(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[Ct]=!0,!this._model||i){var u=new ob(this._api),l=this._theme,f=this._model=new Zl;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,l,this._locale,u)}this._model.setOption(e,{replaceMerge:o},zu);var h={seriesTransition:s,optionChanged:!0};if(n)this[kt]={silent:a,updateParams:h},this[Ct]=!1,this.getZr().wakeUp();else{try{ai(this),Ze.update.call(this,null,h)}catch(c){throw this[kt]=null,this[Ct]=!1,c}this._ssr||this._zr.flush(),this[kt]=null,this[Ct]=!1,Zi.call(this,a),qi.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||Q.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if(Q.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return M(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;M(i,function(u){n.eachComponent({mainType:u},function(l){var f=o._componentsMap[l.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return M(a,function(u){u.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(Uv[n]){var u=s,l=s,f=-s,h=-s,c=[],v=e&&e.pixelRatio||this.getDevicePixelRatio();M(dn,function(_,S){if(_.group===n){var b=i?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas(J(e)),w=_.getDom().getBoundingClientRect();u=a(w.left,u),l=a(w.top,l),f=o(w.right,f),h=o(w.bottom,h),c.push({dom:b,left:w.left,top:w.top})}}),u*=v,l*=v,f*=v,h*=v;var p=f-u,y=h-l,d=Di.createCanvas(),g=uv(d,{renderer:i?"svg":"canvas"});if(g.resize({width:p,height:y}),i){var m="";return M(c,function(_){var S=_.left-u,b=_.top-l;m+='<g transform="translate('+S+","+b+')">'+_.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=m,e.connectedBackgroundColor&&g.painter.setBackgroundColor(e.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return e.connectedBackgroundColor&&g.add(new xt({shape:{x:0,y:0,width:p,height:y},style:{fill:e.connectedBackgroundColor}})),M(c,function(_){var S=new sr({style:{x:_.left*v-u,y:_.top*v-l,image:_.dom}});g.add(S)}),g.refreshImmediately(),d.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return Os(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return Os(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=es(n,e);return M(o,function(s,u){u.indexOf("Models")>=0&&M(s,function(l){var f=l.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(u==="seriesModels"){var h=this._chartsMap[l.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,l))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=es(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),u=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return u!=null?Fb(s,u,i):zb(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;M(bx,function(i){var n=function(a){var o=e.getModel(),s=a.target,u,l=i==="globalout";if(l?u={}:s&&ua(s,function(p){var y=vt(p);if(y&&y.dataIndex!=null){var d=y.dataModel||o.getSeriesByIndex(y.seriesIndex);return u=d&&d.getDataParams(y.dataIndex,y.dataType,s)||{},!0}else if(y.eventData)return u=k({},y.eventData),!0},!0),u){var f=u.componentType,h=u.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=u.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&e[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];u.event=a,u.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:u,model:c,view:v},e.trigger(i,u)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),M(pn,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),M(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),Sw(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&vp(this.getDom(),Ql,"");var i=this,n=i._api,a=i._model;M(i._componentsViews,function(o){o.dispose(a,n)}),M(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete dn[i.id]},t.prototype.resize=function(e){if(!this[Ct]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[kt]&&(a==null&&(a=this[kt].silent),n=!0,this[kt]=null),this[Ct]=!0;try{n&&ai(this),Ze.update.call(this,{type:"resize",animation:k({duration:0},e&&e.animation)})}catch(o){throw this[Ct]=!1,o}this[Ct]=!1,Zi.call(this,a),qi.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(G(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!Gu[e]){var n=Gu[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=k({},e);return i.type=pn[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(G(i)||(i={silent:!!i}),!!ro[e.type]&&this._model){if(this[Ct]){this._pendingActions.push(e);return}var n=i.silent;Ns.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&Q.browser.weChat&&this._throttledZrFlush(),Zi.call(this,n),qi.call(this,n)}},t.prototype.updateLabelLayout=function(){le.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){ai=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),ks(h,!0),ks(h,!1),c.plan()},ks=function(h,c){for(var v=h._model,p=h._scheduler,y=c?h._componentsViews:h._chartsViews,d=c?h._componentsMap:h._chartsMap,g=h._zr,m=h._api,_=0;_<y.length;_++)y[_].__alive=!1;c?v.eachComponent(function(w,x){w!=="series"&&S(x)}):v.eachSeries(S);function S(w){var x=w.__requireNewView;w.__requireNewView=!1;var T="_ec_"+w.id+"_"+w.type,C=!x&&d[T];if(!C){var D=me(w.type),A=c?Ce.getClass(D.main,D.sub):be.getClass(D.sub);C=new A,C.init(v,m),d[T]=C,y.push(C),g.add(C.group)}w.__viewId=C.__id=T,C.__alive=!0,C.__model=w,C.group.__ecComponentInfo={mainType:w.mainType,index:w.componentIndex},!c&&p.prepareView(C,w,v,m)}for(var _=0;_<y.length;){var b=y[_];b.__alive?_++:(!c&&b.renderTask.dispose(),g.remove(b.group),b.dispose(v,m),y.splice(_,1),d[b.__id]===b&&delete d[b.__id],b.__id=b.group.__ecComponentInfo=null)}},ha=function(h,c,v,p,y){var d=h._model;if(d.setUpdatePayload(v),!p){M([].concat(h._componentsViews).concat(h._chartsViews),b);return}var g={};g[p+"Id"]=v[p+"Id"],g[p+"Index"]=v[p+"Index"],g[p+"Name"]=v[p+"Name"];var m={mainType:p,query:g};y&&(m.subType=y);var _=v.excludeSeriesId,S;_!=null&&(S=X(),M(It(_),function(w){var x=we(w,null);x!=null&&S.set(x,!0)})),d&&d.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;if(!x)if(rh(v))if(w instanceof or)v.type===Nr&&!v.notBlur&&!w.get(["emphasis","disabled"])&&T0(w,v,h._api);else{var T=gl(w.mainType,w.componentIndex,v.name,h._api),C=T.focusSelf,D=T.dispatchers;v.type===Nr&&C&&!v.notBlur&&hu(w.mainType,w.componentIndex,h._api),D&&M(D,function(A){v.type===Nr?Ua(A):Ya(A)})}else du(v)&&w instanceof or&&(M0(w,v,h._api),jf(w),Yt(h))},h),d&&d.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;x||b(h[p==="series"?"_chartsMap":"_componentsMap"][w.__viewId])},h);function b(w){w&&w.__alive&&w[c]&&w[c](w.__model,d,h._api,v)}},Ze={prepareAndUpdate:function(h){ai(this),Ze.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,p=this._api,y=this._zr,d=this._coordSysMgr,g=this._scheduler;if(v){v.setUpdatePayload(h),g.restoreData(v,h),g.performSeriesTasks(v),d.create(v,p),g.performDataProcessorTasks(v,h),Bs(this,v),d.update(v,p),e(v),g.performVisualTasks(v,h),Fs(this,v,p,h,c);var m=v.get("backgroundColor")||"transparent",_=v.get("darkMode");y.setBackgroundColor(m),_!=null&&_!=="auto"&&y.setDarkMode(_),le.trigger("afterupdate",v,p)}},updateTransform:function(h){var c=this,v=this._model,p=this._api;if(v){v.setUpdatePayload(h);var y=[];v.eachComponent(function(g,m){if(g!=="series"){var _=c.getViewOfComponentModel(m);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(m,v,p,h);S&&S.update&&y.push(_)}else y.push(_)}});var d=X();v.eachSeries(function(g){var m=c._chartsMap[g.__viewId];if(m.updateTransform){var _=m.updateTransform(g,v,p,h);_&&_.update&&d.set(g.uid,1)}else d.set(g.uid,1)}),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:d}),va(this,v,p,h,{},d),le.trigger("afterupdate",v,p)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),be.markUpdateMethod(h,"updateView"),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),Fs(this,c,this._api,h,{}),le.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(p){p.getData().clearAllVisual()}),be.markUpdateMethod(h,"updateVisual"),e(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(p,y){if(p!=="series"){var d=c.getViewOfComponentModel(y);d&&d.__alive&&d.updateVisual(y,v,c._api,h)}}),v.eachSeries(function(p){var y=c._chartsMap[p.__viewId];y.updateVisual(p,v,c._api,h)}),le.trigger("afterupdate",v,this._api))},updateLayout:function(h){Ze.update.call(this,h)}},Os=function(h,c,v,p){if(h._disposed){h.id;return}for(var y=h._model,d=h._coordSysMgr.getCoordinateSystems(),g,m=es(y,v),_=0;_<d.length;_++){var S=d[_];if(S[c]&&(g=S[c](y,m,p))!=null)return g}},Bs=function(h,c){var v=h._chartsMap,p=h._scheduler;c.eachSeries(function(y){p.updateStreamModes(y,v[y.__viewId])})},Ns=function(h,c){var v=this,p=this.getModel(),y=h.type,d=h.escapeConnect,g=ro[y],m=g.actionInfo,_=(m.update||"update").split(":"),S=_.pop(),b=_[0]!=null&&me(_[0]);this[Ct]=!0;var w=[h],x=!1;h.batch&&(x=!0,w=z(h.batch,function(P){return P=rt(k({},P),h),P.batch=null,P}));var T=[],C,D=du(h),A=rh(h);if(A&&wp(this._api),M(w,function(P){if(C=g.action(P,v._model,v._api),C=C||k({},P),C.type=m.event||C.type,T.push(C),A){var E=hp(h),R=E.queryOptionMap,H=E.mainTypeSpecified,B=H?R.keys()[0]:"series";ha(v,S,P,B),Yt(v)}else D?(ha(v,S,P,"series"),Yt(v)):b&&ha(v,S,P,b.main,b.sub)}),S!=="none"&&!A&&!D&&!b)try{this[kt]?(ai(this),Ze.update.call(this,h),this[kt]=null):Ze[S].call(this,h)}catch(P){throw this[Ct]=!1,P}if(x?C={type:m.event||y,escapeConnect:d,batch:T}:C=T[0],this[Ct]=!1,!c){var L=this._messageCenter;if(L.trigger(C.type,C),D){var I={type:"selectchanged",escapeConnect:d,selected:A0(p),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};L.trigger(I.type,I)}}},Zi=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();Ns.call(this,v,h)}},qi=function(h){!h&&this.trigger("updated")},zv=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[kt]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},Gv=function(h,c){h.on("mouseover",function(v){var p=v.target,y=ua(p,pu);y&&(C0(y,v,c._api),Yt(c))}).on("mouseout",function(v){var p=v.target,y=ua(p,pu);y&&(D0(y,v,c._api),Yt(c))}).on("click",function(v){var p=v.target,y=ua(p,function(m){return vt(m).dataIndex!=null},!0);if(y){var d=y.selected?"unselect":"select",g=vt(y);c._api.dispatchAction({type:d,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function i(h){var c=[],v=[],p=!1;if(h.eachComponent(function(m,_){var S=_.get("zlevel")||0,b=_.get("z")||0,w=_.getZLevelKey();p=p||!!w,(m==="series"?v:c).push({zlevel:S,z:b,idx:_.componentIndex,type:m,key:w})}),p){var y=c.concat(v),d,g;Pa(y,function(m,_){return m.zlevel===_.zlevel?m.z-_.z:m.zlevel-_.zlevel}),M(y,function(m){var _=h.getComponent(m.type,m.idx),S=m.zlevel,b=m.key;d!=null&&(S=Math.max(d,S)),b?(S===d&&b!==g&&S++,g=b):g&&(S===d&&S++,g=""),d=S,_.setZLevel(S)})}}Fs=function(h,c,v,p,y){i(c),Hv(h,c,v,p,y),M(h._chartsViews,function(d){d.__alive=!1}),va(h,c,v,p,y),M(h._chartsViews,function(d){d.__alive||d.remove(c,v)})},Hv=function(h,c,v,p,y,d){M(d||h._componentsViews,function(g){var m=g.__model;l(m,g),g.render(m,c,v,p),s(m,g),f(m,g)})},va=function(h,c,v,p,y,d){var g=h._scheduler;y=k(y||{},{updatedSeries:c.getSeries()}),le.trigger("series:beforeupdate",c,v,y);var m=!1;c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];S.__alive=!0;var b=S.renderTask;g.updatePayload(b,p),l(_,S),d&&d.get(_.uid)&&b.dirty(),b.perform(g.getPerformArgs(b))&&(m=!0),S.group.silent=!!_.get("silent"),o(_,S),jf(_)}),g.unfinished=m||g.unfinished,le.trigger("series:layoutlabels",c,v,y),le.trigger("series:transition",c,v,y),c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];s(_,S),f(_,S)}),a(h,c),le.trigger("series:afterupdate",c,v,y)},Yt=function(h){h[Es]=!0,h.getZr().wakeUp()},Wv=function(h){h[Es]&&(h.getZr().storage.traverse(function(c){hn(c)||n(c)}),h[Es]=!1)};function n(h){for(var c=[],v=h.currentStates,p=0;p<v.length;p++){var y=v[p];y==="emphasis"||y==="blur"||y==="select"||c.push(y)}h.selected&&h.states.select&&c.push("select"),h.hoverState===co&&h.states.emphasis?c.push("emphasis"):h.hoverState===vo&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,p=v.storage,y=0;p.traverse(function(d){d.isGroup||y++}),y>c.get("hoverLayerThreshold")&&!Q.node&&!Q.worker&&c.eachSeries(function(d){if(!d.preventUsingHoverLayer){var g=h._chartsMap[d.__viewId];g.__alive&&g.eachRendered(function(m){m.states.emphasis&&(m.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(p){p.isGroup||(p.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,p=h.get("zlevel")||0;c.eachRendered(function(y){return u(y,v,p,-1/0),!0})}}function u(h,c,v,p){var y=h.getTextContent(),d=h.getTextGuideLine(),g=h.isGroup;if(g)for(var m=h.childrenRef(),_=0;_<m.length;_++)p=Math.max(u(m[_],c,v,p),p);else h.z=c,h.zlevel=v,p=Math.max(h.z2,p);if(y&&(y.z=c,y.zlevel=v,isFinite(p)&&(y.z2=p+2)),d){var S=h.textGuideLineConfig;d.z=c,d.zlevel=v,isFinite(p)&&(d.z2=p+(S&&S.showAbove?1:-1))}return p}function l(h,c){c.eachRendered(function(v){if(!hn(v)){var p=v.getTextContent(),y=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),p&&p.stateTransition&&(p.stateTransition=null),y&&y.stateTransition&&(y.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),p=h.isAnimationEnabled(),y=v.get("duration"),d=y>0?{duration:y,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(g){if(g.states&&g.states.emphasis){if(hn(g))return;if(g instanceof st&&R0(g),g.__dirty){var m=g.prevStates;m&&g.useStates(m)}if(p){g.stateTransition=d;var _=g.getTextContent(),S=g.getTextGuideLine();_&&(_.stateTransition=d),S&&(S.stateTransition=d)}g.__dirty&&n(g)}})}Vv=function(h){return new(function(c){O(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(p){for(;p;){var y=p.__ecComponentInfo;if(y!=null)return h._model.getComponent(y.mainType,y.index);p=p.parent}},v.prototype.enterEmphasis=function(p,y){Ua(p,y),Yt(h)},v.prototype.leaveEmphasis=function(p,y){Ya(p,y),Yt(h)},v.prototype.enterBlur=function(p){x0(p),Yt(h)},v.prototype.leaveBlur=function(p){yp(p),Yt(h)},v.prototype.enterSelect=function(p){mp(p),Yt(h)},v.prototype.leaveSelect=function(p){_p(p),Yt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(p){return h.getViewOfComponentModel(p)},v.prototype.getViewOfSeriesModel=function(p){return h.getViewOfSeriesModel(p)},v}(Ud))(h)},gg=function(h){function c(v,p){for(var y=0;y<v.length;y++){var d=v[y];d[Rs]=p}}M(pn,function(v,p){h._messageCenter.on(p,function(y){if(Uv[h.group]&&h[Rs]!==Fv){if(y&&y.escapeConnect)return;var d=h.makeActionFromEvent(y),g=[];M(dn,function(m){m!==h&&m.group===h.group&&g.push(m)}),c(g,Fv),M(g,function(m){m[Rs]!==_x&&m.dispatchAction(d)}),c(g,Sx)}})})}}(),t}(Me),Kl=yg.prototype;Kl.on=hg("on");Kl.off=hg("off");Kl.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var bx=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var ro={},pn={},Fu=[],zu=[],io=[],mg={},Gu={},dn={},Uv={},xx=+new Date-0,Ql="_echarts_instance_";function iD(r,t,e){{var i=Tx(r);if(i)return i}var n=new yg(r,t,e);return n.id="ec_"+xx++,dn[n.id]=n,vp(r,Ql,n.id),gg(n),le.trigger("afterinit",n),n}function Tx(r){return dn[h0(r,Ql)]}function _g(r,t){mg[r]=t}function Sg(r){ot(zu,r)<0&&zu.push(r)}function wg(r,t){jl(Fu,r,t,hx)}function Cx(r){Jl("afterinit",r)}function Dx(r){Jl("afterupdate",r)}function Jl(r,t){le.on(r,t)}function Li(r,t,e){Y(t)&&(e=t,t="");var i=G(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!pn[t]&&(xe(Nv.test(i)&&Nv.test(t)),ro[i]||(ro[i]={action:e,actionInfo:r}),pn[t]=i)}function Mx(r,t){Rl.register(r,t)}function Ax(r,t){jl(io,r,t,ug,"layout")}function Ur(r,t){jl(io,r,t,lg,"visual")}var Yv=[];function jl(r,t,e,i,n){if((Y(t)||G(t))&&(e=t,t=i),!(ot(Yv,e)>=0)){Yv.push(e);var a=qd.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function bg(r,t){Gu[r]=t}function Lx(r,t,e){var i=ox("registerMap");i&&i(r,t,e)}var Px=oS;Ur(ql,wb);Ur(Lo,bb);Ur(Lo,xb);Ur(ql,Bb);Ur(Lo,Nb);Ur(fg,nx);Sg(Xd);wg(lx,mb);bg("default",Tb);Li({type:Nr,event:Nr,update:Nr},te);Li({type:Ta,event:Ta,update:Ta},te);Li({type:un,event:un,update:un},te);Li({type:Ca,event:Ca,update:Ca},te);Li({type:ln,event:ln,update:ln},te);_g("light",kb);_g("dark",jd);var Xv=[],Ix={registerPreprocessor:Sg,registerProcessor:wg,registerPostInit:Cx,registerPostUpdate:Dx,registerUpdateLifecycle:Jl,registerAction:Li,registerCoordinateSystem:Mx,registerLayout:Ax,registerVisual:Ur,registerTransform:Px,registerLoading:bg,registerMap:Lx,registerImpl:ax,PRIORITY:mx,ComponentModel:nt,ComponentView:Ce,SeriesModel:or,ChartView:be,registerComponentModel:function(r){nt.registerClass(r)},registerComponentView:function(r){Ce.registerClass(r)},registerSeriesModel:function(r){or.registerClass(r)},registerChartView:function(r){be.registerClass(r)},registerSubTypeDefaulter:function(r,t){nt.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){Jw(r,t)}};function Ci(r){if(F(r)){M(r,function(t){Ci(t)});return}ot(Xv,r)>=0||(Xv.push(r),Y(r)&&(r={install:r}),r.install(Ix))}var Ex=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.type="grid",t.dependencies=["xAxis","yAxis"],t.layoutMode="box",t.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},t}(nt),Rx=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var t=this.option;return!t.scale},r.prototype.getCoordSysModel=function(){},r}(),Hu=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",fe).models[0]},t.type="cartesian2dAxis",t}(nt);De(Hu,Rx);var xg={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},kx=et({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},xg),tf=et({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},xg),Ox=et({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},tf),Bx=rt({logBase:10},tf);const Nx={category:kx,value:tf,time:Ox,log:Bx};var Fx=0,Vu=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Fx}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&z(i,zx);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!V(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=X(this.categories))},r}();function zx(r){return G(r)&&r.value!=null?r.value:r+""}var Gx={value:1,category:1,time:1,log:1};function $v(r,t,e,i){M(Gx,function(n,a){var o=et(et({},Nx[a],!0),i,!0),s=function(u){O(l,u);function l(){var f=u!==null&&u.apply(this,arguments)||this;return f.type=t+"Axis."+a,f}return l.prototype.mergeDefaultAndTheme=function(f,h){var c=xn(this),v=c?Ao(f):{},p=h.getTheme();et(f,p.get(a+"Axis")),et(f,this.getDefaultOption()),f.type=Zv(f),c&&xi(f,v,c)},l.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=Vu.createByAxisModel(this))},l.prototype.getCategories=function(f){var h=this.option;if(h.type==="category")return f?h.data:this.__ordinalMeta.categories},l.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},l.type=t+"Axis."+a,l.defaultOption=o,l}(e);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(t+"Axis",Zv)}function Zv(r){return r.type||(r.data?"category":"value")}var Le=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();so(Le);function Wu(r){return r.type==="interval"||r.type==="log"}function Hx(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=ap(a/t);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=Tg(o),u=n.niceTickExtent=[pt(Math.ceil(r[0]/o)*o,s),pt(Math.floor(r[1]/o)*o,s)];return Vx(u,r),n}function zs(r){var t=Math.pow(10,hl(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,pt(e*t)}function Tg(r){return ke(r)+2}function qv(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function Vx(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),qv(r,0,t),qv(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function Po(r,t){return r>=t[0]&&r<=t[1]}function Io(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function Eo(r,t){return r*(t[1]-t[0])+t[0]}var ef=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new Vu({})),F(n)&&(n=new Vu({categories:z(n,function(a){return G(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:V(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),Po(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),Io(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(Eo(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,u=Math.min(s,i.length);o<u;++o){var l=i[o];n[o]=l,a[l]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(Le);Le.registerClass(ef);var Pr=pt,Pi=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return Po(e,this._extent)},t.prototype.normalize=function(e){return Io(e,this._extent)},t.prototype.scale=function(e){return Eo(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=Tg(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var u=1e4;n[0]<a[0]&&(e?s.push({value:Pr(a[0]-i,o)}):s.push({value:n[0]}));for(var l=a[0];l<=a[1]&&(s.push({value:l}),l=Pr(l+i,o),l!==s[s.length-1].value);)if(s.length>u)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:Pr(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],u=i[o-1],l=0,f=[],h=s.value-u.value,c=h/e;l<e-1;){var v=Pr(u.value+(l+1)*c);v>a[0]&&v<a[1]&&f.push(v),l++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=ke(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=Pr(e.value,n,!0);return cd(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=Hx(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=Pr(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=Pr(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(Le);Le.registerClass(Pi);var Wx=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},Cg=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return Fl(e.value,Eh[F1(yi(this._minLevelUnit))]||Eh.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return z1(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=Kx(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=jt,i[1]+=jt),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-jt}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=ca.length,u=Math.min(Wx(ca,this._approxInterval,0,s),s-1);this._interval=ca[u][1],this._minLevelUnit=ca[Math.max(u-1,0)][0]},t.prototype.parse=function(e){return ft(e)?e:+Fe(e)},t.prototype.contain=function(e){return Po(this.parse(e),this._extent)},t.prototype.normalize=function(e){return Io(this.parse(e),this._extent)},t.prototype.scale=function(e){return Eo(e,this._extent)},t.type="time",t}(Pi),ca=[["second",Bl],["minute",Nl],["hour",vn],["quarter-day",vn*6],["half-day",vn*12],["day",jt*1.2],["half-week",jt*3.5],["week",jt*7],["month",jt*31],["quarter",jt*95],["half-year",Ih/2],["year",Ih]];function Ux(r,t,e,i){var n=Fe(t),a=Fe(e),o=function(p){return Rh(n,p,i)===Rh(a,p,i)},s=function(){return o("year")},u=function(){return s()&&o("month")},l=function(){return u()&&o("day")},f=function(){return l()&&o("hour")},h=function(){return f()&&o("minute")},c=function(){return h()&&o("second")},v=function(){return c()&&o("millisecond")};switch(r){case"year":return s();case"month":return u();case"day":return l();case"hour":return f();case"minute":return h();case"second":return c();case"millisecond":return v()}}function Yx(r,t){return r/=jt,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function Xx(r){var t=30*jt;return r/=t,r>6?6:r>3?3:r>2?2:1}function $x(r){return r/=vn,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function Kv(r,t){return r/=t?Nl:Bl,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function Zx(r){return ap(r)}function qx(r,t,e){var i=new Date(r);switch(yi(t)){case"year":case"month":i[sd(e)](0);case"day":i[ud(e)](1);case"hour":i[ld(e)](0);case"minute":i[fd(e)](0);case"second":i[hd(e)](0),i[vd(e)](0)}return i.getTime()}function Kx(r,t,e,i){var n=1e4,a=ad,o=0;function s(D,A,L,I,P,E,R){for(var H=new Date(A),B=A,N=H[I]();B<L&&B<=i[1];)R.push({value:B}),N+=D,H[P](N),B=H.getTime();R.push({value:B,notAdd:!0})}function u(D,A,L){var I=[],P=!A.length;if(!Ux(yi(D),i[0],i[1],e)){P&&(A=[{value:qx(new Date(i[0]),D,e)},{value:i[1]}]);for(var E=0;E<A.length-1;E++){var R=A[E].value,H=A[E+1].value;if(R!==H){var B=void 0,N=void 0,W=void 0,Z=!1;switch(D){case"year":B=Math.max(1,Math.round(t/jt/365)),N=zl(e),W=G1(e);break;case"half-year":case"quarter":case"month":B=Xx(t),N=mi(e),W=sd(e);break;case"week":case"half-week":case"day":B=Yx(t),N=To(e),W=ud(e),Z=!0;break;case"half-day":case"quarter-day":case"hour":B=$x(t),N=bn(e),W=ld(e);break;case"minute":B=Kv(t,!0),N=Co(e),W=fd(e);break;case"second":B=Kv(t,!1),N=Do(e),W=hd(e);break;case"millisecond":B=Zx(t),N=Mo(e),W=vd(e);break}s(B,R,H,N,W,Z,I),D==="year"&&L.length>1&&E===0&&L.unshift({value:L[0].value-B})}}for(var E=0;E<I.length;E++)L.push(I[E]);return I}}for(var l=[],f=[],h=0,c=0,v=0;v<a.length&&o++<n;++v){var p=yi(a[v]);if(N1(a[v])){u(a[v],l[l.length-1]||[],f);var y=a[v+1]?yi(a[v+1]):null;if(p!==y){if(f.length){c=h,f.sort(function(D,A){return D.value-A.value});for(var d=[],g=0;g<f.length;++g){var m=f[g].value;(g===0||f[g-1].value!==m)&&(d.push(f[g]),m>=i[0]&&m<=i[1]&&h++)}var _=(i[1]-i[0])/t;if(h>_*1.5&&c>_/1.5||(l.push(d),h>_||r===a[v]))break}f=[]}}}for(var S=wt(z(l,function(D){return wt(D,function(A){return A.value>=i[0]&&A.value<=i[1]&&!A.notAdd})}),function(D){return D.length>0}),b=[],w=S.length-1,v=0;v<S.length;++v)for(var x=S[v],T=0;T<x.length;++T)b.push({value:x[T].value,level:w-v});b.sort(function(D,A){return D.value-A.value});for(var C=[],v=0;v<b.length;++v)(v===0||b[v].value!==b[v-1].value)&&C.push(b[v]);return C}Le.registerClass(Cg);var Qv=Le.prototype,gn=Pi.prototype,Qx=pt,Jx=Math.floor,jx=Math.ceil,pa=Math.pow,ae=Math.log,rf=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Pi,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=gn.getTicks.call(this,e);return z(o,function(s){var u=s.value,l=pt(pa(this.base,u));return l=u===n[0]&&this._fixMin?da(l,a[0]):l,l=u===n[1]&&this._fixMax?da(l,a[1]):l,{value:l}},this)},t.prototype.setExtent=function(e,i){var n=ae(this.base);e=ae(Math.max(0,e))/n,i=ae(Math.max(0,i))/n,gn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=Qv.getExtent.call(this);i[0]=pa(e,i[0]),i[1]=pa(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=da(i[0],a[0])),this._fixMax&&(i[1]=da(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=ae(e[0])/ae(i),e[1]=ae(e[1])/ae(i),Qv.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=Jm(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[pt(jx(i[0]/a)*a),pt(Jx(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){gn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=ae(e)/ae(this.base),Po(e,this._extent)},t.prototype.normalize=function(e){return e=ae(e)/ae(this.base),Io(e,this._extent)},t.prototype.scale=function(e){return e=Eo(e,this._extent),pa(this.base,e)},t.type="log",t}(Le),Dg=rf.prototype;Dg.getMinorTicks=gn.getMinorTicks;Dg.getLabel=gn.getLabel;function da(r,t){return Qx(r,ke(t))}Le.registerClass(rf);var tT=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;Y(o)?this._modelMinNum=ga(t,o({min:i[0],max:i[1]})):o!=="dataMin"&&(this._modelMinNum=ga(t,o));var s=this._modelMaxRaw=e.get("max",!0);if(Y(s)?this._modelMaxNum=ga(t,s({min:i[0],max:i[1]})):s!=="dataMax"&&(this._modelMaxNum=ga(t,s)),n)this._axisDataLen=e.getCategories().length;else{var u=e.get("boundaryGap"),l=F(u)?u:[u||0,u||0];typeof l[0]=="boolean"||typeof l[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Hr(l[0],1),Hr(l[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,u=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,l=s!=null,f=u!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),u==null&&(u=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(u==null||!isFinite(u))&&(u=NaN);var h=ka(s)||ka(u)||t&&!n;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!f&&(u=0));var c=this._determinedMin,v=this._determinedMax;return c!=null&&(s=c,l=!0),v!=null&&(u=v,f=!0),{min:s,max:u,minFixed:l,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[rT[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=eT[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),eT={min:"_determinedMin",max:"_determinedMax"},rT={min:"_dataMin",max:"_dataMax"};function iT(r,t,e){var i=r.rawExtentInfo;return i||(i=new tT(r,t,e),r.rawExtentInfo=i,i)}function ga(r,t){return t==null?null:ka(t)?NaN:r.parse(t)}function Mg(r,t){var e=r.type,i=iT(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=Id("bar",o),u=!1;if(M(s,function(h){u=u||h.getBaseAxis()===t.axis}),u){var l=Ed(s),f=nT(n,a,t,l);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function nT(r,t,e,i){var n=e.axis.getExtent(),a=Math.abs(n[1]-n[0]),o=gw(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;M(o,function(v){s=Math.min(v.offset,s)});var u=-1/0;M(o,function(v){u=Math.max(v.offset+v.width,u)}),s=Math.abs(s),u=Math.abs(u);var l=s+u,f=t-r,h=1-(s+u)/a,c=f/h-f;return t+=c*(u/l),r-=c*(s/l),{min:r,max:t}}function Jv(r,t){var e=t,i=Mg(r,e),n=i.extent,a=e.get("splitNumber");r instanceof rf&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),u=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:u?e.get("minInterval"):null,maxInterval:u?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function aT(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new ef({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new Cg({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(Le.getClass(t)||Pi)}}function oT(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function Ii(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):V(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):Y(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(Ag(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function Ag(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function sT(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof ef?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=Ii(r),u,l=1;n>40&&(l=Math.ceil(n/40));for(var f=0;f<n;f+=l){var h=i?i[f]:{value:a[0]+f},c=s(h,f),v=o.getTextRect(c),p=uT(v,o.get("rotate")||0);u?u.union(p):u=p}return u}}function uT(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new j(r.x,r.y,a,o);return s}function nf(r){var t=r.get("interval");return t??"auto"}function Lg(r){return r.type==="category"&&nf(r.getLabelModel())===0}function Pg(r,t){var e={};return M(r.mapDimensionsAll(t),function(i){e[y1(r,i)]=!0}),lt(e)}function nD(r,t,e){t&&M(Pg(t,e),function(i){var n=t.getApproximateExtent(i);n[0]<r[0]&&(r[0]=n[0]),n[1]>r[1]&&(r[1]=n[1])})}var lT=function(){function r(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return r.prototype.getAxis=function(t){return this._axes[t]},r.prototype.getAxes=function(){return z(this._dimList,function(t){return this._axes[t]},this)},r.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),wt(this.getAxes(),function(e){return e.scale.type===t})},r.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},r}(),Uu=["x","y"];function jv(r){return r.type==="interval"||r.type==="time"}var fT=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=Uu,e}return t.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var e=this.getAxis("x").scale,i=this.getAxis("y").scale;if(!(!jv(e)||!jv(i))){var n=e.getExtent(),a=i.getExtent(),o=this.dataToPoint([n[0],a[0]]),s=this.dataToPoint([n[1],a[1]]),u=n[1]-n[0],l=a[1]-a[0];if(!(!u||!l)){var f=(s[0]-o[0])/u,h=(s[1]-o[1])/l,c=o[0]-n[0]*f,v=o[1]-a[0]*h,p=this._transform=[f,0,0,h,c,v];this._invTransform=ol([],p)}}},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},t.prototype.containPoint=function(e){var i=this.getAxis("x"),n=this.getAxis("y");return i.contain(i.toLocalCoord(e[0]))&&n.contain(n.toLocalCoord(e[1]))},t.prototype.containData=function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])},t.prototype.containZone=function(e,i){var n=this.dataToPoint(e),a=this.dataToPoint(i),o=this.getArea(),s=new j(n[0],n[1],a[0]-n[0],a[1]-n[1]);return o.intersect(s)},t.prototype.dataToPoint=function(e,i,n){n=n||[];var a=e[0],o=e[1];if(this._transform&&a!=null&&isFinite(a)&&o!=null&&isFinite(o))return ee(n,e,this._transform);var s=this.getAxis("x"),u=this.getAxis("y");return n[0]=s.toGlobalCoord(s.dataToCoord(a,i)),n[1]=u.toGlobalCoord(u.dataToCoord(o,i)),n},t.prototype.clampData=function(e,i){var n=this.getAxis("x").scale,a=this.getAxis("y").scale,o=n.getExtent(),s=a.getExtent(),u=n.parse(e[0]),l=a.parse(e[1]);return i=i||[],i[0]=Math.min(Math.max(Math.min(o[0],o[1]),u),Math.max(o[0],o[1])),i[1]=Math.min(Math.max(Math.min(s[0],s[1]),l),Math.max(s[0],s[1])),i},t.prototype.pointToData=function(e,i){var n=[];if(this._invTransform)return ee(n,e,this._invTransform);var a=this.getAxis("x"),o=this.getAxis("y");return n[0]=a.coordToData(a.toLocalCoord(e[0]),i),n[1]=o.coordToData(o.toLocalCoord(e[1]),i),n},t.prototype.getOtherAxis=function(e){return this.getAxis(e.dim==="x"?"y":"x")},t.prototype.getArea=function(e){e=e||0;var i=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),a=Math.min(i[0],i[1])-e,o=Math.min(n[0],n[1])-e,s=Math.max(i[0],i[1])-a+e,u=Math.max(n[0],n[1])-o+e;return new j(a,o,s,u)},t}(lT),Mn=dt();function Ig(r,t){var e=z(t,function(i){return r.scale.parse(i)});return r.type==="time"&&e.length>0&&(e.sort(),e.unshift(e[0]),e.push(e[e.length-1])),e}function hT(r){var t=r.getLabelModel().get("customValues");if(t){var e=Ii(r),i=r.scale.getExtent(),n=Ig(r,t),a=wt(n,function(o){return o>=i[0]&&o<=i[1]});return{labels:z(a,function(o){var s={value:o};return{formattedLabel:e(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?cT(r):dT(r)}function vT(r,t){var e=r.getTickModel().get("customValues");if(e){var i=r.scale.getExtent(),n=Ig(r,e);return{ticks:wt(n,function(a){return a>=i[0]&&a<=i[1]})}}return r.type==="category"?pT(r,t):{ticks:z(r.scale.getTicks(),function(a){return a.value})}}function cT(r){var t=r.getLabelModel(),e=Eg(r,t);return!t.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e}function Eg(r,t){var e=Rg(r,"labels"),i=nf(t),n=kg(e,i);if(n)return n;var a,o;return Y(i)?a=Ng(r,i):(o=i==="auto"?gT(r):i,a=Bg(r,o)),Og(e,i,{labels:a,labelCategoryInterval:o})}function pT(r,t){var e=Rg(r,"ticks"),i=nf(t),n=kg(e,i);if(n)return n;var a,o;if((!t.get("show")||r.scale.isBlank())&&(a=[]),Y(i))a=Ng(r,i,!0);else if(i==="auto"){var s=Eg(r,r.getLabelModel());o=s.labelCategoryInterval,a=z(s.labels,function(u){return u.tickValue})}else o=i,a=Bg(r,o,!0);return Og(e,i,{ticks:a,tickCategoryInterval:o})}function dT(r){var t=r.scale.getTicks(),e=Ii(r);return{labels:z(t,function(i,n){return{level:i.level,formattedLabel:e(i,n),rawLabel:r.scale.getLabel(i),tickValue:i.value}})}}function Rg(r,t){return Mn(r)[t]||(Mn(r)[t]=[])}function kg(r,t){for(var e=0;e<r.length;e++)if(r[e].key===t)return r[e].value}function Og(r,t,e){return r.push({key:t,value:e}),e}function gT(r){var t=Mn(r).autoInterval;return t??(Mn(r).autoInterval=r.calculateCategoryInterval())}function yT(r){var t=mT(r),e=Ii(r),i=(t.axisRotate-t.labelRotate)/180*Math.PI,n=r.scale,a=n.getExtent(),o=n.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var u=a[0],l=r.dataToCoord(u+1)-r.dataToCoord(u),f=Math.abs(l*Math.cos(i)),h=Math.abs(l*Math.sin(i)),c=0,v=0;u<=a[1];u+=s){var p=0,y=0,d=sl(e({value:u}),t.font,"center","top");p=d.width*1.3,y=d.height*1.3,c=Math.max(c,p,7),v=Math.max(v,y,7)}var g=c/f,m=v/h;isNaN(g)&&(g=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(g,m))),S=Mn(r.model),b=r.getExtent(),w=S.lastAutoInterval,x=S.lastTickCount;return w!=null&&x!=null&&Math.abs(w-_)<=1&&Math.abs(x-o)<=1&&w>_&&S.axisExtent0===b[0]&&S.axisExtent1===b[1]?_=w:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=b[0],S.axisExtent1=b[1]),_}function mT(r){var t=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:t.get("rotate")||0,font:t.getFont()}}function Bg(r,t,e){var i=Ii(r),n=r.scale,a=n.getExtent(),o=r.getLabelModel(),s=[],u=Math.max((t||0)+1,1),l=a[0],f=n.count();l!==0&&u>1&&f/u>2&&(l=Math.round(Math.ceil(l/u)*u));var h=Lg(r),c=o.get("showMinLabel")||h,v=o.get("showMaxLabel")||h;c&&l!==a[0]&&y(a[0]);for(var p=l;p<=a[1];p+=u)y(p);v&&p-u!==a[1]&&y(a[1]);function y(d){var g={value:d};s.push(e?d:{formattedLabel:i(g),rawLabel:n.getLabel(g),tickValue:d})}return s}function Ng(r,t,e){var i=r.scale,n=Ii(r),a=[];return M(i.getTicks(),function(o){var s=i.getLabel(o),u=o.value;t(o.value,s)&&a.push(e?u:{formattedLabel:n(o),rawLabel:s,tickValue:u})}),a}var tc=[0,1],_T=function(){function r(t,e,i){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=i||[0,0]}return r.prototype.contain=function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&t<=n},r.prototype.containData=function(t){return this.scale.contain(t)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(t){return qm(t||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(t,e){var i=this._extent;i[0]=t,i[1]=e},r.prototype.dataToCoord=function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&n.type==="ordinal"&&(i=i.slice(),ec(i,n.count())),Hf(t,tc,i,e)},r.prototype.coordToData=function(t,e){var i=this._extent,n=this.scale;this.onBand&&n.type==="ordinal"&&(i=i.slice(),ec(i,n.count()));var a=Hf(t,i,tc,e);return this.scale.scale(a)},r.prototype.pointToData=function(t,e){},r.prototype.getTicksCoords=function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=vT(this,e),n=i.ticks,a=z(n,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=e.get("alignWithLabel");return ST(this,a,o,t.clamp),a},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var i=this.scale.getMinorTicks(e),n=z(i,function(a){return z(a,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return n},r.prototype.getViewLabels=function(){return hT(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);i===0&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},r.prototype.calculateCategoryInterval=function(){return yT(this)},r}();function ec(r,t){var e=r[1]-r[0],i=t,n=e/i/2;r[0]+=n,r[1]-=n}function ST(r,t,e,i){var n=t.length;if(!r.onBand||e||!n)return;var a=r.getExtent(),o,s;if(n===1)t[0].coord=a[0],o=t[1]={coord:a[1],tickValue:t[0].tickValue};else{var u=t[n-1].tickValue-t[0].tickValue,l=(t[n-1].coord-t[0].coord)/u;M(t,function(v){v.coord-=l/2});var f=r.scale.getExtent();s=1+f[1]-t[n-1].tickValue,o={coord:t[n-1].coord+l*s,tickValue:f[1]+1},t.push(o)}var h=a[0]>a[1];c(t[0].coord,a[0])&&(i?t[0].coord=a[0]:t.shift()),i&&c(a[0],t[0].coord)&&t.unshift({coord:a[0]}),c(a[1],o.coord)&&(i?o.coord=a[1]:t.pop()),i&&c(o.coord,a[1])&&t.push({coord:a[1]});function c(v,p){return v=pt(v),p=pt(p),h?v>p:v<p}}var wT=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,e,i,n)||this;return s.index=0,s.type=a||"value",s.position=o||"bottom",s}return t.prototype.isHorizontal=function(){var e=this.position;return e==="top"||e==="bottom"},t.prototype.getGlobalExtent=function(e){var i=this.getExtent();return i[0]=this.toGlobalCoord(i[0]),i[1]=this.toGlobalCoord(i[1]),e&&i[0]>i[1]&&i.reverse(),i},t.prototype.pointToData=function(e,i){return this.coordToData(this.toLocalCoord(e[this.dim==="x"?0:1]),i)},t.prototype.setCategorySortInfo=function(e){if(this.type!=="category")return!1;this.model.option.categorySortInfo=e,this.scale.setSortInfo(e)},t}(_T);function Yu(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,u=o?"onZero":s,l=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],c={left:0,right:1,top:0,bottom:1,onZero:2},v=t.get("offset")||0,p=l==="x"?[h[2]-v,h[3]+v]:[h[0]-v,h[1]+v];if(o){var y=o.toGlobalCoord(o.dataToCoord(0));p[c.onZero]=Math.max(Math.min(y,p[1]),p[0])}a.position=[l==="y"?p[c[u]]:h[0],l==="x"?p[c[u]]:h[3]],a.rotation=Math.PI/2*(l==="x"?0:1);var d={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=d[s],a.labelOffset=o?p[c[s]]-p[c.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),yn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var g=t.get(["axisLabel","rotate"]);return a.labelRotate=u==="top"?-g:g,a.z2=1,a}function rc(r){return r.get("coordinateSystem")==="cartesian2d"}function ic(r){var t={xAxisModel:null,yAxisModel:null};return M(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,fe).models[0];t[i]=a}),t}var Gs=Math.log;function bT(r,t,e){var i=Pi.prototype,n=i.getTicks.call(e),a=i.getTicks.call(e,!0),o=n.length-1,s=i.getInterval.call(e),u=Mg(r,t),l=u.extent,f=u.fixMin,h=u.fixMax;if(r.type==="log"){var c=Gs(r.base);l=[Gs(l[0])/c,Gs(l[1])/c]}r.setExtent(l[0],l[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:h});var v=i.getExtent.call(r);f&&(l[0]=v[0]),h&&(l[1]=v[1]);var p=i.getInterval.call(r),y=l[0],d=l[1];if(f&&h)p=(d-y)/o;else if(f)for(d=l[0]+p*o;d<l[1]&&isFinite(d)&&isFinite(l[1]);)p=zs(p),d=l[0]+p*o;else if(h)for(y=l[1]-p*o;y>l[0]&&isFinite(y)&&isFinite(l[0]);)p=zs(p),y=l[1]-p*o;else{var g=r.getTicks().length-1;g>o&&(p=zs(p));var m=p*o;d=Math.ceil(l[1]/p)*p,y=pt(d-m),y<0&&l[0]>=0?(y=0,d=pt(m)):d>0&&l[1]<=0&&(d=0,y=-pt(m))}var _=(n[0].value-a[0].value)/s,S=(n[o].value-a[o].value)/s;i.setExtent.call(r,y+p*_,d+p*S),i.setInterval.call(r,p),(_||S)&&i.setNiceExtent.call(r,y+p,d-p)}var xT=function(){function r(t,e,i){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Uu,this._initCartesian(t,e,i),this.model=t}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model);function n(o){var s,u=lt(o),l=u.length;if(l){for(var f=[],h=l-1;h>=0;h--){var c=+u[h],v=o[c],p=v.model,y=v.scale;Wu(y)&&p.get("alignTicks")&&p.get("interval")==null?f.push(v):(Jv(y,p),Wu(y)&&(s=v))}f.length&&(s||(s=f.pop(),Jv(s.scale,s.model)),M(f,function(d){bT(d.scale,d.model,s.scale)}))}}n(i.x),n(i.y);var a={};M(i.x,function(o){nc(i,"y",o,a)}),M(i.y,function(o){nc(i,"x",o,a)}),this.resize(this.model,e)},r.prototype.resize=function(t,e,i){var n=t.getBoxLayoutParams(),a=!i&&t.get("containLabel"),o=bi(n,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var s=this._axesList;u(),a&&(M(s,function(l){if(!l.model.get(["axisLabel","inside"])){var f=sT(l);if(f){var h=l.isHorizontal()?"height":"width",c=l.model.get(["axisLabel","margin"]);o[h]-=f[h]+c,l.position==="top"?o.y+=f.height+c:l.position==="left"&&(o.x+=f.width+c)}}}),u()),M(this._coordsList,function(l){l.calcAffineTransform()});function u(){M(s,function(l){var f=l.isHorizontal(),h=f?[0,o.width]:[0,o.height],c=l.inverse?1:0;l.setExtent(h[c],h[1-c]),TT(l,f?o.x:o.y)})}},r.prototype.getAxis=function(t,e){var i=this._axesMap[t];if(i!=null)return i[e||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(t,e){if(t!=null&&e!=null){var i="x"+t+"y"+e;return this._coordsMap[i]}G(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,a=this._coordsList;n<a.length;n++)if(a[n].getAxis("x").index===t||a[n].getAxis("y").index===e)return a[n]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},r.prototype.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},r.prototype._findConvertTarget=function(t){var e=t.seriesModel,i=t.xAxisModel||e&&e.getReferringComponents("xAxis",fe).models[0],n=t.yAxisModel||e&&e.getReferringComponents("yAxis",fe).models[0],a=t.gridModel,o=this._coordsList,s,u;if(e)s=e.coordinateSystem,ot(o,s)<0&&(s=null);else if(i&&n)s=this.getCartesian(i.componentIndex,n.componentIndex);else if(i)u=this.getAxis("x",i.componentIndex);else if(n)u=this.getAxis("y",n.componentIndex);else if(a){var l=a.coordinateSystem;l===this&&(s=this._coordsList[0])}return{cartesian:s,axis:u}},r.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},r.prototype._initCartesian=function(t,e,i){var n=this,a=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},u={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!u.x||!u.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,M(s.x,function(f,h){M(s.y,function(c,v){var p="x"+h+"y"+v,y=new fT(p);y.master=n,y.model=t,n._coordsMap[p]=y,n._coordsList.push(y),y.addAxis(f),y.addAxis(c)})});function l(f){return function(h,c){if(Hs(h,t)){var v=h.get("position");f==="x"?v!=="top"&&v!=="bottom"&&(v=o.bottom?"top":"bottom"):v!=="left"&&v!=="right"&&(v=o.left?"right":"left"),o[v]=!0;var p=new wT(f,aT(h),[0,0],h.get("type"),v),y=p.type==="category";p.onBand=y&&h.get("boundaryGap"),p.inverse=h.get("inverse"),h.axis=p,p.model=h,p.grid=a,p.index=c,a._axesList.push(p),s[f][c]=p,u[f]++}}}},r.prototype._updateScale=function(t,e){M(this._axesList,function(n){if(n.scale.setExtent(1/0,-1/0),n.type==="category"){var a=n.model.get("categorySortInfo");n.scale.setSortInfo(a)}}),t.eachSeries(function(n){if(rc(n)){var a=ic(n),o=a.xAxisModel,s=a.yAxisModel;if(!Hs(o,e)||!Hs(s,e))return;var u=this.getCartesian(o.componentIndex,s.componentIndex),l=n.getData(),f=u.getAxis("x"),h=u.getAxis("y");i(l,f),i(l,h)}},this);function i(n,a){M(Pg(n,a.dim),function(o){a.scale.unionExtentFromData(n,o)})}},r.prototype.getTooltipAxes=function(t){var e=[],i=[];return M(this.getCartesians(),function(n){var a=t!=null&&t!=="auto"?n.getAxis(t):n.getBaseAxis(),o=n.getOtherAxis(a);ot(e,a)<0&&e.push(a),ot(i,o)<0&&i.push(o)}),{baseAxes:e,otherAxes:i}},r.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,a){var o=new r(n,t,e);o.name="grid_"+a,o.resize(n,e,!0),n.coordinateSystem=o,i.push(o)}),t.eachSeries(function(n){if(rc(n)){var a=ic(n),o=a.xAxisModel,s=a.yAxisModel,u=o.getCoordSysModel(),l=u.coordinateSystem;n.coordinateSystem=l.getCartesian(o.componentIndex,s.componentIndex)}}),i},r.dimensions=Uu,r}();function Hs(r,t){return r.getCoordSysModel()===t}function nc(r,t,e,i){e.getAxesOnZeroOf=function(){return a?[a]:[]};var n=r[t],a,o=e.model,s=o.get(["axisLine","onZero"]),u=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(u!=null)ac(n[u])&&(a=n[u]);else for(var l in n)if(n.hasOwnProperty(l)&&ac(n[l])&&!i[f(n[l])]){a=n[l];break}a&&(i[f(a)]=!0);function f(h){return h.dim+"_"+h.index}}function ac(r){return r&&r.type!=="category"&&r.type!=="time"&&oT(r)}function TT(r,t){var e=r.getExtent(),i=e[0]+e[1];r.toGlobalCoord=r.dim==="x"?function(n){return n+t}:function(n){return i-n+t},r.toLocalCoord=r.dim==="x"?function(n){return n-t}:function(n){return i-n+t}}var Qe=Math.PI,er=function(){function r(t,e){this.group=new At,this.opt=e,this.axisModel=t,rt(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new At({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!oc[t]},r.prototype.add=function(t){oc[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=np(e-t),a,o;return Va(n)?(o=i>0?"top":"bottom",a="center"):Va(n-Qe)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<Qe?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),oc={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],u=[a[1],0],l=s[0]>u[0];o&&(ee(s,s,o),ee(u,u,o));var f=k({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new ir({shape:{x1:s[0],y1:s[1],x2:u[0],y2:u[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});wn(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var c=t.get(["axisLine","symbol"]);if(c!=null){var v=t.get(["axisLine","symbolSize"]);V(c)&&(c=[c,c]),(V(v)||ft(v))&&(v=[v,v]);var p=Sd(t.get(["axisLine","symbolOffset"])||0,v),y=v[0],d=v[1];M([{rotate:r.rotation+Math.PI/2,offset:p[0],r:0},{rotate:r.rotation-Math.PI/2,offset:p[1],r:Math.sqrt((s[0]-u[0])*(s[0]-u[0])+(s[1]-u[1])*(s[1]-u[1]))}],function(g,m){if(c[m]!=="none"&&c[m]!=null){var _=Ti(c[m],-y/2,-d/2,y,d,f.stroke,!0),S=g.r+g.offset,b=l?u:s;_.attr({rotation:g.rotate,x:b[0]+S*Math.cos(r.rotation),y:b[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),e.add(_)}})}}},axisTickLabel:function(r,t,e,i){var n=MT(e,i,t,r),a=LT(e,i,t,r);if(DT(t,a,n),AT(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=ww(z(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));xw(o)}},axisName:function(r,t,e,i){var n=yn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),u=t.get("nameGap")||0,l=t.axis.getExtent(),f=l[0]>l[1]?-1:1,h=[a==="start"?l[0]-f*u:a==="end"?l[1]+f*u:(l[0]+l[1])/2,uc(a)?r.labelOffset+o*u:0],c,v=t.get("nameRotate");v!=null&&(v=v*Qe/180);var p;uc(a)?c=er.innerTextLayout(r.rotation,v??r.rotation,o):(c=CT(r.rotation,a,v||0,l),p=r.axisNameAvailableWidth,p!=null&&(p=Math.abs(p/Math.sin(c.rotation)),!isFinite(p)&&(p=null)));var y=s.getFont(),d=t.get("nameTruncate",!0)||{},g=d.ellipsis,m=yn(r.nameTruncateMaxWidth,d.maxWidth,p),_=new Et({x:h[0],y:h[1],rotation:c.rotation,silent:er.isLabelSilent(t),style:ar(s,{text:n,font:y,overflow:"truncate",width:m,ellipsis:g,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||c.textAlign,verticalAlign:s.get("verticalAlign")||c.textVerticalAlign}),z2:1});if(_o({el:_,componentModel:t,itemName:n}),_.__fullText=n,_.anid="name",t.get("triggerEvent")){var S=er.makeAxisEventDataBase(t);S.targetType="axisName",S.name=n,vt(_).eventData=S}i.add(_),_.updateTransform(),e.add(_),_.decomposeTransform()}}};function CT(r,t,e,i){var n=np(e-r),a,o,s=i[0]>i[1],u=t==="start"&&!s||t!=="start"&&s;return Va(n-Qe/2)?(o=u?"bottom":"top",a="center"):Va(n-Qe*1.5)?(o=u?"top":"bottom",a="center"):(o="middle",n<Qe*1.5&&n>Qe/2?a=u?"left":"right":a=u?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function DT(r,t,e){if(!Lg(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],u=t[t.length-2],l=e[0],f=e[1],h=e[e.length-1],c=e[e.length-2];i===!1?(Xt(a),Xt(l)):sc(a,o)&&(i?(Xt(o),Xt(f)):(Xt(a),Xt(l))),n===!1?(Xt(s),Xt(h)):sc(u,s)&&(n?(Xt(u),Xt(c)):(Xt(s),Xt(h)))}}function Xt(r){r&&(r.ignore=!0)}function sc(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=nl([]);return al(n,n,-r.rotation),e.applyTransform(ci([],n,r.getLocalTransform())),i.applyTransform(ci([],n,t.getLocalTransform())),e.intersect(i)}}function uc(r){return r==="middle"||r==="center"}function Fg(r,t,e,i,n){for(var a=[],o=[],s=[],u=0;u<r.length;u++){var l=r[u].coord;o[0]=l,o[1]=0,s[0]=l,s[1]=e,t&&(ee(o,o,t),ee(s,s,t));var f=new ir({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});wn(f.shape,f.style.lineWidth),f.anid=n+"_"+r[u].tickValue,a.push(f)}return a}function MT(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),u=i.tickDirection*a.get("length"),l=n.getTicksCoords(),f=Fg(l,t.transform,u,rt(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function AT(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),u=i*a.get("length"),l=rt(s.getLineStyle(),rt(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=Fg(o[f],t.transform,u,l,"minorticks_"+f),c=0;c<h.length;c++)r.add(h[c])}}function LT(r,t,e,i){var n=e.axis,a=yn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),u=n.getViewLabels(),l=(yn(i.labelRotate,o.get("rotate"))||0)*Qe/180,f=er.innerTextLayout(i.rotation,l,i.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],v=er.isLabelSilent(e),p=e.get("triggerEvent");return M(u,function(y,d){var g=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(y.tickValue):y.tickValue,m=y.formattedLabel,_=y.rawLabel,S=o;if(h&&h[g]){var b=h[g];G(b)&&b.textStyle&&(S=new mt(b.textStyle,o,e.ecModel))}var w=S.getTextColor()||e.get(["axisLine","lineStyle","color"]),x=n.dataToCoord(g),T=S.getShallow("align",!0)||f.textAlign,C=$(S.getShallow("alignMinLabel",!0),T),D=$(S.getShallow("alignMaxLabel",!0),T),A=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,L=$(S.getShallow("verticalAlignMinLabel",!0),A),I=$(S.getShallow("verticalAlignMaxLabel",!0),A),P=new Et({x,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(y.level||0),style:ar(S,{text:m,align:d===0?C:d===u.length-1?D:T,verticalAlign:d===0?L:d===u.length-1?I:A,fill:Y(w)?w(n.type==="category"?_:n.type==="value"?g+"":g,d):w})});if(P.anid="label_"+g,_o({el:P,componentModel:e,itemName:m,formatterParamsExtra:{isTruncated:function(){return P.isTruncated},value:_,tickIndex:d}}),p){var E=er.makeAxisEventDataBase(e);E.targetType="axisLabel",E.value=_,E.tickIndex=d,n.type==="category"&&(E.dataIndex=g),vt(P).eventData=E}t.add(P),P.updateTransform(),c.push(P),r.add(P),P.decomposeTransform()}),c}}function PT(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return IT(e,r,t),e.seriesInvolved&&RT(e,r),e}function IT(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];M(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var u=An(s.model),l=r.coordSysAxesInfo[u]={};r.coordSysMap[u]=s;var f=s.model,h=f.getModel("tooltip",i);if(M(s.getAxes(),yt(y,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var c=h.get("trigger")==="axis",v=h.get(["axisPointer","type"])==="cross",p=s.getTooltipAxes(h.get(["axisPointer","axis"]));(c||v)&&M(p.baseAxes,yt(y,v?"cross":!0,c)),v&&M(p.otherAxes,yt(y,"cross",!1))}function y(d,g,m){var _=m.model.getModel("axisPointer",n),S=_.get("show");if(!(!S||S==="auto"&&!d&&!Xu(_))){g==null&&(g=_.get("triggerTooltip")),_=d?ET(m,h,n,t,d,g):_;var b=_.get("snap"),w=_.get("triggerEmphasis"),x=An(m.model),T=g||b||m.type==="category",C=r.axesInfo[x]={key:x,axis:m,coordSys:s,axisPointerModel:_,triggerTooltip:g,triggerEmphasis:w,involveSeries:T,snap:b,useHandle:Xu(_),seriesModels:[],linkGroup:null};l[x]=C,r.seriesInvolved=r.seriesInvolved||T;var D=kT(a,m);if(D!=null){var A=o[D]||(o[D]={axesInfo:{}});A.axesInfo[x]=C,A.mapper=a[D].mapper,C.linkGroup=A}}}})}function ET(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],u={};M(s,function(c){u[c]=J(o.get(c))}),u.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(u.type="line");var l=u.label||(u.label={});if(l.show==null&&(l.show=!1),n==="cross"){var f=o.get(["label","show"]);if(l.show=f??!0,!a){var h=u.lineStyle=o.get("crossStyle");h&&rt(l,h.textStyle)}}return r.model.getModel("axisPointer",new mt(u,e,i))}function RT(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||M(r.coordSysAxesInfo[An(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function kT(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(Vs(a[i+"AxisId"],e.id)||Vs(a[i+"AxisIndex"],e.componentIndex)||Vs(a[i+"AxisName"],e.name))return n}}function Vs(r,t){return r==="all"||F(r)&&ot(r,t)>=0||r===t}function OT(r){var t=af(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=Xu(e);a==null&&(n.status=s?"show":"hide");var u=i.getExtent().slice();u[0]>u[1]&&u.reverse(),(o==null||o>u[1])&&(o=u[1]),o<u[0]&&(o=u[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function af(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[An(r)]}function BT(r){var t=af(r);return t&&t.axisPointerModel}function Xu(r){return!!r.get(["handle","show"])}function An(r){return r.type+"||"+r.id}var lc={},zg=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&OT(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=BT(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){lc[e]=i},t.getAxisPointerClass=function(e){return e&&lc[e]},t.type="axis",t}(Ce),$u=dt();function NT(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),u=i.coordinateSystem.getRect(),l=n.getTicksCoords({tickModel:a,clamp:!0});if(l.length){var f=s.length,h=$u(r).splitAreaColors,c=X(),v=0;if(h)for(var p=0;p<l.length;p++){var y=h.get(l[p].tickValue);if(y!=null){v=(y+(f-1)*p)%f;break}}var d=n.toGlobalCoord(l[0].coord),g=o.getAreaStyle();s=F(s)?s:[s];for(var p=1;p<l.length;p++){var m=n.toGlobalCoord(l[p].coord),_=void 0,S=void 0,b=void 0,w=void 0;n.isHorizontal()?(_=d,S=u.y,b=m-_,w=u.height,d=_+b):(_=u.x,S=d,b=u.width,w=m-S,d=S+w);var x=l[p-1].tickValue;x!=null&&c.set(x,v),t.add(new xt({anid:x!=null?"area_"+x:null,shape:{x:_,y:S,width:b,height:w},style:rt({fill:s[v]},g),autoBatch:!0,silent:!0})),v=(v+1)%f}$u(r).splitAreaColors=c}}}function FT(r){$u(r).splitAreaColors=null}var zT=["axisLine","axisTickLabel","axisName"],GT=["splitArea","splitLine","minorSplitLine"],Gg=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.axisPointerClass="CartesianAxisPointer",e}return t.prototype.render=function(e,i,n,a){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new At,this.group.add(this._axisGroup),!!e.get("show")){var s=e.getCoordSysModel(),u=Yu(s,e),l=new er(e,k({handleAutoShown:function(h){for(var c=s.coordinateSystem.getCartesians(),v=0;v<c.length;v++)if(Wu(c[v].getOtherAxis(e.axis).scale))return!0;return!1}},u));M(zT,l.add,l),this._axisGroup.add(l.getGroup()),M(GT,function(h){e.get([h,"show"])&&HT[h](this,this._axisGroup,e,s)},this);var f=a&&a.type==="changeAxisOrder"&&a.isInitSort;f||Rp(o,this._axisGroup,e),r.prototype.render.call(this,e,i,n,a)}},t.prototype.remove=function(){FT(this)},t.type="cartesianAxis",t}(zg),HT={splitLine:function(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color"),u=a.get("showMinLine")!==!1,l=a.get("showMaxLine")!==!1;s=F(s)?s:[s];for(var f=i.coordinateSystem.getRect(),h=n.isHorizontal(),c=0,v=n.getTicksCoords({tickModel:a}),p=[],y=[],d=o.getLineStyle(),g=0;g<v.length;g++){var m=n.toGlobalCoord(v[g].coord);if(!(g===0&&!u||g===v.length-1&&!l)){var _=v[g].tickValue;h?(p[0]=m,p[1]=f.y,y[0]=m,y[1]=f.y+f.height):(p[0]=f.x,p[1]=m,y[0]=f.x+f.width,y[1]=m);var S=c++%s.length,b=new ir({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:p[0],y1:p[1],x2:y[0],y2:y[1]},style:rt({stroke:s[S]},d),silent:!0});wn(b.shape,d.lineWidth),t.add(b)}}}},minorSplitLine:function(r,t,e,i){var n=e.axis,a=e.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=i.coordinateSystem.getRect(),u=n.isHorizontal(),l=n.getMinorTicksCoords();if(l.length)for(var f=[],h=[],c=o.getLineStyle(),v=0;v<l.length;v++)for(var p=0;p<l[v].length;p++){var y=n.toGlobalCoord(l[v][p].coord);u?(f[0]=y,f[1]=s.y,h[0]=y,h[1]=s.y+s.height):(f[0]=s.x,f[1]=y,h[0]=s.x+s.width,h[1]=y);var d=new ir({anid:"minor_line_"+l[v][p].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:h[0],y2:h[1]},style:c,silent:!0});wn(d.shape,c.lineWidth),t.add(d)}},splitArea:function(r,t,e,i){NT(r,t,e,i)}},Hg=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="xAxis",t}(Gg),VT=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=Hg.type,e}return t.type="yAxis",t}(Gg),WT=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="grid",e}return t.prototype.render=function(e,i){this.group.removeAll(),e.get("show")&&this.group.add(new xt({shape:e.coordinateSystem.getRect(),style:rt({fill:e.get("backgroundColor")},e.getItemStyle()),silent:!0,z2:-1}))},t.type="grid",t}(Ce),fc={offset:0};function UT(r){r.registerComponentView(WT),r.registerComponentModel(Ex),r.registerCoordinateSystem("cartesian2d",xT),$v(r,"x",Hu,fc),$v(r,"y",Hu,fc),r.registerComponentView(Hg),r.registerComponentView(VT),r.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}var Er=dt(),hc=J,Ws=gt,YT=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,u=this._handle;if(!o||o==="hide"){s&&s.hide(),u&&u.hide();return}s&&s.show(),u&&u.show();var l={};this.makeElOption(l,a,t,e,i);var f=l.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new At,this.createPointerEl(s,l,t,e),this.createLabelEl(s,l,t,e),i.getZr().add(s);else{var c=yt(vc,e,h);this.updatePointerEl(s,l,c),this.updateLabelEl(s,l,c,e)}pc(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var u=af(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/u>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Er(t).pointerEl=new D_[a.type](hc(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Er(t).labelEl=new Et(hc(e.label));t.add(a),cc(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Er(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Er(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),cc(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=Cl(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(l){Bd(l.event)},onmousedown:Ws(this._onHandleDragMove,this,0,0),drift:Ws(this._onHandleDragMove,this),ondragend:Ws(this._onHandleDragEnd,this)}),i.add(n)),pc(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var u=a.get("size");F(u)||(u=[u,u]),n.scaleX=u[0]/2,n.scaleY=u[1]/2,mw(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){vc(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Us(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Us(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Us(n)),Er(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),_w(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function vc(r,t,e,i){Vg(Er(e).lastProp,i)||(Er(e).lastProp=i,t?nr(e,i,r):(e.stopAnimation(),e.attr(i)))}function Vg(r,t){if(G(r)&&G(t)){var e=!0;return M(t,function(i,n){e=e&&Vg(r[n],i)}),!!e}else return r===t}function cc(r,t){r[t.get(["label","show"])?"show":"hide"]()}function Us(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function pc(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}function XT(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function $T(r,t,e,i,n){var a=e.get("value"),o=qT(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),u=Gl(s.get("padding")||0),l=s.getFont(),f=sl(o,l),h=n.position,c=f.width+u[1]+u[3],v=f.height+u[0]+u[2],p=n.align;p==="right"&&(h[0]-=c),p==="center"&&(h[0]-=c/2);var y=n.verticalAlign;y==="bottom"&&(h[1]-=v),y==="middle"&&(h[1]-=v/2),ZT(h,c,v,i);var d=s.get("backgroundColor");(!d||d==="auto")&&(d=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:ar(s,{text:o,font:l,fill:s.getTextColor(),padding:u,backgroundColor:d}),z2:10}}function ZT(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function qT(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:Ag(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};M(i,function(u){var l=e.getSeriesByIndex(u.seriesIndex),f=u.dataIndexInside,h=l&&l.getDataParams(f);h&&s.seriesData.push(h)}),V(o)?a=o.replace("{value}",a):Y(o)&&(a=o(s))}return a}function Wg(r,t,e){var i=vi();return al(i,i,e.rotation),js(i,i,e.position),Tl([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function KT(r,t,e,i,n,a){var o=er.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),$T(t,i,n,a,{position:Wg(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function QT(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function JT(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var jT=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,u=s.grid,l=a.get("type"),f=dc(u,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(l&&l!=="none"){var c=XT(a),v=tC[l](s,h,f);v.style=c,e.graphicKey=v.type,e.pointer=v}var p=Yu(u.model,n);KT(i,e,p,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=Yu(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=Wg(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,u=o.getGlobalExtent(!0),l=dc(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(u[1],h[f]),h[f]=Math.max(u[0],h[f]);var c=(l[1]+l[0])/2,v=[c,c];v[f]=h[f];var p=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:v,tooltipOption:p[f]}},t}(YT);function dc(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var tC={line:function(r,t,e){var i=QT([t,e[0]],[t,e[1]],gc(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:JT([t-i/2,e[0]],[i,n],gc(r))}}};function gc(r){return r.dim==="x"?0:1}var eC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(nt),Be=dt(),rC=M;function iC(r,t,e){if(!Q.node){var i=t.getZr();Be(i).records||(Be(i).records={}),nC(i,t);var n=Be(i).records[r]||(Be(i).records[r]={});n.handler=e}}function nC(r,t){if(Be(r).initialized)return;Be(r).initialized=!0,e("click",yt(yc,"click")),e("mousemove",yt(yc,"mousemove")),e("globalout",oC);function e(i,n){r.on(i,function(a){var o=sC(t);rC(Be(r).records,function(s){s&&n(s,a,o.dispatchAction)}),aC(o.pendings,t)})}}function aC(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function oC(r,t,e){r.handler("leave",null,e)}function yc(r,t,e,i){t.handler(r,e,i)}function sC(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function mc(r,t){if(!Q.node){var e=t.getZr(),i=(Be(e).records||{})[r];i&&(Be(e).records[r]=null)}}var uC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";iC("axisPointer",n,function(s,u,l){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&l({type:"updateAxisPointer",currTrigger:s,x:u&&u.offsetX,y:u&&u.offsetY})})},t.prototype.remove=function(e,i){mc("axisPointer",i)},t.prototype.dispose=function(e,i){mc("axisPointer",i)},t.type="axisPointer",t}(Ce);function lC(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=Wr(a,r);if(o==null||o<0||F(o))return{point:[]};var s=a.getItemGraphicEl(o),u=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(u&&u.dataToPoint)if(r.isStacked){var l=u.getBaseAxis(),f=u.getOtherAxis(l),h=f.dim,c=l.dim,v=h==="x"||h==="radius"?1:0,p=a.mapDimension(c),y=[];y[v]=a.get(p,o),y[1-v]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=u.dataToPoint(y)||[]}else e=u.dataToPoint(a.getValues(z(u.dimensions,function(g){return a.mapDimension(g)}),o))||[];else if(s){var d=s.getBoundingRect().clone();d.applyTransform(s.transform),e=[d.x+d.width/2,d.y+d.height/2]}return{point:e,el:s}}var _c=dt();function fC(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||gt(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){Ra(n)&&(n=lC({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var u=Ra(n),l=a.axesInfo,f=s.axesInfo,h=i==="leave"||Ra(n),c={},v={},p={list:[],map:{}},y={showPointer:yt(vC,v),showTooltip:yt(cC,p)};M(s.coordSysMap,function(g,m){var _=u||g.containPoint(n);M(s.coordSysAxesInfo[m],function(S,b){var w=S.axis,x=yC(l,S);if(!h&&_&&(!l||x)){var T=x&&x.value;T==null&&!u&&(T=w.pointToData(n)),T!=null&&Sc(S,T,y,!1,c)}})});var d={};return M(f,function(g,m){var _=g.linkGroup;_&&!v[m]&&M(_.axesInfo,function(S,b){var w=v[b];if(S!==g&&w){var x=w.value;_.mapper&&(x=g.axis.scale.parse(_.mapper(x,wc(S),wc(g)))),d[g.key]=x}})}),M(d,function(g,m){Sc(f[m],g,y,!0,c)}),pC(v,f,c),dC(p,n,r,o),gC(f,o,e),c}}function Sc(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=hC(t,r),s=o.payloadBatch,u=o.snapToValue;s[0]&&n.seriesIndex==null&&k(n,s[0]),!i&&r.snap&&a.containData(u)&&u!=null&&(t=u),e.showPointer(r,t,s),e.showTooltip(r,o,u)}}function hC(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return M(t.seriesModels,function(u,l){var f=u.getData().mapDimensionsAll(i),h,c;if(u.getAxisTooltipData){var v=u.getAxisTooltipData(f,r,e);c=v.dataIndices,h=v.nestestValue}else{if(c=u.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!c.length)return;h=u.getData().get(f[0],c[0])}if(!(h==null||!isFinite(h))){var p=r-h,y=Math.abs(p);y<=o&&((y<o||p>=0&&s<0)&&(o=y,s=p,n=h,a.length=0),M(c,function(d){a.push({seriesIndex:u.seriesIndex,dataIndexInside:d,dataIndex:u.getData().getRawIndex(d)})}))}}),{payloadBatch:a,snapToValue:n}}function vC(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function cC(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var u=t.coordSys.model,l=An(u),f=r.map[l];f||(f=r.map[l]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function pC(r,t,e){var i=e.axesInfo=[];M(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function dC(r,t,e,i){if(Ra(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function gC(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=_c(i)[n]||{},o=_c(i)[n]={};M(r,function(l,f){var h=l.axisPointerModel.option;h.status==="show"&&l.triggerEmphasis&&M(h.seriesDataIndices,function(c){var v=c.seriesIndex+" | "+c.dataIndex;o[v]=c})});var s=[],u=[];M(a,function(l,f){!o[f]&&u.push(l)}),M(o,function(l,f){!a[f]&&s.push(l)}),u.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:u}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function yC(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function wc(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function Ra(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function mC(r){zg.registerAxisPointerClass("CartesianAxisPointer",jT),r.registerComponentModel(eC),r.registerComponentView(uC),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!F(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=PT(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},fC)}function aD(r){Ci(UT),Ci(mC)}function oD(r,t,e){var i=t.getBoxLayoutParams(),n=t.get("padding"),a={width:e.getWidth(),height:e.getHeight()},o=bi(i,a,n);_i(t.get("orient"),r,t.get("itemGap"),o.width,o.height),U1(r,i,a,n)}function _C(r,t){var e=Gl(t.get("padding")),i=t.getItemStyle(["color","opacity"]);return i.fill=t.get("backgroundColor"),r=new xt({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:i,silent:!0,z2:-1}),r}var SC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(nt),wC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),u=e.get("textAlign"),l=$(e.get("textBaseline"),e.get("textVerticalAlign")),f=new Et({style:ar(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),c=e.get("subtext"),v=new Et({style:ar(s,{text:c,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),p=e.get("link"),y=e.get("sublink"),d=e.get("triggerEvent",!0);f.silent=!p&&!d,v.silent=!y&&!d,p&&f.on("click",function(){Oh(p,"_"+e.get("target"))}),y&&v.on("click",function(){Oh(y,"_"+e.get("subtarget"))}),vt(f).eventData=vt(v).eventData=d?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),c&&a.add(v);var g=a.getBoundingRect(),m=e.getBoxLayoutParams();m.width=g.width,m.height=g.height;var _=bi(m,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));u||(u=e.get("left")||e.get("right"),u==="middle"&&(u="center"),u==="right"?_.x+=_.width:u==="center"&&(_.x+=_.width/2)),l||(l=e.get("top")||e.get("bottom"),l==="center"&&(l="middle"),l==="bottom"?_.y+=_.height:l==="middle"&&(_.y+=_.height/2),l=l||"top"),a.x=_.x,a.y=_.y,a.markRedraw();var S={align:u,verticalAlign:l};f.setStyle(S),v.setStyle(S),g=a.getBoundingRect();var b=_.margin,w=e.getItemStyle(["color","opacity"]);w.fill=e.get("backgroundColor");var x=new xt({shape:{x:g.x-b[3],y:g.y-b[0],width:g.width+b[1]+b[3],height:g.height+b[0]+b[2],r:e.get("borderRadius")},style:w,subPixelOptimize:!0,silent:!0});a.add(x)}},t.type="title",t}(Ce);function sD(r){r.registerComponentModel(SC),r.registerComponentView(wC)}var bC=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},Zu=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),this._updateSelector(e)},t.prototype._updateSelector=function(e){var i=e.selector,n=this.ecModel;i===!0&&(i=e.selector=["all","inverse"]),F(i)&&M(i,function(a,o){V(a)&&(a={type:a}),i[o]=et(a,bC(n,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var i=!1,n=0;n<e.length;n++){var a=e[n].get("name");if(this.isSelected(a)){this.select(a),i=!0;break}}!i&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var i=[],n=[];e.eachRawSeries(function(u){var l=u.name;n.push(l);var f;if(u.legendVisualProvider){var h=u.legendVisualProvider,c=h.getAllNames();e.isSeriesFiltered(u)||(n=n.concat(c)),c.length?i=i.concat(c):f=!0}else f=!0;f&&vl(u)&&i.push(u.name)}),this._availableNames=n;var a=this.get("data")||i,o=X(),s=z(a,function(u){return(V(u)||ft(u))&&(u={name:u}),o.get(u.name)?null:(o.set(u.name,!0),new mt(u,this,this.ecModel))},this);this._data=wt(s,function(u){return!!u})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var i=this.option.selected,n=this.get("selectedMode");if(n==="single"){var a=this._data;M(a,function(o){i[o.get("name")]=!1})}i[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var i=this.option.selected;i.hasOwnProperty(e)||(i[e]=!0),this[i[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,i=this.option.selected;M(e,function(n){i[n.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,i=this.option.selected;M(e,function(n){var a=n.get("name",!0);i.hasOwnProperty(a)||(i[a]=!0),i[a]=!i[a]})},t.prototype.isSelected=function(e){var i=this.option.selected;return!(i.hasOwnProperty(e)&&!i[e])&&ot(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(nt),oi=yt,qu=M,ya=At,Ug=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new ya),this.group.add(this._selectorGroup=new ya),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,i,n){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var u=e.get("selector",!0),l=e.get("selectorPosition",!0);u&&(!l||l==="auto")&&(l=s==="horizontal"?"end":"start"),this.renderInner(o,e,i,n,u,s,l);var f=e.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=e.get("padding"),v=bi(f,h,c),p=this.layoutInner(e,o,v,a,u,l),y=bi(rt({width:p.width,height:p.height},f),h,c);this.group.x=y.x-p.x,this.group.y=y.y-p.y,this.group.markRedraw(),this.group.add(this._backgroundEl=_C(p,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,i,n,a,o,s,u){var l=this.getContentGroup(),f=X(),h=i.get("selectedMode"),c=[];n.eachRawSeries(function(v){!v.get("legendHoverLink")&&c.push(v.id)}),qu(i.getData(),function(v,p){var y=v.get("name");if(!this.newlineDisabled&&(y===""||y===`
`)){var d=new ya;d.newline=!0,l.add(d);return}var g=n.getSeriesByName(y)[0];if(!f.get(y))if(g){var m=g.getData(),_=m.getVisual("legendLineStyle")||{},S=m.getVisual("legendIcon"),b=m.getVisual("style"),w=this._createItem(g,y,p,v,i,e,_,b,S,h,a);w.on("click",oi(bc,y,null,a,c)).on("mouseover",oi(Ku,g.name,null,a,c)).on("mouseout",oi(Qu,g.name,null,a,c)),n.ssr&&w.eachChild(function(x){var T=vt(x);T.seriesIndex=g.seriesIndex,T.dataIndex=p,T.ssrType="legend"}),f.set(y,!0)}else n.eachRawSeries(function(x){if(!f.get(y)&&x.legendVisualProvider){var T=x.legendVisualProvider;if(!T.containName(y))return;var C=T.indexOfName(y),D=T.getItemVisual(C,"style"),A=T.getItemVisual(C,"legendIcon"),L=_e(D.fill);L&&L[3]===0&&(L[3]=.2,D=k(k({},D),{fill:In(L,"rgba")}));var I=this._createItem(x,y,p,v,i,e,{},D,A,h,a);I.on("click",oi(bc,null,y,a,c)).on("mouseover",oi(Ku,null,y,a,c)).on("mouseout",oi(Qu,null,y,a,c)),n.ssr&&I.eachChild(function(P){var E=vt(P);E.seriesIndex=x.seriesIndex,E.dataIndex=p,E.ssrType="legend"}),f.set(y,!0)}},this)},this),o&&this._createSelector(o,i,a,s,u)},t.prototype._createSelector=function(e,i,n,a,o){var s=this.getSelectorGroup();qu(e,function(l){var f=l.type,h=new Et({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});s.add(h);var c=i.getModel("selectorLabel"),v=i.getModel(["emphasis","selectorLabel"]);Ml(h,{normal:c,emphasis:v},{defaultText:l.title}),vu(h)})},t.prototype._createItem=function(e,i,n,a,o,s,u,l,f,h,c){var v=e.visualDrawType,p=o.get("itemWidth"),y=o.get("itemHeight"),d=o.isSelected(i),g=a.get("symbolRotate"),m=a.get("symbolKeepAspect"),_=a.get("icon");f=_||f||"roundRect";var S=xC(f,a,u,l,v,d,c),b=new ya,w=a.getModel("textStyle");if(Y(e.getLegendIcon)&&(!_||_==="inherit"))b.add(e.getLegendIcon({itemWidth:p,itemHeight:y,icon:f,iconRotate:g,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}));else{var x=_==="inherit"&&e.getData().getVisual("symbol")?g==="inherit"?e.getData().getVisual("symbolRotate"):g:0;b.add(TC({itemWidth:p,itemHeight:y,icon:f,iconRotate:x,itemStyle:S.itemStyle,symbolKeepAspect:m}))}var T=s==="left"?p+5:-5,C=s,D=o.get("formatter"),A=i;V(D)&&D?A=D.replace("{name}",i??""):Y(D)&&(A=D(i));var L=d?w.getTextColor():a.get("inactiveColor");b.add(new Et({style:ar(w,{text:A,x:T,y:y/2,fill:L,align:C,verticalAlign:"middle"},{inheritColor:L})}));var I=new xt({shape:b.getBoundingRect(),style:{fill:"transparent"}}),P=a.getModel("tooltip");return P.get("show")&&_o({el:I,componentModel:o,itemName:i,itemTooltipOption:P.option}),b.add(I),b.eachChild(function(E){E.silent=!0}),I.silent=!h,this.getContentGroup().add(b),vu(b),b.__legendDataIndex=n,b},t.prototype.layoutInner=function(e,i,n,a,o,s){var u=this.getContentGroup(),l=this.getSelectorGroup();_i(e.get("orient"),u,e.get("itemGap"),n.width,n.height);var f=u.getBoundingRect(),h=[-f.x,-f.y];if(l.markRedraw(),u.markRedraw(),o){_i("horizontal",l,e.get("selectorItemGap",!0));var c=l.getBoundingRect(),v=[-c.x,-c.y],p=e.get("selectorButtonGap",!0),y=e.getOrient().index,d=y===0?"width":"height",g=y===0?"height":"width",m=y===0?"y":"x";s==="end"?v[y]+=f[d]+p:h[y]+=c[d]+p,v[1-y]+=f[g]/2-c[g]/2,l.x=v[0],l.y=v[1],u.x=h[0],u.y=h[1];var _={x:0,y:0};return _[d]=f[d]+p+c[d],_[g]=Math.max(f[g],c[g]),_[m]=Math.min(0,c[m]+v[1-y]),_}else return u.x=h[0],u.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(Ce);function xC(r,t,e,i,n,a,o){function s(d,g){d.lineWidth==="auto"&&(d.lineWidth=g.lineWidth>0?2:0),qu(d,function(m,_){d[_]==="inherit"&&(d[_]=g[_])})}var u=t.getModel("itemStyle"),l=u.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=u.getShallow("decal");l.decal=!h||h==="inherit"?i.decal:Nu(h,o),l.fill==="inherit"&&(l.fill=i[n]),l.stroke==="inherit"&&(l.stroke=i[f]),l.opacity==="inherit"&&(l.opacity=(n==="fill"?i:e).opacity),s(l,i);var c=t.getModel("lineStyle"),v=c.getLineStyle();if(s(v,e),l.fill==="auto"&&(l.fill=i.fill),l.stroke==="auto"&&(l.stroke=i.fill),v.stroke==="auto"&&(v.stroke=i.fill),!a){var p=t.get("inactiveBorderWidth"),y=l[f];l.lineWidth=p==="auto"?i.lineWidth>0&&y?2:0:l.lineWidth,l.fill=t.get("inactiveColor"),l.stroke=t.get("inactiveBorderColor"),v.stroke=c.get("inactiveColor"),v.lineWidth=c.get("inactiveWidth")}return{itemStyle:l,lineStyle:v}}function TC(r){var t=r.icon||"roundRect",e=Ti(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function bc(r,t,e,i){Qu(r,t,e,i),e.dispatchAction({type:"legendToggleSelect",name:r??t}),Ku(r,t,e,i)}function Yg(r){for(var t=r.getZr().storage.getDisplayList(),e,i=0,n=t.length;i<n&&!(e=t[i].states.emphasis);)i++;return e&&e.hoverLayer}function Ku(r,t,e,i){Yg(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:i})}function Qu(r,t,e,i){Yg(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:i})}function CC(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0})}function Ki(r,t,e){var i=r==="allSelect"||r==="inverseSelect",n={},a=[];e.eachComponent({mainType:"legend",query:t},function(s){i?s[r]():s[r](t.name),xc(s,n),a.push(s.componentIndex)});var o={};return e.eachComponent("legend",function(s){M(n,function(u,l){s[u?"select":"unSelect"](l)}),xc(s,o)}),i?{selected:o,legendIndex:a}:{name:t.name,selected:o}}function xc(r,t){var e=t||{};return M(r.getData(),function(i){var n=i.get("name");if(!(n===`
`||n==="")){var a=r.isSelected(n);Gr(e,n)?e[n]=e[n]&&a:e[n]=a}}),e}function DC(r){r.registerAction("legendToggleSelect","legendselectchanged",yt(Ki,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",yt(Ki,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",yt(Ki,"inverseSelect")),r.registerAction("legendSelect","legendselected",yt(Ki,"select")),r.registerAction("legendUnSelect","legendunselected",yt(Ki,"unSelect"))}function Xg(r){r.registerComponentModel(Zu),r.registerComponentView(Ug),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,CC),r.registerSubTypeDefaulter("legend",function(){return"plain"}),DC(r)}var MC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,i,n){var a=Ao(e);r.prototype.init.call(this,e,i,n),Tc(this,e,a)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),Tc(this,this.option,e)},t.type="legend.scroll",t.defaultOption=D1(Zu.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(Zu);function Tc(r,t,e){var i=r.getOrient(),n=[1,1];n[i.index]=0,xi(t,e,{type:"box",ignoreSize:!!n})}var Cc=At,Ys=["width","height"],Xs=["x","y"],AC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new Cc),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Cc)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,i,n,a,o,s,u){var l=this;r.prototype.renderInner.call(this,e,i,n,a,o,s,u);var f=this._controllerGroup,h=i.get("pageIconSize",!0),c=F(h)?h:[h,h];p("pagePrev",0);var v=i.getModel("pageTextStyle");f.add(new Et({name:"pageText",style:{text:"xx/xx",fill:v.getTextColor(),font:v.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),p("pageNext",1);function p(y,d){var g=y+"DataIndex",m=Cl(i.get("pageIcons",!0)[i.getOrient().name][d],{onclick:gt(l._pageGo,l,g,i,a)},{x:-c[0]/2,y:-c[1]/2,width:c[0],height:c[1]});m.name=y,f.add(m)}},t.prototype.layoutInner=function(e,i,n,a,o,s){var u=this.getSelectorGroup(),l=e.getOrient().index,f=Ys[l],h=Xs[l],c=Ys[1-l],v=Xs[1-l];o&&_i("horizontal",u,e.get("selectorItemGap",!0));var p=e.get("selectorButtonGap",!0),y=u.getBoundingRect(),d=[-y.x,-y.y],g=J(n);o&&(g[f]=n[f]-y[f]-p);var m=this._layoutContentAndController(e,a,g,l,f,c,v,h);if(o){if(s==="end")d[l]+=m[f]+p;else{var _=y[f]+p;d[l]-=_,m[h]-=_}m[f]+=y[f]+p,d[1-l]+=m[v]+m[c]/2-y[c]/2,m[c]=Math.max(m[c],y[c]),m[v]=Math.min(m[v],y[v]+d[1-l]),u.x=d[0],u.y=d[1],u.markRedraw()}return m},t.prototype._layoutContentAndController=function(e,i,n,a,o,s,u,l){var f=this.getContentGroup(),h=this._containerGroup,c=this._controllerGroup;_i(e.get("orient"),f,e.get("itemGap"),a?n.width:null,a?null:n.height),_i("horizontal",c,e.get("pageButtonItemGap",!0));var v=f.getBoundingRect(),p=c.getBoundingRect(),y=this._showController=v[o]>n[o],d=[-v.x,-v.y];i||(d[a]=f[l]);var g=[0,0],m=[-p.x,-p.y],_=$(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(y){var S=e.get("pageButtonPosition",!0);S==="end"?m[a]+=n[o]-p[o]:g[a]+=p[o]+_}m[1-a]+=v[s]/2-p[s]/2,f.setPosition(d),h.setPosition(g),c.setPosition(m);var b={x:0,y:0};if(b[o]=y?n[o]:v[o],b[s]=Math.max(v[s],p[s]),b[u]=Math.min(0,p[u]+m[1-a]),h.__rectSize=n[o],y){var w={x:0,y:0};w[o]=Math.max(n[o]-p[o]-_,0),w[s]=b[s],h.setClipPath(new xt({shape:w})),h.__rectSize=w[o]}else c.eachChild(function(T){T.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(e);return x.pageIndex!=null&&nr(f,{x:x.contentPosition[0],y:x.contentPosition[1]},y?e:null),this._updatePageInfoView(e,x),b},t.prototype._pageGo=function(e,i,n){var a=this._getPageInfo(i)[e];a!=null&&n.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:i.id})},t.prototype._updatePageInfoView=function(e,i){var n=this._controllerGroup;M(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",c=i[h]!=null,v=n.childOfName(f);v&&(v.setStyle("fill",c?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),v.cursor=c?"pointer":"default")});var a=n.childOfName("pageText"),o=e.get("pageFormatter"),s=i.pageIndex,u=s!=null?s+1:0,l=i.pageCount;a&&o&&a.setStyle("text",V(o)?o.replace("{current}",u==null?"":u+"").replace("{total}",l==null?"":l+""):o({current:u,total:l}))},t.prototype._getPageInfo=function(e){var i=e.get("scrollDataIndex",!0),n=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=Ys[o],u=Xs[o],l=this._findTargetItemIndex(i),f=n.children(),h=f[l],c=f.length,v=c?1:0,p={contentPosition:[n.x,n.y],pageCount:v,pageIndex:v-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return p;var y=S(h);p.contentPosition[o]=-y.s;for(var d=l+1,g=y,m=y,_=null;d<=c;++d)_=S(f[d]),(!_&&m.e>g.s+a||_&&!b(_,g.s))&&(m.i>g.i?g=m:g=_,g&&(p.pageNextDataIndex==null&&(p.pageNextDataIndex=g.i),++p.pageCount)),m=_;for(var d=l-1,g=y,m=y,_=null;d>=-1;--d)_=S(f[d]),(!_||!b(m,_.s))&&g.i<m.i&&(m=g,p.pagePrevDataIndex==null&&(p.pagePrevDataIndex=g.i),++p.pageCount,++p.pageIndex),g=_;return p;function S(w){if(w){var x=w.getBoundingRect(),T=x[u]+w[u];return{s:T,e:T+x[s],i:w.__legendDataIndex}}}function b(w,x){return w.e>=x&&w.s<=x+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var i,n=this.getContentGroup(),a;return n.eachChild(function(o,s){var u=o.__legendDataIndex;a==null&&u!=null&&(a=s),u===e&&(i=s)}),i??a},t.type="legend.scroll",t}(Ug);function LC(r){r.registerAction("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;i!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(n){n.setScrollDataIndex(i)})})}function PC(r){Ci(Xg),r.registerComponentModel(MC),r.registerComponentView(AC),LC(r)}function uD(r){Ci(Xg),Ci(PC)}function Dc(r,t,e){var i=Di.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var $s=function(r){O(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||Ga,typeof e=="string"?o=Dc(e,i,n):G(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(kc(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=Dc("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,u=!1,l=new j(0,0,0,0);function f(m){if(!(!m.isFinite()||m.isZero()))if(o.length===0){var _=new j(0,0,0,0);_.copy(m),o.push(_)}else{for(var S=!1,b=1/0,w=0,x=0;x<o.length;++x){var T=o[x];if(T.intersect(m)){var C=new j(0,0,0,0);C.copy(T),C.union(m),o[x]=C,S=!0;break}else if(u){l.copy(m),l.union(T);var D=m.width*m.height,A=T.width*T.height,L=l.width*l.height,I=L-D-A;I<b&&(b=I,w=x)}}if(u&&(o[w].union(m),S=!0),!S){var _=new j(0,0,0,0);_.copy(m),o.push(_)}u||(u=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var c=e[h];if(c){var v=c.shouldBePainted(n,a,!0,!0),p=c.__isRendered&&(c.__dirty&Se||!v)?c.getPrevPaintRect():null;p&&f(p);var y=v&&(c.__dirty&Se||!c.__isRendered)?c.getPaintRect():null;y&&f(y)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c=i[h],v=c&&c.shouldBePainted(n,a,!0,!0);if(c&&(!v||!c.__zr)&&c.__isRendered){var p=c.getPrevPaintRect();p&&f(p)}}var d;do{d=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(d=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(d);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,u=a.height;i=i||this.clearColor;var l=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,c=this;l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,u/h));var v=this.domBack;function p(y,d,g,m){if(o.clearRect(y,d,g,m),i&&i!=="transparent"){var _=void 0;if(ao(i)){var S=i.global||i.__width===g&&i.__height===m;_=S&&i.__canvasGradient||Ou(o,i,{x:0,y:0,width:g,height:m}),i.__canvasGradient=_,i.__width=g,i.__height=m}else ay(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,_=Bu(o,i,{dirty:function(){c.setUnpainted(),c.painter.refresh()}}));o.save(),o.fillStyle=_||i,o.fillRect(y,d,g,m),o.restore()}l&&(o.save(),o.globalAlpha=f,o.drawImage(v,y,d,g,m),o.restore())}!n||l?p(0,0,s,u):n.length&&M(n,function(y){p(y.x*h,y.y*h,y.width*h,y.height*h)})},t}(Me),Mc=1e5,Ir=314159,ma=.01,IC=.001;function EC(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function RC(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var kC=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=k({},i||{}),this.dpr=i.devicePixelRatio||Ga,this._singleCanvas=a,this.root=t;var o=t.style;o&&(kc(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(a){var f=t,h=f.width,c=f.height;i.width!=null&&(h=i.width),i.height!=null&&(c=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=c*this.dpr,this._width=h,this._height=c;var v=new $s(f,this,this.dpr);v.__builtin__=!0,v.initContext(),u[Ir]=v,v.zlevel=Ir,s.push(Ir),this._domRoot=t}else{this._width=fa(t,0,i),this._height=fa(t,1,i);var l=this._domRoot=RC(this._width,this._height);t.appendChild(l)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var u=a===0?this._backgroundColor:null;s.refresh(u)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(Mc)),a||(a=i.ctx,a.save()),Or(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(Mc)},r.prototype.paintOne=function(t,e){ig(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(l){l.afterBrush&&l.afterBrush()});else{var u=this;Ja(function(){u._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(Ir).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],l=this._layers[u];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||i)&&a.push(l)}for(var f=!0,h=!1,c=function(y){var d=a[y],g=d.ctx,m=o&&d.createRepaintRects(t,e,v._width,v._height),_=i?d.__startIndex:d.__drawIndex,S=!i&&d.incremental&&Date.now,b=S&&Date.now(),w=d.zlevel===v._zlevelList[0]?v._backgroundColor:null;if(d.__startIndex===d.__endIndex)d.clear(!1,w,m);else if(_===d.__startIndex){var x=t[_];(!x.incremental||!x.notClear||i)&&d.clear(!1,w,m)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=d.__startIndex);var T,C=function(I){var P={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(T=_;T<d.__endIndex;T++){var E=t[T];if(E.__inHover&&(h=!0),n._doPaintEl(E,d,o,I,P,T===d.__endIndex-1),S){var R=Date.now()-b;if(R>15)break}}P.prevElClipPaths&&g.restore()};if(m)if(m.length===0)T=d.__endIndex;else for(var D=v.dpr,A=0;A<m.length;++A){var L=m[A];g.save(),g.beginPath(),g.rect(L.x*D,L.y*D,L.width*D,L.height*D),g.clip(),C(L),g.restore()}else g.save(),C(),g.restore();d.__drawIndex=T,d.__drawIndex<d.__endIndex&&(f=!1)},v=this,p=0;p<a.length;p++)c(p);return Q.wxa&&M(this._layers,function(y){y&&y.ctx&&y.ctx.draw&&y.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var u=t.getPaintRect();(!n||u&&u.intersect(n))&&(Or(s,t,a,o),t.setPrevPaintRect(u))}else Or(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Ir);var i=this._layers[t];return i||(i=new $s("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?et(i,this._layerConfig[t],!0):this._layerConfig[t-ma]&&et(i,this._layerConfig[t-ma],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,u=-1;if(!i[t]&&EC(e)){if(a>0&&t>n[0]){for(u=0;u<a-1&&!(n[u]<t&&n[u+1]>t);u++);s=i[n[u]]}if(n.splice(u+1,0,t),i[t]=e,!e.virtual)if(s){var l=s.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,c){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,u;for(u=0;u<t.length;u++){var n=t[u],l=n.zlevel,f=void 0;s!==l&&(s=l,o=0),n.incremental?(f=this.getLayer(l+IC,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(l+(o>0?ma:0),this._needsManuallyCompositing),f.__builtin__||el("ZLevel "+l+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==u&&(f.__dirty=!0),f.__startIndex=u,f.incremental?f.__drawIndex=-1:f.__drawIndex=u,e(u),a=f),n.__dirty&Se&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=u))}e(u),this.eachBuiltinLayer(function(h,c){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,M(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?et(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+ma){var o=this._layers[a];et(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(ot(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=fa(a,0,n),e=fa(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(Ir).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Ir].dom;var e=new $s("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),u=0,l=s.length;u<l;u++){var f=s[u];Or(i,f,o,u===l-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();function lD(r){r.registerPainter("canvas",kC)}export{mt as $,mw as A,_w as B,nt as C,iC as D,vt as E,lC as F,ua as G,Tu as H,qT as I,Re as J,Ag as K,XC as L,$C as M,J as N,Wt as O,Fl as P,H1 as Q,xt as R,Ht as S,qC as T,bi as U,mc as V,Ce as W,hp as X,ho as Y,Et as Z,O as _,sD as a,__ as a$,k as a0,mC as a1,te as a2,Gr as a3,dt as a4,lt as a5,_n as a6,Sa as a7,It as a8,En as a9,kn as aA,h_ as aB,Ll as aC,f_ as aD,KC as aE,be as aF,JS as aG,yo as aH,Al as aI,Ml as aJ,wd as aK,VC as aL,cu as aM,eh as aN,Od as aO,FS as aP,yt as aQ,jC as aR,vw as aS,JC as aT,Li as aU,N_ as aV,Me as aW,_l as aX,Sl as aY,rt as aZ,S_ as a_,st as aa,l_ as ab,Nt as ac,rr as ad,wt as ae,e0 as af,et as ag,xi as ah,Y1 as ai,X as aj,we as ak,_o as al,U1 as am,nr as an,W1 as ao,sr as ap,At as aq,d_ as ar,S1 as as,or as at,D1 as au,zc as av,ft as aw,z as ax,Hr as ay,Dl as az,aD as b,b_ as b0,j as b1,GC as b2,fe as b3,Hf as b4,NC as b5,qm as b6,nD as b7,iT as b8,xo as b9,Ua as ba,Ya as bb,Cl as bc,P_ as bd,oD as be,_C as bf,sl as bg,Aw as bh,es as bi,rD as bj,zC as bk,Sm as bl,at as bm,Zf as bn,Te as bo,Wy as bp,OC as bq,eD as br,_a as bs,u1 as bt,s1 as bu,WC as bv,FC as bw,Uf as bx,tD as by,QC as c,lD as d,uD as e,Q as f,V as g,Ks as h,iD as i,Y as j,UC as k,xu as l,BC as m,se as n,F as o,M as p,gt as q,ZC as r,Gl as s,YC as t,Ci as u,ot as v,$ as w,G as x,Vt as y,HC as z};
