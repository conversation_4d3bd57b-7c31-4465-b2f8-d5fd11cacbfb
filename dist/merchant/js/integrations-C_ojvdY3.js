import{U as t,V as n}from"./index-BPwFJVSY.js";const a=(e,o)=>n("xero/submit",e,o),c=(e,o)=>t("xero/break",{...o}),r=()=>t("xero/info"),i=(e,o)=>t("xero/refreshAccessToken",{...o}),x=()=>t("xero/getAllAccountCode"),g=()=>t("xero/getThemeList"),u=(e,o)=>n("xero/addInvoice",e,o),m=e=>n("xero/updateInvoice",e),p=e=>t("xero/getInvoicePaymentDetail",{params:e}),d=e=>n("xero/invoicePaymentSubmit",e),f=(e,o)=>n("xero/syncChannelData",{...o}),X=()=>t("xero/getAccountConfig"),v=e=>n("xero/updateBrandingThemeConfig",e),I=e=>n("xero/updateInvoiceChannelConfig",e);export{x as a,g as b,u as c,r as d,a as e,X as f,p as g,v as h,I as i,d as p,i as r,f as s,m as u,c as x};
