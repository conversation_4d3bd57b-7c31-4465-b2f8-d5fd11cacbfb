import{s as le}from"./index-D7FsVJ-c.js";import{S as ce,_ as me}from"./subscriptionFormPreview-CmaF54bP.js";import{_ as de}from"./BaseCardType-BnVjnEGA.js";import{s as ue}from"./index-BlFKIn6Y.js";import{s as pe}from"./index-CEksU3zE.js";import{s as he}from"./index-Cbodd6BQ.js";import{d as ye,k as fe,r as B,f as ve,B as _e,a as y,b as s,u as e,e as n,w as m,t as u,j as b,p as x,q as k,F as ge,y as E,z as be,s as ke,A as w,T as ee,n as xe,m as M,v as A,N as te,O as se,o as l,P as we}from"./index-BPwFJVSY.js";import{s as Ce}from"./index-DMmJJ4dH.js";import{_ as Pe,a as Se,b as Ie,c as Be}from"./getAssetsFile-DC-dlhh8.js";import{t as Ee}from"./vee-validate-zod-BFI0y82J.js";import{s as p}from"./index-CQ6UwYfx.js";import{u as Me}from"./checkout-CD3XOa-G.js";import Ae from"./formBankAccountDetails-8kQfbIz-.js";import Ve from"./formPayment-EV5vj1ui.js";import{o as $e,s as oe}from"./types-AmnC9OGP.js";import{_ as Ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./dayjs.min-Bar1rHDB.js";import"./decimal-LZMqkRTm.js";import"./index-DgBXOSrM.js";import"./customer-v2cMYA98.js";import"./plan-DCOwcHy2.js";import"./user-D3NCqkKe.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./BillbuddyLogo-FvDXaJ4h.js";import"./PMI-logo-4SeFW8S8.js";import"./logo-CiIwT2Ho.js";import"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import"./color-DqhIgFrh.js";import"./invioce-payment-Bs21HoFI.js";import"./isUpdated-DXQm7BuR.js";import"./login-bg-kShksnUz.js";import"./customer-M3EzHCil.js";import"./usePlanForm-WYcUl8Fa.js";import"./useListRefresh-D-tL0eHu.js";import"./usePermissions-PkVCxleQ.js";import"./integrations-C_ojvdY3.js";import"./plan-X86n_BnV.js";import"./dict-CLPQcIyV.js";import"./index-CTms-Les.js";const Fe={key:0,class:"loading-state"},Te={class:"checkout-header mb-6"},De={class:"currency-selector mt-4"},Le={class:"flex items-center justify-between p-4 bg-gray-50 rounded-lg"},Re={class:"space-y-4"},je={class:"payment-methods"},qe={class:"flex gap-4 mb-4"},He={key:1,class:"result-state success"},Ue={class:"text-center py-8"},ze={class:"text-2xl font-semibold text-gray-900 mb-2"},Oe={class:"text-gray-600 mb-6"},Ye={class:"text-gray-600 mb-4"},Ge={key:2,class:"result-state error"},Je={class:"text-center py-8"},Ke={class:"text-gray-600 mb-6"},Qe={class:"flex justify-center gap-3"},We={key:3},Xe={class:"checkout-header mb-6"},Ze={class:"merchant-logo flex items-center justify-center"},et=["src"],tt={class:"header-content-merchant"},st={class:"checkout-content"},ot={class:"header-content mt-4"},nt={class:"currency-selector"},at={class:"currency-selector-description"},rt={class:"flex items-center justify-between py-4"},it={class:"currency-label flex items-center gap-2"},lt={class:"payment-form"},ct={class:"card-title"},mt={class:"flex gap-4"},dt={class:"pb-4"},ut=["onClick"],pt={class:"bg-gray-50 p-2 px-4 rounded-lg mb-4"},ht={class:"flex justify-between gap-2"},yt={class:"payment-method-wrap"},ft={class:"payment-method-content"},vt={class:"payment-method-content"},_t={class:"flex justify-end gap-2"},gt=ye({__name:"invite",props:{customerId:{default:""},readonly:{type:Boolean,default:!1}},setup(ne){const V=ne,ae=Ee($e({customer_name:oe().min(2,"Name must be at least 2 characters"),customer_phone:oe()})),C=_e(),o=Me(),{t:d}=fe(),P=B(),S=B(),I=B(),$=i=>{i.style.height="0",i.style.opacity="0",i.style.overflow="hidden"},N=(i,t)=>{const a=i,c=a.scrollHeight;a.style.height="0",a.style.opacity="0",a.offsetHeight,a.style.transition="height 0.5s ease-in-out, opacity 0.2s ease-in-out",a.style.height=`${c}px`,a.style.opacity="1",a.addEventListener("transitionend",function f(h){h.propertyName==="height"&&(a.style.height="auto",a.style.overflow="visible",a.removeEventListener("transitionend",f),t())})},F=(i,t)=>{const a=i;a.style.height=`${a.scrollHeight}px`,a.style.overflow="hidden",a.offsetHeight,a.style.transition="height 0.5s ease-in-out, opacity 0.2s ease-in-out",a.style.height="0",a.style.opacity="0",a.addEventListener("transitionend",function c(f){f.propertyName==="height"&&(a.removeEventListener("transitionend",c),t())})},re=[{code:"AUD",label:"AUD"}],ie=()=>{window.location.href=window.location.origin},T=async()=>{var t,a,c;const{valid:i}=await((t=P.value)==null?void 0:t.validate());if(i){let f=!1;if(o.activePaymentMethod==="card"?await((a=S.value)==null?void 0:a.submitForm())&&(f=!0):o.activePaymentMethod==="bankAccount"&&await((c=I.value)==null?void 0:c.submitForm())&&(f=!0),!f)return;const h=await o.submitInvite();if(h!=null&&h.success){const _=document.createElement("div");_.innerHTML=h.html,document.body.appendChild(_),we(()=>{const g=document.querySelector("#step-up-form");g&&(g==null||g.submit())})}}},D=()=>{var i,t,a;o.resetForm(),(i=P.value)==null||i.resetForm(),(t=S.value)==null||t.resetForm(),(a=I.value)==null||a.resetForm()};return ve(()=>{V.readonly&&D(),C.query.status==="0"?o.setPageStatus("success"):C.query.status==="1"?(o.pageErrorMessage="Sorry Payment Failed",o.setPageStatus("error")):o.setCustomerId(V.customerId||String(C.params.id)||"")}),(i,t)=>{var j,q,H,U,z,O,Y,G,J,K,Q,W,X,Z;const a=Ce,c=ke,f=he,h=pe,_=ue,g=de,L=me,R=le;return l(),y("div",{class:"checkout-page",style:xe(e(o).cssVariables)},[s("div",{class:w(["checkout-container",{"bg-transparent":e(o).pageStatus!=="loading"}])},[e(o).pageStatus==="loading"?(l(),y("div",Fe,[s("div",Te,[n(e(p),{width:"4rem",height:"4rem",class:"mb-4"}),n(e(p),{width:"70%",height:"2rem",class:"mb-2"}),n(e(p),{width:"50%",height:"1.5rem",class:"mb-4"}),s("div",De,[s("div",Le,[n(e(p),{width:"30%",height:"1.5rem"}),n(e(p),{width:"20%",height:"2rem"})])])]),n(a,null,{content:m(()=>[s("div",Re,[n(e(p),{width:"100%",height:"3rem",class:"mb-4"}),n(e(p),{width:"100%",height:"3rem",class:"mb-4"}),n(e(p),{width:"100%",height:"3rem",class:"mb-4"}),s("div",je,[s("div",qe,[n(e(p),{width:"50%",height:"3rem"}),n(e(p),{width:"50%",height:"3rem"})]),n(e(p),{width:"100%",height:"12rem"})])])]),_:1})])):e(o).pageStatus==="success"?(l(),y("div",He,[n(a,null,{content:m(()=>[s("div",Ue,[t[11]||(t[11]=s("i",{class:"!text-6xl pi pi-check-circle text-green-500 mb-4"},null,-1)),s("h2",ze,u(e(d)("checkout.success.title")),1),s("p",Oe,u(e(d)("checkout.success.message")),1),s("p",Ye,u(e(d)("checkout.success.redirect",{seconds:e(o).redirectCountdown})),1),n(c,{label:e(d)("checkout.success.redirectButton"),class:"p-button-primary",onClick:ie},null,8,["label"])])]),_:1})])):e(o).pageStatus==="error"?(l(),y("div",Ge,[n(a,null,{content:m(()=>[s("div",Je,[t[12]||(t[12]=s("i",{class:"pi pi-exclamation-circle text-6xl text-red-500 mb-4"},null,-1)),t[13]||(t[13]=s("h2",{class:"text-2xl font-semibold text-gray-900 mb-2"}," Setup Failed ",-1)),s("p",Ke,u(e(o).pageErrorMessage),1),s("div",Qe,[n(c,{label:"Back",class:"p-button-secondary",onClick:t[0]||(t[0]=r=>i.$router.push("/customers"))}),n(c,{label:"Retry",onClick:D})])])]),_:1})])):(l(),y("div",We,[s("div",Xe,[s("div",Ze,[s("img",{src:e(o).config.logo,alt:"Merchant Logo",class:"max-w-40 max-h-40"},null,8,et)]),s("div",tt,[t[14]||(t[14]=s("span",{class:"mr-2"},"Setup a",-1)),s("h2",null,"Direct Debit Request with "+u((j=e(o).config)==null?void 0:j.merchant_name),1)])]),s("div",st,[s("div",ot,[s("div",nt,[s("p",at,u(e(d)("checkout.description")),1),s("div",rt,[t[15]||(t[15]=s("div",{class:"flex items-center"},[s("img",{src:Pe,alt:"carte-blanche",class:"w-8 w-8"})],-1)),s("div",it,[b(u(e(d)("checkout.currency.label"))+" ($) ",1),n(f,{modelValue:e(o).selectedCurrency,"onUpdate:modelValue":t[1]||(t[1]=r=>e(o).selectedCurrency=r),options:re,"option-label":"label","option-value":"code",class:"currency-dropdown",placeholder:e(d)("checkout.currency.select")},null,8,["modelValue","placeholder"])])])])]),s("div",lt,[s("div",ct,u(e(d)("checkout.basicInformation")),1),n(e(ge),{ref_key:"formRef",ref:P,"validation-schema":e(ae),onSubmit:T},{default:m(()=>[s("div",mt,[n(e(M),{modelValue:e(o).customerInfo.customer_name,"onUpdate:modelValue":t[2]||(t[2]=r=>e(o).customerInfo.customer_name=r),as:"div",class:"w-1/2",name:"customer_name"},{default:m(({field:r,errorMessage:v})=>[t[16]||(t[16]=s("label",{class:"mb-2 block"},"Name",-1)),n(h,A(r,{id:"name",placeholder:e(d)("checkout.form.name.placeholder"),class:"w-full"}),null,16,["placeholder"]),v?(l(),x(_,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[b(u(v),1)]),_:2},1024)):k("",!0)]),_:1,__:[16]},8,["modelValue"]),n(e(M),{modelValue:e(o).customerInfo.customer_phone,"onUpdate:modelValue":t[3]||(t[3]=r=>e(o).customerInfo.customer_phone=r),name:"customer_phone",as:"div",class:"w-1/2"},{default:m(({field:r,errorMessage:v})=>[t[17]||(t[17]=s("label",{class:"mb-2 block"},"Phone Number",-1)),n(h,A({id:"phoneNumber"},r,{placeholder:e(d)("checkout.form.phoneNumber.placeholder"),class:"w-full"}),null,16,["placeholder"]),v?(l(),x(_,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[b(u(v),1)]),_:2},1024)):k("",!0)]),_:1,__:[17]},8,["modelValue"])]),n(e(M),{modelValue:e(o).customerInfo.customer_email,"onUpdate:modelValue":t[4]||(t[4]=r=>e(o).customerInfo.customer_email=r),name:"customer_email",class:"mt-4",as:"div"},{default:m(({field:r,errorMessage:v})=>[t[18]||(t[18]=s("label",{class:"mb-2 block"},"Email",-1)),n(h,A(r,{id:"email",placeholder:e(d)("checkout.form.email.placeholder"),class:"w-full",disabled:""}),null,16,["placeholder"]),v?(l(),x(_,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[b(u(v),1)]),_:2},1024)):k("",!0)]),_:1,__:[18]},8,["modelValue"])]),_:1},8,["validation-schema"]),t[28]||(t[28]=s("div",{class:"card-title mt-8"}," Subscription Details ",-1)),(H=(q=e(o).customerInfo)==null?void 0:q.customer_plan)!=null&&H.plan_id?(l(),x(ce,{key:0,name:(U=e(o).customerInfo)==null?void 0:U.customer_name,"form-data":(z=e(o).customerInfo)==null?void 0:z.customer_plan,"surcharge-rate":(Y=(O=e(o).customerInfo)==null?void 0:O.customer_plan)==null?void 0:Y.surcharge_rate,"unit-quantity":((J=(G=e(o).customerInfo)==null?void 0:G.customer_plan)==null?void 0:J.units)||1,"is-show-forecast":!1,"start-date":(Q=(K=e(o).customerInfo)==null?void 0:K.customer_plan)==null?void 0:Q.start_date},null,8,["name","form-data","surcharge-rate","unit-quantity","start-date"])):k("",!0),(W=e(o).customerInfo.customer_bankings)!=null&&W.length&&((X=e(o).customerInfo)==null?void 0:X.customer_bankings.length)>0&&!e(o).isConfirmPaymentMethod?(l(),y(E,{key:1},[s("div",dt,[t[19]||(t[19]=s("div",{class:"card-title",style:{"margin-block":"24px 8px"}}," Payment Method ",-1)),t[20]||(t[20]=s("p",{class:"mb-4 font-bold text-gray-600"}," This is your existing payment method: ",-1)),(l(!0),y(E,null,be((Z=e(o).customerInfo)==null?void 0:Z.customer_bankings,r=>(l(),y("div",{key:r.id,class:w(["bank-card",{active:e(o).selectedBank===r.id}]),onClick:v=>e(o).setSelectedBank(r.id)},[n(g,{"card-type":r.credit_brand,text:r.account_no,"is-show-card-number":!0},null,8,["card-type","text"])],10,ut))),128))]),s("div",pt,[t[21]||(t[21]=s("p",{class:"font-bold py-3 text-gray-600"}," Do you want to change to a new payment method? ",-1)),s("div",ht,[n(c,{label:"YES, change my payment method",severity:"primary",disabled:e(o).isSelectedBankLoading,onClick:t[5]||(t[5]=r=>e(o).cancelPaymentMethod())},null,8,["disabled"]),n(c,{label:"NO, use selected card",severity:"secondary",loading:e(o).isSelectedBankLoading,onClick:t[6]||(t[6]=r=>e(o).confirmPaymentMethod())},null,8,["loading"])])]),n(L,{class:"support-divide"})],64)):(l(),y(E,{key:2},[n(L,{class:"support-divide"}),t[25]||(t[25]=s("p",{class:"mb-6 font-bold text-gray-600"}," You don’t have an existing payment method. Please add your bank details: ",-1)),t[26]||(t[26]=s("div",{class:"card-title",style:{"margin-block":"24px 8px"}}," Payment Method ",-1)),t[27]||(t[27]=s("span",{class:"card-description"}," All transactions are secure and encrypted. ",-1)),s("div",yt,[e(o).config.open_banking_transactions?(l(),y("div",{key:0,class:w(["payment-method-title flex items-center",[{active:e(o).activePaymentMethod==="bankAccount"}]]),onClick:t[8]||(t[8]=r=>e(o).setActivePaymentMethod("bankAccount"))},[n(R,{class:"mr-2","model-value":e(o).activePaymentMethod==="bankAccount",value:"bankAccount",binary:"",onClick:t[7]||(t[7]=r=>e(o).setActivePaymentMethod("bankAccount"))},null,8,["model-value"]),t[22]||(t[22]=b(" Credit Card ")),t[23]||(t[23]=s("div",{class:"flex items-center gap-2 flex-1 justify-end"},[s("img",{src:Se,alt:"visa",class:"w-8 w-8 border border-gray-200 rounded"}),s("img",{src:Ie,alt:"mastercard",class:"w-8 w-8 border border-gray-200 rounded"}),s("img",{src:Be,alt:"amex",class:"w-8 w-8 border border-gray-200 rounded"})],-1))],2)):k("",!0),n(ee,{onBeforeEnter:$,onEnter:N,onLeave:F},{default:m(()=>[te(s("div",ft,[n(Ve,{ref_key:"formPaymentRef",ref:I},null,512)],512),[[se,e(o).activePaymentMethod==="bankAccount"]])]),_:1}),s("div",{class:w(["payment-method-title",[{active:e(o).activePaymentMethod==="card"}]]),onClick:t[10]||(t[10]=r=>e(o).setActivePaymentMethod("card"))},[n(R,{class:"mr-2","model-value":e(o).activePaymentMethod==="card",value:"card",binary:"",onClick:t[9]||(t[9]=r=>e(o).setActivePaymentMethod("card"))},null,8,["model-value"]),t[24]||(t[24]=b(" Bank Account "))],2),n(ee,{onBeforeEnter:$,onEnter:N,onLeave:F},{default:m(()=>[te(s("div",vt,[n(Ae,{ref_key:"formBankAccountDetailsRef",ref:S},null,512)],512),[[se,e(o).activePaymentMethod==="card"]])]),_:1})]),s("div",_t,[n(c,{label:"CONTINUE",class:"submit-button",onClick:T})])],64))])])]))],2)],4)}}}),ls=Ne(gt,[["__scopeId","data-v-72fa1e76"]]);export{ls as default};
