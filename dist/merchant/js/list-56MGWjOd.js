import{s as ie}from"./index-BFXZ3UuW.js";import{d as ne,k as de,i as ce,l as ue,r as _,c as V,a as h,o as l,q as u,e as pe,b as i,p,u as e,y as me,z as fe,A as _e,t as m,s as he,w as P,j as b}from"./index-BPwFJVSY.js";import{u as ge,_ as ve}from"./useExport-DwWpAtLW.js";import{S as D,_ as ye}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{D as Ee}from"./decimal-LZMqkRTm.js";import{B as Pe}from"./BaseDataTable-BVkRnzNI.js";import{_ as Ae}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{u as Le}from"./useDict-sWoVNiZj.js";import{u as xe}from"./useListRefresh-D-tL0eHu.js";import{u as Se,P as a}from"./usePermissions-PkVCxleQ.js";import{u as be}from"./useRequestList-DvWcnSzt.js";import{r as De,g as we,f as Te}from"./plan-X86n_BnV.js";import{f as q}from"./date-D3KeWVvs.js";import{a as Ne}from"./dict-6kmnRscN.js";import{f as $e}from"./format-3aITXa0f.js";import{_ as ke}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-D7FsVJ-c.js";import"./index-CEksU3zE.js";import"./index-CCP8zFp2.js";import"./index-DgBXOSrM.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-CxOnO8if.js";import"./index-BJi8tw4J.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";import"./plan-DCOwcHy2.js";const Ce={class:"plan-subscription-page"},Fe={key:0,class:"common-stat-card"},Re=["onClick"],Be={class:"common-stat-card__title"},Ve={class:"common-stat-card__count"},qe={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},Ie={class:"flex flex-col"},Ue={class:"flex items-center gap-2 mt-1"},ze={key:2,class:"flex justify-center items-center h-64 bg-white rounded-lg"},We=ne({name:"planSubscriptionList",__name:"list",setup(Oe){const{hasPermission:r,hasAnyPermission:w}=Se(),{t:c}=de(),A=ce(),I=ue(),U=be({requestFn:we,immediate:r(a.PLAN_LIST)}),{list:z,loading:L,total:W,refresh:g,reset:O,search:T,onPageChange:j,failed:M,other:N,failureMessage:X,setSearchParams:v}=U;xe("planSubscriptionList",g);const G=_([{field:"plan_name",header:c("planSubscription.columns.name"),style:{minWidth:"110px"}},{field:"pricing",header:c("planSubscription.columns.pricing"),template:"pricing",style:{minWidth:"160px"}},{field:"created_at",header:c("planSubscription.columns.created"),style:{minWidth:"110px"},sortable:!0,template:"created",sortField:"created_at"},{field:"updated_at",header:c("planSubscription.columns.updated"),style:{minWidth:"200px"},sortable:!0,template:"updated",sortField:"updated_at"},{field:"action",header:"",template:"action",style:{width:"50px"},alignFrozen:"right",frozen:!0}]),$=_([]),y=_([]),x=_(!1),n=_({status:null,name:"","created_at[]":[]}),k=()=>{v(n.value),T()},{isExporting:Y,handleExport:H}=ge({exportFn:Te,getParams:()=>v(n.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file...",life:3e3})}}),J=()=>{O()},K=()=>{g()},Q=()=>{r(a.PLAN_CREATE)&&I.showSelectBid(()=>{A.push({name:"planSubscriptionAdd"})})},C=({data:s})=>{r(a.PLAN_DETAIL)&&A.push({name:"planSubscriptionDetails",params:{id:s.plan_id}})},Z=s=>{r(a.PLAN_UPDATE)&&A.push({name:"planSubscriptionEdit",params:{id:s.plan_id}})},F=s=>{if(!r(a.PLAN_DELETE))return;const o=s?"Are you sure you want to delete this plan?":"Are you sure you want to delete these plans?",f=s?"Delete Plan":"Delete Plans",S=s?[s.plan_id]:y.value.map(d=>d.plan_id);window.$confirm.require({header:f,message:o,icon:"pi pi-exclamation-triangle",accept:()=>{x.value=!0,De(S).then(({code:d=1})=>{d===0&&(window.$toast.add({severity:"success",summary:"Successful",detail:"Deleted",life:3e3}),g())}).catch(d=>{console.error("Failed to delete plan(s):",d),window.$toast.add({severity:"error",summary:"Error",detail:"Failed to delete plan(s)",life:3e3})}).finally(()=>{x.value=!1})}})},ee=s=>Array.isArray(s)?Ee(s[0].amount_flat_fee||0).eq(0)?`${s[0].currency} $${s[0].amount_per_unit}`:`${s[0].currency} $${s[0].amount_per_unit} + ${s[0].amount_flat_fee}`:"",te=s=>{const{sortField:o,sortOrder:f}=s;v({sort_by:o,sort_order:f===1?"asc":"desc"}),T()},{loading:se}=Le("plan_status",s=>{$.value=Ne(s,{label:"All",value:null})}),oe=s=>{n.value.status!==s.status&&(n.value.status=s.status,v(n.value),g())},ae=V(()=>[{name:"name",label:"What are you looking for?",type:D.TEXT,placeholder:"Search for Plan name",maxlength:50,defaultValue:""},{name:"status",label:"Category",type:D.SELECT,placeholder:"All",options:$.value,loading:se,defaultValue:""}]),re=V(()=>[{name:"created_at[]",label:"Created",type:D.DATE_RANGE,placeholder:"Please select date range",defaultValue:[]}]);return(s,o)=>{var R,B;const f=ye,S=ve,d=he,le=ie;return l(),h("div",Ce,[(R=e(N))!=null&&R.stat?(l(),h("div",Fe,[(l(!0),h(me,null,fe((B=e(N))==null?void 0:B.stat,(t,E)=>(l(),h("div",{key:E,class:_e(["common-stat-card__item",{active:n.value.status===t.status}]),onClick:je=>oe(t)},[i("div",Be,m(t.status_text),1),i("div",Ve,m(t.count),1)],10,Re))),128))])):u("",!0),pe(f,{modelValue:n.value,"onUpdate:modelValue":o[0]||(o[0]=t=>n.value=t),loading:e(L),"basic-search-fields":ae.value,"advanced-search-fields":re.value,onSearch:k},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),i("div",qe,[e(r)(e(a).PLAN_EXPORT)?(l(),p(S,{key:0,loading:e(L),"export-loading":e(Y),onExport:e(H)},null,8,["loading","export-loading","onExport"])):u("",!0),e(r)(e(a).PLAN_CREATE)?(l(),p(d,{key:1,severity:"warn",label:e(c)("planSubscription.actions.createPlan"),icon:"pi pi-plus",onClick:Q},null,8,["label"])):u("",!0),e(w)([e(a).PLAN_CREATE,e(a).PLAN_DELETE])?(l(),p(le,{key:2,layout:"vertical"})):u("",!0),e(r)(e(a).PLAN_DELETE)?(l(),p(d,{key:3,icon:"pi pi-trash",loading:x.value,variant:"text",disabled:y.value.length===0,rounded:"",onClick:o[1]||(o[1]=t=>F(null))},null,8,["loading","disabled"])):u("",!0)]),e(r)(e(a).PLAN_LIST)?(l(),p(Pe,{key:1,selection:y.value,"onUpdate:selection":o[2]||(o[2]=t=>y.value=t),"row-hover":e(r)(e(a).PLAN_DETAIL),value:e(z),columns:G.value,scrollable:!0,loading:e(L),paginator:!0,rows:50,"total-records":e(W),lazy:!0,"data-key":"id","sort-mode":"single","sort-field":s.$route.query.sort_by,"sort-order":s.$route.query.sort_order==="desc"?-1:1,"show-search-bar":!1,"search-placeholder":e(c)("common.filters.search"),"type-placeholder":e(c)("common.filters.filterBy"),failed:e(M),"failure-message":e(X),"striped-rows":!0,"show-multiple-column":e(r)(e(a).PLAN_DELETE),onChangeSearch:k,onPage:o[3]||(o[3]=t=>e(j)(t)),onSort:te,onRefresh:K,onReset:J,onRowClick:C},{pricing:P(({data:t})=>[i("div",Ie,[i("span",null,m(ee(t==null?void 0:t.prices)),1),i("div",Ue,[o[4]||(o[4]=i("i",{class:"pi pi-sync"},null,-1)),o[5]||(o[5]=b()),i("span",null,m(e($e)(t)),1)])])]),created:P(({data:t})=>[b(m(e(q)(t==null?void 0:t.created_at)),1)]),updated:P(({data:t})=>[b(m((t==null?void 0:t.updated_at)&&e(q)(t==null?void 0:t.updated_at)),1)]),action:P(({data:t})=>[e(w)([e(a).PLAN_UPDATE,e(a).PLAN_DELETE])?(l(),p(Ae,{key:0,"is-show-detail":!1,"is-show-edit":e(r)(e(a).PLAN_UPDATE),"is-show-delete":e(r)(e(a).PLAN_DELETE),loading:t.__loading,onDetail:E=>C(t),onEdit:E=>Z(t),onDelete:E=>F(t)},null,8,["is-show-edit","is-show-delete","loading","onDetail","onEdit","onDelete"])):u("",!0)]),_:1},8,["selection","row-hover","value","columns","loading","total-records","sort-field","sort-order","search-placeholder","type-placeholder","failed","failure-message","show-multiple-column"])):(l(),h("div",ze,o[6]||(o[6]=[i("p",{class:"text-gray-500"}," You don't have permission to view the plan list. ",-1)])))])}}}),xt=ke(We,[["__scopeId","data-v-ac4f78c2"]]);export{xt as default};
