import{_ as P}from"./BaseTag-dd68UHCy.js";import{U as B,d as W,B as C,i as L,r as u,aa as S,a as R,o as k,e as n,w as q,u as a}from"./index-BPwFJVSY.js";import{B as A}from"./BaseDataTable-BVkRnzNI.js";import{u as T}from"./useListRefresh-D-tL0eHu.js";import{u as E}from"./useRequestList-DvWcnSzt.js";import{d as F}from"./tagStatus-CiU0ihd_.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CxOnO8if.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./index-BFXZ3UuW.js";import"./date-D3KeWVvs.js";import"./dayjs.min-Bar1rHDB.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./invoice-B6mg1HKE.js";import"./plan-DCOwcHy2.js";import"./transaction-CqiD1t2Y.js";const N=r=>B("/payout/list",{params:r}),z={class:"payout-list-page"},D=W({name:"payoutList",__name:"list",setup(r){const m=C(),p=L(),d=u([{field:"currency",header:"Currency",style:{minWidth:"100px"}},{field:"amount",header:"Gross",style:{minWidth:"120px"}},{field:"payment_amount",header:"Payout",style:{minWidth:"120px"}},{field:"account_name",header:"Account name",style:{minWidth:"150px"}},{field:"bsb",header:"BSB",style:{minWidth:"100px"}},{field:"account_no",header:"Account",style:{minWidth:"150px"}},{field:"arrive_by",header:"Arrive by",style:{minWidth:"150px"}},{field:"status",header:"Status",template:"status",style:{minWidth:"120px"}}]),{list:c,loading:f,total:y,refresh:h,onPageChange:_,failed:g,failureMessage:v,search:i,setSearchParams:l}=E({requestFn:N,immediate:!1});T("payoutList",h);const b=u([{label:"All",value:""},{label:"Pending",value:0},{label:"Success",value:1},{label:"Fail",value:2},{label:"Processing",value:3}]),x=t=>{var e;return((e=b.value.find(o=>o.value===t))==null?void 0:e.label)||""},w=t=>{p.push({name:"transactionsList",query:{"created_at[]":[t.data.arrive_by,t.data.arrive_by]}})};return S(()=>{const t=m.query;t&&t.status!==void 0?(l({status:Number(t.status)}),i()):(l({status:void 0}),i())}),(t,e)=>{const o=P;return k(),R("div",z,[n(A,{"row-hover":!0,value:a(c),columns:d.value,scrollable:!0,"show-multiple-column":!1,loading:a(f),paginator:!0,rows:50,"total-records":a(y),lazy:!0,"data-key":"id","show-search-bar":!1,failed:a(g),"failure-message":a(v),"striped-rows":!0,onPage:e[0]||(e[0]=s=>a(_)(s)),onRowClick:e[1]||(e[1]=s=>w(s))},{status:q(({data:s})=>[n(o,{text:x(s.status),type:a(F)(s.status)},null,8,["text","type"])]),_:1},8,["value","columns","loading","total-records","failed","failure-message"])])}}}),ct=G(D,[["__scopeId","data-v-65596e18"]]);export{ct as default};
