import{_ as ue}from"./BaseTag-dd68UHCy.js";import{_ as de}from"./BaseCardType-BnVjnEGA.js";import{d as ce,k as pe,l as fe,B as _e,i as ye,r as n,c as M,aa as ge,a as U,o as m,e as c,b as p,p as f,q as d,u as e,s as C,w as u,t as T,j as $,X as he}from"./index-BPwFJVSY.js";import{s as ve}from"./index-BFXZ3UuW.js";import{B as Ee}from"./BaseDataTable-BVkRnzNI.js";import{_ as Ce}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{u as Te,_ as be}from"./useExport-DwWpAtLW.js";import{S as b,_ as xe}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{u as F}from"./useDict-sWoVNiZj.js";import{u as Se}from"./useListRefresh-D-tL0eHu.js";import{u as we,P as a}from"./usePermissions-PkVCxleQ.js";import{u as ke}from"./useRequestList-DvWcnSzt.js";import{r as V,j as De,k as Le}from"./customer-M3EzHCil.js";import{f as B}from"./date-D3KeWVvs.js";import{a as N}from"./dict-6kmnRscN.js";import{a as Re}from"./tagStatus-CiU0ihd_.js";import{_ as Oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./getAssetsFile-DC-dlhh8.js";import"./BillbuddyLogo-FvDXaJ4h.js";import"./PMI-logo-4SeFW8S8.js";import"./logo-CiIwT2Ho.js";import"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import"./color-DqhIgFrh.js";import"./invioce-payment-Bs21HoFI.js";import"./isUpdated-DXQm7BuR.js";import"./login-bg-kShksnUz.js";import"./index-CxOnO8if.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";import"./invoice-B6mg1HKE.js";import"./plan-DCOwcHy2.js";import"./transaction-CqiD1t2Y.js";const Pe={class:"customer-page"},Ae={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},Me={class:"flex items-center"},Ue={class:"confirmation-content"},$e={key:0},Fe=ce({name:"customersList",__name:"list",setup(Ve){const{hasPermission:r,hasAnyPermission:S}=we(),{t:l}=pe(),W=fe(),I=_e(),w=ye(),q=n([{field:"name",header:"Customer Name",template:"name",style:{minWidth:"160px"}},{field:"email_primary",header:"Email",style:{minWidth:"110px"}},{field:"phone_mobile",header:"Phone",style:{minWidth:"120px"}},{field:"default_payment_method",header:"Default Payment Method",template:"default_payment_method",style:{minWidth:"200px"}},{field:"created_at",header:"Created",template:"created_at",sortable:!0,style:{minWidth:"200px"}},{field:"total_spend",header:"Total Spend",sortable:!0,style:{minWidth:"160px"}},{field:"payments",header:"Payments",sortable:!0,style:{minWidth:"160px"}},{field:"last_payment",header:"Last Payment",template:"last_payment",sortable:!0,style:{minWidth:"200px"}},{field:"action",header:"",template:"action",alignFrozen:"right",frozen:!0}]),{list:z,loading:j,total:X,refresh:_,search:k,onPageChange:G,failed:H,failureMessage:J,loading:D,setSearchParams:y}=ke({requestFn:De,immediate:!1}),{isExporting:K,handleExport:Q}=Te({exportFn:Le,getParams:()=>y(i.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file...",life:3e3})}});Se("customersList",_);const Y=o=>{const{sortField:s,sortOrder:x}=o;y({sort_by:s,sort_order:x===1?"asc":"desc"}),k()},i=n({keyword:"",category:null,status:null,"created_at[]":[]}),v=n([]),Z=n(),L=n([]),R=n([]),{getLabel:ee,loading:te}=F("customer_status",o=>{R.value=N(o,{label:"All",value:null})}),{loading:oe}=F("customer_type",o=>{L.value=N(o,{label:"All Customers",value:null})}),se=M(()=>[{name:"keyword",label:"What are you looking for?",type:b.TEXT,placeholder:"Search for Customer name, email, etc.",maxlength:50,defaultValue:""},{name:"category",label:"Category",type:b.SELECT,placeholder:"All",options:L.value,loading:oe,defaultValue:""},{name:"status",label:"Status",type:b.SELECT,placeholder:"All",options:R.value,loading:te,defaultValue:null}]),ae=M(()=>[{name:"created_at[]",label:"Created At",type:b.DATE_RANGE,placeholder:"Please select date range",defaultValue:[]}]),g=n(!1),h=n({id:"",name:"",email_primary:""}),O=({data:o})=>{r(a.CUSTOMER_DETAIL)&&w.push({name:"customersDetail",params:{id:o.customer_id}})},re=()=>{r(a.CUSTOMER_CREATE)&&W.showSelectBid(()=>{w.push({name:"customersCreateInvite"})})},le=o=>{r(a.CUSTOMER_DELETE)&&(h.value=o,g.value=!0)},ie=()=>{r(a.CUSTOMER_DELETE)&&(g.value=!1,V([h.value.customer_id]).then(o=>{o.code===0&&(window.$toast.add({severity:"success",summary:l("common.success"),detail:l("customersPage.messages.customerDeleted"),life:3e3}),_())}).catch(o=>{console.error("Failed to delete customer:",o),window.$toast.add({severity:"error",summary:l("common.error"),detail:"Failed to delete customer",life:3e3})}),h.value={id:"",name:"",email_primary:""})},P=()=>{y(i.value),k()},ne=()=>{if(!r(a.CUSTOMER_DELETE))return;const o=v.value.map(s=>s.customer_id);window.$confirm.require({header:"Delete Customers",message:"Are you sure you want to delete these customers?",accept:()=>{V(o).then(s=>{s.code===0&&(window.$toast.add({severity:"success",summary:l("common.success"),detail:"Customers deleted successfully",life:3e3}),_())}).catch(s=>{console.error("Failed to delete customers:",s),window.$toast.add({severity:"error",summary:l("common.error"),detail:"Failed to delete customers",life:3e3})})}})};return ge(()=>{const o=I.query;o&&Object.keys(o).length>0?(i.value={keyword:typeof o.keyword=="string"?o.keyword:"",category:o.category?Number(o.category):"",status:o.status?Number(o.status):null,"created_at[]":Array.isArray(o["created_at[]"])?o["created_at[]"]:[]},y(i.value),_()):(i.value={keyword:"",category:"",status:null,"created_at[]":[]},y(i.value),_())}),(o,s)=>{const x=de,me=ue;return m(),U("div",Pe,[c(xe,{modelValue:i.value,"onUpdate:modelValue":s[0]||(s[0]=t=>i.value=t),loading:e(D),"basic-search-fields":se.value,"advanced-search-fields":ae.value,onSearch:P},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),p("div",Ae,[e(r)(e(a).CUSTOMER_EXPORT)?(m(),f(be,{key:0,loading:e(D),"export-loading":e(K),onExport:e(Q)},null,8,["loading","export-loading","onExport"])):d("",!0),e(r)(e(a).CUSTOMER_CREATE)?(m(),f(e(C),{key:1,label:"Add New Customer",icon:"pi pi-plus",severity:"warn",onClick:re},null,8,["onClick"])):d("",!0),e(S)([e(a).CUSTOMER_CREATE,e(a).CUSTOMER_DELETE])?(m(),f(e(ve),{key:2,layout:"vertical"})):d("",!0),e(r)(e(a).CUSTOMER_DELETE)?(m(),f(e(C),{key:3,icon:"pi pi-trash",variant:"text",disabled:v.value.length===0,rounded:"",onClick:ne},null,8,["disabled"])):d("",!0),s[5]||(s[5]=p("div",{class:"flex-1"},null,-1))]),e(r)(e(a).CUSTOMER_LIST)?(m(),f(Ee,{key:0,ref_key:"customerTable",ref:Z,selection:v.value,"onUpdate:selection":s[1]||(s[1]=t=>v.value=t),"row-hover":e(r)(e(a).CUSTOMER_DETAIL),"show-search-bar":!1,value:e(z),columns:q.value,scrollable:!0,"show-multiple-column":e(r)(e(a).CUSTOMER_DELETE),loading:e(j),paginator:!0,rows:50,"total-records":e(X),lazy:!0,"data-key":"id",failed:e(H),"failure-message":e(J),"striped-rows":!0,style:{"--frozen-column-border-bottom":"-8px"},onChangeSearch:P,onPage:s[2]||(s[2]=t=>e(G)(t)),onRowClick:O,onSort:Y},{created_at:u(({data:t})=>[$(T(e(B)(t.created_at)),1)]),last_payment:u(({data:t})=>[$(T(e(B)(t.last_payment)),1)]),name:u(({data:t})=>[p("span",null,T(t==null?void 0:t.name),1)]),default_payment_method:u(({data:t})=>{var E,A;return[p("div",Me,[c(x,{"card-type":(E=t==null?void 0:t.customer_banking[0])==null?void 0:E.credit_brand,text:(A=t==null?void 0:t.customer_banking[0])==null?void 0:A.account_no,"is-show-card-number":!0},null,8,["card-type","text"])])]}),status:u(({data:t})=>[c(me,{text:e(ee)(t.status),type:e(Re)(t.status)},null,8,["text","type"])]),action:u(({data:t})=>[e(S)([e(a).CUSTOMER_DELETE])?(m(),f(Ce,{key:0,"content-width":"30px","is-show-detail":!1,"is-show-edit":!1,"is-show-delete":e(r)(e(a).CUSTOMER_DELETE),loading:t.__loading,onDetail:E=>O(t),onDelete:E=>le(t)},null,8,["is-show-delete","loading","onDetail","onDelete"])):d("",!0)]),_:1},8,["selection","row-hover","value","columns","show-multiple-column","loading","total-records","failed","failure-message"])):d("",!0),c(e(he),{visible:g.value,"onUpdate:visible":s[4]||(s[4]=t=>g.value=t),style:{width:"450px"},header:e(l)("customersPage.dialogs.confirmDelete"),modal:!0},{footer:u(()=>[c(e(C),{label:e(l)("common.no"),icon:"pi pi-times",text:"",onClick:s[3]||(s[3]=t=>g.value=!1)},null,8,["label"]),c(e(C),{label:e(l)("common.yes"),icon:"pi pi-check",text:"",onClick:ie},null,8,["label"])]),default:u(()=>[p("div",Ue,[s[6]||(s[6]=p("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),h.value?(m(),U("span",$e,T(e(l)("customersPage.dialogs.deleteConfirmMessage",{name:h.value.name})),1)):d("",!0)])]),_:1},8,["visible","header"])])}}}),Rt=Oe(Fe,[["__scopeId","data-v-688d97a2"]]);export{Rt as default};
