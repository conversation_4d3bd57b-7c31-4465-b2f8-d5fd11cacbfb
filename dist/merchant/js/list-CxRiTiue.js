import{d as z,k as P,J as R,r as i,c as W,a as I,o as q,e as r,u as o,w as n,s as M,j as O,t as U,b as p,X as j}from"./index-BPwFJVSY.js";import{_ as G}from"./BaseTag-dd68UHCy.js";import{S as w,_ as J}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{B as X}from"./BaseDataTable-BVkRnzNI.js";import{u as Y}from"./useDict-sWoVNiZj.js";import{u as H}from"./useListRefresh-D-tL0eHu.js";import{u as K}from"./useRequestList-DvWcnSzt.js";import{r as Q,g as Z}from"./downloadCenter-xnP8j9T6.js";import{f as ee}from"./date-D3KeWVvs.js";import{a as te}from"./dict-6kmnRscN.js";import{b as ae}from"./tagStatus-CiU0ihd_.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CCP8zFp2.js";import"./index-CEksU3zE.js";import"./index-DgBXOSrM.js";import"./index-D7FsVJ-c.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-CxOnO8if.js";import"./index-BJi8tw4J.js";import"./index-BFXZ3UuW.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";import"./invoice-B6mg1HKE.js";import"./plan-DCOwcHy2.js";import"./transaction-CqiD1t2Y.js";const se=(f,l)=>{const s=document.createElement("a");return s.target="_blank",s.href=f,l&&(s.download=l),s.click(),s.remove(),!0},le={class:"download-center-page"},re=z({name:"downloadCenterList",__name:"list",setup(f){const{t:l}=P(),s=R(),S=i([{field:"id",header:"ID",style:{minWidth:"100px"}},{field:"file_name",header:"File Name",style:{minWidth:"200px"}},{field:"status",header:"Status",template:"status",style:{minWidth:"120px"}},{field:"created_at",header:l("common.created"),template:"created_at",sortable:!0,style:{minWidth:"200px"}},{field:"action",header:"",template:"action",alignFrozen:"right",frozen:!0,style:{width:"50px"}}]),{list:x,loading:_,total:C,refresh:g,setSearchParams:h,search:v,onPageChange:D,failed:F,failureMessage:k}=K({requestFn:Z});H("downloadCenterList",g);const T=t=>{const{sortField:a,sortOrder:c}=t;h({sort_by:a,sort_order:c===1?"asc":"desc"}),v()},m=i({status:"","created_at[]":[]}),y=i([]),B=W(()=>[{name:"created_at[]",label:l("common.created"),type:w.DATE_RANGE,placeholder:"Select Date Range",defaultValue:[]},{name:"status",label:"Status",type:w.SELECT,placeholder:"All",options:y.value,defaultValue:""}]),d=i(!1),b=i({}),L=()=>{h(m.value),v()},V=t=>{se(t.url,t.file_name)},A=()=>{b.value.id&&Q(b.value.id.toString()).then(t=>{t.code===0?(s.add({severity:"success",summary:l("common.success"),detail:"File deleted successfully",life:3e3}),d.value=!1,g()):s.add({severity:"error",summary:l("common.error"),detail:t.message||"Failed to delete file",life:3e3})}).catch(t=>{s.add({severity:"error",summary:l("common.error"),detail:t.message||"Failed to delete file",life:3e3})})},{getLabel:E}=Y("download_status",t=>{y.value=te(t,{label:"All",value:""})});return(t,a)=>{const c=J,N=G,u=M,$=j;return q(),I("div",le,[r(c,{modelValue:m.value,"onUpdate:modelValue":a[0]||(a[0]=e=>m.value=e),loading:o(_),"basic-search-fields":B.value,onSearch:L},null,8,["modelValue","loading","basic-search-fields"]),r(X,{value:o(x),columns:S.value,scrollable:!0,"show-multiple-column":!1,loading:o(_),paginator:!0,rows:50,"total-records":o(C),lazy:!0,"data-key":"id","show-search-bar":!1,failed:o(F),"failure-message":o(k),"striped-rows":!0,onPage:a[1]||(a[1]=e=>o(D)(e)),onSort:T},{created_at:n(({data:e})=>[O(U(o(ee)(e.created_at)),1)]),status:n(({data:e})=>[r(N,{text:o(E)(e.status),type:o(ae)(e.status)},null,8,["text","type"])]),action:n(({data:e})=>[r(u,{severity:"secondary",icon:"pi pi-download",onClick:ie=>V(e)},null,8,["onClick"])]),_:1},8,["value","columns","loading","total-records","failed","failure-message"]),r($,{visible:d.value,"onUpdate:visible":a[3]||(a[3]=e=>d.value=e),modal:"",header:"Confirm Delete",style:{width:"450px"}},{footer:n(()=>[r(u,{label:"No",icon:"pi pi-times",severity:"secondary",onClick:a[2]||(a[2]=e=>d.value=!1)}),r(u,{label:"Yes",icon:"pi pi-check",severity:"danger",onClick:A})]),default:n(()=>[a[4]||(a[4]=p("div",{class:"confirmation-content"},[p("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}}),p("span",null,"Are you sure you want to delete this file?")],-1))]),_:1,__:[4]},8,["visible"])])}}}),Ie=oe(re,[["__scopeId","data-v-627e6cc4"]]);export{Ie as default};
