import{d as R,k as U,J as j,r as c,c as q,a as D,o as I,e as n,u as a,w as l,s as z,j as d,t as o,q as O,b as t,X}from"./index-BPwFJVSY.js";import{B as H}from"./BaseDataTable-BVkRnzNI.js";import{_ as J}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{_ as G}from"./BaseTag-dd68UHCy.js";import{S as K,_ as Q}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{F as m}from"./index-Cui9J1ai.js";import{u as Y}from"./useListRefresh-D-tL0eHu.js";import{u as Z}from"./useRequestList-DvWcnSzt.js";import{g as tt,a as et}from"./invoice-CnWRWdDk.js";import{f as u}from"./date-D3KeWVvs.js";import{_ as st}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CxOnO8if.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./index-BFXZ3UuW.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./dayjs.min-Bar1rHDB.js";const at={class:"invoice-page"},ot={key:0,class:"flex flex-col gap-4"},lt={class:"border-b pb-4 mb-4"},it={class:"text-xl font-bold mb-3"},nt={class:"flex justify-between items-start"},rt={class:"flex items-center"},dt={class:"text-right"},mt={class:"font-medium"},ut={class:"grid grid-cols-2 gap-6 mb-6"},pt={class:"font-medium"},ct={class:"font-medium"},ft={class:"border-t border-gray-200 pt-4 mb-6"},vt={class:"grid grid-cols-2 gap-6"},bt={class:"font-medium"},xt={class:"border-t border-gray-200 pt-4"},_t={class:"grid grid-cols-2 gap-6"},gt={class:"flex justify-end gap-2 mt-6 pt-4 border-t"},yt=R({name:"invoiceList",__name:"list",setup(ht){const{t:S}=U(),T=j(),A=c([{field:"id",header:"ID",style:{width:"60px"}},{field:"invoice_number",header:"Invoice Number",style:{minWidth:"140px"}},{field:"date",header:"Issue Date",style:{minWidth:"120px"},template:"issue_date"},{field:"due_date",header:"Due Date",style:{minWidth:"120px"},template:"due_date"},{field:"sub_total",header:"Subtotal",style:{minWidth:"100px"},template:"sub_total"},{field:"total_tax",header:"Tax",style:{minWidth:"100px"},template:"total_tax"},{field:"amount_due",header:"Amount Due",style:{minWidth:"120px"},template:"amount_due"},{field:"amount_paid",header:"Amount Paid",style:{minWidth:"120px"},template:"amount_paid"},{field:"status",header:"Status",style:{minWidth:"120px"},template:"status"},{field:"created_at",header:S("common.created","Created Date"),template:"created_at",sortable:!0,style:{minWidth:"150px"}},{field:"action",header:"",template:"action",style:{width:"50px"},alignFrozen:"right",frozen:!0}]),{list:C,loading:b,total:w,refresh:k,search:x,onPageChange:B,failed:F,failureMessage:V,setSearchParams:_}=Z({requestFn:et});Y("invoiceList",k);const g=r=>r&&{AUTHORISED:"info",PAID:"paid",VOIDED:"failed",DRAFT:"upcoming"}[r]||"upcoming",W=r=>{const{sortField:e,sortOrder:v}=r;_({sort_by:e,sort_order:v===1?"asc":"desc"}),x()},f=c({invoice_number:""}),N=q(()=>[{name:"invoice_number",label:"Invoice Number",type:K.TEXT,placeholder:"Search by invoice number",maxlength:50,defaultValue:""}]),p=c(!1),i=c(null),P=r=>{tt({id:r.id}).then(e=>{e.code===0&&(i.value=e.data,p.value=!0)}).catch(()=>{T.add({severity:"error",summary:"Error",detail:"Failed to load invoice details",life:3e3})})},E=()=>{_(f.value),x()};return(r,e)=>{const v=Q,y=G,h=z,L=J,$=H,M=X;return I(),D("div",at,[n(v,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=s=>f.value=s),loading:a(b),"basic-search-fields":N.value,onSearch:E},null,8,["modelValue","loading","basic-search-fields"]),n($,{"show-search-bar":!1,value:a(C),columns:A.value,scrollable:!0,"show-multiple-column":!1,loading:a(b),paginator:!0,rows:20,"total-records":a(w),lazy:!0,"data-key":"id",failed:a(F),"failure-message":a(V),"striped-rows":!0,"search-placeholder":"Search invoices...",onPage:e[1]||(e[1]=s=>a(B)(s)),onSort:W},{issue_date:l(({data:s})=>[d(o(a(u)(s.date)),1)]),due_date:l(({data:s})=>[d(o(a(u)(s.due_date)),1)]),sub_total:l(({data:s})=>[d(o(a(m).formatAmount(s.sub_total)),1)]),total_tax:l(({data:s})=>[d(o(a(m).formatAmount(s.total_tax)),1)]),amount_due:l(({data:s})=>[d(o(a(m).formatAmount(s.amount_due)),1)]),amount_paid:l(({data:s})=>[d(o(a(m).formatAmount(s.amount_paid)),1)]),status:l(({data:s})=>[n(y,{text:s.status,type:g(s.status)},null,8,["text","type"])]),created_at:l(({data:s})=>[d(o(a(u)(s.created_at)),1)]),action:l(({data:s})=>[n(L,{"content-width":"50px"},{default:l(()=>[n(h,{icon:"pi pi-eye",text:"",title:"View Details",onClick:Dt=>P(s)},null,8,["onClick"])]),_:2},1024)]),_:1},8,["value","columns","loading","total-records","failed","failure-message"]),n(M,{visible:p.value,"onUpdate:visible":e[3]||(e[3]=s=>p.value=s),modal:"",header:"Invoice Details",style:{width:"800px"},closable:!0},{default:l(()=>[i.value?(I(),D("div",ot,[t("div",lt,[t("h2",it," Invoice "+o(i.value.invoice_number),1),t("div",nt,[t("div",rt,[e[4]||(e[4]=t("span",{class:"mr-2"},"Status:",-1)),n(y,{text:i.value.status,type:g(i.value.status)},null,8,["text","type"])]),t("div",dt,[e[6]||(e[6]=t("div",{class:"mb-1"},[t("span",{class:"text-gray-600 mr-2"},"Issue Date:")],-1)),t("div",null,[e[5]||(e[5]=t("span",{class:"text-gray-600 mr-2"},"Due Date:",-1)),t("span",mt,o(a(u)(i.value.due_date)),1)])])])]),t("div",ut,[t("div",null,[e[7]||(e[7]=t("h3",{class:"text-gray-600 text-sm mb-1"}," Invoice Number ",-1)),t("p",pt,o(i.value.invoice_number),1)]),e[9]||(e[9]=t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Contact ID "),t("p",{class:"font-mono text-sm break-all"})],-1)),e[10]||(e[10]=t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Type "),t("p",{class:"font-medium"})],-1)),t("div",null,[e[8]||(e[8]=t("h3",{class:"text-gray-600 text-sm mb-1"}," Currency ",-1)),t("p",ct,o(i.value.currency),1)])]),t("div",ft,[e[15]||(e[15]=t("h3",{class:"font-bold text-lg mb-3"}," Financial Details ",-1)),t("div",vt,[t("div",null,[e[11]||(e[11]=t("h3",{class:"text-gray-600 text-sm mb-1"}," Subtotal ",-1)),t("p",bt,o(a(m).formatAmount(i.value.sub_total)),1)]),e[12]||(e[12]=t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Total Tax "),t("p",{class:"font-medium"})],-1)),e[13]||(e[13]=t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Amount Due "),t("p",{class:"text-red-500 font-bold"})],-1)),e[14]||(e[14]=t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Amount Paid "),t("p",{class:"text-green-500 font-bold"})],-1))])]),t("div",xt,[e[18]||(e[18]=t("h3",{class:"font-bold text-lg mb-3"}," System Information ",-1)),e[19]||(e[19]=t("div",{class:"grid grid-cols-2 gap-6 mb-3"},[t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Invoice ID "),t("p",{class:"font-mono text-sm break-all"})]),t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Tenant ID "),t("p",{class:"font-mono text-sm break-all"})])],-1)),t("div",_t,[t("div",null,[e[16]||(e[16]=t("h3",{class:"text-gray-600 text-sm mb-1"}," Created Date ",-1)),t("div",null,o(a(u)(i.value.created_at)),1)]),e[17]||(e[17]=t("div",null,[t("h3",{class:"text-gray-600 text-sm mb-1"}," Updated Date ")],-1))])]),t("div",gt,[n(h,{type:"button",label:"Close",class:"p-button-primary",onClick:e[2]||(e[2]=s=>p.value=!1)})])])):O("",!0)]),_:1},8,["visible"])])}}}),Kt=st(yt,[["__scopeId","data-v-1da5714d"]]);export{Kt as default};
