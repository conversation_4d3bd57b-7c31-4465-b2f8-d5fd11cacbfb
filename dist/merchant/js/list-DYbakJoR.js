import{_ as oe}from"./BaseTag-dd68UHCy.js";import{_ as ne}from"./BaseCardType-BnVjnEGA.js";import{u as le,_ as ie}from"./useExport-DwWpAtLW.js";import{S as i,_ as ue}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{F as de}from"./index-Cui9J1ai.js";import{d as ce,i as me,B as pe,r as c,c as w,f as _e,a as n,o as s,q as _,e as T,b as y,p as N,u as a,y as fe,z as he,A as ye,t as m,w as p,j as ve}from"./index-BPwFJVSY.js";import{B as be}from"./BaseDataTable-BVkRnzNI.js";import{u as C}from"./useDict-sWoVNiZj.js";import{u as ge,P as v}from"./usePermissions-PkVCxleQ.js";import{u as Te}from"./useRequestList-DvWcnSzt.js";import{T as P,a as V}from"./transaction-CqiD1t2Y.js";import{g as xe,e as Se}from"./transactions-CkZWY3pR.js";import{f as F}from"./date-D3KeWVvs.js";import{a as L}from"./dict-6kmnRscN.js";import{_ as Ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./getAssetsFile-DC-dlhh8.js";import"./BillbuddyLogo-FvDXaJ4h.js";import"./PMI-logo-4SeFW8S8.js";import"./logo-CiIwT2Ho.js";import"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import"./color-DqhIgFrh.js";import"./invioce-payment-Bs21HoFI.js";import"./isUpdated-DXQm7BuR.js";import"./login-bg-kShksnUz.js";import"./index-D7FsVJ-c.js";import"./index-CEksU3zE.js";import"./index-CCP8zFp2.js";import"./index-DgBXOSrM.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-CxOnO8if.js";import"./index-BJi8tw4J.js";import"./index-BFXZ3UuW.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";const Ae={key:0,class:"common-stat-card"},De=["onClick"],ke={class:"common-stat-card__title"},Re={class:"common-stat-card__count"},we={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},Ne={key:0},Ce={key:0},Pe={key:0,class:"w-30"},Ve={key:2,class:"flex justify-center items-center h-64 bg-white rounded-lg"},Fe=ce({name:"TransactionList",__name:"list",setup(Le){const{hasPermission:f}=ge(),B=Te({requestFn:xe}),q=me(),I=pe(),{list:W,loading:b,total:X,refresh:h,search:x,onPageChange:$,failed:O,failureMessage:M,other:S,setSearchParams:u}=B,j=c([{field:"payment_amount",header:"Amount",style:{minWidth:"110px"},template:"payment_amount",sortable:!0},{field:"status",header:"Status",style:{minWidth:"100px"},template:"status"},{field:"payment_method",header:"Payment Method",template:"payment_method",style:{minWidth:"120px"}},{field:"trans_invoice_number",header:"TXN Invoice Number",style:{minWidth:"120px"}},{field:"remark",header:"Description",style:{minWidth:"150px"}},{field:"customer",header:"Customer",template:"customer",style:{minWidth:"150px"}},{field:"date",header:"Date",style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"date"},{field:"refunded_date",header:"Refunded date",style:{minWidth:"150px"},sortable:!0,template:"refunded_date"}]),r=c({status:null,amount:"","created_at[]":[],trans_type:null,trans_invoice_number:"",description:"",credit_brand:"","refunded_date[]":[]}),z=c([]),E=c([]),A=c([]),D=c([]),G=w(()=>[{name:"keyword",label:"Search Transactions",type:i.TEXT,placeholder:"Search for a transaction",defaultValue:""},{name:"status",label:"Status",type:i.SELECT,placeholder:"All",options:E.value,defaultValue:""}]),U=w(()=>[{name:"trans_invoice_number",label:"TXN Invoice Number",type:i.TEXT,placeholder:"Search for a TXN Invoice Number",defaultValue:""},{name:"description",label:"Description",type:i.TEXT,placeholder:"Search for a description",defaultValue:""},{name:"credit_brand",label:"Payment Type",type:i.SELECT,placeholder:"All",options:A.value,defaultValue:""},{name:"created_at[]",label:"Date",type:i.DATE_RANGE,placeholder:"Select Date Range",defaultValue:D.value},{name:"refunded_date[]",label:"Refunded Date",type:i.DATE_RANGE,placeholder:"Select Refunded Date Range",defaultValue:D.value}]),Y=()=>{u(r.value,["refunded_date[]"]),x()},H=()=>{h()},J=t=>{const{sortField:o,sortOrder:g}=t;u({sort_by:o,sort_order:g===1?"asc":"desc"}),x()},{isExporting:K,handleExport:Q}=le({exportFn:Se,getParams:()=>u(r.value),onExportStart:()=>{window.$toast.add({severity:"info",summary:"Export Started",detail:"Preparing your export file...",life:3e3})}}),Z=({data:t})=>{f(v.TRANS_DETAIL)&&q.push({name:"transactionsDetail",params:{id:t.id}})},{getLabel:ee}=C("trans_status",t=>{z.value=t,E.value=L(t,{label:"All",value:null}).filter(o=>o.value!==3)});C("credit_brand",t=>{A.value=L(t)});const te=t=>{r.value.status!==t.status&&(r.value.status=t.status,u(r.value),h())};return _e(()=>{const t=I.query;t&&Object.keys(t).length>0?(r.value={status:t.status?Number(t.status):null,amount:"","created_at[]":Array.isArray(t["created_at[]"])?t["created_at[]"]:[],trans_type:t.trans_type?Number(t.trans_type):null,trans_invoice_number:t.trans_invoice_number,description:t.description,credit_brand:t.credit_brand,"refunded_date[]":Array.isArray(t["refunded_date[]"])?t["refunded_date[]"]:[]},u(r.value),h()):(r.value={status:null,amount:"","created_at[]":[],trans_type:null,trans_invoice_number:"",description:"",credit_brand:"","refunded_date[]":[]},u(r.value),h())}),(t,o)=>{var k,R;const g=ue,ae=ie,re=ne,se=oe;return s(),n("div",null,[(k=a(S))!=null&&k.stat?(s(),n("div",Ae,[(s(!0),n(fe,null,he((R=a(S))==null?void 0:R.stat,(e,l)=>(s(),n("div",{key:l,class:ye(["common-stat-card__item",{active:r.value.status===e.status}]),onClick:d=>te(e)},[y("div",ke,m(e.status_text),1),y("div",Re,m(e.count),1)],10,De))),128))])):_("",!0),T(g,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=e=>r.value=e),loading:a(b),"basic-search-fields":G.value,"advanced-search-fields":U.value,onSearch:Y},null,8,["modelValue","loading","basic-search-fields","advanced-search-fields"]),y("div",we,[a(f)(a(v).TRANS_EXPORT)?(s(),N(ae,{key:0,loading:a(b),"export-loading":a(K),onExport:a(Q)},null,8,["loading","export-loading","onExport"])):_("",!0)]),a(f)(a(v).TRANS_LIST)?(s(),N(be,{key:1,"row-hover":a(f)(a(v).TRANS_DETAIL),value:a(W),columns:j.value,"show-edit-column":!1,"show-search-bar":!1,scrollable:!0,"show-multiple-column":!1,loading:a(b),paginator:!0,rows:50,"total-records":a(X),lazy:!0,"data-key":"id","sort-mode":"single","sort-field":t.$route.query.sort_by,"sort-order":t.$route.query.sort_order==="desc"?-1:1,"search-placeholder":"Search","type-placeholder":"Filter By",failed:a(O),"failure-message":a(M),"striped-rows":!0,onPage:a($),onSort:J,onRefresh:H,onRowClick:Z},{payment_amount:p(({data:e})=>[ve(m(a(de).formatAmount(e==null?void 0:e.payment_amount)),1)]),date:p(({data:e})=>[(e==null?void 0:e.trans_type)===a(V).Payment?(s(),n("span",Ne,m(a(F)(e.created_at)),1)):_("",!0)]),refunded_date:p(({data:e})=>[(e==null?void 0:e.trans_type)===a(V).Refund?(s(),n("span",Ce,m(a(F)(e.created_at)),1)):_("",!0)]),payment_method:p(({data:e})=>{var l,d;return[T(re,{text:(l=e.customer_banking)==null?void 0:l.account_no,"card-type":(d=e.customer_banking)==null?void 0:d.credit_brand,"is-show-card-number":!0},null,8,["text","card-type"])]}),customer:p(({data:e})=>{var l,d;return[(l=e==null?void 0:e.customer)!=null&&l.name?(s(),n("span",Pe,m((d=e==null?void 0:e.customer)==null?void 0:d.name),1)):_("",!0)]}),status:p(({data:e})=>[T(se,{type:e.status===a(P).SUCCEEDED?"paid":e.status===a(P).FAILED?"failed":"upcoming",text:a(ee)(e.status),class:"w-30"},null,8,["type","text"])]),_:1},8,["row-hover","value","columns","loading","total-records","sort-field","sort-order","failed","failure-message","onPage"])):(s(),n("div",Ve,o[1]||(o[1]=[y("p",{class:"text-gray-500"}," You don't have permission to view the transaction list. ",-1)])))])}}}),St=Ee(Fe,[["__scopeId","data-v-d93bec42"]]);export{St as default};
