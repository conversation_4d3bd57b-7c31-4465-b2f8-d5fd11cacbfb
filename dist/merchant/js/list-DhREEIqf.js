import{s as ue}from"./index-BlFKIn6Y.js";import{s as ce}from"./index-Cbodd6BQ.js";import{d as de,J as me,i as pe,l as ve,r as u,c as fe,a as F,o as c,e as i,b as n,p,q as d,u as s,s as ye,w as o,j as A,t as v,X as _e,F as be,m as he,bH as ge,bF as N,bI as Se}from"./index-BPwFJVSY.js";import{_ as we}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{_ as Ce}from"./BaseTag-dd68UHCy.js";import{S as xe,_ as Ae}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{t as ke,c as Be,e as Ee}from"./index.esm-DOIZz6Kf.js";import{B as De}from"./BaseDataTable-BVkRnzNI.js";import{u as Te}from"./useDict-sWoVNiZj.js";import{u as $e}from"./useListRefresh-D-tL0eHu.js";import{u as Le,P as S}from"./usePermissions-PkVCxleQ.js";import{u as Ve}from"./useRequestList-DvWcnSzt.js";import{f as Fe}from"./date-D3KeWVvs.js";import{a as Ne}from"./dict-6kmnRscN.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-CCP8zFp2.js";import"./index-DgBXOSrM.js";import"./index-D7FsVJ-c.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-CxOnO8if.js";import"./index-BJi8tw4J.js";import"./index-BFXZ3UuW.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";const Pe={class:"bid-account-list-page"},Re={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},qe={class:"no-wrap"},We={class:"flex gap-4"},ze={class:"confirmation-content"},je={key:0},Oe={class:"flex gap-2 justify-end"},Ie=de({name:"userBidAccountList",__name:"list",setup(Me){const k=me(),w=Ve({requestFn:Se}),B=pe(),E=ve(),{hasPermission:D,hasAnyPermission:U}=Le(),{list:P,loading:T,total:R,refresh:g,setSearchParams:q,search:W,onPageChange:z,failed:j,failureMessage:O}=w;$e("userBidAccountList",g);const I=u([{field:"",header:"Business",style:{minWidth:"200px"},template:"businessName"},{field:"account_name",header:"Account Name",style:{minWidth:"150px"}},{field:"bsb",header:"BSB"},{field:"account_no",header:"Account Number",style:{minWidth:"150px"}},{field:"status",header:"Status",style:{minWidth:"100px"},template:"status"},{field:"user",header:"Creator",style:{minWidth:"120px"},template:"creator"},{field:"reviewer",header:"Reviewer",style:{minWidth:"120px"},template:"reviewer"},{field:"created_at",header:"Created Date",style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"created_at"},{field:"action",header:"",style:{width:"50px"},template:"action",alignFrozen:"right",frozen:!0}]),y=u(!1),_=u(!1),$=u([]),M=u([{label:"Approve",value:3},{label:"Reject",value:1}]),C=u({status:void 0}),L=u({type:0,id:0,business_id:""}),Y=ke(Be({type:Ee().required("Type is required")})),H=fe(()=>[{name:"status",label:"Status",type:xe.SELECT,placeholder:"All",options:$.value,defaultValue:""}]),J=a=>{const{sortField:t,sortOrder:x}=a;w.setParams({sort_by:t,sort_order:x===1?"asc":"desc"}),w.search()},X=a=>{switch(a){case 1:return"default";case-1:case-2:return"failed";case 3:return"paid";default:return"paid"}},G=()=>{q(C.value),W()},K=()=>{B.push({name:"userBidAccountCreate"})},Q=a=>{B.push({name:"userBidAccountEdit",params:{id:a.id}})},b=u(!1),f=u(null),Z=a=>{f.value=a,b.value=!0},ee=async()=>{if(f.value)try{await ge({id:f.value.id.toString()}),k.add({severity:"success",summary:"Success",detail:"Bid account deleted successfully"}),b.value=!1,f.value=null,g()}catch{k.add({severity:"error",summary:"Error",detail:"Failed to delete bid account"})}},te=a=>{y.value=!0,L.value={business_id:a.business_id,id:a.id,type:3}},se=a=>{window.$confirm.require({header:"Cancel Settlement Account",message:"Are you sure you want to cancel this Settlement Account?",acceptLabel:"Cancel",accept:async()=>{const{code:t}=await N({id:a.id.toString(),type:"2"});t===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Bid account cancelled successfully"}),g())}})},ae=async a=>{console.log(a),_.value=!0;try{const{code:t}=await N({...a});t===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Bid account updated successfully"}),y.value=!1,g())}catch{window.$toast.add({severity:"error",summary:"Error",detail:"Failed to update bid account"})}finally{_.value=!1}},{getLabel:oe}=Te("business_account_status",a=>{$.value=Ne(a)});return(a,t)=>{const x=Ae,r=ye,le=Ce,ie=we,V=_e,ne=ce,re=ue;return c(),F("div",Pe,[i(x,{modelValue:C.value,"onUpdate:modelValue":t[0]||(t[0]=e=>C.value=e),loading:s(T),"basic-search-fields":H.value,onSearch:G},null,8,["modelValue","loading","basic-search-fields"]),n("div",Re,[i(r,{label:"Add New Settlement Account",class:"!px-8",severity:"warn",icon:"pi pi-plus",onClick:K})]),i(De,{"row-hover":!1,columns:I.value,value:s(P),loading:s(T),"total-records":s(R),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,scrollable:!0,"show-multiple-column":!1,"search-placeholder":"Search Settlement Account ...",failed:s(j),"failure-message":s(O),"striped-rows":!0,onSort:J,onPage:t[1]||(t[1]=e=>s(z)(e))},{businessName:o(({data:e})=>{var l,m;return[n("div",qe,v((l=e==null?void 0:e.business)==null?void 0:l.business_name)+" - "+v((m=e==null?void 0:e.business)==null?void 0:m.business_id),1)]}),status:o(({data:e})=>[i(le,{text:s(oe)(e.status),type:X(e.status)},null,8,["text","type"])]),creator:o(({data:e})=>{var l;return[n("div",null,v(((l=e.user)==null?void 0:l.name)||"-"),1)]}),reviewer:o(({data:e})=>{var l;return[n("div",null,v(((l=e.reviewer)==null?void 0:l.name)||"-"),1)]}),date:o(({data:e})=>[n("div",null,v(s(Fe)(e.created_at)),1)]),action:o(({data:e})=>[s(U)([s(S).USER_UPDATE,s(S).USER_DELETE])?(c(),p(ie,{key:0},{default:o(()=>{var l,m;return[n("div",We,[s(D)(s(S).USER_UPDATE)&&(e==null?void 0:e.status)!==3?(c(),p(r,{key:0,severity:"secondary",onClick:h=>Q(e)},{default:o(()=>t[6]||(t[6]=[A(" Edit ")])),_:2,__:[6]},1032,["onClick"])):d("",!0),s(D)(s(S).USER_DELETE)?(c(),p(r,{key:1,severity:"secondary",onClick:h=>Z(e)},{default:o(()=>t[7]||(t[7]=[A(" Delete ")])),_:2,__:[7]},1032,["onClick"])):d("",!0),((l=s(E).user)==null?void 0:l.type)===0&&(e==null?void 0:e.status)===1?(c(),p(r,{key:2,label:"Review",onClick:h=>te(e)},null,8,["onClick"])):d("",!0),((m=s(E).user)==null?void 0:m.type)===0&&(e==null?void 0:e.status)!==-2?(c(),p(r,{key:3,label:"CANCEL",onClick:h=>se(e)},null,8,["onClick"])):d("",!0)])]}),_:2},1024)):d("",!0)]),_:1},8,["columns","value","loading","total-records","failed","failure-message"]),i(V,{visible:b.value,"onUpdate:visible":t[3]||(t[3]=e=>b.value=e),style:{width:"450px"},header:"Confirm Delete",modal:!0},{footer:o(()=>[i(r,{label:"No",icon:"pi pi-times",text:"",onClick:t[2]||(t[2]=e=>b.value=!1)}),i(r,{label:"Yes",icon:"pi pi-check",text:"",onClick:ee})]),default:o(()=>[n("div",ze,[t[8]||(t[8]=n("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),f.value?(c(),F("span",je,' Are you sure you want to delete the bid account "'+v(f.value.account_name)+'"? ',1)):d("",!0)])]),_:1},8,["visible"]),y.value?(c(),p(V,{key:0,visible:y.value,"onUpdate:visible":t[5]||(t[5]=e=>y.value=e),style:{width:"450px"},header:"Review New Settlement Account",modal:!0},{default:o(()=>[i(s(be),{"initial-values":L.value,"validation-schema":s(Y),onSubmit:ae},{default:o(()=>[i(s(he),{class:"mb-4",as:"div",name:"type",label:"type"},{default:o(({field:e,errorMessage:l,handleChange:m})=>[t[9]||(t[9]=n("label",{for:"type",class:"block font-medium text-gray-700 mb-2"}," Type ",-1)),i(ne,{modelValue:e.value,"onUpdate:modelValue":h=>e.value=h,options:M.value,"option-label":"label","option-value":"value",class:"w-full",onValueChange:m},null,8,["modelValue","onUpdate:modelValue","options","onValueChange"]),l?(c(),p(re,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:o(()=>[A(v(l),1)]),_:2},1024)):d("",!0)]),_:1,__:[9]}),n("div",Oe,[i(r,{label:"No",icon:"pi pi-times",loading:_.value,onClick:t[4]||(t[4]=e=>_.value=!1)},null,8,["loading"]),i(r,{label:"Yes",icon:"pi pi-check",loading:_.value,type:"submit"},null,8,["loading"])])]),_:1},8,["initial-values","validation-schema"])]),_:1},8,["visible"])):d("",!0)])}}}),Ct=Ue(Ie,[["__scopeId","data-v-6292c2a8"]]);export{Ct as default};
