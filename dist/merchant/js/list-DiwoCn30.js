import{d as se,k as ae,J as oe,i as ne,r as m,c as le,a as f,o as p,e as r,b as i,u as s,s as re,w as l,p as ie,q as A,t as u,y as ce,z as me,X as ue,bO as de,bP as pe}from"./index-BPwFJVSY.js";import{_ as he}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{_ as fe}from"./BaseTag-dd68UHCy.js";import{S,_ as _e}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{B as ve}from"./BaseDataTable-BVkRnzNI.js";import{u as F}from"./useDict-sWoVNiZj.js";import{u as ye}from"./useListRefresh-D-tL0eHu.js";import{u as ge,P as h}from"./usePermissions-PkVCxleQ.js";import{u as be}from"./useRequestList-DvWcnSzt.js";import{f as xe}from"./date-D3KeWVvs.js";import{a as L}from"./dict-6kmnRscN.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CCP8zFp2.js";import"./index-CEksU3zE.js";import"./index-DgBXOSrM.js";import"./index-D7FsVJ-c.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-CxOnO8if.js";import"./index-BJi8tw4J.js";import"./index-BFXZ3UuW.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";const Ee={class:"merchant-list-page"},we={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},Te={class:"flex flex-wrap gap-2"},De={class:"confirmation-content"},ke={key:0},Ue=se({name:"merchantList",__name:"list",setup(Ce){const{t:a}=ae(),E=oe(),_=be({requestFn:pe}),v=ne(),{hasPermission:y,hasAnyPermission:M}=ge(),{list:B,loading:w,total:R,refresh:T,setSearchParams:V,search:W,onPageChange:$,failed:z,failureMessage:q}=_;ye("merchantsList",T);const O=m([{field:"name",header:a("merchant.columns.name","Username"),style:{minWidth:"150px"},template:"name",sortable:!0,sortField:"name"},{field:"email",header:a("merchant.columns.email","Email"),style:{minWidth:"200px"}},{field:"status",header:a("merchant.columns.status","Status"),style:{minWidth:"100px"},template:"status"},{field:"business_ids",header:"Business",template:"business"},{field:"type",header:a("merchant.columns.type","Type"),style:{minWidth:"120px"},template:"type"},{field:"mfa_check",header:a("merchant.columns.2fa","2FA Status"),style:{minWidth:"120px"},template:"2fa"},{field:"created_at",header:a("common.created","Created Date"),style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"created_at"},{field:"action",header:"",style:{width:"50px"},template:"action",alignFrozen:"right",frozen:!0}]),D=m([]),k=m([]),g=m({name:"",email:"",status:"",type:"","created_at[]":[]}),I=le(()=>[{name:"name",label:"Username",type:S.TEXT,placeholder:"Search for Username",maxlength:50,defaultValue:""},{name:"status",label:"Status",type:S.SELECT,placeholder:"All",options:D.value,defaultValue:""},{name:"type",label:"Type",type:S.SELECT,placeholder:"All",options:k.value,defaultValue:""}]),N=t=>{const{sortField:o,sortOrder:b}=t;_.setParams({sort_by:o,sort_order:b===1?"asc":"desc"}),_.search()},X=t=>t===void 0?"upcoming":{1:"paid",0:"failed"}[String(t)]||"upcoming",G=t=>t===void 0?"Unknown":{1:"Enabled",0:"Disabled"}[String(t)]||"Unknown",J=t=>t===void 0?"Unknown":{1:"Admin",2:"User",3:"Guest"}[String(t)]||"Unknown",j=({data:t})=>{v.push({name:"userDetail",params:{id:t.id}})},H=t=>{v.push({name:"userEdit",params:{id:t.id}})},K=()=>{v.push({name:"userCreate"})},d=m(!1),c=m(null),Q=t=>{c.value=t,d.value=!0},Y=()=>{c.value&&(d.value=!1,de(String(c.value.id)).then(t=>{E.add({severity:"success",summary:a("common.success","Success"),detail:a("merchant.messages.deleteSuccess","Merchant deleted successfully")}),T()}).catch(t=>{console.error("Failed to delete user:",t),E.add({severity:"error",summary:a("common.error","Error"),detail:a("merchant.messages.deleteFailed","Failed to delete merchant")})}),c.value=null)},Z=()=>{V(g.value),W()};return F("user_status",t=>{D.value=L(t,{label:"All",value:""})}),F("user_type",t=>{k.value=L(t,{label:"All",value:""})}),(t,o)=>{const b=_e,x=re,U=fe,ee=he,te=ue;return p(),f("div",Ee,[r(b,{modelValue:g.value,"onUpdate:modelValue":o[0]||(o[0]=e=>g.value=e),loading:s(w),"basic-search-fields":I.value,onSearch:Z},null,8,["modelValue","loading","basic-search-fields"]),i("div",we,[r(x,{label:s(a)("merchant.buttons.create"),class:"!px-8",severity:"warn",icon:"pi pi-plus",onClick:K},null,8,["label"])]),r(ve,{"row-hover":s(y)(s(h).USER_DETAIL),columns:O.value,value:s(B),loading:s(w),"total-records":s(R),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,scrollable:!0,"search-placeholder":"Search merchants...",failed:s(z),"failure-message":s(q),"striped-rows":!0,onSort:N,onPage:o[1]||(o[1]=e=>s($)(e)),onRowClick:j},{name:l(({data:e})=>[i("div",null,u(e.name),1)]),status:l(({data:e})=>[r(U,{text:G(e.status),type:X(e.status)},null,8,["text","type"])]),type:l(({data:e})=>[i("div",null,u(J(e.type)),1)]),business:l(({data:e})=>[i("div",Te,[(p(!0),f(ce,null,me(e==null?void 0:e.merchant_user_business,n=>{var C,P;return p(),f("div",{key:n.business_id,class:"max-w-[200px] whitespace-nowrap"},u((C=n==null?void 0:n.business)==null?void 0:C.business_name)+" - "+u((P=n==null?void 0:n.business)==null?void 0:P.business_id),1)}),128))])]),"2fa":l(({data:e})=>[r(U,{text:e.mfa_check===1?"Enabled":"Disabled",type:e.mfa_check===1?"paid":"failed"},null,8,["text","type"])]),date:l(({data:e})=>[i("div",null,u(s(xe)(e.created_at)),1)]),action:l(({data:e})=>[s(M)([s(h).USER_UPDATE,s(h).USER_DELETE])&&e.type!==0?(p(),ie(ee,{key:0,"is-show-detail":!1,"is-show-delete":e.type!==0&&s(y)(s(h).USER_DELETE),"is-show-edit":e.type!==0&&s(y)(s(h).USER_UPDATE),loading:e.__loading,onEdit:n=>H(e),onDelete:n=>Q(e)},null,8,["is-show-delete","is-show-edit","loading","onEdit","onDelete"])):A("",!0)]),_:1},8,["row-hover","columns","value","loading","total-records","failed","failure-message"]),r(te,{visible:d.value,"onUpdate:visible":o[3]||(o[3]=e=>d.value=e),style:{width:"450px"},header:s(a)("merchant.dialogs.confirmDelete"),modal:!0},{footer:l(()=>[r(x,{label:s(a)("common.no"),icon:"pi pi-times",text:"",onClick:o[2]||(o[2]=e=>d.value=!1)},null,8,["label"]),r(x,{label:s(a)("common.yes"),icon:"pi pi-check",text:"",onClick:Y},null,8,["label"])]),default:l(()=>[i("div",De,[o[4]||(o[4]=i("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),c.value?(p(),f("span",ke,u(s(a)("merchant.dialogs.deleteConfirmMessage",{name:c.value.name})),1)):A("",!0)])]),_:1},8,["visible","header"])])}}}),nt=Se(Ue,[["__scopeId","data-v-3c7c26af"]]);export{nt as default};
