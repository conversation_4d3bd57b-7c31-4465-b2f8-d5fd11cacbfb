import{d as W,k as X,i as j,r as m,c as z,a as O,o as G,e as i,b as l,u as a,s as H,w as n,t as f,j as g,X as J,bA as K,bB as Q}from"./index-BPwFJVSY.js";import{s as Y}from"./index-CEksU3zE.js";import{_ as Z}from"./BaseTag-dd68UHCy.js";import{B as ee}from"./BaseDataTable-BVkRnzNI.js";import{S as te,_ as se}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{u as _}from"./useDict-sWoVNiZj.js";import{u as ae}from"./useListRefresh-D-tL0eHu.js";import{u as oe}from"./useRequestList-DvWcnSzt.js";import{f as ie}from"./date-D3KeWVvs.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CxOnO8if.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";import"./index-D7FsVJ-c.js";import"./index-BFXZ3UuW.js";import"./index-DlkyKXFh.js";import"./index-BsWpeBjZ.js";import"./dict-CLPQcIyV.js";import"./dayjs.min-Bar1rHDB.js";const ne={class:"merchant-list-page"},re={class:"flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},ue={class:"space-y-4"},me={class:"field"},de={class:"field"},ce=W({name:"userBidList",__name:"list",setup(pe){const{t:d}=X(),w=j(),c=oe({requestFn:Q}),{list:B,loading:b,total:S,refresh:h,setSearchParams:x,search:T,onPageChange:C,failed:D,failureMessage:L}=c;ae("userBidList",h);const v=m({settlementType:[],merchantBusinessStatus:[],bpaySwitch:[]}),V=m([{field:"merchant_name",header:"Business Name"},{field:"business_id",header:"Business ID"},{field:"business_name",header:"Business Name",style:{minWidth:"150px"}},{field:"settlement_type",header:"Settlement Type",template:"settlementType"},{field:"status",header:"Status",template:"status"},{field:"created_at",header:d("common.created","Created Date"),style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"created_at"}]),p=m({merchant_name:"",business_id:"",settlement_type:"",status:"","created_at[]":[]}),k=z(()=>[{name:"bid",label:"Business ID",type:te.TEXT,placeholder:"Search for Business ID",maxlength:50,defaultValue:""}]),N=s=>{const{sortField:e,sortOrder:u}=s;c.setParams({sort_by:e,sort_order:u===1?"asc":"desc"}),c.search()},$=s=>s===void 0?"upcoming":{1:"paid",0:"failed"}[String(s)]||"upcoming",M=({data:s})=>{if(s.status!==1){window.$toast.add({severity:"warn",summary:"Error",detail:"Merchant is not enabled"});return}w.push({name:"userBidDetail",params:{id:s.id.toString()}})},I=()=>{x(p.value),T()},{getLabel:P}=_("settlement_type",s=>{v.value.settlementType=s}),{getLabel:E}=_("merchant_business_status",s=>{v.value.merchantBusinessStatus=s}),{getLabel:F}=_("bpay_switch"),r=m(!1),o=m({business_name:"",location:""}),q=()=>{r.value=!0,o.value={business_name:"",location:""}},R=async()=>{if(!o.value.business_name.trim()||!o.value.location.trim()){window.$toast.add({severity:"warn",summary:"Warning",detail:"Please fill in all required fields"});return}const{code:s}=await K({business_name:o.value.business_name,location:o.value.location});s===0&&(window.$toast.add({severity:"success",summary:d("common.success"),detail:d("merchant.messages.createSuccess","Merchant created successfully")}),r.value=!1,h())};return(s,e)=>{const u=H,U=Z,y=Y,A=J;return G(),O("div",ne,[i(se,{modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=t=>p.value=t),loading:a(b),"basic-search-fields":k.value,onSearch:I},null,8,["modelValue","loading","basic-search-fields"]),l("div",re,[i(u,{label:"Add New Business",class:"!px-8",severity:"warn",icon:"pi pi-plus",onClick:q})]),i(ee,{columns:V.value,value:a(B),loading:a(b),"total-records":a(S),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,scrollable:!0,"show-multiple-column":!1,"search-placeholder":"Search merchants...",failed:a(D),"failure-message":a(L),"striped-rows":!0,"row-hover":!0,onSort:N,onPage:e[1]||(e[1]=t=>a(C)(t)),onRowClick:M},{status:n(({data:t})=>[i(U,{text:a(E)(t.status),type:$(t.status)},null,8,["text","type"])]),settlementType:n(({data:t})=>[g(f(a(P)(t.settlement_type)),1)]),bpaySwitch:n(({data:t})=>[g(f(a(F)(t.bpay_switch)),1)]),date:n(({data:t})=>[l("div",null,f(a(ie)(t.created_at)),1)]),_:1},8,["columns","value","loading","total-records","failed","failure-message"]),i(A,{visible:r.value,"onUpdate:visible":e[5]||(e[5]=t=>r.value=t),header:"Add New Business",style:{width:"450px"},modal:""},{footer:n(()=>[i(u,{label:"Cancel",icon:"pi pi-times",severity:"secondary",onClick:e[4]||(e[4]=t=>r.value=!1)}),i(u,{label:"Create",icon:"pi pi-check",onClick:R})]),default:n(()=>[l("div",ue,[l("div",me,[e[6]||(e[6]=l("label",{for:"business_name",class:"block font-medium mb-2"},"Business Name *",-1)),i(y,{id:"business_name",modelValue:o.value.business_name,"onUpdate:modelValue":e[2]||(e[2]=t=>o.value.business_name=t),class:"w-full",placeholder:"Enter business name"},null,8,["modelValue"])]),l("div",de,[e[7]||(e[7]=l("label",{for:"location",class:"block font-medium mb-2"},"Location *",-1)),i(y,{id:"location",modelValue:o.value.location,"onUpdate:modelValue":e[3]||(e[3]=t=>o.value.location=t),class:"w-full",placeholder:"Enter location"},null,8,["modelValue"])])])]),_:1},8,["visible"])])}}}),Ue=le(ce,[["__scopeId","data-v-283d5348"]]);export{Ue as default};
