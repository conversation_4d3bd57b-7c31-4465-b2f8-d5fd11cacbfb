import{d as T,k as E,r as p,l as U,a as P,b as l,e as t,u as s,n as A,j as g,w as m,F as G,m as k,p as y,q as w,v as $,t as f,s as H,i as L,x as O,o as v}from"./index-BPwFJVSY.js";import{s as j}from"./index-CCP8zFp2.js";import{s as z}from"./index-DES6SMz0.js";import{s as D}from"./index-BlFKIn6Y.js";import{s as Q}from"./index-CEksU3zE.js";import{s as Y}from"./index-CBr4Z_s8.js";import{t as J,c as K,a as W,b as V}from"./index.esm-DOIZz6Kf.js";import{b as X}from"./bill-buddy-hq-horizontal-logo-Dcb479Ot.js";import{_ as Z}from"./login-bg-kShksnUz.js";import{G as ee}from"./index-DVRN33rP.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DDQWKtix.js";import"./index-BjQt0t51.js";const ae={class:"auth-container"},le={class:"logo-wrap"},te={class:"logo"},se={class:"auth-content-wrap"},re={class:"auth-content"},ne={class:"flex gap-4 justify-between tools"},ie={class:"flex items-center gap-2"},me={for:"rememberMe"},ue=T({__name:"loginView",setup(de){const{t:r}=E(),_=p(!1),x=p(null),n=p(),C=L(),I=U(),o=p({email:"",password:"",rememberMe:!1,google_token:""}),S=J(K({email:V().required(r("validation.emailRequired")).email(r("validation.emailInvalid")),password:V().required(r("validation.passwordRequired")),google_token:V().required(r("validation.googleTokenRequired")),rememberMe:W()})),q=i=>{i&&(o.value.google_token=i,n.value&&n.value.setFieldValue("google_token",i))},B=()=>{o.value.google_token="",n.value&&n.value.setFieldValue("google_token","")},F=()=>{o.value.google_token="",n.value&&n.value.setFieldValue("google_token","")},h=async()=>{var d,c;const{valid:i,errors:e}=await((d=n.value)==null?void 0:d.validate());if(i){console.log(i,e);try{_.value=!0,await I.login(o.value.email,o.value.password,o.value.google_token,o.value.rememberMe),C.push({name:O.ROOT})}catch{o.value.google_token="",(c=x.value)==null||c.reset()}finally{_.value=!1}}};return document.title="Bill Buddy HQ",(i,e)=>{const d=Y,c=Q,b=D,M=z,N=j,R=H;return v(),P("div",ae,[l("div",le,[l("div",te,[t(d,{src:s(X),width:"250px",alt:"Image"},null,8,["src"])])]),l("div",{class:"auth-bg-wrap",style:A({backgroundImage:`url(${s(Z)})`,backgroundSize:"100% 100%"})},null,4),l("div",se,[l("div",re,[e[6]||(e[6]=l("h1",{class:"title"}," Hello! ",-1)),e[7]||(e[7]=l("p",{class:"subtitle"},[g(" Login "),l("span",{class:"subtitle-account"},"Your Account")],-1)),t(s(G),{ref_key:"formRef",ref:n,"validation-schema":s(S),class:"login-form",onSubmit:h},{default:m(()=>[t(s(k),{modelValue:o.value.email,"onUpdate:modelValue":e[0]||(e[0]=a=>o.value.email=a),as:"div",class:"flex flex-col gap-4",name:"email"},{default:m(({field:a,errorMessage:u})=>[t(c,$(a,{class:"form-input",name:"email",type:"text",placeholder:s(r)("login.email")}),null,16,["placeholder"]),u?(v(),y(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[g(f(u),1)]),_:2},1024)):w("",!0)]),_:1},8,["modelValue"]),t(s(k),{modelValue:o.value.password,"onUpdate:modelValue":e[1]||(e[1]=a=>o.value.password=a),as:"div",class:"flex flex-col gap-4",name:"password"},{default:m(({field:a,errorMessage:u})=>[t(M,$(a,{type:"text",placeholder:s(r)("login.password"),"toggle-mask":"",fluid:"",feedback:!1}),null,16,["placeholder"]),u?(v(),y(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[g(f(u),1)]),_:2},1024)):w("",!0)]),_:1},8,["modelValue"]),l("div",ne,[l("div",ie,[t(N,{modelValue:o.value.rememberMe,"onUpdate:modelValue":e[2]||(e[2]=a=>o.value.rememberMe=a),name:"rememberMe",binary:""},null,8,["modelValue"]),l("label",me,f(s(r)("login.rememberMe")),1)]),t(R,{text:"",label:s(r)("login.forgotPassword"),class:"!p-0",onClick:e[3]||(e[3]=a=>i.$router.push("/forgot-password"))},null,8,["label"])]),t(s(k),{modelValue:o.value.google_token,"onUpdate:modelValue":e[4]||(e[4]=a=>o.value.google_token=a),as:"div",class:"flex flex-col",name:"google_token"},{default:m(({errorMessage:a})=>[e[5]||(e[5]=l("p",{class:"text-lg !m-0",style:{"font-weight":"600"}}," *CAPTCHA ",-1)),t(ee,{ref_key:"recaptchaRef",ref:x,name:"google_token",class:"mb-2 mt-2",onVerify:q,onExpired:B,onError:F},null,512),a?(v(),y(b,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:m(()=>[g(f(a),1)]),_:2},1024)):w("",!0)]),_:1,__:[5]},8,["modelValue"]),t(R,{class:"login-submit",loading:_.value,severity:"warn",label:"LOGIN",onClick:h},null,8,["loading"])]),_:1},8,["validation-schema"])])])])}}}),Re=oe(ue,[["__scopeId","data-v-47a83cfe"]]);export{Re as default};
