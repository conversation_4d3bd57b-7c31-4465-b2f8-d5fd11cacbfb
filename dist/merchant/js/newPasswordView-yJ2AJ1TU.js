import{d as T,r as p,a as n,b as s,e as l,u as c,n as v,w as m,F as I,m as k,q as _,p as E,j as u,v as V,y as O,z as j,A as $,t as b,s as D,B as Z,i as q,o as r,C as Y}from"./index-BPwFJVSY.js";import{s as G}from"./index-BlFKIn6Y.js";import{s as H}from"./index-DES6SMz0.js";import{s as J}from"./index-CBr4Z_s8.js";import{_ as K}from"./isUpdated-DXQm7BuR.js";import{b as M}from"./BillbuddyLogo-FvDXaJ4h.js";import{_ as Q}from"./login-bg-kShksnUz.js";import{t as W}from"./vee-validate-zod-BFI0y82J.js";import{o as X,s as F}from"./types-AmnC9OGP.js";import{_ as ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DDQWKtix.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";const se={class:"auth-container"},te={class:"logo-wrap"},ae={class:"logo"},oe={class:"auth-content-wrap"},re={class:"auth-content"},le={key:0},ne={class:"flex flex-col gap-4"},ce={key:0,class:"strength-container"},ie={class:"strength-bars"},de={class:"strength-indicator"},me={key:1,class:"password-updated flex flex-col justify-center items-center gap-4"},ue=T({__name:"newPasswordView",setup(pe){const S=Z(),U=q(),g=p(!1),h=p(!1),N=p(),i=p({password:"",reEnterPassword:""}),A=W(X({password:F().min(8,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}).regex(/\d/,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}).regex(/[a-z]/,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}).regex(/[A-Z]/,{message:"Use at least 8 characters. One uppercase letter, one lowercase letter, one number."}),reEnterPassword:F().refine(t=>t===i.value.password,{message:"Password do not match"})})),B=async t=>{var e;try{g.value=!0;const a=(e=S.params)==null?void 0:e.token;(await Y({new_password:t.password,token:a})).code===0&&(h.value=!0,setTimeout(()=>{U.push("/merchant/login")},2e3))}catch{}finally{g.value=!1}},f=t=>{const e={hasLower:/[a-z]/.test(t),hasUpper:/[A-Z]/.test(t),hasNumber:/\d/.test(t),isLongEnough:t.length>=12},a=Object.values(e).filter(Boolean).length;return a<=1?"Low":a<=3?"Average":"Excellent"},L=t=>{const e=f(t);return{Low:"#eb001b",Average:"#FFD700",Excellent:"#7ed956"}[e]},C=t=>{const e=f(t),a={Low:"#eb001b",Average:"#FFD700",Excellent:"#7ed956"};return[{active:e!=="None",color:a[e]},{active:["Average","Excellent"].includes(e),color:["Average","Excellent"].includes(e)?a[e]:"#fff"},{active:e==="Excellent",color:e==="Excellent"?a[e]:"#fff"}]};return(t,e)=>{const a=J,w=H,x=G,z=D;return r(),n("div",se,[s("div",te,[s("div",ae,[l(a,{src:c(M),width:"230px",alt:"Image"},null,8,["src"])])]),s("div",{class:"auth-bg-wrap",style:v({backgroundImage:`url(${c(Q)})`,backgroundSize:"100% 100%"})},null,4),s("div",oe,[s("div",re,[h.value?(r(),n("div",me,e[7]||(e[7]=[s("div",{class:"img"},[s("img",{src:K,alt:"",style:{width:"136px"}})],-1),s("div",{class:"title"}," Password Updated! ",-1),s("div",{class:"forgot-subtitle",style:{"margin-top":"0"}}," Please wait.You will be redirected to the Login Page. ",-1)]))):(r(),n("div",le,[e[5]||(e[5]=s("h1",{class:"title"}," New Password ",-1)),e[6]||(e[6]=s("p",{class:"forgot-subtitle"}," Enter a new password. ",-1)),l(c(I),{ref_key:"newPasswordFormRef",ref:N,"validation-schema":c(A),class:"flex flex-col gap-6 w-full new-password-form",onSubmit:B},{default:m(()=>[s("div",ne,[l(c(k),{modelValue:i.value.password,"onUpdate:modelValue":e[0]||(e[0]=o=>i.value.password=o),as:"div",class:"flex flex-col gap-4",name:"password"},{default:m(({field:o,errorMessage:d})=>{var y;return[e[3]||(e[3]=s("label",{class:"form-label"},[u("New Password"),s("span",{class:"text-[#ff3131]"},"*")],-1)),l(w,V(o,{type:"text",placeholder:"Password","toggle-mask":"",fluid:"",feedback:!1}),null,16),((y=o.value)==null?void 0:y.length)>0?(r(),n("div",ce,[s("div",ie,[(r(!0),n(O,null,j(C(o.value),(P,R)=>(r(),n("div",{key:R,class:$(["strength-segment",{active:P.active}]),style:v({backgroundColor:P.color})},null,6))),128))]),s("small",de,[e[2]||(e[2]=u(" Password strength: ")),s("span",{style:v({color:L(o.value)})},b(f(o.value)),5)])])):_("",!0),d?(r(),E(x,{key:1,class:"px-[1rem]",severity:"error",variant:"simple"},{default:m(()=>[u(b(d),1)]),_:2},1024)):_("",!0)]}),_:1,__:[3]},8,["modelValue"]),l(c(k),{modelValue:i.value.reEnterPassword,"onUpdate:modelValue":e[1]||(e[1]=o=>i.value.reEnterPassword=o),as:"div",class:"flex flex-col gap-4 mt-4",name:"reEnterPassword"},{default:m(({field:o,errorMessage:d})=>[e[4]||(e[4]=s("label",{class:"form-label"},[u("Re-enter Password"),s("span",{class:"text-[#ff3131]"},"*")],-1)),l(w,V(o,{type:"text",placeholder:"Re-enter Password","toggle-mask":"",fluid:"",feedback:!1}),null,16),d?(r(),E(x,{key:0,class:"px-[1rem]",severity:"error",variant:"simple"},{default:m(()=>[u(b(d),1)]),_:2},1024)):_("",!0)]),_:1,__:[4]},8,["modelValue"])]),l(z,{loading:g.value,class:"!mt-6 w-full login-submit",type:"submit",severity:"warn",label:"SUBMIT"},null,8,["loading"])]),_:1},8,["validation-schema"])]))])])])}}}),Fe=ee(ue,[["__scopeId","data-v-d7f35be9"]]);export{Fe as default};
