import{s as j}from"./index-DMmJJ4dH.js";import{s as q}from"./index-BFXZ3UuW.js";import{s as G}from"./index-AtQkrpJ9.js";import{d as H,B as W,i as X,r as p,c as J,f as K,a as c,o as s,b as e,p as _,q as u,u as l,s as Z,e as g,N as ee,O as te,w as oe,t as n,A as ae,y as P,z as ne,j as $,bx as se}from"./index-BPwFJVSY.js";import{D as m}from"./decimal-LZMqkRTm.js";import{Q as le}from"./browser-AVoJjGFw.js";import{I as y}from"./invoice-B6mg1HKE.js";import{g as ie,s as re}from"./invoice-CnWRWdDk.js";import{f as M}from"./date-D3KeWVvs.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./dayjs.min-Bar1rHDB.js";const ce={class:"pay-invoice-wrap flex flex-col lg:flex-row gap-4"},ue={class:"pay-invoice-container lg:w-3/5"},me={class:"header-actions"},ve={class:"action-buttons"},pe={class:"flex gap-2"},_e={key:0,class:"loading-container"},ye={class:"invoice-header"},fe={class:"invoice-parties"},he={class:"recipient"},we={class:"invoice-right"},ge={class:"invoice-meta"},be={class:"meta-item"},ke={class:"meta-item"},xe={class:"meta-item"},Ie={key:0,class:"meta-item"},Ce={class:"text-ellipsis max-w-[200px]"},De={class:"merchant-name"},Se={class:"invoice-items"},Ae={key:0},Te={key:1},Ee={style:{"border-bottom":"none"}},Ne={class:"text-right",colspan:"4",style:{"border-bottom":"none"}},Le={style:{"border-bottom":"none"}},Pe={class:"invoice-summary"},$e={class:"amount-due flex justify-between items-center"},Me={key:2,class:"error-container"},Re={class:"preview-container lg:w-2/5 p-2 bg-white rounded-xl"},Ye=["srcdoc"],Be=H({__name:"payMyInvoiceDetailAndSend",setup(Qe){const b=W(),R=X(),t=p({}),f=p(!0),k=p(!1),Y=J(()=>{var a;return(a=t.value)!=null&&a.email_html?`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 100%;
          margin: 0;
          padding: 16px;
          background: white;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        table {
          border-collapse: collapse;
          width: 100%;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      ${t.value.email_html}
    </body>
    </html>
  `:""}),x=async()=>{try{f.value=!0;const a=await ie({id:Number(b.params.id)});t.value=a.data}catch{window.$toast.add({severity:"error",summary:"Error",detail:"Failed to load invoice details"}),f.value=!1}finally{f.value=!1}},B=()=>{window.$confirm.require({message:"Are you sure you want to send this invoice?",header:"Send Invoice",icon:"pi pi-envelope",acceptClass:"p-button-warn",rejectClass:"p-button-primary",accept:()=>{k.value=!0,re({id:Number(b.params.id)}).then(a=>{a.code===0&&window.$toast.add({severity:"success",summary:"Success",detail:"Invoice sent successfully"})}).finally(()=>{k.value=!1})},reject:()=>{}})},h=p(!1),v=p(null),Q=()=>{var a;se(((a=t.value)==null?void 0:a.payment_url)||"",()=>window.$toast.add({severity:"success",summary:"Success",detail:"Copy successfully"}))},U=()=>{var d,w;h.value=!0;const a={size:300,dark:"#000000",light:"#ffffff"},o=((d=t.value)==null?void 0:d.payment_url)||"";if(!o)return window.$toast.add({severity:"error",summary:"Error",detail:"Please refresh and try again."});try{le.toCanvas(v.value,o,{width:a.size,margin:2,color:{dark:a.dark,light:a.light}},i=>{i&&console.error(i)})}catch(i){console.error(i)}try{const i=document.createElement("a");i.download=`Payment_QRCode_${new Date().getTime()}.png`,i.href=((w=v==null?void 0:v.value)==null?void 0:w.toDataURL("image/png"))||"",i.click()}catch(i){console.error(i)}h.value=!1},F=()=>{var o;const a=((o=t.value)==null?void 0:o.invoice_pdf_url)||"";if(!a)return window.$toast.add({severity:"error",summary:"Error",detail:"Please refresh and try again."});window.open(a,"_blank")},z=()=>{R.push({name:"payMyInvoiceEditInvoice",params:{id:b.params.id}})};return K(()=>{x()}),(a,o)=>{var I,C,D,S,A,T;const d=Z,w=G,i=q,O=j;return s(),c("div",ce,[e("div",ue,[e("div",me,[e("div",ve,[e("div",null,[((I=t.value)==null?void 0:I.status)===l(y).UNPAID?(s(),_(d,{key:0,icon:"pi pi-chevron-left",severity:"info",label:"Back to Edit",onClick:z})):u("",!0)]),e("div",pe,[(C=t.value)!=null&&C.payment_url&&[l(y).UNPAID,l(y).PARTIAL].includes((D=t.value)==null?void 0:D.status)?(s(),_(d,{key:0,icon:"pi pi-copy",label:"Copy Link",onClick:Q})):u("",!0),(S=t.value)!=null&&S.payment_url&&[l(y).UNPAID,l(y).PARTIAL].includes((A=t.value)==null?void 0:A.status)?(s(),_(d,{key:1,icon:"pi pi-download",severity:"help",label:"Download QR Code",loading:h.value,onClick:U},null,8,["loading"])):u("",!0),(T=t.value)!=null&&T.payment_url?(s(),_(d,{key:2,icon:"pi pi-file-pdf",severity:"info",label:"Download Invoice",loading:h.value,onClick:F},null,8,["loading"])):u("",!0),g(d,{icon:"pi pi-envelope",severity:"warn",label:"Send Email",loading:k.value,onClick:B},null,8,["loading"]),ee(e("canvas",{ref_key:"canvas",ref:v},null,512),[[te,!1]])])])]),f.value?(s(),c("div",_e,[g(w),o[0]||(o[0]=e("p",null,"Loading invoice details...",-1))])):t.value?(s(),_(O,{key:1,class:"invoice-card"},{content:oe(()=>{var E,N,L;return[e("div",ye,[e("div",fe,[e("div",he,[o[1]||(o[1]=e("h3",null,"TAX INVOICE",-1)),e("p",null,n(t.value.customer.name),1)])]),e("div",we,[e("div",ge,[e("div",be,[o[2]||(o[2]=e("h3",null,"Invoice number",-1)),e("p",null,n(t.value.invoice_number),1)]),e("div",ke,[o[3]||(o[3]=e("h3",null,"Issue date",-1)),e("p",null,n(l(M)(t.value.created_at,"DD MMM YYYY")),1)]),e("div",xe,[o[4]||(o[4]=e("h3",null,"Due date",-1)),e("p",{class:ae({"due-today":t.value.due_date==="Today"})},n(l(M)(t.value.due_date,"DD MMM YYYY")),3)]),(E=t.value)!=null&&E.reference?(s(),c("div",Ie,[o[5]||(o[5]=e("h3",null,"Reference",-1)),e("p",Ce,n((N=t.value)==null?void 0:N.reference),1)])):u("",!0)]),e("div",De,[e("h3",null,n((L=t.value.merchant)==null?void 0:L.merchant_name),1)])])]),g(i),e("div",Se,[e("table",null,[o[8]||(o[8]=e("thead",null,[e("tr",null,[e("th",null,"Description"),e("th",null,"Quantity"),e("th",null,"Unit Price"),e("th",null,"GST"),e("th",null,"Amount AUD")])],-1)),e("tbody",null,[(s(!0),c(P,null,ne(t.value.line_items,(r,V)=>(s(),c("tr",{key:V},[e("td",null,n(r.description),1),e("td",null,n(Number(r.quantity).toFixed(2).toString()),1),e("td",null,n(r.unit_amount),1),r.unit_amount!==r.line_amount?(s(),c("td",Ae,n(new(l(m))((r==null?void 0:r.line_amount)||1).div(new(l(m))(r==null?void 0:r.unit_amount)).sub(1).mul(100))+" % ",1)):(s(),c("td",Te," 0 ")),e("td",null,n(r.line_amount),1)]))),128)),e("tr",null,[o[6]||(o[6]=e("td",{class:"text-right",colspan:"4",style:{"border-bottom":"none"}}," Subtotal ",-1)),e("td",Ee,n(t.value.sub_total),1)]),e("tr",null,[e("td",Ne,[o[7]||(o[7]=$(" TOTAL GST ")),Number(t.value.total_tax)!==0?(s(),c(P,{key:0},[$(n(new(l(m))(t.value.total_tax).div(new(l(m))(t.value.sub_total)).mul(100))+"% ",1)],64)):u("",!0)]),e("td",Le,n(t.value.total_tax),1)])])])]),e("div",Pe,[e("div",$e,[e("h3",null,"TOTAL "+n(t.value.currency),1),e("h2",null,n(new(l(m))(t.value.sub_total).add(new(l(m))(t.value.total_tax)).toFixed(2)),1)])])]}),_:1})):(s(),c("div",Me,[o[9]||(o[9]=e("i",{class:"pi pi-exclamation-triangle",style:{"font-size":"2rem"}},null,-1)),o[10]||(o[10]=e("p",null,"Failed to load invoice. Please try again.",-1)),g(d,{label:"Retry",onClick:x})]))]),e("div",Re,[e("iframe",{srcdoc:Y.value,class:"email-iframe",frameborder:"0",scrolling:"auto"},null,8,Ye)])])}}}),Je=de(Be,[["__scopeId","data-v-749b9225"]]);export{Je as default};
