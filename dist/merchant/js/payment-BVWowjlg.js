import{s as J}from"./index-CTms-Les.js";import{s as K}from"./index-DgBXOSrM.js";import{s as W}from"./index-BlFKIn6Y.js";import{s as ee}from"./index-CEksU3zE.js";import{s as te}from"./index-AtQkrpJ9.js";import{d as ae,k as se,r as h,B as oe,Q as T,c as ne,a as x,b as e,q as y,t as o,u as n,A as F,e as m,s as ie,p as g,w as _,F as le,m as P,v as V,j as k,o as f}from"./index-BPwFJVSY.js";import{G as re}from"./index-DVRN33rP.js";import{g as ce,p as me}from"./accounting-BSj3P-wh.js";import{t as ue}from"./vee-validate-zod-BFI0y82J.js";import{D as C}from"./decimal-LZMqkRTm.js";import{o as de,s as w,n as pe,Z as _e}from"./types-AmnC9OGP.js";import{_ as fe}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ve={class:"payment-container"},be={class:"payment-content border-round overflow-hidden"},ye={key:0,class:"col-1 md:col-4 pl-md-4"},ge={class:"invoice-details p-4 border-1 border-round h-full"},he={class:"invoice-summary-title mb-3"},Pe={class:"flex"},ke={class:"col-1"},xe={class:"flex justify-content-between mb-2"},we={class:"font-medium"},Ne={class:"flex justify-content-between mb-2"},Ve={class:"font-medium"},Ce={class:"flex justify-content-between mb-2"},De={class:"font-medium"},Ie={class:"flex justify-content-between mb-2"},Ae={key:0,class:"font-medium"},Xe={key:1,class:"font-medium"},Fe={class:"flex justify-content-between mb-2"},Se={class:"font-medium ml-2"},je={key:1,class:"payment-result p-4"},Re={class:"result-icon text-center mb-4"},Te={class:"text-center mb-3"},Ue={class:"text-center mb-4"},$e={key:0,class:"transaction-details border-1 border-round p-3 mb-4"},Ee={class:"grid"},Ye={class:"col-12"},Be={class:"flex justify-content-between mb-2"},qe={class:"font-medium"},Me={class:"flex justify-content-between mb-2"},Le={class:"font-medium"},ze={class:"flex justify-content-center"},Ge={key:2,class:"payment-form p-4 relative"},Ze={class:"grid"},He={class:"field mb-4"},Oe={for:"invoiceNumber",class:"block mb-2"},Qe={class:"flex justify-content-between gap-4"},Je={class:"field mb-4"},Ke={for:"amount",class:"block mb-2"},We={class:"field mb-4"},et={for:"email",class:"block mb-2"},tt={class:"field mb-4"},at={for:"name",class:"block mb-2"},st={for:"creditCardNumber",class:"block mb-2"},ot={class:"field mb-4"},nt={for:"expiryDate",class:"block mb-2"},it={for:"securityCode",class:"block mb-2"},lt={class:"field mb-4"},rt={class:"block mb-2"},ct={class:"field"},mt=ae({__name:"payment",setup(ut){const{t:r}=se(),$=oe(),U=h($.params.id),E=h(),D=h(!1),S=h(!1),j=h(!1),l=T({show:!1,success:!1,message:"",amount:"",date:""}),Y=ne(()=>!l.show),R=h(null),I=h(!1),A=h(999999999999),B=ue(de({invoiceNumber:w().min(1,{message:"Invoice number is required"}),amount:pe({invalid_type_error:"Amount is required"}).min(.01,{message:"Amount must be greater than 0"}).superRefine((v,t)=>v>A.value?(t.addIssue({code:_e.custom,message:`Amount must be less than ${A.value}`}),!1):!0).nullable(),email:w().email({message:"Please enter a valid email address"}),name:w().min(1,{message:"Name is required"}),creditCardNumber:w().min(13,{message:"Card number must be between 13 and 19 digits"}).max(19,{message:"Card number must be between 13 and 19 digits"}).refine(v=>{const t=v.replace(/\D/g,"");let u=0,b=!1;for(let d=t.length-1;d>=0;d--){let p=Number.parseInt(t.charAt(d));b&&(p*=2,p>9&&(p-=9)),u+=p,b=!b}return u%10===0},"Invalid card number"),expiryDate:w().regex(/^\d{2}\/\d{2}$/,"Expiry date must be in MM/YY format").refine(v=>{const[t,u]=v.split("/"),b=new Date,d=b.getFullYear()%100,p=b.getMonth()+1,N=Number.parseInt(t),X=Number.parseInt(u);return N<1||N>12?!1:!(X<d||X===d&&N<p)},"Card has expired or invalid date"),securityCode:w().regex(/^\d{3,4}$/,"Security code must be 3 or 4 digits"),google_token:w({required_error:"Please complete the reCAPTCHA verification"})})),s=T({invoiceNumber:"",amount:0,email:"",name:"",creditCardNumber:"",expiryDate:"",securityCode:"",recaptcha:!1,google_token:""}),i=T({total:"0",amountPaid:"0",amountDue:"0",surcharge_rate:"0",surcharge_type:"0",currency:"AUD"}),q=()=>i.surcharge_type==="1"?new C(s.amount||0).mul(new C(1).add(new C(i.surcharge_rate||0).div(100))).toFixed(2):new C(s.amount||0).add(new C(i.surcharge_rate||0)).toFixed(2),M=v=>{v&&(s.google_token=v,I.value=!0)},L=()=>{I.value=!1},z=()=>{I.value=!1},G=async v=>{var t,u,b;if(I.value||(s.recaptcha=!1),!!v.google_token){S.value=!0;try{const{data:d,code:p}=await me({amount:Number(s.amount),card_number:s.creditCardNumber,email:s.email,expiration_month:s.expiryDate.split("/")[0],expiration_year:s.expiryDate.split("/")[1],first_name:s.name,last_name:s.name,security_code:s.securityCode,google_token:s.google_token,invoice_number:s.invoiceNumber,invoice_token:U.value});p===0?(l.success=!0,l.message=r("invoicePaymentPage.paymentSuccessMessage"),l.amount=`$${(t=s.amount)==null?void 0:t.toFixed(2)}`||"",l.date=new Date().toLocaleString(),l.show=!0):(l.success=!1,l.message=d.message||r("invoicePaymentPage.paymentFailedMessage"),l.show=!0,(u=R.value)==null||u.reset())}catch(d){l.success=!1,l.message=d instanceof Error?d.message:r("invoicePaymentPage.paymentFailedMessage"),l.show=!0,(b=R.value)==null||b.reset()}finally{S.value=!1}}},Z=async()=>{var v,t;if(s.invoiceNumber){j.value=!0;try{const{data:u,code:b}=await ce({invoice_token:U.value,invoice_number:s.invoiceNumber});b===0?(i.total=u.total,i.amountPaid=u.amount_paid,i.amountDue=u.amount_due,i.surcharge_rate=String((v=u.surcharge_rate)==null?void 0:v.fee_value),i.surcharge_type=String((t=u.surcharge_rate)==null?void 0:t.fee_rate),A.value=Number(u.amount_due),i.currency=u.currency,D.value=!0):(i.total="0",i.amountPaid="0",i.amountDue="0",i.surcharge_rate="0",i.surcharge_type="0",A.value=999999999999,D.value=!1)}finally{j.value=!1}}},H=()=>{window.location.reload()};return(v,t)=>{const u=ie,b=te,d=ee,p=W,N=K,X=J;return f(),x("div",ve,[e("div",be,[D.value?(f(),x("div",ye,[e("div",ge,[e("h3",he,o(n(r)("invoicePaymentPage.invoiceSummary")),1),e("div",Pe,[e("div",ke,[e("div",xe,[e("span",null,o(n(r)("invoicePaymentPage.invoiceTotal")),1),e("span",we,o(i.total),1)]),e("div",Ne,[e("span",null,o(n(r)("invoicePaymentPage.invoiceAmountPaid")),1),e("span",Ve,o(i.amountPaid),1)]),e("div",Ce,[e("span",null,o(n(r)("invoicePaymentPage.invoiceAmountDue")),1),e("span",De,o(i.amountDue),1)]),e("div",Ie,[t[9]||(t[9]=e("span",{class:"mr-1"},"Surcharge: ",-1)),i.surcharge_type==="1"?(f(),x("span",Ae,o(i.surcharge_rate)+"% ",1)):(f(),x("span",Xe,o(i.surcharge_rate),1))]),e("div",Fe,[t[10]||(t[10]=e("span",null,"Actual Payment Amount: ",-1)),e("span",Se,[e("span",null,o(q()),1)])])])])])])):y("",!0),l.show?(f(),x("div",je,[e("div",{class:F(["result-container border-round p-4",l.success?"success":"error"])},[e("div",Re,[e("i",{class:F(l.success?"pi pi-check-circle":"pi pi-times-circle"),style:{"font-size":"4rem"}},null,2)]),e("h2",Te,o(l.success?n(r)("invoicePaymentPage.paymentSuccessTitle"):n(r)("invoicePaymentPage.paymentFailedTitle")),1),e("p",Ue,o(l.message),1),l.success?(f(),x("div",$e,[e("div",Ee,[e("div",Ye,[e("div",Be,[e("span",null,o(n(r)("invoicePaymentPage.amountPaid")),1),e("span",qe,o(l.amount),1)]),e("div",Me,[e("span",null,o(n(r)("invoicePaymentPage.paymentDate")),1),e("span",Le,o(l.date),1)])])])])):y("",!0),e("div",ze,[m(u,{type:"button",label:l.success?n(r)("invoicePaymentPage.done"):n(r)("invoicePaymentPage.tryAgain"),icon:l.success?"pi pi-check":"pi pi-refresh",class:F(l.success?"p-button-success":"p-button-primary"),onClick:t[0]||(t[0]=a=>l.success?H():l.show=!1)},null,8,["label","icon","class"])])],2)])):y("",!0),Y.value?(f(),x("div",Ge,[S.value?(f(),g(b,{key:0,class:"loading-spinner","stroke-width":"4"})):y("",!0),m(n(le),{ref_key:"formRef",ref:E,"validation-schema":n(B),onSubmit:G},{default:_(()=>[e("div",Ze,[e("div",{class:F(["col-1",{"md:col-1":D.value}])},[m(n(P),{modelValue:s.invoiceNumber,"onUpdate:modelValue":t[1]||(t[1]=a=>s.invoiceNumber=a),name:"invoiceNumber"},{default:_(({field:a,errorMessage:c})=>[e("div",He,[e("label",Oe,o(n(r)("invoicePaymentPage.invoiceNumber"))+" *",1),e("div",Qe,[m(d,V({id:"invoiceNumber"},a,{class:"w-full",placeholder:"Please enter invoice number"}),null,16),m(u,{type:"button",label:n(r)("invoicePaymentPage.fetchInvoice"),class:"fetch-button",loading:j.value,onClick:Z},null,8,["label","loading"])]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.amount,"onUpdate:modelValue":t[2]||(t[2]=a=>s.amount=a),name:"amount"},{default:_(({field:a,errorMessage:c,handleChange:O})=>[e("div",Je,[e("label",Ke,o(n(r)("invoicePaymentPage.amountOfPayment"))+" ("+o(i.currency)+")",1),m(N,{id:"amount",modelValue:a.value,"onUpdate:modelValue":Q=>a.value=Q,class:"w-full",placeholder:n(r)("invoicePaymentPage.atLeastTwoDecimalPlaces"),"min-fraction-digits":2,"max-fraction-digits":2,max:999999999999,disabled:Number(i.total)===0||Number(i.amountDue)===0,onValueChange:O},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","onValueChange"]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.email,"onUpdate:modelValue":t[3]||(t[3]=a=>s.email=a),name:"email"},{default:_(({field:a,errorMessage:c})=>[e("div",We,[e("label",et,o(n(r)("invoicePaymentPage.email")),1),m(d,V({id:"email"},a,{disabled:Number(i.total)===0,class:"w-full",placeholder:"<EMAIL>"}),null,16,["disabled"]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.name,"onUpdate:modelValue":t[4]||(t[4]=a=>s.name=a),name:"name"},{default:_(({field:a,errorMessage:c})=>[e("div",tt,[e("label",at,o(n(r)("invoicePaymentPage.name")),1),m(d,V({id:"name"},a,{disabled:Number(i.total)===0,class:"w-full",placeholder:"Full Name"}),null,16,["disabled"]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.creditCardNumber,"onUpdate:modelValue":t[5]||(t[5]=a=>s.creditCardNumber=a),as:"div",class:"field mb-4",name:"creditCardNumber"},{default:_(({field:a,errorMessage:c})=>[e("label",st,o(n(r)("invoicePaymentPage.creditCardNumber")),1),m(d,V({id:"creditCardNumber"},a,{disabled:Number(i.total)===0,class:"w-full",placeholder:"XXXX XXXX XXXX XXXX"}),null,16,["disabled"]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.expiryDate,"onUpdate:modelValue":t[6]||(t[6]=a=>s.expiryDate=a),as:"div",class:"field mb-4",name:"expiryDate"},{default:_(({field:a,errorMessage:c})=>[e("div",ot,[e("label",nt,o(n(r)("invoicePaymentPage.expireDate")),1),m(X,{"model-value":a.value,disabled:Number(i.total)===0,placeholder:"MM/YY",mask:"99/99",class:"w-full"},null,8,["model-value","disabled"]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.securityCode,"onUpdate:modelValue":t[7]||(t[7]=a=>s.securityCode=a),as:"div",class:"field mb-4",name:"securityCode"},{default:_(({field:a,errorMessage:c})=>[e("label",it,o(n(r)("invoicePaymentPage.securityCode")),1),m(d,V({id:"securityCode"},a,{disabled:Number(i.total)===0,class:"w-full",placeholder:"123"}),null,16,["disabled"]),c?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(c),1)]),_:2},1024)):y("",!0)]),_:1},8,["modelValue"]),m(n(P),{modelValue:s.google_token,"onUpdate:modelValue":t[8]||(t[8]=a=>s.google_token=a),name:"google_token"},{default:_(({errorMessage:a})=>[e("div",lt,[e("label",rt,o(n(r)("invoicePaymentPage.verification")||"Verification"),1),m(re,{ref_key:"recaptchaRef",ref:R,class:"mb-2",onVerify:M,onExpired:L,onError:z},null,512),a?(f(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:_(()=>[k(o(a),1)]),_:2},1024)):y("",!0)])]),_:1},8,["modelValue"]),e("div",ct,[m(u,{type:"submit",label:n(r)("invoicePaymentPage.submit"),class:"w-full p-button-primary submit-button"},null,8,["label"])])],2)])]),_:1},8,["validation-schema"])])):y("",!0)])])}}}),wt=fe(mt,[["__scopeId","data-v-1900c39d"]]);export{wt as default};
