import{s as ee}from"./index-CTms-Les.js";import{s as te}from"./index-CBr4Z_s8.js";import{s as se}from"./index-BlFKIn6Y.js";import{s as ae}from"./index-CEksU3zE.js";import{s as oe}from"./index-D7FsVJ-c.js";import{s as ne}from"./index-DMmJJ4dH.js";import{s as le}from"./index-BFXZ3UuW.js";import{s as ie}from"./index-AtQkrpJ9.js";import{d as re,k as ue,r as C,B as de,Q as Y,c as ce,a as x,b as e,q as f,A as I,t as l,u as d,e as c,s as me,p as k,w as p,F as pe,y as B,z as be,m as V,v as D,j as _,o as m}from"./index-BPwFJVSY.js";import{t as _e,c as fe,b as P}from"./index.esm-DOIZz6Kf.js";import{D as M}from"./decimal-LZMqkRTm.js";import{b as ve}from"./BillbuddyLogo-FvDXaJ4h.js";import{c as ye}from"./invioce-payment-Bs21HoFI.js";import{G as he}from"./index-DVRN33rP.js";import{g as ge,p as Ne}from"./integrations-C_ojvdY3.js";import{f as E}from"./date-D3KeWVvs.js";import{_ as xe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DDQWKtix.js";import"./dayjs.min-Bar1rHDB.js";const we={class:"payment-container"},ke={class:"payment-content border-round overflow-hidden"},Ve={key:0,class:"payment-result p-4 dark:text-white"},Ce={class:"result-icon text-center mb-4"},Pe={class:"text-center mb-3"},De={class:"text-center mb-4"},Me={key:0,class:"transaction-details border-1 border-round p-3 mb-4"},Ae={class:"flex flex-col gap-2"},Ie={class:"flex justify-center mb-2"},Se={class:"font-medium"},qe={class:"flex justify-center mb-2"},Ue={class:"font-medium"},Re={class:"flex justify-content-center"},Xe={key:1,class:"payment-form relative"},Ye={class:"flex justify-center mb-4"},Be=["src"],Fe={class:"invoice-header"},$e={class:"invoice-parties"},je={class:"recipient"},Ee={class:"invoice-meta"},Te={class:"meta-item"},Le={class:"meta-item"},ze={class:"meta-item"},Ge={class:"invoice-items"},Qe={class:"invoice-summary"},He={class:"summary-row"},Je={class:"amount-due flex justify-between items-center"},Ke={class:"!mb-0"},Oe={class:"grid"},We={class:"field mb-4 border-y-1 border-y-gray-200 py-4"},Ze={class:"flex justify-content-between gap-2"},et={class:"flex items-center gap-2"},tt={class:"flex items-center gap-2"},st={class:"payment-forms"},at={class:"payment-form"},ot={class:"field mb-4"},nt={for:"name",class:"block mb-2"},lt={for:"creditCardNumber",class:"block mb-2"},it={class:"flex flex-start mb-4"},rt={class:"field mb-4"},ut={for:"expiryDate",class:"block mb-2"},dt={for:"securityCode",class:"block mb-2"},ct={class:"field mb-4"},mt={class:"block mb-2"},pt={key:0,class:"payment-invoice"},bt={class:"col-1 md:col-4 pl-md-4"},_t={class:"invoice-details border-1 border-round h-full"},ft={class:"flex justify-content-between mb-2 px-4 mt-2"},vt={class:"font-medium font-bold"},yt={class:"flex justify-content-between mb-2 px-4"},ht={class:"font-medium font-bold"},gt={class:"flex justify-content-between mb-2 px-4"},Nt={class:"font-medium font-bold"},xt={class:"flex justify-content-between mb-2 px-4"},wt={key:0,class:"font-medium font-bold"},kt={key:1,class:"font-medium font-bold"},Vt={class:"flex justify-content-between mb-2 px-4"},Ct={class:"font-medium ml-2 font-bold"},Pt={class:"field"},Dt=re({__name:"payment",setup(Mt){const{t:v}=ue(),T=de(),S=C(T.params.id),F=C(),q=C(!1),U=C(!1),$=C(!1),i=Y({show:!1,success:!1,message:"",amount:"",date:""}),L=ce(()=>!i.show),R=C(null),A=C(!1),j=C(999999999999),z=_e(fe({name:P().required("Name is required"),creditCardNumber:P().when("paymentMethod",{is:o=>o===2||o===3,then:o=>o.required("Card number must be between 13 and 19 digits").min(13,"Card number must be between 13 and 19 digits").max(19,"Card number must be between 13 and 19 digits").test("luhn","Invalid card number",t=>{if(!t)return!1;const y=t.replace(/\D/g,"");let b=0,r=!1;for(let h=y.length-1;h>=0;h--){let g=Number.parseInt(y.charAt(h));r&&(g*=2,g>9&&(g-=9)),b+=g,r=!r}return b%10===0}),otherwise:o=>o.notRequired()}),expiryDate:P().when("paymentMethod",{is:o=>o===2||o===3,then:o=>o.required("Expiry date must be in MM/YY format").matches(/^\d{2}\/\d{2}$/,"Expiry date must be in MM/YY format").test("expiry","Card has expired or invalid date",t=>{if(!t)return!1;const[y,b]=t.split("/"),r=new Date,h=r.getFullYear()%100,g=r.getMonth()+1,w=Number.parseInt(y),N=Number.parseInt(b);return w<1||w>12?!1:!(N<h||N===h&&w<g)}),otherwise:o=>o.notRequired()}),securityCode:P().when("paymentMethod",{is:o=>o===2||o===3,then:o=>o.required("Security code must be 3 or 4 digits").matches(/^\d{3,4}$/,"Security code must be 3 or 4 digits"),otherwise:o=>o.notRequired()}),google_token:P().required("Please complete the reCAPTCHA verification"),bsbNumber:P().when("paymentMethod",{is:1,then:o=>o.required("BSB Number is required").max(6,"BSB Number must be less than 6 digits"),otherwise:o=>o.notRequired()}),accountName:P().when("paymentMethod",{is:1,then:o=>o.required("Account Name is required"),otherwise:o=>o.notRequired()}),accountNumber:P().when("paymentMethod",{is:1,then:o=>o.required("Account Number is required"),otherwise:o=>o.notRequired()})})),n=Y({invoiceNumber:"",amount:0,paymentMethod:2,email:"",name:"",creditCardNumber:"",expiryDate:"",securityCode:"",recaptcha:!1,bsbNumber:"",accountName:"",accountNumber:"",google_token:""}),s=Y({total:"0",amountPaid:"0",amountDue:"0",surcharge_rate:"0",surcharge_type:"0",created_at:"",currency:"AUD",customer:{name:"",customer_id:null},customer_id:"",due_date:"",id:0,invoice_id:"",invoice_number:"",line_items:[],sub_total:"0"}),G=()=>s.surcharge_type==="1"?new M(n.amount||0).mul(new M(1).add(new M(s.surcharge_rate||0).div(100))).toFixed(2):new M(n.amount||0).add(new M(s.surcharge_rate||0)).toFixed(2),Q=o=>{o&&(n.google_token=o,A.value=!0)},H=()=>{A.value=!1},J=()=>{A.value=!1},K=async o=>{var t,y;if(A.value||(n.recaptcha=!1),!!o.google_token){U.value=!0;try{const b={name:n.name,google_token:n.google_token,invoice_number:n.invoiceNumber,type:n.paymentMethod,invoice_token:S.value};n.paymentMethod===2?b.card={card_number:n.creditCardNumber,expiration_month:n.expiryDate.split("/")[0],expiration_year:n.expiryDate.split("/")[1],security_code:n.securityCode}:n.paymentMethod===1&&(b.bank={bsb:n.bsbNumber,account_name:n.accountName,account_no:n.accountNumber});const{data:r,code:h}=await Ne(b);h===0?(i.success=!0,i.message=v("invoicePaymentPage.paymentSuccessMessage"),i.amount=`$${s.amountDue}`||"",i.date=new Date().toLocaleString(),i.show=!0):(i.success=!1,i.message=r.message||v("invoicePaymentPage.paymentFailedMessage"),i.show=!0,(t=R.value)==null||t.reset())}catch(b){i.success=!1,i.message=b instanceof Error?b.message:v("invoicePaymentPage.paymentFailedMessage"),i.show=!0,(y=R.value)==null||y.reset()}finally{U.value=!1}}};(async(o={})=>{var t,y,b;if(n.invoiceNumber&&o.invoice_number||S.value&&o.id){$.value=!0;try{const{data:r,code:h,message:g}=await ge(o);h===0?(s.customer.name=r.customer.name,s.customer_id=r.customer.customer_id,s.invoice_number=r.invoice_number,s.invoice_id=r.invoice_id,s.created_at=r.created_at,s.due_date=r.due_date,s.line_items=r.line_items,s.sub_total=r.sub_total,s.total=r.sub_total,s.amountPaid=r.amount_paid,s.amountDue=r.amount_due,s.surcharge_rate=String((t=r.surcharge_fees)==null?void 0:t.fee_value),s.surcharge_type=String((y=r.surcharge_fees)==null?void 0:y.fee_rate),j.value=Number(r.amount_due),s.currency="AUD",r.invoice_number&&(n.invoiceNumber=r.invoice_number,(b=F.value)==null||b.setFieldValue("invoiceNumber",r.invoice_number))):(s.total="0",s.amountPaid="0",s.amountDue="0",s.surcharge_rate="0",s.surcharge_type="0",j.value=999999999999,q.value=!1,i.success=!1,i.message=g||v("invoicePaymentPage.paymentFailedMessage"),i.show=!0)}finally{$.value=!1}}})({id:S.value});const O=()=>{window.location.reload()};return(o,t)=>{const y=me,b=ie,r=le,h=ne,g=oe,w=ae,N=se,W=te,Z=ee;return m(),x("div",we,[e("div",ke,[i.show?(m(),x("div",Ve,[e("div",{class:I(["result-container border-round p-4 py-8",i.success?"success":"error"])},[e("div",Ce,[e("i",{class:I(i.success?"pi pi-check-circle":"pi pi-times-circle"),style:{"font-size":"4rem"}},null,2)]),e("h2",Pe,l(i.success?"Submit Success":"Submit Failed"),1),e("p",De,l(i.message),1),i.success?(m(),x("div",Me,[e("div",Ae,[e("div",Ie,[e("span",null,l(d(v)("invoicePaymentPage.amountPaid")),1),e("span",Se,l(i.amount),1)]),e("div",qe,[e("span",null,l(d(v)("invoicePaymentPage.paymentDate")),1),e("span",Ue,l(i.date),1)])])])):f("",!0),e("div",Re,[c(y,{type:"button",label:i.success?d(v)("invoicePaymentPage.done"):d(v)("invoicePaymentPage.tryAgain"),icon:i.success?"pi pi-check":"pi pi-refresh",class:I(i.success?"p-button-success":"p-button-primary"),onClick:t[0]||(t[0]=a=>i.success?O():i.show=!1)},null,8,["label","icon","class"])])],2)])):f("",!0),L.value?(m(),x("div",Xe,[e("div",Ye,[e("img",{src:d(ve),alt:"logo",class:"w-100"},null,8,Be)]),U.value?(m(),k(b,{key:0,class:"loading-spinner","stroke-width":"4"})):s.created_at?(m(),k(h,{key:1,class:"invoice-card mb-4"},{content:p(()=>[e("div",Fe,[e("div",$e,[e("div",je,[t[10]||(t[10]=e("h3",null,"To",-1)),e("p",null,l(s.customer.name),1)])]),e("div",Ee,[e("div",Te,[t[11]||(t[11]=e("h3",null,"Invoice number",-1)),e("p",null,l(s.invoice_number),1)]),e("div",Le,[t[12]||(t[12]=e("h3",null,"Issue date",-1)),e("p",null,l(d(E)(s.created_at,"DD MMM YYYY")),1)]),e("div",ze,[t[13]||(t[13]=e("h3",null,"Due date",-1)),e("p",null,l(d(E)(s.due_date,"DD MMM YYYY")),1)])])]),c(r),e("div",Ge,[e("table",null,[t[14]||(t[14]=e("thead",null,[e("tr",null,[e("th",null,"Description"),e("th",null,"Quantity"),e("th",null,"Unit Price"),e("th",null,"Amount")])],-1)),e("tbody",null,[(m(!0),x(B,null,be(s.line_items,(a,u)=>(m(),x("tr",{key:u},[e("td",null,l(a.description),1),e("td",null,l(a.quantity),1),e("td",null,l(a.unit_amount),1),e("td",null,l(a.line_amount),1)]))),128))])])]),e("div",Qe,[e("div",He,[t[15]||(t[15]=e("span",null,"Subtotal",-1)),e("span",null,l(s.sub_total),1)]),e("div",Je,[t[16]||(t[16]=e("h3",{class:"!mb-0"}," Amount due ",-1)),e("h2",Ke,l(s.currency)+" "+l(s.sub_total),1)])])]),_:1})):f("",!0),c(d(pe),{ref_key:"formRef",ref:F,"validation-schema":d(z),onSubmit:K},{default:p(()=>[e("div",Oe,[e("div",{class:I(["col-1",{"md:col-1":q.value}])},[c(d(V),{modelValue:n.paymentMethod,"onUpdate:modelValue":t[1]||(t[1]=a=>n.paymentMethod=a),name:"paymentMethod"},{default:p(({field:a,handleChange:u})=>[e("div",We,[e("div",Ze,[e("div",et,[c(g,{modelValue:a.value,"onUpdate:modelValue":X=>a.value=X,"input-id":"creditCard",name:"paymentMethod",value:2,onValueChange:u},null,8,["modelValue","onUpdate:modelValue","value","onValueChange"]),t[17]||(t[17]=e("label",{for:"creditCard"},"Card",-1))]),e("div",tt,[c(g,{modelValue:a.value,"onUpdate:modelValue":X=>a.value=X,"input-id":"bankAccount",name:"paymentMethod",value:1,onValueChange:u},null,8,["modelValue","onUpdate:modelValue","value","onValueChange"]),t[18]||(t[18]=e("label",{for:"bankAccount"},"Bank Account",-1))])])])]),_:1},8,["modelValue"]),e("div",st,[e("div",at,[c(d(V),{modelValue:n.name,"onUpdate:modelValue":t[2]||(t[2]=a=>n.name=a),name:"name"},{default:p(({field:a,errorMessage:u})=>[e("div",ot,[e("label",nt,l(d(v)("invoicePaymentPage.name")),1),c(w,D({id:"name"},a,{disabled:Number(s.total)===0,class:"w-full",placeholder:"Full Name"}),null,16,["disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)])]),_:1},8,["modelValue"]),n.paymentMethod===2?(m(),x(B,{key:0},[c(d(V),{modelValue:n.creditCardNumber,"onUpdate:modelValue":t[3]||(t[3]=a=>n.creditCardNumber=a),as:"div",class:"field mb-4",name:"creditCardNumber"},{default:p(({field:a,errorMessage:u})=>[e("label",lt,[_(l(d(v)("invoicePaymentPage.creditCardNumber"))+" ",1),t[19]||(t[19]=e("span",{class:"text-red-500"},"*",-1))]),c(w,D({id:"creditCardNumber"},a,{disabled:Number(s.total)===0,class:"w-full",placeholder:"XXXX XXXX XXXX XXXX"}),null,16,["disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)]),_:1},8,["modelValue"]),e("div",it,[c(W,{src:d(ye),alt:"creditCardNumber",style:{width:"110px"}},null,8,["src"])]),c(d(V),{modelValue:n.expiryDate,"onUpdate:modelValue":t[4]||(t[4]=a=>n.expiryDate=a),as:"div",class:"field mb-4",name:"expiryDate"},{default:p(({field:a,errorMessage:u})=>[e("div",rt,[e("label",ut,[_(l(d(v)("invoicePaymentPage.expireDate"))+" ",1),t[20]||(t[20]=e("span",{class:"text-red-500"},"*",-1))]),c(Z,{"model-value":a.value,disabled:Number(s.total)===0,placeholder:"MM/YY",mask:"99/99",class:"w-full"},null,8,["model-value","disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)])]),_:1},8,["modelValue"]),c(d(V),{modelValue:n.securityCode,"onUpdate:modelValue":t[5]||(t[5]=a=>n.securityCode=a),as:"div",class:"field mb-4",name:"securityCode"},{default:p(({field:a,errorMessage:u})=>[e("label",dt,[_(l(d(v)("invoicePaymentPage.securityCode"))+" ",1),t[21]||(t[21]=e("span",{class:"text-red-500"},"*",-1))]),c(w,D({id:"securityCode"},a,{disabled:Number(s.total)===0,class:"w-full",placeholder:"123"}),null,16,["disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)]),_:1},8,["modelValue"])],64)):f("",!0),n.paymentMethod===1?(m(),x(B,{key:1},[c(d(V),{modelValue:n.bsbNumber,"onUpdate:modelValue":t[6]||(t[6]=a=>n.bsbNumber=a),as:"div",class:"field mb-4",name:"bsbNumber"},{default:p(({field:a,errorMessage:u})=>[t[22]||(t[22]=e("label",{for:"bsbNumber",class:"block mb-2"},[_("BSB Number "),e("span",{class:"text-red-500"},"*")],-1)),c(w,D({id:"bsbNumber"},a,{disabled:Number(s.total)===0,class:"w-full",placeholder:"123",maxlength:"6"}),null,16,["disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)]),_:1,__:[22]},8,["modelValue"]),c(d(V),{modelValue:n.accountName,"onUpdate:modelValue":t[7]||(t[7]=a=>n.accountName=a),as:"div",class:"field mb-4",name:"accountName"},{default:p(({field:a,errorMessage:u})=>[t[23]||(t[23]=e("label",{for:"accountName",class:"block mb-2"},[_("Account Name "),e("span",{class:"text-red-500"},"*")],-1)),c(w,D({id:"accountName"},a,{disabled:Number(s.total)===0,class:"w-full",placeholder:"Account Name"}),null,16,["disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)]),_:1,__:[23]},8,["modelValue"]),c(d(V),{modelValue:n.accountNumber,"onUpdate:modelValue":t[8]||(t[8]=a=>n.accountNumber=a),as:"div",class:"field mb-4",name:"accountNumber"},{default:p(({field:a,errorMessage:u})=>[t[24]||(t[24]=e("label",{for:"accountNumber",class:"block mb-2"},[_("Account Number "),e("span",{class:"text-red-500"},"*")],-1)),c(w,D({id:"accountNumber"},a,{disabled:Number(s.total)===0,class:"w-full",placeholder:"Account Number"}),null,16,["disabled"]),u?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(u),1)]),_:2},1024)):f("",!0)]),_:1,__:[24]},8,["modelValue"])],64)):f("",!0),c(d(V),{modelValue:n.google_token,"onUpdate:modelValue":t[9]||(t[9]=a=>n.google_token=a),name:"google_token"},{default:p(({errorMessage:a})=>[e("div",ct,[e("label",mt,l(d(v)("invoicePaymentPage.verification")||"Verification"),1),c(he,{ref_key:"recaptchaRef",ref:R,class:"mb-2",onVerify:Q,onExpired:H,onError:J},null,512),a?(m(),k(N,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[_(l(a),1)]),_:2},1024)):f("",!0)])]),_:1},8,["modelValue"])]),q.value?(m(),x("div",pt,[e("div",bt,[e("div",_t,[t[30]||(t[30]=e("h3",{class:"invoice-summary-title"}," Amount ",-1)),e("div",ft,[t[25]||(t[25]=e("span",null,"Invoice Total: ",-1)),e("span",vt,l(s.total),1)]),e("div",yt,[t[26]||(t[26]=e("span",null,"Invoice amount paid: ",-1)),e("span",ht,l(s.amountPaid),1)]),e("div",gt,[t[27]||(t[27]=e("span",null,[_("Invoice amount "),e("strong",null,"DUE"),_(": ")],-1)),e("span",Nt,l(s.amountDue),1)]),e("div",xt,[t[28]||(t[28]=e("span",{class:"mr-1"},"Surcharge: ",-1)),s.surcharge_type==="1"?(m(),x("span",wt,l(s.surcharge_rate)+"% ",1)):(m(),x("span",kt,l(s.surcharge_rate),1))]),e("div",Vt,[t[29]||(t[29]=e("span",null,"Actual Payment Amount: ",-1)),e("span",Ct,l(G()),1)])])])])):f("",!0)]),e("div",Pt,[c(y,{type:"submit",label:"SUBMIT",class:"w-full h-12 p-button-primary submit-button",severity:"warn"})])],2)])]),_:1},8,["validation-schema"])])):f("",!0)])])}}}),Kt=xe(Dt,[["__scopeId","data-v-682a621b"]]);export{Kt as default};
