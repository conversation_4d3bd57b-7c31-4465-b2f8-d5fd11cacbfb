import{V as s,U as e}from"./index-BPwFJVSY.js";const n=(t,a)=>e("/plan/list",{params:t,...a}),p=(t,a)=>s("/plan/create",t,a),c=t=>s("/plan/delete",{plan_ids:t}),r=t=>e("/plan/detail",{params:{plan_id:t}}),i=t=>s("/plan/update",t),l=t=>e("/plan/export",{params:t}),d=(t,a)=>s("/subscription/create",t,a),u=t=>s("/subscription/sendModPaymentMethodMail",t),m=(t,a)=>s("/subscription/update",t,a);export{m as a,r as b,d as c,p as d,i as e,l as f,n as g,c as r,u};
