import{d as C,k as q,l as R,r as f,a as U,b as s,e as l,t as r,u as a,w as n,p,q as u,j as g,s as j,i as M,o as c}from"./index-BPwFJVSY.js";import{s as A}from"./index-CCP8zFp2.js";import{s as L}from"./index-DES6SMz0.js";import{s as D}from"./index-BlFKIn6Y.js";import{s as E}from"./index-CEksU3zE.js";import{s as F}from"./index-CBr4Z_s8.js";import{h as Z,s as G}from"./index-Kfvu7AH2.js";import{o as H,b as J,s as v}from"./types-AmnC9OGP.js";import{_ as K}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DDQWKtix.js";import"./index-BjQt0t51.js";const O={class:"auth-container"},Q={class:"logo-wrap"},W={class:"logo"},X={class:"auth-content-wrap"},Y={class:"auth-content"},$={class:"title"},ee={class:"subtitle"},se={class:"flex flex-col gap-4"},ae={for:"email"},te={class:"flex flex-col gap-4"},oe={for:"password"},re={class:"flex flex-col gap-4"},ie={for:"confirmPassword"},le={class:"flex flex-col gap-4"},ne={class:"flex items-center gap-2"},ce={for:"agree"},de={class:"flex gap-4 justify-between"},me=C({__name:"registerView",setup(pe){const{t:e}=q(),P=M(),V=R(),_=f(!1),T=f({email:"",password:"",confirmPassword:"",agree:!1}),I=f(Z(H({email:v({message:e("validation.emailRequired")}).min(1,{message:e("validation.emailRequired")}).email({message:e("validation.emailInvalid")}),password:v().min(8,{message:e("validation.passwordMinLength")}).regex(/[A-Z]/,{message:e("validation.passwordUppercase")}).regex(/[a-z]/,{message:e("validation.passwordLowercase")}).regex(/\d/,{message:e("validation.passwordNumber")}),confirmPassword:v().min(1,{message:e("validation.confirmPasswordRequired")}),agree:J().refine(i=>i===!0,{message:e("validation.agreeTerms")})}).refine(i=>i.password===i.confirmPassword,{message:e("validation.passwordsNotMatch"),path:["confirmPassword"]}))),N=async({values:i,valid:d})=>{if(d)try{_.value=!0,await V.register(i.email,i.password),P.push("/login")}catch{}finally{_.value=!1}};return(i,d)=>{const S=F,z=E,m=D,w=L,B=A,h=j;return c(),U("div",O,[s("div",Q,[s("div",W,[l(S,{src:"/placeholder.svg",alt:"Image",width:"100%"})])]),s("div",X,[s("div",Y,[s("h1",$,r(a(e)("register.title")),1),s("p",ee,r(a(e)("register.subtitle")),1),l(a(G),{"initial-values":T.value,resolver:I.value,class:"flex flex-col gap-6 w-full",onSubmit:N},{default:n(o=>{var b,x,y,k;return[s("div",se,[s("label",ae,r(a(e)("register.email")),1),l(z,{name:"email",type:"text",placeholder:a(e)("register.email")},null,8,["placeholder"]),(b=o.email)!=null&&b.invalid?(c(),p(m,{key:0,severity:"error",size:"small",variant:"simple"},{default:n(()=>[g(r(o.email.error.message),1)]),_:2},1024)):u("",!0)]),s("div",te,[s("label",oe,r(a(e)("register.password")),1),l(w,{name:"password",type:"text",placeholder:a(e)("register.password"),"toggle-mask":"",fluid:"",feedback:!0},null,8,["placeholder"]),(x=o.password)!=null&&x.invalid?(c(),p(m,{key:0,severity:"error",size:"small",variant:"simple"},{default:n(()=>{var t;return[g(r((t=o.password.error)==null?void 0:t.message),1)]}),_:2},1024)):u("",!0)]),s("div",re,[s("label",ie,r(a(e)("register.confirmPassword")),1),l(w,{name:"confirmPassword",type:"text",placeholder:a(e)("register.confirmPassword"),"toggle-mask":"",fluid:"",feedback:!1},null,8,["placeholder"]),(y=o.confirmPassword)!=null&&y.invalid?(c(),p(m,{key:0,severity:"error",size:"small",variant:"simple"},{default:n(()=>{var t;return[g(r((t=o.confirmPassword.error)==null?void 0:t.message),1)]}),_:2},1024)):u("",!0)]),s("div",le,[s("div",ne,[l(B,{modelValue:o.agree,"onUpdate:modelValue":t=>o.agree=t,name:"agree",binary:""},null,8,["modelValue","onUpdate:modelValue"]),s("label",ce,r(a(e)("register.agreeTerms")),1)]),(k=o.agree)!=null&&k.invalid?(c(),p(m,{key:0,severity:"error",size:"small",variant:"simple"},{default:n(()=>{var t;return[g(r((t=o.agree.error)==null?void 0:t.message),1)]}),_:2},1024)):u("",!0)]),s("div",de,[l(h,{class:"!p-0",text:"",label:a(e)("register.haveAccount"),onClick:d[0]||(d[0]=t=>i.$router.push("/login"))},null,8,["label"])]),l(h,{loading:_.value,class:"!mt-6 w-full",type:"submit",severity:"secondary",label:a(e)("button.submit")},null,8,["loading","label"])]}),_:1},8,["initial-values","resolver","onSubmit"])])])])}}}),Pe=K(me,[["__scopeId","data-v-b1d36096"]]);export{Pe as default};
