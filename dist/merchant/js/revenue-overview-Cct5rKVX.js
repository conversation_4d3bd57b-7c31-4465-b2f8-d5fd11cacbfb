import{s as M}from"./index-BFXZ3UuW.js";import{g as L}from"./report-BieIWVzh.js";import{a as A,b as v}from"./format-3aITXa0f.js";import{d as C,i as S,r as p,f as k,a as j,o as N,b as l,e as z,h as D}from"./index-BPwFJVSY.js";import{i as F,a as Y}from"./install-CHL8RKGY.js";import{u as B,i as b,a as E,b as H,d as I,e as Q,c as q}from"./installCanvasRenderer-p5Q0jy4r.js";import{i as G}from"./install-Db2jL4kZ.js";import{i as T}from"./install-W_lq_5hT.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./plan-DCOwcHy2.js";const P={class:"breakdown mt-10"},R=C({__name:"revenue-overview",setup(J){const O=S();B([E,H,F,I,G,Q,Y,q,T]);const d=p(),s=p({title:{text:"Gross Transaction Value ($)",subtext:"",left:"10px",top:"10px",textStyle:{fontSize:14},subtextStyle:{fontSize:28,fontWeight:"bold",color:"#333",lineHeight:36,height:36}},graphic:[{type:"text",left:"center",top:"50px",height:28,lineHeight:28,textVerticalAlign:"middle",style:{text:"",fill:"#ff1111"}}],grid:{bottom:"20px",left:"60px",right:"20px",top:"100px"},color:["#9373f6"],xAxis:{type:"category",data:[]},yAxis:{type:"value",axisLabel:{formatter:v},max:250,interval:50},series:[{data:[1],type:"bar"}]}),x=p(),i=p({title:{text:"Net Transaction Value ($)",subtext:"",left:"10px",top:"10px",textStyle:{fontSize:14},subtextStyle:{fontSize:28,fontWeight:"bold",color:"#333",lineHeight:36,height:36}},graphic:[{type:"text",left:"center",top:"50px",height:28,lineHeight:28,textVerticalAlign:"middle",style:{text:"",fill:"#ff1111"}}],grid:{bottom:"20px",left:"60px",right:"20px",top:"100px"},color:["#9373f6"],xAxis:{type:"category",data:[]},yAxis:{type:"value",axisLabel:{formatter:v},max:250,interval:50},series:[{data:[1],type:"bar"}]}),y=p(),n=p({title:{text:"Gross volume by month",left:"10px",top:"10px",textStyle:{fontSize:14}},grid:{bottom:"20px",left:"60px",right:"40px",top:"45px"},color:["#9373f6","#dc9256"],xAxis:[{type:"category",data:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],axisPointer:{type:"shadow"},axisLabel:{formatter:(a,t)=>t===0||t===11?a:""}}],yAxis:[{type:"value",min:0,max:250,interval:50,axisLabel:{formatter:v}},{type:"value",min:0,max:25,interval:5,axisLabel:{formatter:"{value} %"}}],series:[{name:"Evaporation",type:"bar",data:[2,4.9,7,23.2,25.6,76.7,135.6,162.2,32.6,20,6.4,3.3]},{name:"Temperature",type:"line",yAxisIndex:1,data:[2,2.2,3.3,4.5,6.3,10.2,20.3,23.4,23,16.5,12,6.2]}],toolbox:{show:!0,itemSize:15,feature:{myTool2:{show:!0,title:"Expand",icon:"path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891",onclick(){window.$toast.add({summary:"myToolHandler2",detail:"myToolHandler2",life:3e3})}}}}}),c=p(),m=p({title:{text:"Gross volume by quarter",left:"10px",top:"10px",textStyle:{fontSize:14}},grid:{bottom:"50px",left:"60px",right:"40px",top:"50px"},legend:{data:["Gross volume","Year on year"],left:"left",bottom:5},color:["#9373f6","#dc9256"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:[{type:"category",data:["Q1","Q2","Q3","Q4"],axisPointer:{type:"shadow"}}],yAxis:[{type:"value",min:0,max:250,interval:50,axisLabel:{formatter:v}},{type:"value",min:0,max:25,interval:5,axisLabel:{formatter:"{value} %"}}],series:[{name:"Gross volume",type:"bar",data:[2,25.6,76.7,135.6,20,6.4,3.3],tooltip:{valueFormatter(a){return`${a}$`}}},{name:"Year on year",type:"line",yAxisIndex:1,tooltip:{valueFormatter(a){return`${a}%`}},data:[2,6.3,10.2,20.3,16.5,12,6.2]}]}),_=()=>{L().then(a=>{const t=a.data;s.value.title.subtext=`$${A(t.total.payment_amount)}`,s.value.graphic[0].style.text=`${t.yoy.total.payment_amount>0?"↑":"↓"}${Math.abs(t.yoy.total.payment_amount)}% YoY`,s.value.graphic[0].style.fill=t.yoy.total.payment_amount>0?"#4CAF50":"#FF5252",s.value.series[0].data=Object.values(t.monthly).map(e=>Number(e.payment_amount));const o=Math.max(...Object.values(t.monthly).map(e=>e.payment_amount)),u=Math.max(...Object.values(t.yoy.monthly).map(e=>e.payment_amount)),f=Math.max(...Object.values(t.quarterly).map(e=>e.payment_amount)),h=Math.max(...Object.values(t.yoy.quarterly).map(e=>e.payment_amount)),{max:g,interval:w}=r(o);s.value.yAxis={type:"value",axisLabel:{formatter:e=>v(e)},max:g,interval:w},i.value.title.subtext=`$${A(t.total.net_amount)}`,i.value.graphic[0].style.text=`${t.yoy.total.net_amount>0?"↑":"↓"}${Math.abs(t.yoy.total.net_amount)}% YoY`,i.value.graphic[0].style.fill=t.yoy.total.net_amount>0?"#4CAF50":"#FF5252",i.value.series[0].data=Object.values(t.monthly).map(e=>Number(e.net_amount)),i.value.yAxis={type:"value",axisLabel:{formatter:e=>v(e)},max:g,interval:w},n.value.series[0].data=Object.values(t.monthly).map(e=>Number(e.payment_amount)),n.value.series[1].data=Object.values(t.yoy.monthly).map(e=>Number(e.payment_amount)),n.value.xAxis[0].data=Object.keys(t.monthly).map(e=>e),n.value.yAxis[0]={type:"value",axisLabel:{formatter:e=>v(e)},min:0,max:r(o).max,interval:r(o).interval},n.value.yAxis[1]={type:"value",min:0,max:r(u).max,interval:r(u).interval,axisLabel:{formatter:"{value} %"}},m.value.series[0].data=Object.values(t.quarterly).map(e=>Number(e.payment_amount)),m.value.series[1].data=Object.values(t.yoy.quarterly).map(e=>Number(e.payment_amount)),m.value.xAxis[0].data=Object.keys(t.quarterly).map(e=>(console.log(e),e)),m.value.yAxis[0]={type:"value",axisLabel:{formatter:e=>v(e)},min:0,max:r(f).max,interval:r(f).interval},m.value.yAxis[1]={type:"value",min:0,max:r(h).max,interval:r(h).interval,axisLabel:{formatter:"{value} %"}},d.value&&d.value.setOption(s.value),x.value&&x.value.setOption(i.value),y.value&&y.value.setOption(n.value),c.value&&c.value.setOption(m.value)})};function r(a){const o=10**Math.floor(Math.log10(a)),u=Math.ceil(a/o)*o,f=u/5;return{max:u,interval:f}}k(()=>{const a=document.getElementById("gross"),t=document.getElementById("netAmount"),o=document.getElementById("breakdown"),u=document.getElementById("last-month");a&&(d.value=b(a),s.value&&d.value.setOption(s.value)),t&&(x.value=b(t),i.value&&x.value.setOption(i.value)),o&&(y.value=b(o),n.value&&y.value.setOption(n.value)),u&&(c.value=b(u),m.value&&c.value.setOption(m.value)),Promise.all([_()])});const $=()=>{O.push({name:"reportDetail"})};return(a,t)=>{const o=M;return N(),j("div",null,[t[4]||(t[4]=l("div",{class:"text-xl font-semibold"}," Revenue Overview ",-1)),l("div",null,[l("div",{class:"mt-6"},[t[1]||(t[1]=l("div",{class:"describe"}," Here's an overview of your key metrics over the last 12 months. ",-1)),l("div",{class:"chart flex gap-8 mt-6"},[l("div",{class:"border border-gray-200 rounded-lg h-[280px] w-md shadow-sm transition-shadow hover:shadow-md"},[l("div",{id:"gross",onClick:$})]),t[0]||(t[0]=l("div",{class:"border border-gray-200 rounded-lg h-[280px] w-md shadow-sm transition-shadow hover:shadow-md"},[l("div",{id:"netAmount"})],-1))])]),l("div",P,[t[2]||(t[2]=l("div",{class:"title font-semibold text-lg"}," Gross volume breakdown ",-1)),z(o),t[3]||(t[3]=D('<div class="describe w-2xl" data-v-8452c240><p class="leading-8" data-v-8452c240></p></div><div class="w-5xl h-[280px] mt-6" data-v-8452c240><div id="breakdown" data-v-8452c240></div></div><div class="mt-8" data-v-8452c240><div class="describe w-2xl" data-v-8452c240><p class="leading-8" data-v-8452c240></p><div class="w-5xl h-[280px] mt-6" data-v-8452c240><div id="last-month" data-v-8452c240></div></div></div></div>',3))])])])}}}),rt=V(R,[["__scopeId","data-v-8452c240"]]);export{rt as default};
