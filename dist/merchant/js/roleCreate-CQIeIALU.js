import{d as T,k as U,J as j,i as q,r as b,Q as I,f as D,a as E,o as h,b as m,e as n,w as c,u as a,m as y,p as g,q as k,t as u,v as V,j as _,s as K,F as O,bQ as Q,bR as z}from"./index-BPwFJVSY.js";import{s as J}from"./index-C-q-4jsU.js";import{s as W}from"./index-BlFKIn6Y.js";import{s as A}from"./index-CEksU3zE.js";import{t as G,c as H,b as S}from"./index.esm-DOIZz6Kf.js";import{u as X}from"./useListRefresh-D-tL0eHu.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-wTWY-tN1.js";import"./index-DgYXzrxo.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";const Z={class:"merchant-role-create-page"},M={class:"p-4 bg-white rounded-2xl"},ee={class:"field"},se={for:"name",class:"mb-2 block"},ae={class:"field"},oe={for:"slug",class:"mb-2 block"},te={for:"permissions",class:"mb-2 block"},le={class:"flex justify-content-end mt-6"},ne=T({name:"UserRoleCreate",__name:"roleCreate",setup(re){const{t:s}=U(),L=j(),C=q(),x=b(),f=b(!1),{backWithRefresh:$}=X("userRoleList",()=>{}),t=I({name:"",slug:"",permissions:[]}),w=b([]),R=i=>i.map(e=>{var r;return{key:String(e.id),label:e.name,data:e,children:(r=e==null?void 0:e.children)!=null&&r.length?R(e.children):[]}}),F=async()=>{const i=await Q();w.value=R(i.data)},N=G(H({name:S().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})).required(),slug:S().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})).matches(/^[a-z0-9-]+$/,"Slug must contain only lowercase letters, numbers, and hyphens").required()})),P=async()=>{var i;f.value=!0;try{const e=await((i=x.value)==null?void 0:i.validate()),r=[];for(const d in t.permissions)Object.prototype.hasOwnProperty.call(t.permissions,d)&&t.permissions[d].checked&&r.push(d);if(!e.valid){f.value=!1;return}const p={role_name:e.values.name,role_mark:e.values.slug,permissions:r},{code:v}=await z(p);if(v!==0)throw new Error("Failed to create role");L.add({severity:"success",summary:s("common.success"),detail:s("merchant.role.messages.createSuccess","Role created successfully"),life:3e3}),$()}catch(e){console.error("Failed to create role:",e)}finally{f.value=!1}},B=()=>{C.back()};return D(()=>{F()}),(i,e)=>{const r=A,p=W,v=J,d=K;return h(),E("div",Z,[m("div",M,[n(a(O),{ref_key:"formRef",ref:x,"validation-schema":a(N),class:"merchant-role-form flex flex-col gap-4",onSubmit:P},{default:c(()=>[n(a(y),{modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),name:"name",class:"form-col"},{default:c(({field:o,errorMessage:l})=>[m("div",ee,[m("label",se,u(a(s)("merchant.role.columns.name","Role Name"))+"*",1),n(r,V({id:"name"},o,{placeholder:a(s)("merchant.role.columns.name","Role Name"),class:"w-full",autofocus:""}),null,16,["placeholder"]),l?(h(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[_(u(l),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),n(a(y),{modelValue:t.slug,"onUpdate:modelValue":e[1]||(e[1]=o=>t.slug=o),name:"slug",class:"form-col"},{default:c(({field:o,errorMessage:l})=>[m("div",ae,[m("label",oe,u(a(s)("merchant.role.columns.slug","Slug"))+"*",1),n(r,V({id:"slug"},o,{placeholder:a(s)("merchant.role.columns.slug","Slug"),class:"w-full"}),null,16,["placeholder"]),l?(h(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[_(u(l),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),n(a(y),{name:"permissions",class:"form-col"},{default:c(({errorMessage:o})=>[m("label",te,u(a(s)("merchant.role.columns.permissions","Permissions")),1),n(v,{"selection-keys":t.permissions,"onUpdate:selectionKeys":e[2]||(e[2]=l=>t.permissions=l),value:w.value,"selection-mode":"checkbox",class:"w-full md:w-[30rem] !p-0",placeholder:"Select permissions"},{content:c(({node:l})=>[_(u(l),1)]),_:1},8,["selection-keys","value"]),o?(h(),g(p,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:c(()=>[_(u(o),1)]),_:2},1024)):k("",!0)]),_:1}),m("div",le,[n(d,{type:"button",label:a(s)("common.cancel"),icon:"pi pi-times",class:"p-button-text mr-2",onClick:B},null,8,["label"]),n(d,{type:"submit",label:a(s)("common.save"),icon:"pi pi-check",loading:f.value},null,8,["label","loading"])])]),_:1},8,["validation-schema"])])])}}}),ye=Y(ne,[["__scopeId","data-v-79f893ed"]]);export{ye as default};
