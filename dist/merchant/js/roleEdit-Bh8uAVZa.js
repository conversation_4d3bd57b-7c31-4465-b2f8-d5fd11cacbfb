import{d as I,k as O,J as A,i as Q,B as q,r as _,Q as z,f as J,a as K,o as g,b as t,t as u,u as n,e as d,w as p,m as y,p as b,q as k,v as F,j as x,s as M,F as W,bQ as G,bT as H,bU as X}from"./index-BPwFJVSY.js";import{s as Y}from"./index-C-q-4jsU.js";import{s as Z}from"./index-BlFKIn6Y.js";import{s as ee}from"./index-CEksU3zE.js";import{t as se}from"./vee-validate-zod-BFI0y82J.js";import{u as ae}from"./useListRefresh-D-tL0eHu.js";import{o as le,s as L}from"./types-AmnC9OGP.js";import{_ as oe}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-wTWY-tN1.js";import"./index-DgYXzrxo.js";import"./index-B8Vin95C.js";import"./index-CCP8zFp2.js";const te={class:"merchant-role-edit-page"},ne={class:"flex justify-between items-center mb-4"},re={class:"text-3xl"},ie={class:"p-4 bg-white rounded-2xl"},ce={class:"field"},me={for:"name",class:"mb-2 block"},de={class:"field"},ue={for:"slug",class:"mb-2 block"},pe={class:"field"},fe={for:"permissions",class:"mb-2 block"},he={class:"flex flex-col gap-2"},_e={class:"flex align-items-center"},ge={class:"flex justify-end mt-6 gap-2"},ve=I({name:"UserRoleEdit",__name:"roleEdit",setup(ye){const{backWithRefresh:N}=ae("userRoleList",()=>{}),{t:s}=O(),R=A(),j=Q(),B=q(),w=_(),f=_(!1),P=_(B.params.id),c=z({name:"",slug:"",permissions:[],localPermissions:{}}),S=_([]),V=new Map,v=l=>l.map(e=>{var r,a;return V.set(String(e.id),{key:String(e.id),label:e.name,data:e,children:(r=e==null?void 0:e.children)!=null&&r.length?v(e.children):[]}),{key:String(e.id),label:e.name,data:e,children:(a=e==null?void 0:e.children)!=null&&a.length?v(e.children):[]}}),E=async()=>{const l=await G();S.value=v(l.data)},T=l=>{var r,a,h;const e={};for(const m of l){let o=!1;if(m.children&&m.children.length===((a=(r=V.get(String(m.id)))==null?void 0:r.children)==null?void 0:a.length)&&(o=!0),e[m.id]={checked:!0,partialChecked:o},(h=m.children)!=null&&h.length){const i=T(m.children);Object.assign(e,i)}}return e},C=async()=>{try{f.value=!0;const l=await H(P.value),{name:e,slug:r,permissions:a}=l.data;c.name=e,c.slug=r,a&&Array.isArray(a)&&(c.localPermissions=T(a))}catch(l){console.error("Failed to fetch role detail:",l),R.add({severity:"error",summary:s("common.error"),detail:s("merchant.role.messages.fetchRolesFailed","Failed to fetch role detail"),life:3e3})}finally{f.value=!1}},U=se(le({name:L().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})),slug:L().min(3,s("validation.minLength",{min:3})).max(50,s("validation.maxLength",{max:50})).regex(/^[a-z0-9-]+$/,s("validation.alphanumericDashOnly"))})),$=async()=>{var l;f.value=!0;try{const e=await((l=w.value)==null?void 0:l.validate());if(!e.valid){f.value=!1;return}const r={role_name:e.values.name,role_mark:e.values.slug,permissions:Object.keys(c.localPermissions).map(h=>Number(h))},{code:a}=await X(P.value,r);a===0&&(R.add({severity:"success",summary:s("common.success"),detail:s("merchant.role.messages.updateSuccess","Role updated successfully"),life:3e3}),N())}catch(e){console.error("Failed to update role:",e)}finally{f.value=!1}},D=()=>{j.back()};return J(()=>{Promise.all([C()]).then(()=>{E()})}),(l,e)=>{const r=ee,a=Z,h=Y,m=M;return g(),K("div",te,[t("div",ne,[t("h1",re,u(n(s)("menu.userRoleEdit","Edit Role")),1)]),t("div",ie,[d(n(W),{ref_key:"formRef",ref:w,"validation-schema":n(U),class:"merchant-role-form flex flex-col gap-4",onSubmit:$},{default:p(()=>[d(n(y),{modelValue:c.name,"onUpdate:modelValue":e[0]||(e[0]=o=>c.name=o),name:"name",class:"form-col"},{default:p(({field:o,errorMessage:i})=>[t("div",ce,[t("label",me,u(n(s)("merchant.role.columns.name","Role Name"))+"*",1),d(r,F({id:"name"},o,{placeholder:n(s)("merchant.role.columns.name","Role Name"),class:"w-full",autofocus:""}),null,16,["placeholder"]),i?(g(),b(a,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[x(u(i),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),d(n(y),{modelValue:c.slug,"onUpdate:modelValue":e[1]||(e[1]=o=>c.slug=o),name:"slug",class:"form-col"},{default:p(({field:o,errorMessage:i})=>[t("div",de,[t("label",ue,u(n(s)("merchant.role.columns.slug","Slug"))+"*",1),d(r,F({id:"slug"},o,{placeholder:n(s)("merchant.role.columns.slug","Slug"),class:"w-full"}),null,16,["placeholder"]),i?(g(),b(a,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[x(u(i),1)]),_:2},1024)):k("",!0)])]),_:1},8,["modelValue"]),d(n(y),{name:"permissions",class:"form-col"},{default:p(({errorMessage:o})=>[t("div",pe,[t("label",fe,u(n(s)("merchant.role.columns.permissions","Permissions")),1),t("div",he,[d(h,{"selection-keys":c.localPermissions,"onUpdate:selectionKeys":e[2]||(e[2]=i=>c.localPermissions=i),value:S.value,"selection-mode":"checkbox","meta-key-selection":!1,class:"w-full md:w-[30rem] !p-0"},{default:p(({node:i})=>[t("div",_e,[t("span",null,u(i.label),1)])]),_:1},8,["selection-keys","value"])]),o?(g(),b(a,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:p(()=>[x(u(o),1)]),_:2},1024)):k("",!0)])]),_:1}),t("div",ge,[d(m,{type:"button",label:n(s)("common.cancel"),icon:"pi pi-times",class:"p-button-text mr-2",onClick:D},null,8,["label"]),d(m,{type:"submit",label:n(s)("common.save"),icon:"pi pi-check",loading:f.value},null,8,["label","loading"])])]),_:1},8,["validation-schema"])])])}}}),je=oe(ve,[["__scopeId","data-v-8cd922d7"]]);export{je as default};
