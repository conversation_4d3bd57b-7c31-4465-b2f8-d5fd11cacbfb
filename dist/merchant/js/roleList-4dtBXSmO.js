import{d as X,k as G,J as j,i as H,r as c,c as K,a as _,o as d,e as n,q as g,u as s,s as Q,w as u,p as Y,b as v,t as D,X as Z,bV as ee,bJ as se}from"./index-BPwFJVSY.js";import{_ as te}from"./BaseDataTableActions.vue_vue_type_style_index_0_lang-D0gTfNRP.js";import{S as L,_ as oe}from"./BaseSearch.vue_vue_type_style_index_0_lang-CD3Y6vFD.js";import{B as ae}from"./BaseDataTable-BVkRnzNI.js";import{u as re}from"./useListRefresh-D-tL0eHu.js";import{u as le,P as r}from"./usePermissions-PkVCxleQ.js";import{u as ie}from"./useRequestList-DvWcnSzt.js";import{f as ne}from"./date-D3KeWVvs.js";import{_ as me}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-CCP8zFp2.js";import"./index-CEksU3zE.js";import"./index-DgBXOSrM.js";import"./index-D7FsVJ-c.js";import"./index-Cbodd6BQ.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DlkyKXFh.js";import"./index-B8Vin95C.js";import"./index-BsWpeBjZ.js";import"./index-CxOnO8if.js";import"./index-BJi8tw4J.js";import"./index-BFXZ3UuW.js";import"./dayjs.min-Bar1rHDB.js";const ce={class:"merchant-role-list-page"},de={key:0,class:"flex gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8"},ue={class:"confirmation-content"},pe={key:0},he=X({name:"merchantRoleList",__name:"roleList",setup(fe){const{hasPermission:i,hasAnyPermission:x}=le(),{t}=G(),E=j(),R=H(),p=ie({requestFn:se}),{list:T,loading:y,total:C,refresh:S,reset:w,setSearchParams:P,search:k,onPageChange:A,failed:F,failureMessage:O}=p;re("userRoleList",w);const B=c([{field:"name",header:t("merchant.role.columns.name","Role Name"),style:{minWidth:"150px"},sortable:!0,sortField:"name"},{field:"slug",header:t("merchant.role.columns.slug","Slug"),style:{minWidth:"150px"}},{field:"created_at",header:t("common.created","Created Date"),style:{minWidth:"150px"},sortable:!0,template:"date",sortField:"created_at"},{field:"action",header:"",style:{width:"60px"},template:"action",alignFrozen:"right",frozen:!0}]),b=c({name:"","created_at[]":[]}),V=K(()=>[{name:"name",label:t("merchant.role.columns.name","Role Name"),type:L.TEXT,placeholder:t("merchant.role.search.namePlaceholder","Search by role name"),maxlength:50,defaultValue:""},{name:"created_at[]",label:t("common.created","Created Date"),type:L.DATE_RANGE,placeholder:t("merchant.search.datePlaceholder","Select Date Range"),defaultValue:[]}]),$=a=>{P(a),k()},N=a=>{const{sortField:o,sortOrder:h}=a;p.setParams({sort_by:o,sort_order:h===1?"asc":"desc"}),p.search()},U=()=>{i(r.ROLE_CREATE)&&R.push({name:"userRoleCreate"})},q=a=>{i(r.ROLE_UPDATE)&&R.push({name:"userRoleEdit",params:{id:a.id}})},m=c(!1),l=c(null),z=a=>{i(r.ROLE_DELETE)&&(l.value=a,m.value=!0)},M=()=>{var a;(a=l.value)!=null&&a.id&&(m.value=!1,ee(String(l.value.id)).then(o=>{E.add({severity:"success",summary:t("common.success","Success"),detail:t("merchant.role.messages.deleteSuccess","Role deleted successfully")}),S()}).catch(o=>{console.error("Failed to delete role:",o),E.add({severity:"error",summary:t("common.error","Error"),detail:t("merchant.role.messages.deleteFailed","Failed to delete role")})}),l.value=null)};return(a,o)=>{const h=oe,f=Q,W=te,I=Z;return d(),_("div",ce,[n(h,{modelValue:b.value,"onUpdate:modelValue":o[0]||(o[0]=e=>b.value=e),loading:s(y),"basic-search-fields":V.value,onSearch:$},null,8,["modelValue","loading","basic-search-fields"]),s(i)(s(r).ROLE_CREATE)?(d(),_("div",de,[n(f,{label:"CREATE A ROLE",icon:"pi pi-plus",severity:"warn",class:"!px-8",onClick:U})])):g("",!0),n(ae,{columns:B.value,value:s(T),"row-hover":!0,loading:s(y),"total-records":s(C),paginator:!0,rows:50,lazy:!0,"data-key":"id","show-search-bar":!1,"show-multiple-column":!1,scrollable:!0,"search-placeholder":"Search roles...",failed:s(F),"striped-rows":!1,"failure-message":s(O),onSort:N,onPage:o[1]||(o[1]=e=>s(A)(e))},{date:u(({data:e})=>[v("div",null,D(s(ne)(e.created_at)),1)]),action:u(({data:e})=>[s(x)([s(r).ROLE_UPDATE,s(r).ROLE_DELETE])&&(e==null?void 0:e.slug)!=="administrator"?(d(),Y(W,{key:0,loading:e.__loading,"is-show-delete":(e==null?void 0:e.slug)!=="administrator"&&s(i)(s(r).ROLE_DELETE),"is-show-edit":(e==null?void 0:e.slug)!=="administrator"&&s(i)(s(r).ROLE_UPDATE),"is-show-detail":!1,onEdit:J=>q(e),onDelete:J=>z(e)},null,8,["loading","is-show-delete","is-show-edit","onEdit","onDelete"])):g("",!0)]),_:1},8,["columns","value","loading","total-records","failed","failure-message"]),n(I,{visible:m.value,"onUpdate:visible":o[3]||(o[3]=e=>m.value=e),style:{width:"450px"},header:s(t)("merchant.role.dialogs.confirmDelete","确认删除"),modal:!0},{footer:u(()=>[n(f,{label:s(t)("common.no","取消"),icon:"pi pi-times",text:"",onClick:o[2]||(o[2]=e=>m.value=!1)},null,8,["label"]),n(f,{label:s(t)("common.yes","确定"),icon:"pi pi-check",text:"",onClick:M},null,8,["label"])]),default:u(()=>{var e;return[v("div",ue,[o[4]||(o[4]=v("i",{class:"pi pi-exclamation-triangle mr-3",style:{"font-size":"2rem"}},null,-1)),l.value?(d(),_("span",pe,D(s(t)("merchant.role.dialogs.deleteConfirmMessage",{name:l.value.name},`确定要删除角色 "${(e=l.value)==null?void 0:e.name}" 吗？该操作不可恢复。`)),1)):g("",!0)])]}),_:1},8,["visible","header"])])}}}),ze=me(he,[["__scopeId","data-v-32258e7a"]]);export{ze as default};
