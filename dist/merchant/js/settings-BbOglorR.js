import{d as U,k as B,J as N,r as v,Q as q,f as S,a as g,o as n,b as e,t as r,u as t,e as c,p as w,q as y,A as h,w as m,j as p,s as M,bW as R}from"./index-BPwFJVSY.js";import{s as T}from"./index-BlFKIn6Y.js";import{s as $}from"./index-DES6SMz0.js";import{s as D}from"./index-AtQkrpJ9.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DDQWKtix.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";const j={class:"settings-container"},z={class:"settings-header"},A={class:"settings-title"},E={class:"settings-content"},F={key:0,class:"loading-container"},J={key:1},L={class:"password-section max-w-200"},Q={class:"p-grid"},W={class:"p-col-12 p-md-4"},G={class:"field"},H={for:"currentPassword"},K={class:"p-col-12 p-md-4"},O={class:"field"},X={for:"newPassword"},Y={class:"p-col-12 p-md-4"},Z={class:"field"},ss={for:"confirmPassword"},es={class:"form-actions"},os=U({__name:"settings",setup(ts){const{t:o}=B(),k=N(),f=v(!1),_=v(!1),a=v(!1),s=q({currentPassword:"",newPassword:"",confirmPassword:""});S(async()=>{b()});const b=async()=>{f.value=!0;try{await new Promise(d=>setTimeout(d,500))}catch(d){console.error("Failed to load user settings",d)}finally{f.value=!1}},V=async()=>{if(a.value=!0,!(!s.currentPassword||!s.newPassword||!s.confirmPassword)&&s.newPassword===s.confirmPassword){_.value=!0;try{const{code:d}=await R({password:s.currentPassword,new_password:s.newPassword});d===0&&(s.currentPassword="",s.newPassword="",s.confirmPassword="",a.value=!1,k.add({severity:"success",summary:o("common.success"),detail:o("user.settings.passwordUpdated"),life:3e3}))}finally{_.value=!1}}};return(d,i)=>{const x=D,P=$,u=T,C=M;return n(),g("div",j,[e("div",z,[e("div",A,[e("h1",null,r(t(o)("user.settings.title")),1),e("p",null,r(t(o)("user.settings.subtitle")),1)])]),e("div",E,[f.value?(n(),g("div",F,[c(x)])):(n(),g("div",J,[e("div",L,[e("div",Q,[e("div",W,[e("div",G,[e("label",H,r(t(o)("user.settings.currentPassword")),1),c(P,{id:"currentPassword",modelValue:s.currentPassword,"onUpdate:modelValue":i[0]||(i[0]=l=>s.currentPassword=l),feedback:!1,"toggle-mask":"",class:h(["w-full",{"p-invalid":a.value&&!s.currentPassword}])},null,8,["modelValue","class"]),a.value&&!s.currentPassword?(n(),w(u,{key:0,variant:"simple",severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.currentPasswordRequired")),1)]),_:1})):y("",!0)])]),e("div",K,[e("div",O,[e("label",X,r(t(o)("user.settings.newPassword")),1),c(P,{id:"newPassword",modelValue:s.newPassword,"onUpdate:modelValue":i[1]||(i[1]=l=>s.newPassword=l),"toggle-mask":"",class:h(["w-full",{"p-invalid":a.value&&!s.newPassword}])},null,8,["modelValue","class"]),a.value&&!s.newPassword?(n(),w(u,{key:0,variant:"simple",severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.newPasswordRequired")),1)]),_:1})):y("",!0)])]),e("div",Y,[e("div",Z,[e("label",ss,r(t(o)("user.settings.confirmPassword")),1),c(P,{id:"confirmPassword",modelValue:s.confirmPassword,"onUpdate:modelValue":i[2]||(i[2]=l=>s.confirmPassword=l),feedback:!1,"toggle-mask":"",class:h(["w-full",{"p-invalid":a.value&&!s.confirmPassword}])},null,8,["modelValue","class"]),a.value&&!s.confirmPassword?(n(),w(u,{key:0,variant:"simple",severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.confirmPasswordRequired")),1)]),_:1})):a.value&&s.newPassword!==s.confirmPassword?(n(),w(u,{key:1,severity:"error"},{default:m(()=>[p(r(t(o)("user.settings.passwordsDoNotMatch")),1)]),_:1})):y("",!0)])])]),e("div",es,[c(C,{label:t(o)("user.settings.updatePassword"),icon:"pi pi-lock",loading:_.value,class:"p-button-primary",severity:"warn",onClick:V},null,8,["label","loading"])])])]))])])}}}),ws=I(os,[["__scopeId","data-v-1be80f21"]]);export{ws as default};
