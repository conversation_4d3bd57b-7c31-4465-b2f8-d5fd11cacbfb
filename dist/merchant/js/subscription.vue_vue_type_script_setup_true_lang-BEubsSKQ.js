import{s as A}from"./index-Cbodd6BQ.js";import{s as N,a as C}from"./index-CxOnO8if.js";import{a as O}from"./report-BieIWVzh.js";import{d as S,r as i,S as B,f as D,a as E,o as R,b as a,e as r,w as T,j as V,t as y}from"./index-BPwFJVSY.js";import{u as $,i as _,a as j,b as z,d as I,e as W,c as M}from"./installCanvasRenderer-p5Q0jy4r.js";const P={class:"overview mt-4"},U={class:"ranking mt-10"},q={class:"title font-semibold text-lg mb-2 relative"},F={class:"absolute top-[-8px] left-70 z-100"},G={class:"w-2xl h-[350px]"},H={class:"title font-semibold text-lg"},J={class:"w-2xl h-[350px]"},K={class:"title font-semibold text-lg"},L={class:"w-2xl h-[350px]"},Q={class:"title font-semibold text-lg"},ae=S({__name:"subscription",setup(X){$([j,z,I,W,M]);const m=i([]),x=i(),l=i({xAxis:{type:"category",data:["1","2","3","4"]},yAxis:{type:"value"},title:{text:"",left:"center",top:"10px",textStyle:{color:"#888",fontWeight:"normal",fontSize:18}},series:[{data:[30,20,35,15],type:"line"}]}),h=i(),o=i({xAxis:{type:"category",data:["1","2","3","4"]},yAxis:{type:"value"},title:{text:"B Subscription count ",left:"center",top:"10px",textStyle:{color:"#888",fontWeight:"normal",fontSize:18}},series:[{data:[30,20,35,15],type:"line"}]}),f=i(),n=i({xAxis:{type:"category",data:["1","2","3","4"]},yAxis:{type:"value"},title:{text:"B Subscription count ",left:"center",top:"10px",textStyle:{color:"#888",fontWeight:"normal",fontSize:18}},series:[{data:[30,20,35,15],type:"line"}]}),w=i([{name:"3 days",code:"3"},{name:"7 days",code:"7"},{name:"14 days",code:"14"},{name:"30 days",code:"30"}]),p=i({name:"3 days",code:"3"}),b=()=>{const c={days:p.value.code};O(c).then(t=>{const e=t.data;console.log(e),m.value=[],l.value.xAxis.data=[],l.value.series[0].data=[],o.value.xAxis.data=[],o.value.series[0].data=[],n.value.xAxis.data=[],n.value.series[0].data=[],Object.keys(e.summary).forEach(s=>{var g;return m.value.push({plan:(g=e==null?void 0:e.summary[Number(s)])==null?void 0:g.plan_name,customer:e.summary[Number(s)].active_plans,trend:e.summary[Number(s)].trend_plans,churnRate:e.summary[Number(s)].churned_plans/e.summary[Number(s)].total_plans,monthly:e.summary[Number(s)].mrr,annual:e.summary[Number(s)].arr})});const d=e.trends.top_plans[0]??"No Data",u=e.trends.top_plans[1]??"No Data",v=e.trends.top_plans[2]??"No Data";l.value.title.text=d,o.value.title.text=u,n.value.title.text=v,d!=="No Data"&&Object.keys(e.trends.daily_changes[d]).forEach(s=>(l.value.xAxis.data.push(s),l.value.series[0].data.push(e.trends.daily_changes[d][s].active_plans))),u!=="No Data"&&Object.keys(e.trends.daily_changes[u]).forEach(s=>(o.value.xAxis.data.push(s),o.value.series[0].data.push(e.trends.daily_changes[u][s].active_plans))),v!=="No Data"&&Object.keys(e.trends.daily_changes[v]).forEach(s=>(n.value.xAxis.data.push(s),n.value.series[0].data.push(e.trends.daily_changes[v][s].active_plans))),l.value&&x.value.setOption(l.value),o.value&&h.value.setOption(o.value),n.value&&f.value.setOption(n.value)})};return B(p,()=>{b()}),D(()=>{const c=document.getElementById("overviewA"),t=document.getElementById("overviewB"),e=document.getElementById("overviewC");c&&(x.value=_(c),l.value&&x.value.setOption(l.value)),t&&(h.value=_(t),o.value&&h.value.setOption(o.value)),e&&(f.value=_(e),n.value&&f.value.setOption(n.value)),Promise.all([b()])}),(c,t)=>{const e=N,d=C,u=A;return R(),E("div",null,[t[6]||(t[6]=a("div",{class:"text-xl font-semibold"}," Subscription ",-1)),a("div",null,[a("div",P,[t[1]||(t[1]=a("div",{class:"title font-semibold text-lg mb-2"}," Subscription overview ",-1)),a("div",null,[r(d,{value:m.value,"show-gridlines":"","table-style":"min-width: 50rem"},{default:T(()=>[r(e,{field:"plan",header:"Active sub & plans"}),r(e,{field:"customer",header:"customer"}),r(e,{field:"trend",header:"Trend"}),r(e,{field:"churnRate",header:"Churn Rate"}),r(e,{field:"monthly",header:"Monthly Recurring Revenue"}),r(e,{field:"annual",header:"Annual Recurring Revenue"})]),_:1},8,["value"])])]),a("div",U,[a("div",q,[t[2]||(t[2]=V(" Top 3 subscription for last ")),a("div",F,[r(u,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=v=>p.value=v),options:w.value,"option-label":"name",class:"bg-transparent"},null,8,["modelValue","options"])])]),a("div",G,[a("div",H,y(l.value.title.text),1),t[3]||(t[3]=a("div",{id:"overviewA",style:{width:"100%",height:"100%"}},null,-1))]),a("div",J,[a("div",K,y(o.value.title.text),1),t[4]||(t[4]=a("div",{id:"overviewB",style:{width:"100%",height:"100%"}},null,-1))]),a("div",L,[a("div",Q,y(n.value.title.text),1),t[5]||(t[5]=a("div",{id:"overviewC",style:{width:"100%",height:"100%"}},null,-1))])])])])}}});export{ae as _};
