import{_ as ie}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a as f,b as a,o as m,d as pe,r as ye,c as T,S as ve,q as v,p as De,t as n,u as l,y as _,j as U,e as q,z as _e}from"./index-BPwFJVSY.js";import{d as G}from"./dayjs.min-Bar1rHDB.js";import{D as r}from"./decimal-LZMqkRTm.js";import{s as j}from"./index-DgBXOSrM.js";import{I as Y}from"./customer-v2cMYA98.js";import{B as P,a as W,S as o,U as S,R as ge,P as g,b as z}from"./plan-DCOwcHy2.js";import{S as V}from"./user-D3NCqkKe.js";const Be={},we={class:"divider-container"};function Ue(ee,e){return m(),f("div",we,e[0]||(e[0]=[a("div",{class:"line"},null,-1),a("div",{class:"dots"},[a("div",{class:"dot dot-red"}),a("div",{class:"dot dot-blue"})],-1)]))}const Pe=ie(Be,[["render",Ue],["__scopeId","data-v-0ae18ce8"]]),Me={class:"plan-subscription-preview flex-1"},he={key:0,class:"plan-subscription-title"},be={class:"preview-content"},Ce={class:"preview-card mb-5 rounded-lg overflow-hidden"},Te={class:"flex"},Se={class:"preview-card-content"},ke={key:0,class:"flex"},xe={class:"preview-card-content"},Re={class:"flex"},Ne={class:"preview-card-content"},Ee={class:"flex"},Fe={class:"preview-card-content"},Oe={class:"flex"},Qe={class:"preview-card-content"},Ye={class:"flex"},Ie={class:"preview-card-content"},Ae={key:0,class:"flex"},Ve={class:"preview-card-content"},Ge={class:"flex"},We={class:"preview-card-content"},$e={class:"flex"},qe={class:"preview-card-content"},je={class:"preview-card mb-5 rounded-lg overflow-hidden"},ze={class:"flex"},Xe={class:"preview-card-content"},Le={key:0},He={key:1},Je={key:0},Ke={key:1},Ze={class:"flex"},et={class:"preview-card-content"},tt={class:"flex"},at={class:"preview-card-content"},nt={class:"flex"},rt={class:"preview-card-content font-bold primary-color"},st={key:1,class:"preview-card"},it={class:"flex"},lt={class:"preview-card-content"},ot={class:"mr-2"},ut={class:"mr-2"},dt={class:"mr-2"},mt={class:"mr-2"},ft={class:"mr-2"},ct={class:"mr-2"},pt={class:"mr-2"},yt={class:"mr-2"},vt={class:"mr-2"},Dt={class:"mr-2"},_t={class:"mr-2"},gt={class:"mr-2"},Bt={class:"mr-2"},wt={class:"mr-2"},Ut={class:"mr-2"},Pt={class:"mr-2"},Mt={class:"mr-2"},ht={key:1,class:"flex flex-col"},bt={key:0,class:"flex items-center"},Ct={class:"mt-2 font-medium"},Tt={class:"mr-2"},St={class:"mr-2"},kt={class:"mr-2"},xt={class:"mr-2"},Rt={class:"mx-2"},Nt={class:"mr-2"},Et={key:2},Ft={class:"flex"},Ot={class:"preview-card-content"},Qt={class:"flex"},Yt={class:"preview-card-content"},It={class:"flex"},At={class:"preview-card-content"},Vt={class:"font-bold primary-color"},Gt=pe({name:"CustomerSubscriptionFormPreview",__name:"subscriptionFormPreview",props:{formData:{type:Object,required:!0},name:{type:String,required:!0},planName:{type:String,required:!1,default:""},startDate:{type:Date,required:!0},unitQuantity:{type:Number,required:!0},surchargeRate:{type:Object,default(){return{fee_rate:"",fee_value:""}}},gst:{type:Object,default(){return{fee_rate:"",fee_value:""}}},isShowForecast:{type:Boolean,default:!1}},setup(ee){const e=ee,k=ye(1),te={[P.Daily]:{unit:"day",multiplier:1},[P.Weekly]:{unit:"week",multiplier:1},[P.Fortnightly]:{unit:"week",multiplier:2},[P.Monthly]:{unit:"month",multiplier:1},[P.Yearly]:{unit:"year",multiplier:1},[P.Every3Months]:{unit:"month",multiplier:3},[P.Every6Months]:{unit:"month",multiplier:6},[P.Custom]:{unit:"day",multiplier:0}},X={[W.Day]:"day",[W.Week]:"week",[W.Month]:"month",[W.Year]:"year"},le={[P.Daily]:"Daily",[P.Weekly]:"Weekly",[P.Monthly]:"Monthly",[P.Yearly]:"Yearly",[P.Every3Months]:"Every 3 months",[P.Every6Months]:"Every 6 months",[P.Custom]:"Custom"},u=(s,t)=>!s||s===""?"$0.00":`$${s instanceof r?s.toFixed(2):new r(s).toFixed(2)}`,ae=s=>{if(!s)return"";if(s===P.Custom){const t=e.formData.schedule_type===o.Recurring?e.formData.recurringBillingPeriodCustomUnit:e.formData.unitBasedBillingPeriodCustomUnit;return`Every ${e.formData.schedule_type===o.Recurring?e.formData.recurringBillingPeriodCustom:e.formData.unitBasedBillingPeriodCustom} ${X[t]}`}return le[s]||s},oe=()=>{var p;if(!((p=e.formData.unitBasedTieredPricing)!=null&&p.length))return new r(0);let s=new r(0);const t=new r(e.unitQuantity);if(e.formData.unitBasedTieredCurrencyPaymentMethod===z.Volume){let c=e.formData.unitBasedTieredPricing[0];for(const D of e.formData.unitBasedTieredPricing){const w=new r(D.firstUnit),B=D.lastUnit===Y?new r(1/0):new r(D.lastUnit);if(t.gte(w)&&t.lte(B)){c=D;break}}const C=new r(c.perUnit||0),h=new r(c.flatFee||0);return C.mul(t).plus(h)}const i=[...e.formData.unitBasedTieredPricing||[]].sort((c,C)=>c.firstUnit-C.firstUnit);for(let c=0;c<i.length;c++){const C=i[c],h=new r(C.firstUnit),R=C.lastUnit===Y?new r(1/0):new r(C.lastUnit);if(t.gte(h)){let D;t.gte(R)?D=R.eq(new r(1/0))?t.minus(h).plus(1):R.minus(h).plus(1):D=t.minus(h).plus(1);const w=new r(C.perUnit||0),B=new r(C.flatFee||0),N=D.mul(w).plus(B);s=s.plus(N)}}return s},M=T(()=>{let s=new r(0),t=new r(1);if(e.formData.schedule_type===o.UnitBased&&e.formData.unitBasedModel===S.StandardPricing&&(t=new r(e.unitQuantity||1)),e.formData.schedule_type===o.Recurring)e.formData.recurringPricingModel===ge.StandardPricing&&(s=new r(e.formData.recurringAmount||0).mul(t));else if(e.formData.schedule_type===o.UnitBased)if(e.formData.unitBasedModel===S.StandardPricing){const y=new r(e.formData.unitBasedIncrement||1);s=new r(e.formData.unitBasedAmount||0).mul(y).mul(t)}else e.formData.unitBasedModel===S.TieredPricing&&(s=oe());else s=new r(e.formData.oneOffAmount||0);return s}),d=T(()=>e.formData.schedule_type===o.Recurring?e.formData.recurringCurrency:e.formData.schedule_type===o.UnitBased?e.formData.unitBasedModel===S.StandardPricing?e.formData.unitBasedCurrency:e.formData.unitBasedTieredCurrency:e.formData.oneOffCurrency),I=T(()=>e.formData.is_inclusive_gst?new r(0):e.gst.fee_rate===V.PERCENTAGE?M.value.mul(new r(e.gst.fee_value).div(100)):e.gst.fee_rate===V.FIXED?new r(e.gst.fee_value):new r(0)),A=T(()=>{const s=new r(1);if(!e.formData.is_surcharge)return new r(0);if(e.surchargeRate.fee_rate===V.PERCENTAGE){const t=Number.parseFloat(e.surchargeRate.fee_value||"0")/100;return M.value.mul(new r(t)).mul(s)}else if(e.surchargeRate.fee_rate===V.FIXED)return new r(e.surchargeRate.fee_value||"0").mul(s);return new r(0)}),ue=T(()=>e.formData.is_inclusive_gst?M.value.plus(A.value):M.value.plus(I.value).plus(A.value)),x=T(()=>{let s=new r(1),t=new r(1);[o.Recurring,o.UnitBased].includes(e.formData.schedule_type)&&e.formData.end_date_type===g.SpecifyByTerm&&e.formData.end_terms&&(s=new r(e.formData.end_terms||1)),e.formData.schedule_type===o.Recurring&&e.formData.end_date_type===g.SpecifyByEndDate&&e.formData.end_date&&(t=new r(F(e.formData.recurringBillingPeriod,e.formData.end_date,e.formData.recurringBillingPeriodCustom,e.formData.recurringBillingPeriodCustomUnit)||1)),e.formData.schedule_type===o.UnitBased&&e.formData.end_date_type===g.SpecifyByEndDate&&e.formData.end_date&&(t=new r(F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit)||1));let y=M.value.mul(k.value).mul(s).mul(t);return[o.Recurring,o.UnitBased].includes(e.formData.schedule_type)&&e.formData.end_date_type===g.SpecifyByTerm&&e.formData.end_terms&&(y=new r(y)),y}),L=T(()=>{const s=new r(1);let t=new r(1);if([o.Recurring,o.UnitBased].includes(e.formData.schedule_type)&&e.formData.end_date_type===g.SpecifyByTerm&&e.formData.end_terms&&(t=new r(e.formData.end_terms||1)),e.formData.schedule_type===o.Recurring&&e.formData.end_date_type===g.SpecifyByEndDate&&e.formData.end_date){const i=F(e.formData.recurringBillingPeriod,e.formData.end_date,e.formData.recurringBillingPeriodCustom,e.formData.recurringBillingPeriodCustomUnit);return I.value.mul(new r(i)).mul(s).mul(t)}if(e.formData.schedule_type===o.UnitBased&&e.formData.end_date_type===g.SpecifyByEndDate&&e.formData.end_date){const i=F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit);return I.value.mul(s).mul(t).mul(new r(i))}let y=I.value.mul(k.value).mul(s).mul(t);return[o.Recurring,o.UnitBased].includes(e.formData.schedule_type)&&e.formData.end_date_type===g.SpecifyByTerm&&(y=new r(y)),y}),H=T(()=>{const s=new r(1);let t=new r(1);if([o.Recurring,o.UnitBased].includes(e.formData.schedule_type)&&e.formData.end_date_type===g.SpecifyByTerm&&e.formData.end_terms&&(t=new r(e.formData.end_terms||1)),e.formData.schedule_type===o.Recurring&&e.formData.end_date_type===g.SpecifyByEndDate&&e.formData.end_date){const i=F(e.formData.recurringBillingPeriod,e.formData.end_date,e.formData.recurringBillingPeriodCustom,e.formData.recurringBillingPeriodCustomUnit);return A.value.mul(new r(i)).mul(s).mul(t)}if(e.formData.schedule_type===o.UnitBased&&e.formData.end_date_type===g.SpecifyByEndDate&&e.formData.end_date){const i=F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit);return A.value.mul(new r(i)).mul(s).mul(t)}let y=A.value.mul(k.value).mul(s).mul(t);return[o.Recurring,o.UnitBased].includes(e.formData.schedule_type)&&e.formData.end_date_type===g.SpecifyByTerm&&(y=new r(y)),y}),de=T(()=>e.formData.is_inclusive_gst?new r(x.value.toString()).plus(new r(H.value.toString())):new r(x.value.toString()).plus(new r(L.value.toString())).plus(new r(H.value.toString()))),F=(s,t,y,i)=>{if(!s||!t)return 1;const p=G(e.startDate),c=G(t),C=s;if(C!==P.Custom&&C){const{unit:h,multiplier:R}=te[C];if(h==="day"){const D=p.startOf("day"),B=c.startOf("day").diff(D,"day")+1;return Math.max(1,Math.ceil(B/R))}else if(h==="week"){const D=p.startOf("day"),w=c.startOf("day"),B=w.diff(D,"week"),N=B*7,E=w.diff(D,"day")+1>N,O=Math.max(1,B+(E?1:0));return Math.max(1,Math.ceil(O/R))}else if(h==="month"){const D=p.month(),w=p.year(),B=c.month(),b=c.year()-w,E=B-D+b*12;return E===0||c.date()<p.date()&&E>0||c.date()===p.date()?Math.max(1,Math.ceil(E/R)):Math.max(1,Math.ceil((E+1)/R))}else if(h==="year"){const D=p.month(),w=p.date(),B=c.month(),N=c.date(),b=c.year()-p.year();if(b===0)return 1;{const E=B>D||B===D&&N>w,O=b+(E?1:0);return Math.max(1,Math.ceil(O/R))}}else{const D=c.diff(p,h);return Math.max(1,Math.ceil(D/R))}}else if(y&&i){const h=y,D=X[i];if(D){const w=Number(h.toString());if(D==="day"){const B=p.startOf("day"),b=c.startOf("day").diff(B,"day")+1;return Math.max(1,Math.ceil(b/w))}else if(D==="week"){const B=p.day(),N=c.day(),b=p.startOf("day"),O=c.startOf("day").diff(b,"day")+1,Q=Math.floor(O/7),Z=O%7>0||N<B,se=Q+(Z?1:0);return Math.max(1,Math.ceil(se/w))}else if(D==="month"){const B=p.month(),N=p.year(),b=c.month(),O=c.year()-N,Q=b-B+O*12;return Q===0||c.date()<p.date()&&Q>0||c.date()===p.date()?Math.max(1,Math.ceil(Q/w)):Math.max(1,Math.ceil((Q+1)/w))}else if(D==="year"){const B=p.month(),N=p.date(),b=c.month(),E=c.date(),O=c.year()-p.year();if(O===0)return 1;{const Q=b>B||b===B&&E>N,Z=O+(Q?1:0);return Math.max(1,Math.ceil(Z/w))}}else{const B=c.diff(p,D);return Math.max(1,Math.ceil(B/w))}}}return 1},ne=()=>{if(!e.surchargeRate||!e.surchargeRate.fee_rate)return"";if(e.surchargeRate.fee_rate==="1")return`${Number.parseFloat(e.surchargeRate.fee_value||"0")}%`;if(e.surchargeRate.fee_rate==="2"){const s=Number.parseFloat(e.surchargeRate.fee_value||"0");return`${u(s,d.value||"AUD")}`}return""},me=s=>{const t=G();let y,i,p;if(e.formData.schedule_type===o.Recurring)y=e.formData.recurringBillingPeriod,i=e.formData.recurringBillingPeriodCustomUnit,p=e.formData.recurringBillingPeriodCustom;else if(e.formData.schedule_type===o.UnitBased)y=e.formData.unitBasedBillingPeriod,i=e.formData.unitBasedBillingPeriodCustomUnit,p=e.formData.unitBasedBillingPeriodCustom;else return;if(y&&y!==P.Custom){const c=y,{unit:C,multiplier:h}=te[c];return t.add(s*h,C).format("DD/MM/YYYY")}if(!(!i||!p)&&Object.values(W).includes(i)){const c=X[i];return t.add(p*s,c).format("DD/MM/YYYY")}},J=T(()=>{let s=0;for(const t of e.formData.unitBasedTieredPricing)(e.unitQuantity>=t.firstUnit&&t.lastUnit!==Y&&Number(t.lastUnit)>=Number(e.unitQuantity)||e.unitQuantity>=t.firstUnit&&t.lastUnit===Y)&&(s=t.perUnit);return s}),K=T(()=>{let s=0;for(const t of e.formData.unitBasedTieredPricing)(e.unitQuantity>=t.firstUnit&&t.lastUnit!==Y&&Number(t.lastUnit)>=Number(e.unitQuantity)||e.unitQuantity>=t.firstUnit&&t.lastUnit===Y)&&(s=t.flatFee);return s}),fe=T(()=>{if(!e.formData.plan_id)return"";if(e.formData.schedule_type===o.OneOff)return"NA";switch(e.formData.end_date_type){case g.SpecifyByEndDate:return e.formData.end_date&&G(e.formData.end_date).format("DD/MM/YYYY");case g.GoodTillCancel:return"Infinite";case g.SpecifyByTerm:return e.formData.end_terms&&me(e.formData.end_terms)}return""});ve([()=>e.formData.end_date_type,()=>e.formData.schedule_type],()=>{k.value=1});const $=T(()=>e.formData.schedule_type===o.UnitBased&&e.formData.unitBasedModel===S.StandardPricing?new r(e.formData.unitBasedIncrement||1).toNumber():1),re=T(()=>{if(e.formData.schedule_type!==o.UnitBased)return new r(0);if(e.formData.unitBasedModel===S.StandardPricing)return new r(e.formData.unitBasedAmount||0);if(e.formData.unitBasedModel===S.TieredPricing){const s=e.unitQuantity,t=e.formData.unitBasedTieredPricing||[];let y=t[0]||{perUnit:0};for(const i of t){const p=i.firstUnit,c=i.lastUnit===Y?1/0:Number(i.lastUnit);if(s>=p&&s<=c){y=i;break}}return new r(y.perUnit||0)}return new r(0)}),ce=T(()=>{if(e.formData.schedule_type!==o.UnitBased||e.formData.unitBasedModel!==S.TieredPricing)return new r(0);const s=e.unitQuantity,t=e.formData.unitBasedTieredPricing||[];let y=t[0]||{flatFee:0};for(const i of t){const p=i.firstUnit,c=i.lastUnit===Y?1/0:Number(i.lastUnit);if(s>=p&&s<=c){y=i;break}}return new r(y.flatFee||0)});return(s,t)=>{const y=Pe;return m(),f("div",Me,[e.isShowForecast?(m(),f("h1",he," Subscription review ")):v("",!0),a("div",be,[a("div",Ce,[a("div",Te,[t[4]||(t[4]=a("div",{class:"preview-card-label"}," Customer name: ",-1)),a("div",Se,n(e.name),1)]),e.isShowForecast?(m(),f("div",ke,[t[5]||(t[5]=a("div",{class:"preview-card-label"}," Plan name: ",-1)),a("div",xe,n(e.planName),1)])):v("",!0),a("div",Re,[t[6]||(t[6]=a("div",{class:"preview-card-label"}," Start date: ",-1)),a("div",Ne,n(l(G)(e.startDate).format("DD/MM/YYYY")),1)]),a("div",Ee,[t[7]||(t[7]=a("div",{class:"preview-card-label"}," End date: ",-1)),a("div",Fe,n(fe.value),1)]),a("div",Oe,[t[8]||(t[8]=a("div",{class:"preview-card-label"}," Frequency: ",-1)),a("div",Qe,n(e.formData.schedule_type===l(o).Recurring?ae(e.formData.recurringBillingPeriod):e.formData.schedule_type===l(o).UnitBased?ae(e.formData.unitBasedBillingPeriod):"One-off"),1)]),e.formData.schedule_type===l(o).UnitBased?(m(),f(_,{key:1},[a("div",Ye,[t[9]||(t[9]=a("div",{class:"preview-card-label"}," Unit type: ",-1)),a("div",Ie,n(e.formData.unitBasedModel===l(S).StandardPricing?e.formData.unitBasedModelType==="Custom"?e.formData.unitBasedModelTypeCustom:e.formData.unitBasedModelType:e.formData.unitBasedTieredModelType==="Custom"?e.formData.unitBasedTieredModelTypeCustom:e.formData.unitBasedTieredModelType),1)]),e.formData.schedule_type===l(o).UnitBased&&e.formData.unitBasedModel===l(S).StandardPricing?(m(),f("div",Ae,[t[10]||(t[10]=a("div",{class:"preview-card-label"}," Increment: ",-1)),a("div",Ve,n(e.formData.unitBasedIncrement)+" "+n(e.formData.unitBasedModelType),1)])):v("",!0),a("div",Ge,[t[11]||(t[11]=a("div",{class:"preview-card-label"}," Quantity: ",-1)),a("div",We,n(e.unitQuantity),1)]),a("div",$e,[t[12]||(t[12]=a("div",{class:"preview-card-label"}," Billable units: ",-1)),a("div",qe,n($.value),1)])],64)):v("",!0),t[13]||(t[13]=a("div",{class:"preview-card-divider"},null,-1))]),a("div",je,[a("div",ze,[t[14]||(t[14]=a("div",{class:"preview-card-label"}," Subtotal: ",-1)),a("div",Xe,[e.formData.schedule_type===l(o).Recurring?(m(),f(_,{key:0},[U(n(u(M.value,d.value)),1)],64)):v("",!0),[l(o).UnitBased].includes(e.formData.schedule_type)?(m(),f(_,{key:1},[e.formData.schedule_type===l(o).UnitBased?(m(),f(_,{key:0},[e.formData.unitBasedModel===l(S).StandardPricing?(m(),f("div",Le,[a("span",null,n(u(re.value,d.value)),1),a("span",null," × "+n($.value),1),a("span",null," × "+n(e.unitQuantity),1),a("span",null," = "+n(u(M.value,d.value)),1)])):e.formData.unitBasedModel===l(S).TieredPricing?(m(),f("div",He,[e.formData.unitBasedTieredCurrencyPaymentMethod===l(z).Volume?(m(),f("div",Je,[a("span",null,n(u(re.value,d.value)),1),a("span",null," × "+n(e.unitQuantity),1),a("span",null," + "+n(u(ce.value,d.value)),1),a("span",null," (flat fee) = "+n(u(M.value,d.value)),1)])):(m(),f("div",Ke,n(u(M.value,d.value)),1))])):v("",!0)],64)):e.formData.schedule_type===l(o).Recurring?(m(),f(_,{key:1},[U(n(u(M.value,d.value)),1)],64)):(m(),f(_,{key:2},[U(n(u(M.value,d.value)),1)],64))],64)):v("",!0),[l(o).OneOff].includes(e.formData.schedule_type)?(m(),f(_,{key:2},[U(n(u(M.value,d.value)),1)],64)):v("",!0)])]),a("div",Ze,[t[16]||(t[16]=a("div",{class:"preview-card-label"}," GST: ",-1)),a("div",et,[U(n(u(I.value,d.value))+" ",1),I.value.eq(0)?v("",!0):(m(),f(_,{key:0},[t[15]||(t[15]=a("div",{class:"preview-card-content-divider"},null,-1)),U(" "+n(e.gst.fee_rate===l(V).PERCENTAGE?`${e.gst.fee_value}%`:e.gst.fee_value),1)],64))])]),a("div",tt,[t[18]||(t[18]=a("div",{class:"preview-card-label"}," Surcharge: ",-1)),a("div",at,[U(n(u(A.value,d.value))+" ",1),t[17]||(t[17]=a("div",{class:"preview-card-content-divider"},null,-1)),U(" "+n(ne()),1)])]),a("div",nt,[t[19]||(t[19]=a("div",{class:"preview-card-label"}," Total: ",-1)),a("div",rt,n(u(ue.value,d.value)),1)])]),e.isShowForecast?(m(),De(y,{key:0,class:"support-divide"})):v("",!0),e.isShowForecast?(m(),f("div",st,[t[69]||(t[69]=a("div",{class:"forecast-header"},[a("h3",{class:"forecast-title"}," Forecast "),a("div",{class:"forecast-disclaimer"}," Disclaimer: Forecasts are for reference only and may not be accurate. Use at your own discretion. ")],-1)),t[70]||(t[70]=a("div",{class:"preview-card-divider"},null,-1)),a("div",it,[t[63]||(t[63]=a("div",{class:"preview-card-label"}," Subtotal: ",-1)),a("div",lt,[[l(o).UnitBased].includes(e.formData.schedule_type)?(m(),f(_,{key:0},[e.formData.unitBasedModel===l(S).StandardPricing?(m(),f(_,{key:0},[e.formData.end_date_type===l(g).SpecifyByEndDate?(m(),f(_,{key:0},[a("span",ot,n(u(e.formData.unitBasedAmount,d.value)),1),t[20]||(t[20]=a("span",{class:"mr-2"},"×",-1)),a("span",ut,n($.value),1),t[21]||(t[21]=a("span",{class:"mr-2"},"×",-1)),a("span",dt,n(e.unitQuantity),1),t[22]||(t[22]=a("span",{class:"mr-2"},"×",-1)),a("span",mt,n(F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit)),1),t[23]||(t[23]=a("span",{class:"mr-2"},"periods",-1)),a("span",null,"= "+n(u(x.value,d.value)),1)],64)):v("",!0),e.formData.end_date_type===l(g).SpecifyByTerm&&e.formData.end_terms?(m(),f(_,{key:1},[a("span",ft,n(u(e.formData.unitBasedAmount,d.value)),1),t[24]||(t[24]=a("span",{class:"mr-2"},"×",-1)),a("span",ct,n(e.formData.end_terms),1),t[25]||(t[25]=a("span",{class:"mr-2"},"×",-1)),a("span",pt,n(e.unitQuantity),1),a("span",null,"= "+n(u(x.value,d.value)),1)],64)):v("",!0),e.formData.end_date_type===l(g).GoodTillCancel?(m(),f(_,{key:2},[q(l(j),{modelValue:k.value,"onUpdate:modelValue":t[0]||(t[0]=i=>k.value=i),class:"forecast-input mr-2",min:1,max:120,"input-class":"text-center"},null,8,["modelValue"]),t[26]||(t[26]=a("span",{class:"mr-2"},"months",-1)),t[27]||(t[27]=a("span",{class:"mr-2"},"×",-1)),a("span",yt,n(u(e.formData.unitBasedAmount,d.value)),1),t[28]||(t[28]=a("span",{class:"mr-2"},"×",-1)),a("span",vt,n($.value),1),t[29]||(t[29]=a("span",{class:"mr-2"},"×",-1)),a("span",Dt,n(e.unitQuantity),1),a("span",null,"= "+n(u(x.value,d.value)),1)],64)):v("",!0)],64)):v("",!0),e.formData.unitBasedModel===l(S).TieredPricing?(m(),f(_,{key:1},[e.formData.unitBasedTieredCurrencyPaymentMethod===l(z).Volume?(m(),f(_,{key:0},[e.formData.end_date_type===l(g).SpecifyByEndDate?(m(),f(_,{key:0},[t[30]||(t[30]=U(" ( ")),a("span",null,n(u(J.value,d.value)),1),t[31]||(t[31]=a("span",null,"×",-1)),a("span",null,n(e.unitQuantity),1),t[32]||(t[32]=a("span",null,"+",-1)),a("span",null,n(K.value)+" (flat fee)",1),t[33]||(t[33]=U(" ) ")),a("span",null," × "+n(F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit))+" periods ",1),a("span",null,"= "+n(u(x.value,d.value)),1)],64)):v("",!0),e.formData.end_date_type===l(g).SpecifyByTerm&&e.formData.end_terms?(m(),f(_,{key:1},[t[34]||(t[34]=U(" ( ")),a("span",_t,n(u(J.value,d.value)),1),t[35]||(t[35]=a("span",{class:"mr-2"},"×",-1)),a("span",gt,n(e.unitQuantity),1),t[36]||(t[36]=a("span",{class:"mr-2"},"+",-1)),a("span",Bt,n(K.value)+" (flat fee)",1),t[37]||(t[37]=U(" ) ")),t[38]||(t[38]=a("span",{class:"mr-2"},"×",-1)),a("span",wt,n(e.formData.end_terms),1),a("span",null,"= "+n(u(x.value,d.value)),1)],64)):v("",!0),e.formData.end_date_type===l(g).GoodTillCancel?(m(),f(_,{key:2},[q(l(j),{modelValue:k.value,"onUpdate:modelValue":t[1]||(t[1]=i=>k.value=i),class:"forecast-input mr-2",min:1,max:120,"input-class":"text-center"},null,8,["modelValue"]),t[39]||(t[39]=a("span",{class:"mr-2"},"months",-1)),t[40]||(t[40]=a("span",{class:"mr-2"},"×",-1)),a("span",Ut,n(e.unitQuantity),1),t[41]||(t[41]=a("span",{class:"mr-2"},"×",-1)),a("span",Pt,n(u(J.value,d.value)),1),t[42]||(t[42]=a("span",{class:"mr-2"},"+",-1)),a("span",Mt,n(K.value)+" (flat fee)",1),a("span",null,"= "+n(u(x.value,d.value)),1)],64)):v("",!0)],64)):v("",!0),e.formData.unitBasedTieredCurrencyPaymentMethod===l(z).Graduated?(m(),f("div",ht,[(m(!0),f(_,null,_e(e.formData.unitBasedTieredPricing,(i,p)=>(m(),f("div",{key:p},[e.unitQuantity>=i.firstUnit?(m(),f("div",bt,[t[43]||(t[43]=U(" ( ")),a("span",null,n(u(new(l(r))(i.perUnit||0),d.value)),1),t[44]||(t[44]=a("span",null,"×",-1)),a("span",null,n(i.lastUnit===l(Y)?Number(e.unitQuantity)-Number(i.firstUnit)+1:Math.min(Number(e.unitQuantity),Number(i.lastUnit))-Number(i.firstUnit)+1),1),t[45]||(t[45]=a("span",null,"+",-1)),a("span",null,n(u(new(l(r))(i.flatFee||0),d.value)),1),t[46]||(t[46]=a("span",null,"(flat fee)",-1)),t[47]||(t[47]=U(" ) ")),a("span",null," × "+n(F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit))+" periods ",1),t[48]||(t[48]=a("span",{class:"mr-2"},"=",-1)),a("span",null,n(u(new(l(r))(i.perUnit||0).mul(new(l(r))(i.lastUnit===l(Y)?Number(e.unitQuantity)-Number(i.firstUnit)+1:Math.min(Number(e.unitQuantity),Number(i.lastUnit))-Number(i.firstUnit)+1)).plus(new(l(r))(i.flatFee||0)).mul(F(e.formData.unitBasedBillingPeriod,e.formData.end_date,e.formData.unitBasedBillingPeriodCustom,e.formData.unitBasedBillingPeriodCustomUnit)),d.value)),1)])):v("",!0)]))),128)),a("div",Ct,[t[53]||(t[53]=U(" Total: ")),e.formData.end_date_type===l(g).GoodTillCancel?(m(),f(_,{key:0},[q(l(j),{modelValue:k.value,"onUpdate:modelValue":t[2]||(t[2]=i=>k.value=i),class:"forecast-input mr-2",min:1,max:120,"input-class":"text-center"},null,8,["modelValue"]),t[49]||(t[49]=a("span",{class:"mr-2"},"months",-1)),t[50]||(t[50]=a("span",{class:"mr-2"},"×",-1))],64)):v("",!0),e.formData.end_date_type===l(g).SpecifyByTerm&&e.formData.end_terms?(m(),f(_,{key:1},[a("span",Tt,n(u(M.value,d.value)),1),t[51]||(t[51]=a("span",{class:"mr-2"},"×",-1)),a("span",St,n(e.formData.end_terms),1),t[52]||(t[52]=a("span",{class:"mr-2"},"periods",-1))],64)):v("",!0),a("span",kt,n(u(x.value,d.value)),1)])])):v("",!0)],64)):v("",!0)],64)):v("",!0),[l(o).Recurring].includes(e.formData.schedule_type)?(m(),f(_,{key:1},[e.formData.end_date_type===l(g).SpecifyByEndDate?(m(),f(_,{key:0},[a("span",null,n(u(M.value,d.value)),1),t[54]||(t[54]=a("span",null," × ",-1)),a("span",xt,n(F(e.formData.recurringBillingPeriod,e.formData.end_date,e.formData.recurringBillingPeriodCustom,e.formData.recurringBillingPeriodCustomUnit)),1),t[55]||(t[55]=a("span",{class:"mr-2"},"periods",-1)),t[56]||(t[56]=a("span",{class:"mr-2"},"×",-1)),a("span",null,n(k.value),1),t[57]||(t[57]=a("span",null," = ",-1)),a("span",null,n(u(x.value,d.value)),1)],64)):v("",!0),e.formData.end_date_type===l(g).SpecifyByTerm&&e.formData.end_terms?(m(),f(_,{key:1},[a("span",null,n(u(M.value,d.value)),1),t[58]||(t[58]=a("span",null," × ",-1)),a("span",null,n(e.formData.end_terms),1),t[59]||(t[59]=a("span",null," = ",-1)),a("span",null,n(u(x.value,d.value)),1)],64)):v("",!0),e.formData.end_date_type===l(g).GoodTillCancel?(m(),f(_,{key:2},[q(l(j),{modelValue:k.value,"onUpdate:modelValue":t[3]||(t[3]=i=>k.value=i),class:"forecast-input",min:1,max:120,"input-class":"text-center"},null,8,["modelValue"]),a("span",Rt,[t[60]||(t[60]=a("span",{class:"mr-2"},"months",-1)),t[61]||(t[61]=a("span",{class:"mr-2"},"×",-1)),a("span",Nt,n(u(M.value,d.value)),1),t[62]||(t[62]=a("span",{class:"mr-2"},"=",-1)),a("span",null,n(u(x.value,d.value)),1)])],64)):v("",!0)],64)):v("",!0),[l(o).OneOff].includes(e.formData.schedule_type)?(m(),f("span",Et,n(u(x.value,d.value)),1)):v("",!0)])]),a("div",Ft,[t[65]||(t[65]=a("div",{class:"preview-card-label"}," GST: ",-1)),a("div",Ot,[U(n(u(L.value,d.value))+" ",1),L.value.eq(0)?v("",!0):(m(),f(_,{key:0},[t[64]||(t[64]=a("div",{class:"preview-card-content-divider"},null,-1)),U(" "+n(e.gst.fee_rate===l(V).PERCENTAGE?`${e.gst.fee_value}%`:e.gst.fee_value),1)],64))])]),a("div",Qt,[t[67]||(t[67]=a("div",{class:"preview-card-label"}," Surcharge: ",-1)),a("div",Yt,[U(n(u(H.value,d.value))+" ",1),t[66]||(t[66]=a("div",{class:"preview-card-content-divider"},null,-1)),U(" "+n(ne()),1)])]),a("div",It,[t[68]||(t[68]=a("div",{class:"preview-card-label"}," Total: ",-1)),a("div",At,[a("span",Vt,n(u(de.value,d.value)),1)])])])):v("",!0)])])}}}),Wt=ie(Gt,[["__scopeId","data-v-ac7f64b8"]]),Kt=Object.freeze(Object.defineProperty({__proto__:null,default:Wt},Symbol.toStringTag,{value:"Module"}));export{Wt as S,Pe as _,Kt as s};
