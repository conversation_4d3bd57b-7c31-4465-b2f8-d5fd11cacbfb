import{I as c}from"./invoice-B6mg1HKE.js";import{C as i}from"./plan-DCOwcHy2.js";import{T as E}from"./transaction-CqiD1t2Y.js";var r=(e=>(e[e.NotStart=0]="NotStart",e[e.InProgress=1]="InProgress",e[e.Succeeded=2]="Succeeded",e[e.Failed=3]="Failed",e))(r||{}),n=(e=>(e[e.PENDING=0]="PENDING",e[e.COMPLETED=1]="COMPLETED",e[e.FAILED=2]="FAILED",e[e.PROCESSING=3]="PROCESSING",e))(n||{});const I=e=>({[i.ACTIVE]:"paid",[i.INACTIVE]:"failed",[i.PROCESSING]:"upcoming",[i.PROCESSED]:"upcoming",[i.ON_HOLD]:"upcoming",[i.COMPLETED]:"upcoming",[i.CANCELLED]:"upcoming"})[e]||"upcoming",o=e=>({[E.PENDING]:"upcoming",[E.SUCCEEDED]:"paid",[E.FAILED]:"failed",[E.REFUNDED]:"upcoming",[E.DISPUTED]:"upcoming",[E.UNCAPTURED]:"upcoming"})[e]||"upcoming",a=e=>({[r.InProgress]:"upcoming",[r.Succeeded]:"paid",[r.Failed]:"failed",[r.NotStart]:"upcoming"})[e]||"upcoming",C=e=>({[n.PENDING]:"upcoming",[n.COMPLETED]:"paid",[n.FAILED]:"failed",[n.PROCESSING]:"upcoming"})[e]||"upcoming",N=e=>({[c.UNPAID]:"default",[c.PARTIAL]:"paid",[c.RECEIVED]:"paid",[c.OVERDUE]:"failed"})[e]||"upcoming";export{o as a,a as b,N as c,C as d,I as g};
