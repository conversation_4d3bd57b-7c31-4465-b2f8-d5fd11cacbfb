import{s as Qe,a as Ke}from"./index-CxOnO8if.js";import{E as et,a1 as Be,d as tt,r as z,S as Ce,f as at,a as rt,o as nt,b as P,e as O,w as q,t as le,j as he}from"./index-BPwFJVSY.js";import{s as it}from"./index-Cbodd6BQ.js";import{b as ot}from"./report-BieIWVzh.js";import{a4 as Re,o as xe,S as F,U as st,bl as lt,b4 as ut,bm as N,bn as Pe,w as Ie,aY as je,aZ as vt,bo as Ne,bp as $e,bq as me,p as ue,aw as ze,br as ct,_ as we,aH as Ue,a0 as X,aD as dt,aF as gt,Z as ft,aA as ye,an as Oe,aB as pt,aN as ht,aM as mt,aJ as yt,aI as bt,bs as St,bt as xt,bu as wt,q as Ve,aQ as Ye,bv as At,bw as Lt,bx as Dt,at as Tt,by as Mt,u as _t,i as ke,a as Ct,b as Pt,d as It,e as Nt,c as $t}from"./installCanvasRenderer-p5Q0jy4r.js";import{g as ve,i as Ot,a as Vt}from"./install-CHL8RKGY.js";import{i as kt}from"./install-Db2jL4kZ.js";import{i as Gt}from"./install-W_lq_5hT.js";var Ge=Math.PI*2,ce=Math.PI/180;function Fe(a,t){return st(a.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()})}function qe(a,t){var r=Fe(a,t),e=a.get("center"),n=a.get("radius");xe(n)||(n=[0,n]);var s=F(r.width,t.getWidth()),i=F(r.height,t.getHeight()),u=Math.min(s,i),d=F(n[0],u/2),f=F(n[1],u/2),c,p,g=a.coordinateSystem;if(g){var o=g.dataToPoint(e);c=o[0]||0,p=o[1]||0}else xe(e)||(e=[e,e]),c=F(e[0],s)+r.x,p=F(e[1],i)+r.y;return{cx:c,cy:p,r0:d,r:f}}function Et(a,t,r){t.eachSeriesByType(a,function(e){var n=e.getData(),s=n.mapDimension("value"),i=Fe(e,r),u=qe(e,r),d=u.cx,f=u.cy,c=u.r,p=u.r0,g=-e.get("startAngle")*ce,o=e.get("endAngle"),l=e.get("padAngle")*ce;o=o==="auto"?g-Ge:-o*ce;var v=e.get("minAngle")*ce,h=v+l,b=0;n.each(s,function(B){!isNaN(B)&&b++});var S=n.getSum(s),m=Math.PI/(S||b)*2,T=e.get("clockwise"),L=e.get("roseType"),M=e.get("stillShowZeroSum"),$=n.getDataExtent(s);$[0]=0;var y=T?1:-1,x=[g,o],G=y*l/2;lt(x,!T),g=x[0],o=x[1];var W=Xe(e);W.startAngle=g,W.endAngle=o,W.clockwise=T;var j=Math.abs(o-g),R=j,Q=0,E=g;if(n.setLayout({viewRect:i,r:c}),n.each(s,function(B,k){var w;if(isNaN(B)){n.setItemLayout(k,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:T,cx:d,cy:f,r0:p,r:L?NaN:c});return}L!=="area"?w=S===0&&M?m:B*m:w=j/b,w<h?(w=h,R-=h):Q+=B;var C=E+y*w,_=0,D=0;l>w?(_=E+y*w/2,D=_):(_=E+G,D=C-G),n.setItemLayout(k,{angle:w,startAngle:_,endAngle:D,clockwise:T,cx:d,cy:f,r0:p,r:L?ut(B,$,[p,c]):c}),E=C}),R<Ge&&b)if(R<=.001){var Y=j/b;n.each(s,function(B,k){if(!isNaN(B)){var w=n.getItemLayout(k);w.angle=Y;var C=0,_=0;Y<l?(C=g+y*(k+1/2)*Y,_=C):(C=g+y*k*Y+G,_=g+y*(k+1)*Y-G),w.startAngle=C,w.endAngle=_}})}else m=R/Q,E=g,n.each(s,function(B,k){if(!isNaN(B)){var w=n.getItemLayout(k),C=w.angle===h?h:B*m,_=0,D=0;C<l?(_=E+y*C/2,D=_):(_=E+G,D=E+y*C-G),w.startAngle=_,w.endAngle=D,E+=y*C}})})}var Xe=Re();function Bt(a){return{seriesType:a,reset:function(t,r){var e=r.findComponents({mainType:"legend"});if(!(!e||!e.length)){var n=t.getData();n.filterSelf(function(s){for(var i=n.getName(s),u=0;u<e.length;u++)if(!e[u].isSelected(i))return!1;return!0})}}}}function Ze(a,t,r,e,n,s,i,u){var d=n-a,f=s-t,c=r-a,p=e-t,g=Math.sqrt(c*c+p*p);c/=g,p/=g;var o=d*c+f*p,l=o/g;l*=g;var v=i[0]=a+l*c,h=i[1]=t+l*p;return Math.sqrt((v-n)*(v-n)+(h-s)*(h-s))}var Z=new N,A=new N,I=new N,H=new N,U=new N,de=[],V=new N;function Rt(a,t){if(t<=180&&t>0){t=t/180*Math.PI,Z.fromArray(a[0]),A.fromArray(a[1]),I.fromArray(a[2]),N.sub(H,Z,A),N.sub(U,I,A);var r=H.len(),e=U.len();if(!(r<.001||e<.001)){H.scale(1/r),U.scale(1/e);var n=H.dot(U),s=Math.cos(t);if(s<n){var i=Ze(A.x,A.y,I.x,I.y,Z.x,Z.y,de);V.fromArray(de),V.scaleAndAdd(U,i/Math.tan(Math.PI-t));var u=I.x!==A.x?(V.x-A.x)/(I.x-A.x):(V.y-A.y)/(I.y-A.y);if(isNaN(u))return;u<0?N.copy(V,A):u>1&&N.copy(V,I),V.toArray(a[1])}}}}function jt(a,t,r){if(r<=180&&r>0){r=r/180*Math.PI,Z.fromArray(a[0]),A.fromArray(a[1]),I.fromArray(a[2]),N.sub(H,A,Z),N.sub(U,I,A);var e=H.len(),n=U.len();if(!(e<.001||n<.001)){H.scale(1/e),U.scale(1/n);var s=H.dot(t),i=Math.cos(r);if(s<i){var u=Ze(A.x,A.y,I.x,I.y,Z.x,Z.y,de);V.fromArray(de);var d=Math.PI/2,f=Math.acos(U.dot(t)),c=d+f-r;if(c>=d)N.copy(V,I);else{V.scaleAndAdd(U,u/Math.tan(Math.PI/2-c));var p=I.x!==A.x?(V.x-A.x)/(I.x-A.x):(V.y-A.y)/(I.y-A.y);if(isNaN(p))return;p<0?N.copy(V,A):p>1&&N.copy(V,I)}V.toArray(a[1])}}}}function be(a,t,r,e){var n=r==="normal",s=n?a:a.ensureState(r);s.ignore=t;var i=e.get("smooth");i&&i===!0&&(i=.3),s.shape=s.shape||{},i>0&&(s.shape.smooth=i);var u=e.getModel("lineStyle").getLineStyle();n?a.useStyle(u):s.style=u}function zt(a,t){var r=t.smooth,e=t.points;if(e)if(a.moveTo(e[0][0],e[0][1]),r>0&&e.length>=3){var n=$e(e[0],e[1]),s=$e(e[1],e[2]);if(!n||!s){a.lineTo(e[1][0],e[1][1]),a.lineTo(e[2][0],e[2][1]);return}var i=Math.min(n,s)*r,u=me([],e[1],e[0],i/n),d=me([],e[1],e[2],i/s),f=me([],u,d,.5);a.bezierCurveTo(u[0],u[1],u[0],u[1],f[0],f[1]),a.bezierCurveTo(d[0],d[1],d[0],d[1],e[2][0],e[2][1])}else for(var c=1;c<e.length;c++)a.lineTo(e[c][0],e[c][1])}function Ut(a,t,r){var e=a.getTextGuideLine(),n=a.getTextContent();if(!n){e&&a.removeTextGuideLine();return}for(var s=t.normal,i=s.get("show"),u=n.ignore,d=0;d<Pe.length;d++){var f=Pe[d],c=t[f],p=f==="normal";if(c){var g=c.get("show"),o=p?u:Ie(n.states[f]&&n.states[f].ignore,u);if(o||!Ie(g,i)){var l=p?e:e&&e.states[f];l&&(l.ignore=!0),e&&be(e,!0,f,c);continue}e||(e=new je,a.setTextGuideLine(e),!p&&(u||!i)&&be(e,!0,"normal",t.normal),a.stateProxy&&(e.stateProxy=a.stateProxy)),be(e,!1,f,c)}}if(e){vt(e.style,r),e.style.fill=null;var v=s.get("showAbove"),h=a.textGuideLineConfig=a.textGuideLineConfig||{};h.showAbove=v||!1,e.buildPath=zt}}function Yt(a,t){t=t||"labelLine";for(var r={normal:a.getModel(t)},e=0;e<Ne.length;e++){var n=Ne[e];r[n]=a.getModel([n,t])}return r}var Ft=Math.PI/180;function Ee(a,t,r,e,n,s,i,u,d,f){if(a.length<2)return;function c(v){for(var h=v.rB,b=h*h,S=0;S<v.list.length;S++){var m=v.list[S],T=Math.abs(m.label.y-r),L=e+m.len,M=L*L,$=Math.sqrt(Math.abs((1-T*T/b)*M)),y=t+($+m.len2)*n,x=y-m.label.x,G=m.targetTextWidth-x*n;He(m,G,!0),m.label.x=y}}function p(v){for(var h={list:[],maxY:0},b={list:[],maxY:0},S=0;S<v.length;S++)if(v[S].labelAlignTo==="none"){var m=v[S],T=m.label.y>r?b:h,L=Math.abs(m.label.y-r);if(L>=T.maxY){var M=m.label.x-t-m.len2*n,$=e+m.len,y=Math.abs(M)<$?Math.sqrt(L*L/(1-M*M/$/$)):$;T.rB=y,T.maxY=L}T.list.push(m)}c(h),c(b)}for(var g=a.length,o=0;o<g;o++)if(a[o].position==="outer"&&a[o].labelAlignTo==="labelLine"){var l=a[o].label.x-f;a[o].linePoints[1][0]+=l,a[o].label.x=f}ct(a,d,d+i)&&p(a)}function qt(a,t,r,e,n,s,i,u){for(var d=[],f=[],c=Number.MAX_VALUE,p=-Number.MAX_VALUE,g=0;g<a.length;g++){var o=a[g].label;Se(a[g])||(o.x<t?(c=Math.min(c,o.x),d.push(a[g])):(p=Math.max(p,o.x),f.push(a[g])))}for(var g=0;g<a.length;g++){var l=a[g];if(!Se(l)&&l.linePoints){if(l.labelStyleWidth!=null)continue;var o=l.label,v=l.linePoints,h=void 0;l.labelAlignTo==="edge"?o.x<t?h=v[2][0]-l.labelDistance-i-l.edgeDistance:h=i+n-l.edgeDistance-v[2][0]-l.labelDistance:l.labelAlignTo==="labelLine"?o.x<t?h=c-i-l.bleedMargin:h=i+n-p-l.bleedMargin:o.x<t?h=o.x-i-l.bleedMargin:h=i+n-o.x-l.bleedMargin,l.targetTextWidth=h,He(l,h)}}Ee(f,t,r,e,1,n,s,i,u,p),Ee(d,t,r,e,-1,n,s,i,u,c);for(var g=0;g<a.length;g++){var l=a[g];if(!Se(l)&&l.linePoints){var o=l.label,v=l.linePoints,b=l.labelAlignTo==="edge",S=o.style.padding,m=S?S[1]+S[3]:0,T=o.style.backgroundColor?0:m,L=l.rect.width+T,M=v[1][0]-v[2][0];b?o.x<t?v[2][0]=i+l.edgeDistance+L+l.labelDistance:v[2][0]=i+n-l.edgeDistance-L-l.labelDistance:(o.x<t?v[2][0]=o.x+l.labelDistance:v[2][0]=o.x-l.labelDistance,v[1][0]=v[2][0]+M),v[1][1]=v[2][1]=o.y}}}function He(a,t,r){if(r===void 0&&(r=!1),a.labelStyleWidth==null){var e=a.label,n=e.style,s=a.rect,i=n.backgroundColor,u=n.padding,d=u?u[1]+u[3]:0,f=n.overflow,c=s.width+(i?0:d);if(t<c||r){var p=s.height;if(f&&f.match("break")){e.setStyle("backgroundColor",null),e.setStyle("width",t-d);var g=e.getBoundingRect();e.setStyle("width",Math.ceil(g.width)),e.setStyle("backgroundColor",i)}else{var o=t-d,l=t<c?o:r?o>a.unconstrainedWidth?null:o:null;e.setStyle("width",l)}var v=e.getBoundingRect();s.width=v.width;var h=(e.style.margin||0)+2.1;s.height=v.height+h,s.y-=(s.height-p)/2}}}function Se(a){return a.position==="center"}function Xt(a){var t=a.getData(),r=[],e,n,s=!1,i=(a.get("minShowLabelAngle")||0)*Ft,u=t.getLayout("viewRect"),d=t.getLayout("r"),f=u.width,c=u.x,p=u.y,g=u.height;function o(M){M.ignore=!0}function l(M){if(!M.ignore)return!0;for(var $ in M.states)if(M.states[$].ignore===!1)return!0;return!1}t.each(function(M){var $=t.getItemGraphicEl(M),y=$.shape,x=$.getTextContent(),G=$.getTextGuideLine(),W=t.getItemModel(M),j=W.getModel("label"),R=j.get("position")||W.get(["emphasis","label","position"]),Q=j.get("distanceToLabelLine"),E=j.get("alignTo"),Y=F(j.get("edgeDistance"),f),B=j.get("bleedMargin"),k=W.getModel("labelLine"),w=k.get("length");w=F(w,f);var C=k.get("length2");if(C=F(C,f),Math.abs(y.endAngle-y.startAngle)<i){ue(x.states,o),x.ignore=!0,G&&(ue(G.states,o),G.ignore=!0);return}if(l(x)){var _=(y.startAngle+y.endAngle)/2,D=Math.cos(_),J=Math.sin(_),ae,oe,Ae,se;e=y.cx,n=y.cy;var K=R==="inside"||R==="inner";if(R==="center")ae=y.cx,oe=y.cy,se="center";else{var ge=(K?(y.r+y.r0)/2*D:y.r*D)+e,fe=(K?(y.r+y.r0)/2*J:y.r*J)+n;if(ae=ge+D*3,oe=fe+J*3,!K){var Le=ge+D*(w+d-y.r),De=fe+J*(w+d-y.r),Te=Le+(D<0?-1:1)*C,Me=De;E==="edge"?ae=D<0?c+Y:c+f-Y:ae=Te+(D<0?-Q:Q),oe=Me,Ae=[[ge,fe],[Le,De],[Te,Me]]}se=K?"center":E==="edge"?D>0?"right":"left":D>0?"left":"right"}var re=Math.PI,ee=0,ne=j.get("rotate");if(ze(ne))ee=ne*(re/180);else if(R==="center")ee=0;else if(ne==="radial"||ne===!0){var We=D<0?-_+re:-_;ee=We}else if(ne==="tangential"&&R!=="outside"&&R!=="outer"){var te=Math.atan2(D,J);te<0&&(te=re*2+te);var Je=J>0;Je&&(te=re+te),ee=te-re}if(s=!!ee,x.x=ae,x.y=oe,x.rotation=ee,x.setStyle({verticalAlign:"middle"}),K){x.setStyle({align:se});var pe=x.states.select;pe&&(pe.x+=x.x,pe.y+=x.y)}else{var ie=x.getBoundingRect().clone();ie.applyTransform(x.getComputedTransform());var _e=(x.style.margin||0)+2.1;ie.y-=_e/2,ie.height+=_e,r.push({label:x,labelLine:G,position:R,len:w,len2:C,minTurnAngle:k.get("minTurnAngle"),maxSurfaceAngle:k.get("maxSurfaceAngle"),surfaceNormal:new N(D,J),linePoints:Ae,textAlign:se,labelDistance:Q,labelAlignTo:E,edgeDistance:Y,bleedMargin:B,rect:ie,unconstrainedWidth:ie.width,labelStyleWidth:x.style.width})}$.setTextConfig({inside:K})}}),!s&&a.get("avoidLabelOverlap")&&qt(r,e,n,d,f,g,c,p);for(var v=0;v<r.length;v++){var h=r[v],b=h.label,S=h.labelLine,m=isNaN(b.x)||isNaN(b.y);if(b){b.setStyle({align:h.textAlign}),m&&(ue(b.states,o),b.ignore=!0);var T=b.states.select;T&&(T.x+=b.x,T.y+=b.y)}if(S){var L=h.linePoints;m||!L?(ue(S.states,o),S.ignore=!0):(Rt(L,h.minTurnAngle),jt(L,h.surfaceNormal,h.maxSurfaceAngle),S.setShape({points:L}),b.__hostTarget.textGuideLineConfig={anchor:new N(L[0][0],L[0][1])})}}}var Zt=function(a){we(t,a);function t(r,e,n){var s=a.call(this)||this;s.z2=2;var i=new ft;return s.setTextContent(i),s.updateData(r,e,n,!0),s}return t.prototype.updateData=function(r,e,n,s){var i=this,u=r.hostModel,d=r.getItemModel(e),f=d.getModel("emphasis"),c=r.getItemLayout(e),p=X(ve(d.getModel("itemStyle"),c,!0),c);if(isNaN(p.startAngle)){i.setShape(p);return}if(s){i.setShape(p);var g=u.getShallow("animationType");u.ecModel.ssr?(ye(i,{scaleX:0,scaleY:0},u,{dataIndex:e,isFrom:!0}),i.originX=p.cx,i.originY=p.cy):g==="scale"?(i.shape.r=c.r0,ye(i,{shape:{r:c.r}},u,e)):n!=null?(i.setShape({startAngle:n,endAngle:n}),ye(i,{shape:{startAngle:c.startAngle,endAngle:c.endAngle}},u,e)):(i.shape.endAngle=c.startAngle,Oe(i,{shape:{endAngle:c.endAngle}},u,e))}else pt(i),Oe(i,{shape:p},u,e);i.useStyle(r.getItemVisual(e,"style")),ht(i,d);var o=(c.startAngle+c.endAngle)/2,l=u.get("selectedOffset"),v=Math.cos(o)*l,h=Math.sin(o)*l,b=d.getShallow("cursor");b&&i.attr("cursor",b),this._updateLabel(u,r,e),i.ensureState("emphasis").shape=X({r:c.r+(f.get("scale")&&f.get("scaleSize")||0)},ve(f.getModel("itemStyle"),c)),X(i.ensureState("select"),{x:v,y:h,shape:ve(d.getModel(["select","itemStyle"]),c)}),X(i.ensureState("blur"),{shape:ve(d.getModel(["blur","itemStyle"]),c)});var S=i.getTextGuideLine(),m=i.getTextContent();S&&X(S.ensureState("select"),{x:v,y:h}),X(m.ensureState("select"),{x:v,y:h}),mt(this,f.get("focus"),f.get("blurScope"),f.get("disabled"))},t.prototype._updateLabel=function(r,e,n){var s=this,i=e.getItemModel(n),u=i.getModel("labelLine"),d=e.getItemVisual(n,"style"),f=d&&d.fill,c=d&&d.opacity;yt(s,bt(i),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:f,defaultOpacity:c,defaultText:r.getFormattedLabel(n,"normal")||e.getName(n)});var p=s.getTextContent();s.setTextConfig({position:null,rotation:null}),p.attr({z2:10});var g=r.get(["label","position"]);if(g!=="outside"&&g!=="outer")s.removeTextGuideLine();else{var o=this.getTextGuideLine();o||(o=new je,this.setTextGuideLine(o)),Ut(this,Yt(i),{stroke:f,opacity:St(u.get(["lineStyle","opacity"]),c,1)})}},t}(Ue),Ht=function(a){we(t,a);function t(){var r=a!==null&&a.apply(this,arguments)||this;return r.ignoreLabelLineUpdate=!0,r}return t.prototype.render=function(r,e,n,s){var i=r.getData(),u=this._data,d=this.group,f;if(!u&&i.count()>0){for(var c=i.getItemLayout(0),p=1;isNaN(c&&c.startAngle)&&p<i.count();++p)c=i.getItemLayout(p);c&&(f=c.startAngle)}if(this._emptyCircleSector&&d.remove(this._emptyCircleSector),i.count()===0&&r.get("showEmptyCircle")){var g=Xe(r),o=new Ue({shape:X(qe(r,n),g)});o.useStyle(r.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=o,d.add(o)}i.diff(u).add(function(l){var v=new Zt(i,l,f);i.setItemGraphicEl(l,v),d.add(v)}).update(function(l,v){var h=u.getItemGraphicEl(v);h.updateData(i,l,f),h.off("click"),d.add(h),i.setItemGraphicEl(l,h)}).remove(function(l){var v=u.getItemGraphicEl(l);dt(v,r,l)}).execute(),Xt(r),r.get("animationTypeUpdate")!=="expansion"&&(this._data=i)},t.prototype.dispose=function(){},t.prototype.containPoint=function(r,e){var n=e.getData(),s=n.getItemLayout(0);if(s){var i=r[0]-s.cx,u=r[1]-s.cy,d=Math.sqrt(i*i+u*u);return d<=s.r&&d>=s.r0}},t.type="pie",t}(gt);function Wt(a,t,r){t=xe(t)&&{coordDimensions:t}||X({encodeDefine:a.getEncode()},t);var e=a.getSource(),n=xt(e,t).dimensions,s=new wt(n,a);return s.initData(e,r),s}var Jt=function(){function a(t,r){this._getDataWithEncodedVisual=t,this._getRawData=r}return a.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},a.prototype.containName=function(t){var r=this._getRawData();return r.indexOfName(t)>=0},a.prototype.indexOfName=function(t){var r=this._getDataWithEncodedVisual();return r.indexOfName(t)},a.prototype.getItemVisual=function(t,r){var e=this._getDataWithEncodedVisual();return e.getItemVisual(t,r)},a}(),Qt=Re(),Kt=function(a){we(t,a);function t(){return a!==null&&a.apply(this,arguments)||this}return t.prototype.init=function(r){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new Jt(Ve(this.getData,this),Ve(this.getRawData,this)),this._defaultLabelLine(r)},t.prototype.mergeOption=function(){a.prototype.mergeOption.apply(this,arguments)},t.prototype.getInitialData=function(){return Wt(this,{coordDimensions:["value"],encodeDefaulter:Ye(At,this)})},t.prototype.getDataParams=function(r){var e=this.getData(),n=Qt(e),s=n.seats;if(!s){var i=[];e.each(e.mapDimension("value"),function(d){i.push(d)}),s=n.seats=Lt(i,e.hostModel.get("percentPrecision"))}var u=a.prototype.getDataParams.call(this,r);return u.percent=s[r]||0,u.$vars.push("percent"),u},t.prototype._defaultLabelLine=function(r){Dt(r,"labelLine",["show"]);var e=r.labelLine,n=r.emphasis.labelLine;e.show=e.show&&r.label.show,n.show=n.show&&r.emphasis.label.show},t.type="series.pie",t.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},t}(Tt);function ea(a){return{seriesType:a,reset:function(t,r){var e=t.getData();e.filterSelf(function(n){var s=e.mapDimension("value"),i=e.get(s,n);return!(ze(i)&&!isNaN(i)&&i<0)})}}}function ta(a){a.registerChartView(Ht),a.registerSeriesModel(Kt),Mt("pie",a.registerAction),a.registerLayout(Ye(Et,"pie")),a.registerProcessor(Bt("pie")),a.registerProcessor(ea("pie"))}var aa=et.extend({name:"columngroup"}),ra={name:"BaseColumnGroup",extends:Be,props:{type:{type:String,default:null}},style:aa,provide:function(){return{$pcColumnGroup:this,$parentInstance:this}}},na={name:"ColumnGroup",extends:ra,inheritAttrs:!1,inject:["$columnGroups"],mounted:function(){var t;(t=this.$columnGroups)===null||t===void 0||t.add(this.$)},unmounted:function(){var t;(t=this.$columnGroups)===null||t===void 0||t.delete(this.$)},render:function(){return null}},ia={name:"Row",extends:Be,inject:["$rows"],mounted:function(){var t;(t=this.$rows)===null||t===void 0||t.add(this.$)},unmounted:function(){var t;(t=this.$rows)===null||t===void 0||t.delete(this.$)},render:function(){return null}};const oa={class:"w-2xl h-[350px] relative"},sa={class:"absolute top-0 right-0 z-100"},la={class:"acceptance mt-10"},ua={class:"mt-4 relative"},va={class:"absolute top-[-40px] right-0 z-100"},ca={class:"flex flex-col gap-y-2"},da={class:"text-[#333] font-semibold text-xl"},xa=tt({__name:"transaction-payout",setup(a){_t([Ct,Pt,Ot,ta,It,kt,Nt,Vt,$t,Gt]);const t=z(),r=z({title:{text:"Transactions & Payout",left:"center",top:"10px",textStyle:{color:"#888",fontWeight:"normal",fontSize:18}},tooltip:{trigger:"axis"},legend:{data:["Transactions","Payout"],left:"center",bottom:"1px"},grid:{left:"3%",right:"4%",bottom:"7%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:[""]},yAxis:{type:"value",position:"right"},series:[{name:"Transactions",type:"line",stack:"Total",data:[1]},{name:"Payout",type:"line",stack:"Total",data:[1]}]}),e=z(),n=z({title:{text:"Transactions type breakdown",left:"center",top:"10px",textStyle:{color:"#888",fontWeight:"normal",fontSize:18}},tooltip:{trigger:"item"},legend:{left:"center",bottom:"1px"},series:[{name:"Access From",type:"pie",radius:"50%",avoidLabelOverlap:!1,padAngle:1,itemStyle:{borderRadius:5},label:{show:!0,position:"inner",formatter:`{b}
{d}%`},labelLine:{show:!1},data:[{}]}]}),s=z([{}]),i=z([{name:"3 days",code:"3"},{name:"7 days",code:"7"},{name:"14 days",code:"14"},{name:"30 days",code:"30"}]),u=z({name:"3 days",code:"3"}),d=z([{name:"Last 1 month",code:"1"},{name:"Last 3 month",code:"3"},{name:"Last a year",code:"12"}]),f=z({name:"Last 1 month",code:"1"}),c=z(""),p=()=>{const g={trCountLast:u.value.code,costLast:f.value.code};ot(g).then(o=>{const l=o.data;n.value.series[0].data=[],s.value=[],c.value=`${l.cost_date_range.start} - ${l.cost_date_range.end}`,r.value.series[0].data=Object.values(l.daily).map(v=>Number(v)),r.value.series[1].data=Object.values(l.payout_daily).map(v=>Number(v)),r.value.xAxis.data=Object.keys(l.daily).map(v=>v),Object.keys(l.by_brand).forEach(v=>n.value.series[0].data.push({name:v,value:l.by_brand[v]})),s.value=[],Object.keys(l.cost_by_brand).forEach(v=>s.value.push({monthly:v,netSales:l.cost_by_brand[v].net_amount,percent:l.cost_by_brand[v].fee_amount,dollar:l.cost_by_brand[v].fee_ratio})),r.value&&t.value.setOption(r.value),n.value&&e.value.setOption(n.value)})};return Ce(u,()=>{p()}),Ce(f,()=>{p()}),at(()=>{const g=document.getElementById("count"),o=document.getElementById("breakdown");g&&(t.value=ke(g),r.value&&t.value.setOption(r.value)),o&&(e.value=ke(o),n.value&&e.value.setOption(n.value)),Promise.all([p()])}),(g,o)=>{const l=it,v=Qe,h=ia,b=na,S=Ke;return nt(),rt("div",null,[o[6]||(o[6]=P("div",{class:"text-xl font-semibold"}," Transactions count for last ",-1)),P("div",null,[P("div",oa,[P("div",sa,[O(l,{modelValue:u.value,"onUpdate:modelValue":o[0]||(o[0]=m=>u.value=m),options:i.value,"option-label":"name",class:"bg-transparent"},null,8,["modelValue","options"])]),o[2]||(o[2]=P("div",{id:"count",style:{width:"100%",height:"100%"}},null,-1))]),o[5]||(o[5]=P("div",{class:"breakdown mt-10"},[P("div",{class:"title font-semibold text-lg"}," Transactions type break down "),P("div",{class:"w-2xl h-[350px]"},[P("div",{id:"breakdown",style:{width:"100%",height:"100%"}})])],-1)),P("div",la,[o[4]||(o[4]=P("div",{class:"title font-semibold text-lg"}," Cost of acceptance for last ",-1)),P("div",ua,[P("div",va,[O(l,{modelValue:f.value,"onUpdate:modelValue":o[1]||(o[1]=m=>f.value=m),options:d.value,"option-label":"name",class:"bg-transparent"},null,8,["modelValue","options"])]),P("div",null,[O(S,{value:s.value,"table-style":"min-width: 50rem","show-gridlines":""},{default:q(()=>[O(b,{type:"header"},{default:q(()=>[O(h,null,{default:q(()=>[O(v,{rowspan:3},{header:q(()=>[P("div",ca,[o[3]||(o[3]=P("span",null,"Calculated timeframe",-1)),P("span",da,le(c.value),1)])]),_:1}),O(v,{header:"Net Sales",rowspan:3}),O(v,{header:"Cost of Acceptance",colspan:4})]),_:1}),O(h,null,{default:q(()=>[O(v,{header:"($)"}),O(v,{header:"(%) of net sales"})]),_:1})]),_:1}),O(v,{field:"monthly"}),O(v,{field:"netSales"},{body:q(m=>[he(" $"+le(m.data.netSales),1)]),_:1}),O(v,{field:"percent"},{body:q(m=>[he(" $"+le(m.data.percent),1)]),_:1}),O(v,{field:"dollar"},{body:q(m=>[he(le(m.data.dollar)+"% ",1)]),_:1})]),_:1},8,["value"])])])])])])}}});export{xa as _};
