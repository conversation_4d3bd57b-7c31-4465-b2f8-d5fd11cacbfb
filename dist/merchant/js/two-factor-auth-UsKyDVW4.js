import{s as R}from"./index-BlFKIn6Y.js";import{D as $,E as q,G as L,H as Q,a as h,o as f,y as U,z as j,I as G,e as b,A as D,v as H,d as J,k as W,J as X,l as Y,r as w,c as Z,i as ee,x as te,f as ne,b as s,q as A,t as p,u as i,j as B,s as se,K as oe,p as P,w as ae,L as ie,M as re}from"./index-BPwFJVSY.js";import{s as ue}from"./index-AtQkrpJ9.js";import{s as le,a as ce}from"./index-CEksU3zE.js";import{Q as de}from"./browser-AVoJjGFw.js";import{_ as pe}from"./_plugin-vue_export-helper-DlAUqK2U.js";var fe=$`
    .p-inputotp {
        display: flex;
        align-items: center;
        gap: dt('inputotp.gap');
    }

    .p-inputotp-input {
        text-align: center;
        width: dt('inputotp.input.width');
    }

    .p-inputotp-input.p-inputtext-sm {
        text-align: center;
        width: dt('inputotp.input.sm.width');
    }

    .p-inputotp-input.p-inputtext-lg {
        text-align: center;
        width: dt('inputotp.input.lg.width');
    }
`,ve={root:"p-inputotp p-component",pcInputText:"p-inputotp-input"},me=q.extend({name:"inputotp",style:fe,classes:ve}),he={name:"BaseInputOtp",extends:ce,props:{readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},length:{type:Number,default:4},mask:{type:Boolean,default:!1},integerOnly:{type:Boolean,default:!1}},style:me,provide:function(){return{$pcInputOtp:this,$parentInstance:this}}},E={name:"InputOtp",extends:he,inheritAttrs:!1,emits:["change","focus","blur"],data:function(){return{tokens:[]}},watch:{modelValue:{immediate:!0,handler:function(e){this.tokens=e?e.split(""):new Array(this.length)}}},methods:{getTemplateAttrs:function(e){return{value:this.tokens[e]}},getTemplateEvents:function(e){var t=this;return{input:function(r){return t.onInput(r,e)},keydown:function(r){return t.onKeyDown(r)},focus:function(r){return t.onFocus(r)},blur:function(r){return t.onBlur(r)},paste:function(r){return t.onPaste(r)}}},onInput:function(e,t){this.tokens[t]=e.target.value,this.updateModel(e),e.inputType==="deleteContentBackward"?this.moveToPrev(e):(e.inputType==="insertText"||e.inputType==="deleteContentForward"||L()&&e instanceof CustomEvent)&&this.moveToNext(e)},updateModel:function(e){var t=this.tokens.join("");this.writeValue(t,e),this.$emit("change",{originalEvent:e,value:t})},moveToPrev:function(e){var t=this.findPrevInput(e.target);t&&(t.focus(),t.select())},moveToNext:function(e){var t=this.findNextInput(e.target);t&&(t.focus(),t.select())},findNextInput:function(e){var t=e.nextElementSibling;if(t)return t.nodeName==="INPUT"?t:this.findNextInput(t)},findPrevInput:function(e){var t=e.previousElementSibling;if(t)return t.nodeName==="INPUT"?t:this.findPrevInput(t)},onFocus:function(e){e.target.select(),this.$emit("focus",e)},onBlur:function(e){this.$emit("blur",e)},onClick:function(e){setTimeout(function(){return e.target.select()},1)},onKeyDown:function(e){if(!(e.ctrlKey||e.metaKey))switch(e.code){case"ArrowLeft":this.moveToPrev(e),e.preventDefault();break;case"ArrowUp":case"ArrowDown":e.preventDefault();break;case"Backspace":e.target.value.length===0&&(this.moveToPrev(e),e.preventDefault());break;case"ArrowRight":this.moveToNext(e),e.preventDefault();break;case"Enter":case"NumpadEnter":case"Tab":break;default:(this.integerOnly&&!(e.code!=="Space"&&Number(e.key)>=0&&Number(e.key)<=9)||this.tokens.join("").length>=this.length&&e.code!=="Delete")&&e.preventDefault();break}},onPaste:function(e){var t=e.clipboardData.getData("text");if(t.length){var l=t.substring(0,this.length);(!this.integerOnly||!isNaN(l))&&(this.tokens=l.split(""),this.updateModel(e))}e.preventDefault()}},computed:{inputMode:function(){return this.integerOnly?"numeric":"text"},inputType:function(){return this.mask?"password":"text"}},components:{OtpInputText:le}};function ye(n,e,t,l,r,c){var v=Q("OtpInputText");return f(),h("div",H({class:n.cx("root")},n.ptmi("root")),[(f(!0),h(U,null,j(n.length,function(m){return G(n.$slots,"default",{key:m,events:c.getTemplateEvents(m-1),attrs:c.getTemplateAttrs(m-1),index:m},function(){return[b(v,{value:r.tokens[m-1],type:c.inputType,class:D(n.cx("pcInputText")),name:n.$formName,inputmode:c.inputMode,variant:n.variant,readonly:n.readonly,disabled:n.disabled,size:n.size,invalid:n.invalid,tabindex:n.tabindex,unstyled:n.unstyled,onInput:function(y){return c.onInput(y,m-1)},onFocus:e[0]||(e[0]=function(d){return c.onFocus(d)}),onBlur:e[1]||(e[1]=function(d){return c.onBlur(d)}),onPaste:e[2]||(e[2]=function(d){return c.onPaste(d)}),onKeydown:e[3]||(e[3]=function(d){return c.onKeyDown(d)}),onClick:e[4]||(e[4]=function(d){return c.onClick(d)}),pt:n.ptm("pcInputText")},null,8,["value","type","class","name","inputmode","variant","readonly","disabled","size","invalid","tabindex","unstyled","onInput","pt"])]})}),128))],16)}E.render=ye;const ge={class:"two-factor-auth-container"},we={class:"two-factor-auth-card"},be={key:0,class:"loading-container"},_e={key:1},ke={key:0,class:"setup-step"},Ae={class:"setup-instructions"},Te={class:"qr-code-container"},xe={class:"qr-code-main"},Ce={key:0,class:"qr-code"},Ie={class:"scan-instruction"},Fe={class:"qr-code-apps"},Ne={class:"app-download"},Se=["src"],Be={class:"app-download"},Pe=["src"],De={key:0,class:"secret-key-container"},Ee={class:"secret-key-wrapper"},Oe={key:0,class:"secret-key"},Ke={class:"action-buttons"},Ve={key:1,class:"verification-step"},Me={class:"verification-description"},ze={class:"verification-form"},Re={class:"verification-code-container"},$e={class:"verification-code-label"},qe={class:"flex items-center justify-center w-full mt-6"},Le={class:"action-buttons"},Qe=J({__name:"two-factor-auth",setup(n){const{t:e}=W(),t=ee(),l=X(),r=Y(),c=()=>{u.value&&u.value.secret&&navigator.clipboard.writeText(u.value.secret).then(()=>{C.value=!0,l.add({severity:"success",summary:e("common.success"),detail:e("user.twoFactorAuth.secretKeyCopied")}),setTimeout(()=>{C.value=!1},3e3)}).catch(()=>{l.add({severity:"error",summary:e("common.error"),detail:e("user.twoFactorAuth.copyFailed")})})},v=w(!1),m=w(!1),d=w(!1),y=w(""),_=w(""),C=w(!1),u=w({secret:"",google2fa_url:"",ios_download_path:"",android_download_path:"",is_bind:!1}),O=o=>{o&&u.value.google2fa_url&&de.toCanvas(o,u.value.google2fa_url,{width:240,margin:0})},I=Z(()=>{const o=String(y.value);return o&&o.length===6&&/^\d+$/.test(o)?(T(),!0):!1}),K=async()=>{v.value=!0;try{const{data:o,code:a=1}=await ie();a===0&&(u.value=o,o.is_bind&&(g.value=2))}catch(o){F(o,e("user.twoFactorAuth.errorGettingQRCode"))}finally{v.value=!1}},V=async()=>{v.value=!0;try{const{code:o,message:a}=await re({secret:u.value.secret,verify_code:String(y.value)});return o!==0?(_.value=a||e("user.twoFactorAuth.invalidCode"),!1):(v.value=!1,_.value="",d.value=!0,!0)}catch(o){return v.value=!1,F(o,e("user.twoFactorAuth.errorEnabling")),!1}},M=()=>{y.value="",_.value=""},F=(o,a)=>{const x=(o==null?void 0:o.message)||a;_.value=x,l.add({severity:"error",summary:e("user.twoFactorAuth.error"),detail:x,life:5e3})},g=w(1),T=async()=>{g.value===1?g.value=2:g.value===2&&I.value&&(await V()?(await r.initializeFromStorage(),v.value=!1,l.add({severity:"success",summary:e("user.twoFactorAuth.success"),detail:e("user.twoFactorAuth.setupComplete")}),t.push({name:te.ROOT})):y.value="")},N=()=>{g.value>1?(g.value--,M()):(r.logout(),t.push("/"))};return ne(async()=>{await K()}),(o,a)=>{const x=ue,k=se,z=R;return f(),h("div",ge,[s("div",we,[v.value?(f(),h("div",be,[b(x)])):(f(),h("div",_e,[g.value===1?(f(),h("div",ke,[s("h2",null,p(i(e)("user.twoFactorAuth.setupStep")),1),s("div",Ae,[s("ol",null,[s("li",null,p(i(e)("user.twoFactorAuth.downloadApp")),1),s("li",null,p(i(e)("user.twoFactorAuth.scanQRCode")),1),s("li",null,p(i(e)("user.twoFactorAuth.getVerificationCode")),1)])]),s("div",Te,[s("div",xe,[u.value.google2fa_url?(f(),h("div",Ce,[s("canvas",{id:"qrcode-canvas",ref:O})])):A("",!0),s("p",Ie,[a[2]||(a[2]=s("i",{class:"pi pi-mobile"},null,-1)),B(" "+p(i(e)("user.twoFactorAuth.scanQRCode")),1)])]),s("div",Fe,[s("div",Ne,[s("img",{src:u.value.android_download_path,alt:"Google Authenticator",class:"app-icon"},null,8,Se),a[3]||(a[3]=s("span",{class:"app-label"},"Android",-1))]),s("div",Be,[s("img",{src:u.value.ios_download_path,alt:"Authy",class:"app-icon"},null,8,Pe),a[4]||(a[4]=s("span",{class:"app-label"},"iOS",-1))])])]),u.value.secret?(f(),h("div",De,[s("h3",null,p(i(e)("user.twoFactorAuth.manualSetup")),1),s("p",null,p(i(e)("user.twoFactorAuth.enterSecretKey")),1),s("div",Ee,[u.value?(f(),h("div",Oe,p(u.value.secret),1)):A("",!0),b(k,{icon:"pi pi-copy",severity:"secondary",text:"",rounded:"","aria-label":"Copy",class:"copy-button",onClick:c})])])):A("",!0),s("div",Ke,[b(k,{label:i(e)("common.back"),severity:"secondary",outlined:"",disabled:v.value,onClick:N},null,8,["label","disabled"]),b(k,{label:i(e)("common.next"),severity:"primary",disabled:v.value,onClick:T},null,8,["label","disabled"])])])):g.value===2?(f(),h("div",Ve,[s("h2",null,p(i(e)("user.twoFactorAuth.verificationStep")),1),s("p",Me,p(i(e)("user.twoFactorAuth.enterCodeFromApp")),1),s("div",ze,[s("div",Re,[s("div",$e,p(i(e)("user.twoFactorAuth.verificationCode")),1),b(i(E),{id:"verificationCode",modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=S=>y.value=S),disabled:m.value,length:6,"input-class":"otp-input",separator:"",size:"large",class:D({"p-invalid":_.value}),onKeyup:oe(T,["enter"])},null,8,["modelValue","disabled","class"]),s("div",qe,[_.value?(f(),P(z,{key:0,severity:"error",size:"small",variant:"simple"},{default:ae(()=>[B(p(_.value),1)]),_:1})):A("",!0)])])]),s("div",Le,[b(k,{label:"Back To Login",severity:"secondary",outlined:"",onClick:a[1]||(a[1]=S=>i(r).logout())}),u.value.is_bind?A("",!0):(f(),P(k,{key:0,label:i(e)("common.back"),severity:"secondary",outlined:"",disabled:m.value,onClick:N},null,8,["label","disabled"])),b(k,{style:{"margin-left":"auto"},label:i(e)("user.twoFactorAuth.verifyAndEnable"),severity:"primary",loading:m.value,disabled:!I.value||m.value,onClick:T},null,8,["label","loading","disabled"])])])):A("",!0)]))])])}}}),Xe=pe(Qe,[["__scopeId","data-v-da16badf"]]);export{Xe as default};
