import{t as X,c as g,a as b,g as $,b as m,e as w,d as C,f as M}from"./index.esm-DOIZz6Kf.js";import{d as p}from"./dayjs.min-Bar1rHDB.js";import{r as c,l as Y,bu as N,i as B,B as E,f as G,bw as x}from"./index-BPwFJVSY.js";import{n as k}from"./customer-M3EzHCil.js";import{a as P,b as j,u as F,c as U}from"./integrations-C_ojvdY3.js";import{g as z}from"./invoice-CnWRWdDk.js";function Z(r){const d=c(""),q=c(),D=Y(),v=c(null),{user:o,activeBid:f}=N(D),_=B(),I=E(),y=c(null),n=c({createInvoice:!1,updateInvoice:!1,loadingCustomers:!0,loadingConfig:!0,loadingXeroChartOfAccounts:!0,loadingXeroInvoiceTemplates:!0,loadingDetail:!1}),l=c({customers:[],xeroChartOfAccounts:[],xeroInvoiceTemplates:[]}),i=c({selectCustomer:"",firstName:"",lastName:"",email:"",phone:"",address:"",paymentDueDate:p().add(1,"day").toDate(),xeroChartOfAccount:"",xeroInvoiceTemplate:"",paymentAmount:0,reference:"",addLineItems:!0,lineItems:[{description:"",amount:""}],isInclusiveGst:!1}),S=X(g({selectCustomer:r==="addCustomer"?m().optional():m().required("Customer is required"),paymentDueDate:M().required("Payment Due Date is required").min(p().toDate(),"Payment Due Date must be at least tomorrow"),xeroChartOfAccount:C().test("xeroChartOfAccount","Xero Chart of Account is required",e=>{var t;return!((t=o==null?void 0:o.value)!=null&&t.xero_link&&!e)}),xeroInvoiceTemplate:C().test("xeroInvoiceTemplate","Xero Invoice Template is required",e=>{var t;return!((t=o==null?void 0:o.value)!=null&&t.xero_link&&!e)}),paymentAmount:w().notRequired(),reference:m().notRequired(),addLineItems:b().default(!1),lineItems:$().when("addLineItems",{is:!0,then:e=>e.of(g({description:m().required("Description is required"),amount:w().typeError("Amount is required").min(1,"Please enter a positive number").required("Amount is required")})).min(1,'Line items are required when "Add Line Items" is enabled'),otherwise:e=>e.of(g({description:m().notRequired(),amount:C().notRequired()})).notRequired()}),isInclusiveGst:b().optional()})),T=e=>{const t=[];e.addLineItems&&e.lineItems.forEach(a=>{t.push({description:a.description,unit_amount:a.amount})});const s={customer_id:e.selectCustomer,due_date:p(e.paymentDueDate).format("YYYY-MM-DD"),line_items:t,reference:e==null?void 0:e.reference,theme_id:e==null?void 0:e.xeroInvoiceTemplate,is_inclusive_gst:e.isInclusiveGst?1:0,is_line_items:e.addLineItems?1:0,account_code:e.xeroChartOfAccount,payment_amount:String(e.paymentAmount)};n.value.createInvoice=!0,U(s,{headers:{"Business-Id":d.value}}).then(a=>{a.code===0&&(r==="add"||r==="addCustomer")?(window.$toast.add({severity:"success",summary:"Success",detail:"Invoice created successfully"}),_.replace({name:"payMyInvoiceCreateAndSend",params:{id:a.data.id}})):a.code===0&&r==="addCustomer"&&(window.$toast.add({severity:"success",summary:"Success",detail:"Invoice created successfully"}),_.back())}).finally(()=>{n.value.createInvoice=!1})},L=async e=>{const t=[];e.addLineItems&&e.lineItems.forEach(a=>{t.push({line_item_id:a==null?void 0:a.id,description:a.description,unit_amount:a.amount})});const s={id:Number(I.params.id),customer_id:e.selectCustomer,due_date:p(e.paymentDueDate).format("YYYY-MM-DD"),reference:e==null?void 0:e.reference,line_items:t,is_inclusive_gst:e.isInclusiveGst?1:0,is_line_items:e.addLineItems?1:0,account_code:e.xeroChartOfAccount,payment_amount:String(e.paymentAmount)};try{n.value.updateInvoice=!0;const{code:a,data:u}=await F(s);a===0&&(window.$toast.add({severity:"success",summary:"Success",detail:"Invoice updated successfully"}),_.replace({name:"payMyInvoiceEditAndSend",params:{editId:I.params.id,id:u.id}}))}finally{n.value.updateInvoice=!1}},h=async()=>{var e,t;try{const s=await k({},{headers:{"Business-Id":d.value}});if(l.value.customers=s.data,(e=y.value)!=null&&e.customer&&r==="edit"){const a=l.value.customers.find(u=>{var A;return u.customer_id===((A=y.value)==null?void 0:A.customer_id)});if(a){const u=(t=String(a.name))==null?void 0:t.split(" ");i.value.firstName=u[0]||"",i.value.lastName=u[1]||"",i.value.email=a.email_primary,i.value.phone=a.phone_mobile,i.value.address=a.address||""}}}finally{n.value.loadingCustomers=!1}},O=async()=>{try{n.value.loadingDetail=!0;const{data:e}=await z({id:Number(I.params.id)});y.value=e,d.value=e.business_id,i.value.selectCustomer=e.customer_id,i.value.paymentDueDate=new Date(e.due_date),i.value.reference=e.reference,i.value.addLineItems=e.is_line_items===1,i.value.isInclusiveGst=e.is_inclusive_gst===1,e.line_items&&e.is_line_items===1?(i.value.addLineItems=!0,i.value.lineItems=e.line_items.map(t=>({id:t.line_item_id,description:t.description,amount:t.unit_amount})),e.line_items[0].account_code&&(i.value.xeroChartOfAccount=e.line_items[0].account_code),e.branding_theme_id&&(i.value.xeroInvoiceTemplate=e.branding_theme_id)):i.value.paymentAmount=e.sub_total||0}finally{n.value.loadingDetail=!1}},R=e=>{e&&(q.value=e)};return G(async()=>{var e;if(["add"].includes(r)){n.value.loadingConfig=!0;const{data:{fee_config:t=[]}}=await x();D.showSelectBid(()=>{d.value=f.value;const s=t==null?void 0:t.find(a=>a.business_id===f.value);s&&(v.value=s),n.value.loadingConfig=!1,h()})}if(["addCustomer"].includes(r)&&(d.value=f.value),["edit"].includes(r)){const{data:{fee_config:t=[]}}=await x(),s=t==null?void 0:t.find(a=>a.business_id===f.value);s&&(v.value=s),n.value.loadingConfig=!1,await O(),await h()}(e=o==null?void 0:o.value)!=null&&e.xero_link&&(P().then(t=>{l.value.xeroChartOfAccounts=t.data,n.value.loadingXeroChartOfAccounts=!1}),j().then(t=>{l.value.xeroInvoiceTemplates=t.data,n.value.loadingXeroInvoiceTemplates=!1}))}),{schema:S,formData:i,options:l,loadings:n,config:v,handleSubmit:T,handleEditSubmit:L,updateCustomers:h,setFormRef:R}}export{Z as u};
