import{u as g}from"./dict-CLPQcIyV.js";import{r as d,c as y}from"./index-BPwFJVSY.js";function R(l,n){const a=g(),t=d([]),i=Reflect.get(a,"isTypeLoading"),r=Reflect.apply(i,a,[l]),s=async()=>{const e=Reflect.get(a,"getDictByType");return t.value=await Reflect.apply(e,a,[l]),n&&n(t.value),t.value},u=e=>{const c=t.value.find(o=>o.value===e);return c?c.label:""},p=e=>{const c=t.value.find(o=>o.label===e);return c?c.value:void 0},f=y(()=>t.value.map(e=>({label:e.label,value:e.value}))),v=()=>{const e=Reflect.get(a,"clearDictCache");Reflect.apply(e,a,[l])};return s(),{dictList:t,loading:r,loadDict:s,getLabel:u,getValue:p,options:f,clearCache:v}}export{R as u};
