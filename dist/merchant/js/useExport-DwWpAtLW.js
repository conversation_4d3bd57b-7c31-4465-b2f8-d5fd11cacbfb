import{d as y,r as v,a as f,o as m,e as n,s as B,w as E,b as i,y as h,z as w,t as L,X as C,J as k}from"./index-BPwFJVSY.js";import{s as S}from"./index-D7FsVJ-c.js";const F={class:"export-dialog"},V={class:"export-options"},$={class:"file-type-options"},D=["for"],U={class:"dialog-footer"},O=y({__name:"BaseExportDialog",props:{title:{default:"Export Options"},exportButtonLabel:{default:"EXPORT"},cancelButtonLabel:{default:"CANCEL"},loading:{type:Boolean,default:!1},exportLoading:{type:Boolean,default:!1}},emits:["export"],setup(e,{emit:d}){const a=e,x=d,o=v(!1),t=v("csv"),p=[{name:"CSV",value:"csv"}],r=()=>{o.value=!0,t.value="csv"},u=()=>{t.value&&(x("export",t.value),o.value=!1)};return(P,s)=>{const c=B,g=S,b=C;return m(),f("div",F,[n(c,{label:"Export",icon:"pi pi-share-alt",loading:a.loading,onClick:r},null,8,["loading"]),n(b,{visible:o.value,"onUpdate:visible":s[2]||(s[2]=l=>o.value=l),header:a.title,modal:!0,closable:!0,style:{width:"300px"},class:"export-dialog"},{footer:E(()=>[i("div",U,[n(c,{label:a.cancelButtonLabel,severity:"secondary",loading:a.exportLoading,class:"dialog-cancel-button w-26",onClick:s[1]||(s[1]=l=>o.value=!1)},null,8,["label","loading"]),n(c,{label:a.exportButtonLabel,severity:"warn",disabled:!t.value,loading:a.exportLoading,class:"w-26",onClick:u},null,8,["label","disabled","loading"])])]),default:E(()=>[i("div",V,[i("div",$,[(m(),f(h,null,w(p,l=>i("div",{key:l.value,class:"format-option"},[n(g,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=_=>t.value=_),value:l.value,"input-id":`format_${l.value}`},null,8,["modelValue","value","input-id"]),i("label",{for:`format_${l.value}`,class:"ml-2"},L(l.name),9,D)])),64))])])]),_:1},8,["visible","header"])])}}});function T(e){const d=k(),a=v(!1);return{isExporting:a,handleExport:async o=>{try{a.value=!0,e.onExportStart&&e.onExportStart();const p={...e.getParams(),format:o},r=await e.exportFn(p);if(r.data&&r.data.url){const u=r.data.url;window.open(u,"_blank"),d.add({severity:"success",summary:"Export Successful",detail:`Data has been exported as ${o.toUpperCase()}`,life:3e3}),e.onExportSuccess&&e.onExportSuccess(u)}else throw new Error("Export URL not found in response")}catch(t){console.error("Export failed:",t),d.add({severity:"error",summary:"Export Failed",detail:"Failed to export data. Please try again.",life:3e3}),e.onExportError&&e.onExportError(t)}finally{a.value=!1}}}}export{O as _,T as u};
