import{Z as o,r as c,i as n,aa as u}from"./index-BPwFJVSY.js";const R=o("list",()=>{const e=c(new Map);return{setNeedRefresh:(s,a)=>{e.value.set(s,a)},getNeedRefresh:s=>e.value.get(s)||!1,clearRefreshFlag:s=>{e.value.delete(s)}}},{persist:!0});function l(e,t){const r=R(),f=n();return u(()=>{r.getNeedRefresh(e)&&(t(),r.clearRefreshFlag(e))}),{backWithRefresh:(h=!0)=>{r.setNeedRefresh(e,!0),h&&f.back()},withRefresh:()=>{r.setNeedRefresh(e,!0)}}}export{l as u};
