import{l as S,c as a}from"./index-BPwFJVSY.js";var O=(t=>(t.HOME_GET_SUM_DATA="merchantHomeGetSumData",t.HOME_GET_PAYMENT_DATA="merchantHomeGetPaymentData",t.HOME_GET_BUSINESS_PERFORMANCE_SUMMARY="merchantHomeGetBusinessPerformanceSummary",t.CUSTOMER_GET_CUSTOMER_ID="merchantCustomerGetCustomerId",t.CUSTOMER_CREATE="merchantCustomerCreate",t.CUSTOMER_SEND_INVITE_MAIL="merchantCustomerSendInviteCustomerMail",t.CUSTOMER_UPDATE="merchantCustomerUpdate",t.CUSTOMER_DELETE="merchantCustomerDelete",t.CUSTOMER_DETAIL="merchantCustomerDetail",t.CUSTOMER_LIST="merchantCustomerList",t.CUSTOMER_EXPORT="merchantCustomerExport",t.CUSTOMER_COMMUNICATION_HISTORY="merchantCustomerCommunicationHistory",t.CUSTOMER_NOTIFICATION_SWITCH="merchantCustomerNotificationSwitch",t.INVITATION_SUBMIT="merchantInvitationSubmit",t.INVITATION_INFO="merchantInvitationInfo",t.PLAN_CREATE="merchantPlanCreate",t.PLAN_UPDATE="merchantPlanUpdate",t.PLAN_DETAIL="merchantPlanDetail",t.PLAN_LIST="merchantPlanList",t.PLAN_DELETE="merchantPlanDelete",t.PLAN_EXPORT="merchantPlanExport",t.GET_USER_INFO="merchantGetUserInfo",t.USER_UPDATE_PASSWORD="merchantUserUpdatePassword",t.GET_MERCHANT_CONFIG="merchantGetMerchantConfig",t.ROLE_LIST="merchantRoleList",t.ROLE_DETAIL="merchantRoleDetail",t.ROLE_CREATE="merchantRoleCreate",t.ROLE_UPDATE="merchantRoleUpdate",t.ROLE_DELETE="merchantRoleDelete",t.USER_LIST="merchantUserList",t.USER_DETAIL="merchantUserDetail",t.USER_CREATE="merchantUserCreate",t.USER_UPDATE="merchantUserUpdate",t.AUTH_USER_UPDATE="merchantAuthUserUpdate",t.USER_DELETE="merchantUserDelete",t.TRANS_LIST="merchantTransList",t.TRANS_DETAIL="merchantTransDetail",t.TRANS_REFUND="merchantTransRefund",t.TRANS_CREATE="merchantTransCreate",t.TRANS_EXPORT="merchantTransExport",t.REPORT_REVENUE="merchantReportRevenue",t.REPORT_REVENUE_DETAIL="merchantReportRevenueDetail",t.REPORT_TRANSACTION_PAYOUT="merchantReportTransactionPayout",t.REPORT_SUBSCRIPTION="merchantReportSubscription",t.SUBSCRIPTION_CREATE="merchantSubscriptionCreate",t.SUBSCRIPTION_UPDATE="merchantSubscriptionUpdate",t.SUBSCRIPTION_DETAIL="merchantSubscriptionDetail",t.SUBSCRIPTION_CANCEL="merchantSubscriptionCancel",t.SUBSCRIPTION_SEND_MOD_PAYMENT_METHOD_MAIL="merchantSubscriptionSendModPaymentMethodMail",t.SUBSCRIPTION_GET_MOD_PAYMENT_METHOD_INFO="merchantSubscriptionGetModPaymentMethodInfo",t.SUBSCRIPTION_MOD_PAYMENT_METHOD="merchantSubscriptionModPaymentMethod",t.PAYOUT_LIST="merchantPayoutList",t.COMMUNICATION_CONFIG_DETAIL="merchantCommunicationConfigDetail",t.COMMUNICATION_CONFIG_LIST="merchantCommunicationConfigList",t.COMMUNICATION_CONFIG_UPDATE="merchantCommunicationConfigUpdate",t.DOWNLOAD_CENTER_LIST="merchantDownloadCenterList",t.DOWNLOAD_CENTER_UPLOAD="merchantDownloadCenterUpload",t.OFFICIAL_MERCHANT_QUESTIONNAIRE="officialMerchantQuestionnaire",t.OFFICIAL_PAYER_SUPPORT="officialPayerSupport",t.OFFICIAL_BILLER_SUPPORT="officialBillerSupport",t.OFFICIAL_CONTACT_MESSAGE="officialContactMessage",t.OFFICIAL_CUSTOMER_FEEDBACK="officialCustomerFeedback",t.GET_DICT_DATA_CONST="merchantGetDictDataConst",t.GET_COUNTRY="merchantGetCountry",t.XERO_SUBMIT="merchantXeroSubmit",t.XERO_BREAK="merchantXeroBreak",t.XERO_INFO="merchantXeroInfo",t.XERO_REFRESH_ACCESS_TOKEN="merchantXeroRefreshAccessToken",t.XERO_ADD_CONTACT="merchantXeroAddContact",t.XERO_UPDATE_CONTACT="merchantXeroUpdateContact",t.XERO_GET_CONTACT_LIST="merchantXeroGetContactList",t.XERO_GET_CONTACT_DETAIL="merchantXeroGetContactDetail",t.XERO_ADD_ACCOUNT="merchantXeroAddAccount",t.XERO_UPDATE_ACCOUNT="merchantXeroUpdateAccount",t.XERO_GET_ACCOUNT_LIST="merchantXeroGetAccountList",t.XERO_GET_ACCOUNT_DETAIL="merchantXeroGetAccountDetail",t.XERO_DELETE_ACCOUNT="merchantXeroDeleteAccount",t.XERO_ADD_INVOICE="merchantXeroAddInvoice",t.XERO_UPDATE_INVOICE="merchantXeroUpdateInvoice",t.XERO_GET_INVOICE_LIST="merchantXeroGetInvoiceList",t.XERO_GET_INVOICE_DETAIL="merchantXeroGetInvoiceDetail",t.XERO_NOTIFY="merchantXeroNotify",t.XERO_GET_ALL_ACCOUNT_CODE="merchantXeroGetAllAccountCode",t.XERO_GET_ALL_CUSTOMER="merchantXeroGetAllCustomer",t.XERO_DELETE_INVOICE="merchantXeroDeleteInvoice",t.XERO_GET_THEME_LIST="merchantXeroGetThemeList",t.XERO_GET_INVOICE_PAYMENT_DETAIL="merchantXeroGetInvoicePaymentDetail",t.XERO_SEND_INVOICE_EMAIL="merchantXeroSendInvoiceEmail",t.XERO_INVOICE_PAYMENT_SUBMIT="merchantXeroInvoicePaymentSubmit",t.XERO_SYNC_CHANNEL_DATA="merchantXeroSyncChannelData",t.XERO_GET_INVOICE_STATUS="merchantXeroGetInvoiceStatus",t.XERO_GET_ACCOUNT_CONFIG="merchantXeroGetAccountConfig",t.XERO_UPDATE_BRANDING_THEME_CONFIG="merchantXeroUpdateBrandingThemeConfig",t.XERO_UPDATE_INVOICE_CHANNEL_CONFIG="merchantXeroUpdateInvoiceChannelConfig",t.XERO_CREATE_CUSTOMER="merchantXeroCreateCustomer",t.SUPPORT="merchantSupport",t))(O||{});function N(){const t=S(),T=e=>{const E=String(e);return t.hasPermission(E)},c=e=>{const E=e.map(_=>String(_));return t.hasAnyPermission(E)},C=e=>{const E=e.map(_=>String(_));return t.hasAllPermissions(E)},n=e=>a(()=>T(e)),r=e=>a(()=>c(e)),I=e=>a(()=>C(e)),A=a(()=>t.userPermissions),R=a(()=>t.isLoggedIn);return{hasPermission:T,hasAnyPermission:c,hasAllPermissions:C,canAccess:n,canAccessAny:r,canAccessAll:I,userPermissions:A,isLoggedIn:R,Permissions:O}}export{O as P,N as u};
