import{d as H}from"./dayjs.min-Bar1rHDB.js";import{l as x,k as ee,r as I,Q as $,f as ie}from"./index-BPwFJVSY.js";import{u as ne}from"./useListRefresh-D-tL0eHu.js";import{u as se,P as k}from"./usePermissions-PkVCxleQ.js";import{I as g}from"./customer-v2cMYA98.js";import{S as p,B as f,U as y,c,P,b as O,R as T,a as L}from"./plan-DCOwcHy2.js";import{l as te}from"./customer-M3EzHCil.js";import{a as re,b as ue}from"./integrations-C_ojvdY3.js";import{b as le,d as oe,e as de}from"./plan-X86n_BnV.js";import{u as ce}from"./dict-CLPQcIyV.js";import{s as m,p as ae,u as V,n as o,o as h,b as me,d as pe,f as _e,Z as l,g as ge}from"./types-AmnC9OGP.js";function fe(i,r){return Object.assign(r,{plan_id:i.plan_id,description:i.description,plan_name:i.plan_name,is_inclusive_gst:i.is_inclusive_gst,schedule_type:i.schedule_type,end_date:i.end_date?new Date(i.end_date):new Date,is_surcharge:i.is_surcharge,end_date_type:i.end_date_type,end_terms:i.end_terms,status:i.status,surcharge_rate:i.surcharge_rate,units:i.units,start_date:i.start_date?H(i.start_date).toDate():null,invoice_theme_id:(i==null?void 0:i.invoice_theme_id)||"",invoice_account_code:(i==null?void 0:i.invoice_account_code)||""}),i.schedule_type===p.OneOff&&(Reflect.set(r,"oneOffAmount",Number(i.prices[0].amount_per_unit)),Reflect.set(r,"oneOffCurrency",i.prices[0].currency),Reflect.set(r,"oneOffPricingModel",i.pricing_model)),i.schedule_type===p.Recurring&&(Reflect.set(r,"recurringPricingModel",i.pricing_model),Reflect.set(r,"recurringBillingPeriod",i.process_type),r.recurringBillingPeriod===f.Custom&&(Reflect.set(r,"recurringBillingPeriodCustom",i.custom_cycle),Reflect.set(r,"recurringBillingPeriodCustomUnit",i.custom_cycle_type)),Reflect.set(r,"recurringAmount",i.prices[0].amount_per_unit),Reflect.set(r,"recurringCurrency",i.prices[0].currency)),i.schedule_type===p.UnitBased&&(Reflect.set(r,"unitBasedModel",i.pricing_model),Reflect.set(r,"unitBasedBillingPeriod",i.process_type),r.unitBasedBillingPeriod===f.Custom&&(Reflect.set(r,"unitBasedBillingPeriodCustom",i.custom_cycle),Reflect.set(r,"unitBasedBillingPeriodCustomUnit",i.custom_cycle_type)),r.unitBasedModel===y.StandardPricing&&(["Unit","Hours","User"].includes(i.prices[0].unit_type)?Reflect.set(r,"unitBasedModelType",i.prices[0].unit_type):(Reflect.set(r,"unitBasedModelType",c.Custom),Reflect.set(r,"unitBasedModelTypeCustom",i.prices[0].unit_type)),Reflect.set(r,"unitBasedAmount",i.prices[0].amount_per_unit),Reflect.set(r,"unitBasedCurrency",i.prices[0].currency),Reflect.set(r,"unitBasedIncrement",Number(i.prices[0].units))),r.unitBasedModel===y.TieredPricing&&(["Unit","Hours","User"].includes(i.prices[0].unit_type)?Reflect.set(r,"unitBasedTieredModelType",i.prices[0].unit_type):(Reflect.set(r,"unitBasedTieredModelType",c.Custom),Reflect.set(r,"unitBasedTieredModelTypeCustom",i.prices[0].unit_type)),Reflect.set(r,"unitBasedTieredCurrency",i.prices[0].currency),Reflect.set(r,"unitBasedTieredPricing",i.prices.map(a=>({firstUnit:Number(a.first_unit),lastUnit:a.last_unit===null?g:a.last_unit,perUnit:Number(a.amount_per_unit),flatFee:Number(a.amount_flat_fee)}))))),r}function qe(i,r){const{user:a,activeBid:j}=x(),{hasPermission:w}=se(),{t}=ee(),{getDictByType:C,isTypeLoading:G}=ce(),{backWithRefresh:N}=ne("planSubscriptionList",()=>{}),q=I(""),R=I(!1),S=I(!1),_=I({status:[],process_type:[],currency:[],billing_period:[],billing_period_custom_unit:[],recurring_pricing_model:[{label:"Standard Pricing",value:T.StandardPricing}],unit_based_pricing_model:[{label:"Standard Pricing",value:y.StandardPricing},{label:"Tiered pricing",value:y.TieredPricing}],unit_based_model_type:[{label:"Unit",value:c.Unit},{label:"Hours",value:c.Hours},{label:"User",value:c.User},{label:"Custom",value:c.Custom}],one_off_pricing_model:[{label:"Flat",value:T.Flat}],unit_based_tiered_payment_method:[{label:"Volume",value:O.Volume},{label:"Graduated",value:O.Graduated}],endDateType:[{label:"Specify by end date",value:P.SpecifyByEndDate},{label:"Specify by term",value:P.SpecifyByTerm},{label:"Good till cancel",value:P.GoodTillCancel}],invoiceTemplate:[],invoiceChartOfAccount:[]}),s=$({plan_name:"",amount:0,schedule_type:p.Recurring,process_type:1,description:"",end_date:new Date,is_inclusive_gst:!1,is_surcharge:!1,plan_id:"",status:null,currency:null,billing_period:null,billing_period_custom:null,billing_period_custom_unit:null,pricing_model:null,end_date_type:P.SpecifyByEndDate,end_terms:1,recurringPricingModel:T.StandardPricing,recurringAmount:null,recurringCurrency:null,recurringBillingPeriod:f.Monthly,recurringBillingPeriodCustom:1,recurringBillingPeriodCustomUnit:L.Week,unitBasedModel:T.StandardPricing,unitBasedModelType:c.Unit,unitBasedModelTypeCustom:"",unitBasedTieredModelType:c.Unit,unitBasedTieredModelTypeCustom:"",unitBasedAmount:null,unitBasedCurrency:null,unitBasedIncrement:null,unitBasedBillingPeriod:f.Monthly,unitBasedBillingPeriodCustom:1,unitBasedBillingPeriodCustomUnit:L.Week,unitBasedTieredPricing:[{firstUnit:1,lastUnit:"2",perUnit:0,flatFee:0},{firstUnit:3,lastUnit:g,perUnit:0,flatFee:0}],unitBasedTieredCurrency:null,unitBasedTieredCurrencyPaymentMethod:O.Volume,oneOffAmount:null,oneOffCurrency:null,oneOffPricingModel:T.Flat,invoice_theme_id:"",invoice_account_code:""}),v=$({plan_name:"",amount:"",schedule_type:"",description:"",currency:"",process_type:"",end_date:"",is_inclusive_gst:"",billing_period:"",billing_period_custom:"",pricing_model:"",billing_period_custom_unit:"",recurringBillingPeriodCustom:"",end_terms:"",invoice_theme_id:"",invoice_account_code:""}),M=m({message:t("validation.required")}).nullable(),A=ae(e=>e===""||e===null||e===void 0?"":typeof e=="number"?e:typeof e=="string"&&!Number.isNaN(Number(e))?Number(e):e,V([m().min(1,{message:t("validation.required")}),o().min(.01,{message:t("validation.required")})])),Y=h({plan_name:m().min(1,{message:t("validation.required")}).max(100),process_type:o().min(1,{message:t("validation.required")}),end_date:pe().nullable(),is_inclusive_gst:me().optional(),description:m().max(500).optional(),schedule_type:o().min(1).max(3)}),W=h({end_date_type:o().min(1,{message:t("validation.required")}).nullable(),end_terms:o().min(1,{message:t("validation.required")}).nullable(),recurringPricingModel:o().min(1,{message:t("validation.required")}).nullable(),recurringAmount:A.nullable(),recurringCurrency:M.nullable(),recurringBillingPeriod:o().min(1,{message:t("validation.required")}).nullable(),recurringBillingPeriodCustom:o().nullable(),recurringBillingPeriodCustomUnit:o().nullable()}),Z=h({end_date_type:o().min(1,{message:t("validation.required")}).nullable(),end_terms:o().min(0,{message:t("validation.required")}).nullable(),unitBasedModel:o().min(1,{message:t("validation.required")}).nullable(),unitBasedModelType:m().min(1,{message:t("validation.required")}),unitBasedModelTypeCustom:m().optional(),unitBasedAmount:A.nullable(),unitBasedCurrency:M.nullable(),unitBasedIncrement:o().min(.1,{message:t("validation.required")}).nullable(),unitBasedBillingPeriod:o().min(1,{message:t("validation.required")}).nullable(),unitBasedBillingPeriodCustom:o().nullable(),unitBasedBillingPeriodCustomUnit:o().nullable(),unitBasedTieredModelType:m().min(1,{message:t("validation.required")}),unitBasedTieredModelTypeCustom:m().optional(),unitBasedTieredPricing:_e(h({firstUnit:o().min(1),lastUnit:V([m(),o()]),perUnit:o().min(0),flatFee:o().min(0)})).min(1,{message:t("validation.required")}),unitBasedTieredCurrency:M.nullable(),unitBasedTieredCurrencyPaymentMethod:o().min(1,{message:t("validation.required")}).nullable()}),X=h({oneOffAmount:A.nullable(),oneOffCurrency:M.nullable(),oneOffPricingModel:o().min(1,{message:t("validation.required")}).nullable()}),Q=h({invoice_theme_id:m().optional(),invoice_account_code:m().optional()}),z=Y.merge(W).merge(Z).merge(X).merge(Q).superRefine((e,n)=>{e.end_date_type===P.SpecifyByEndDate&&(e.end_date||n.addIssue({code:l.custom,message:t("validation.required"),path:["end_date"]})),e.end_date_type===P.SpecifyByTerm&&(!e.end_terms||e.end_terms===0)&&n.addIssue({code:l.custom,message:t("validation.required"),path:["end_terms"]}),e.schedule_type===p.Recurring&&(e.recurringPricingModel===T.StandardPricing&&((!e.recurringAmount||!e.recurringCurrency)&&n.addIssue({code:l.custom,message:t("validation.required"),path:["recurringAmount"]}),e.recurringBillingPeriod||n.addIssue({code:l.custom,message:t("validation.required"),path:["recurringBillingPeriod"]})),e.recurringBillingPeriod===f.Custom&&(!e.recurringBillingPeriodCustom||!e.recurringBillingPeriodCustomUnit)&&n.addIssue({code:l.custom,message:t("validation.required"),path:["recurringBillingPeriodCustom"]})),e.schedule_type===p.UnitBased&&(e.unitBasedModelType===c.Custom&&!e.unitBasedModelTypeCustom&&n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedModelTypeCustom"]}),e.unitBasedModel===y.StandardPricing?((!e.unitBasedAmount||!e.unitBasedCurrency)&&n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedAmount"]}),e.unitBasedBillingPeriod||n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedBillingPeriod"]}),e.unitBasedIncrement||n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedIncrement"]}),e.unitBasedModelType===c.Custom&&(!e.unitBasedModelTypeCustom||e.unitBasedModelTypeCustom.trim()==="")&&n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedModelTypeCustom"]})):e.unitBasedModel===y.TieredPricing&&(e.unitBasedTieredModelType===c.Custom&&!e.unitBasedTieredModelTypeCustom&&n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedTieredModelTypeCustom"]}),e.unitBasedTieredModelType===c.Custom&&(!e.unitBasedTieredModelTypeCustom||e.unitBasedTieredModelTypeCustom.trim()==="")&&n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedTieredModelTypeCustom"]}),e.unitBasedTieredCurrency||n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedTieredCurrency"]}),e.unitBasedBillingPeriod||n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedBillingPeriod"]}),e.unitBasedTieredPricing.forEach((u,d)=>{if((u.perUnit===null||u.perUnit===void 0||u.perUnit<0)&&n.addIssue({code:l.custom,message:t("validation.per_unit_invalid"),path:[`unitBasedTieredPricing.${d}.perUnit`]}),(u.flatFee===null||u.flatFee===void 0||u.flatFee<0)&&n.addIssue({code:l.custom,message:t("validation.flat_fee_invalid"),path:[`unitBasedTieredPricing.${d}.flatFee`]}),(!u.firstUnit||u.firstUnit<=0||!Number.isInteger(u.firstUnit))&&n.addIssue({code:l.custom,message:t("validation.first_unit_invalid"),path:[`unitBasedTieredPricing.${d}.firstUnit`]}),u.lastUnit!==g&&(typeof u.lastUnit=="string"?Number(u.lastUnit):u.lastUnit)<u.firstUnit&&n.addIssue({code:l.custom,message:t("validation.last_unit_invalid"),path:[`unitBasedTieredPricing.${d}.lastUnit`]}),d>0){const B=e.unitBasedTieredPricing[d-1],U=typeof B.lastUnit=="string"?B.lastUnit===g?1/0:Number(B.lastUnit):B.lastUnit;u.firstUnit<=U&&n.addIssue({code:l.custom,message:t("validation.tiered_pricing_overlap"),path:[`unitBasedTieredPricing.${d}.firstUnit`]}),U!==1/0&&u.firstUnit>U+1&&n.addIssue({code:l.custom,message:t("validation.tiered_pricing_gap"),path:[`unitBasedTieredPricing.${d}.firstUnit`]})}else u.firstUnit!==1&&n.addIssue({code:l.custom,message:t("validation.first_tier_must_start_with_one"),path:[`unitBasedTieredPricing.${d}.firstUnit`]})})),e.unitBasedBillingPeriod===f.Custom&&(!e.unitBasedBillingPeriodCustom||!e.unitBasedBillingPeriodCustomUnit)&&n.addIssue({code:l.custom,message:t("validation.required"),path:["unitBasedBillingPeriodCustom"]})),a!=null&&a.xero_link&&(e.invoice_account_code&&!e.invoice_theme_id&&n.addIssue({code:l.custom,message:t("validation.required"),path:["invoice_theme_id"]}),e.invoice_theme_id&&!e.invoice_account_code&&n.addIssue({code:l.custom,message:t("validation.required"),path:["invoice_account_code"]}))}),F=async e=>{if(i==="edit"&&!r||i==="view"&&!e||i==="customerView"&&!e)return;const n=i==="customerView"?te:le;R.value=!0;try{const{code:u,data:d}=await n(r||e,q.value);u===0&&d&&fe(d,s)}finally{R.value=!1}},E=async()=>{try{Object.keys(v).forEach(n=>{v[n]=""});const e={...s,amount:s.amount===""||s.amount===null||s.amount===void 0?"":s.amount};return await z.parseAsync(e),!0}catch(e){return console.log("error, ",e),e instanceof ge&&e.errors.forEach(n=>{if(n.path.length>0){const u=n.path[0];if(u.includes(".")){const d=u.split("."),B=d[0];if(B==="unitBasedTieredPricing"){const U=d.length>=2?Number.parseInt(d[1])+1:"";v.unitBasedTieredPricing=`${t("planSubscription.tieredPricing.tier")} ${U}: ${n.message}`}else v[B]=n.message}else v[u]=n.message}}),!1}},b=e=>typeof e=="number"&&!Number.isNaN(e)?String(e):e||"0",J=async e=>{if(!(i==="add"&&!w(k.PLAN_CREATE))&&!(i==="edit"&&!w(k.PLAN_UPDATE))&&(e&&(e==null||e.preventDefault()),await E())){S.value=!0;try{const n={plan_name:s.plan_name,description:s.description,schedule_type:s.schedule_type,is_inclusive_gst:s.is_inclusive_gst,is_surcharge:s.is_surcharge,end_date:H(s.end_date).format("YYYY-MM-DD HH:mm:ss"),end_date_type:s.end_date_type,end_terms:s.end_terms,prices:[],invoice_theme_id:s.invoice_theme_id,invoice_account_code:s.invoice_account_code};if(s.schedule_type===p.OneOff)n.pricing_model=s.oneOffPricingModel,n.prices.push({currency:s.oneOffCurrency,amount_per_unit:b(s.oneOffAmount),amount_flat_fee:null,first_unit:1,last_unit:g,unit_type:"",units:1});else if(s.schedule_type===p.Recurring)n.pricing_model=s.recurringPricingModel,n.process_type=s.recurringBillingPeriod,s.recurringBillingPeriod===f.Custom&&(n.custom_cycle_type=s.recurringBillingPeriodCustomUnit,n.custom_cycle=s.recurringBillingPeriodCustom),n.prices.push({currency:s.recurringCurrency,amount_per_unit:b(s.recurringAmount),amount_flat_fee:null,first_unit:1,last_unit:g,unit_type:"",units:1});else if(s.schedule_type===p.UnitBased)switch(n.pricing_model=s.unitBasedModel,n.process_type=s.unitBasedBillingPeriod,s.unitBasedBillingPeriod===f.Custom&&(n.custom_cycle_type=s.unitBasedBillingPeriodCustomUnit,n.custom_cycle=s.unitBasedBillingPeriodCustom),s.unitBasedModel){case y.StandardPricing:n.prices.push({currency:s.unitBasedCurrency,amount_per_unit:b(s.unitBasedAmount),amount_flat_fee:null,first_unit:1,last_unit:g,unit_type:s.unitBasedModelType===c.Custom?s.unitBasedModelTypeCustom:s.unitBasedModelType,units:s.unitBasedIncrement});break;case y.TieredPricing:{n.tiered_type=s.unitBasedTieredCurrencyPaymentMethod,n.prices=s.unitBasedTieredPricing.map(u=>({currency:s.unitBasedTieredCurrency,amount_per_unit:b(u.perUnit),amount_flat_fee:b(u.flatFee),first_unit:u.firstUnit,last_unit:u.lastUnit,unit_type:s.unitBasedTieredModelType===c.Custom?s.unitBasedTieredModelTypeCustom:s.unitBasedTieredModelType,units:1}));break}}if(i==="view"||i==="customerView")return n;if(i==="add"){const{code:u}=await oe(n,{headers:{"Business-Id":j}});u===0&&(window.$toast.add({severity:"success",summary:"Successful",detail:"Plan created successfully"}),N())}else{const{code:u}=await de({...n,status:s.status,plan_id:r});u===0&&(window.$toast.add({severity:"success",summary:"Successful",detail:"Plan updated successfully"}),N())}}catch(n){console.error(`Error ${i==="add"?"creating":"updating"} plan:`,n),window.$toast.add({severity:"error",summary:"Error",detail:`An error occurred while ${i==="add"?"creating":"updating"} the plan`,life:3e3})}finally{S.value=!1}}},K=e=>{F(e)},D=e=>{q.value=e};return ie(()=>{Promise.all([C("currency").then(e=>{_.value.currency=e,s.recurringCurrency=e[0].value,s.unitBasedCurrency=e[0].value,s.oneOffCurrency=e[0].value,s.unitBasedTieredCurrency=e[0].value}),C("plan_process_type").then(e=>{_.value.billing_period=e}),C("plan_tiered_type").then(e=>{_.value.process_type=e}),C("plan_custom_cycle_type").then(e=>{_.value.billing_period_custom_unit=e})]),i==="edit"&&r&&(F(),C("plan_status").then(e=>{_.value.status=e})),a!=null&&a.xero_link&&(re().then(e=>{_.value.invoiceChartOfAccounts=e.data.map(n=>({label:n.name,value:n.account_code}))}),ue().then(e=>{_.value.invoiceTemplate=e.data.map(n=>({label:n.name||"",value:n.theme_id||""}))}))}),{formData:s,formErrors:v,loading:R,options:_,optionsLoading:G,submitting:S,validateForm:E,submitForm:J,INFINITE:g,setPlanId:K,setCustomerId:D,customerId:q,mode:i}}export{fe as a,qe as u};
