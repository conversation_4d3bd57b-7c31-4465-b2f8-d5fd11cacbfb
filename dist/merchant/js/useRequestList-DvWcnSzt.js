import{d}from"./dayjs.min-Bar1rHDB.js";import{r as n,Q as x,f as C}from"./index-BPwFJVSY.js";function Q(D,Y){let c=null;return function(...h){c&&window.clearTimeout(c),c=window.setTimeout(()=>{D.apply(this,h)},Y)}}function E(D){const{requestFn:Y,defaultParams:c={},immediate:h=!0,page_size:j=50,debounce:_=!0,debounceTime:q=200}=D,f=n([]),y=n(!1),p=n(null),m=n(!1),v=n(""),g=n(0),b=n(null),l=n(1),o=x({...c}),w=t=>{const s={};for(const e in t)(Array.isArray(t[e])&&t[e].length>0||!Array.isArray(t[e])&&String(t[e]).length>0)&&(s[e]=t[e]);return s},i=async t=>{try{y.value=!0,p.value=null,m.value=!1,v.value="";const s={page_size:j,page:l.value,...w(o),...t},e=await Y(s);if(e.code!==0){m.value=!0,v.value=e.message||"Failed",f.value=[],g.value=0;return}const{data:a=[],total:r=0,...u}=e.data;f.value=a,g.value=r,b.value=u}catch(s){p.value=s,m.value=!0,v.value=s.message||"搜索出错，请稍后重试",f.value=[],g.value=0}finally{y.value=!1}},F=_?Q(i,q):i,A=t=>{Object.assign(o,t),l.value=1},L=(t,s=[])=>{const e={...t};for(const a in e){if((a==="created_at[]"||a==="updated_at[]")&&Array.isArray(e[a])&&e[a].length===2){const r=e[a],u=d(r[0]).format("YYYY-MM-DD"),P=r!=null&&r[1]?d(r[1]).format("YYYY-MM-DD"):u;e[a]=[u,P]}if(s.includes(a)&&Array.isArray(e[a])&&e[a].length===2){const r=e[a],u=d(r[0]).format("YYYY-MM-DD"),P=r!=null&&r[1]?d(r[1]).format("YYYY-MM-DD"):u;e[a]=[u,P]}}return Object.assign(o,e),o},M=()=>(l.value=1,F()),O=t=>(A(t),M()),S=()=>i(),T=()=>(l.value=1,Object.assign(o,c),i()),k=t=>(l.value=t.page+1,i({page_size:t.rows})),z=()=>w(o);return C(()=>{h&&M()}),{list:f,loading:y,error:p,failed:m,failureMessage:v,total:g,currentPage:l,queryParams:o,other:b,refresh:S,reset:T,setParams:A,search:M,updateParams:O,onPageChange:k,getSearchParams:z,setSearchParams:L}}export{E as u};
