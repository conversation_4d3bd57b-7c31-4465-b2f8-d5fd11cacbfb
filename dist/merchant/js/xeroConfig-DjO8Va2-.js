import{d as N,r,a as h,o as u,b as e,e as t,A as V,p as _,q as f,w as s,t as j,j as B,s as T,by as L}from"./index-BPwFJVSY.js";import{_ as z}from"./BaseFileUpload-D-FKhw5d.js";import{s as D}from"./index-Cbodd6BQ.js";import{s as F}from"./index-DMmJJ4dH.js";import{a as I,s as Y}from"./index-CxOnO8if.js";import{s as q}from"./index-CCP8zFp2.js";import{f as E,h as M,i as P}from"./integrations-C_ojvdY3.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./downloadCenter-xnP8j9T6.js";import"./index-BlFKIn6Y.js";import"./index-SkqdGoZA.js";import"./index-DgYXzrxo.js";import"./index-wTWY-tN1.js";import"./index-CEksU3zE.js";import"./index-BjQt0t51.js";import"./index-kWy1s9tq.js";import"./index-DgBXOSrM.js";import"./index-BJi8tw4J.js";import"./index-B8Vin95C.js";import"./index-D7FsVJ-c.js";const H={class:"xero-config"},J={class:"steps-container"},K={class:"step-circle"},O={key:0,class:"pi pi-check"},Q={key:1},R={class:"content-area"},W={class:"template-name"},Z={class:"checkbox-wrapper"},ee={class:"checkbox-wrapper"},oe={class:"checkbox-wrapper"},ae={class:"checkbox-wrapper"},le={class:"form-grid"},te={class:"form-section"},se={class:"field"},ne={class:"form-section"},ie={class:"logo-upload-area"},ce={class:"current-logo"},ue={class:"logo-preview"},de={class:"actions-bar"},re={class:"actions-right"},pe=N({__name:"xeroConfig",setup(me){const b=r([]),k=r([]),m=r(null),v=r(null),p=r(!1),l=r(1);function x(){l.value===1&&(l.value=2)}function C(){l.value===2&&(l.value=1)}const y=async()=>{const{data:n}=await E();b.value=n.branding_themes||[],k.value=n.bank_accounts||[],m.value=n.bank_account_id||null,v.value=n.logo?[{url:n.logo}]:null};async function S(){var n,o;if(l.value===1)try{p.value=!0;const{code:i}=await M({update_list:b.value});i===0&&(window.$toast.add({severity:"success",summary:"Saved successfully",detail:"Your configuration has been saved."}),y())}finally{p.value=!1}else if(l.value===2){p.value=!0;const i=((o=(n=v.value)==null?void 0:n[0])==null?void 0:o.url)||"",{code:d}=await P({bank_account_id:m.value,logo:i,file_name:"xero"});d===0&&(window.$toast.add({severity:"success",summary:"Saved successfully",detail:"Your configuration has been saved."}),y()),p.value=!1}}return y(),(n,o)=>{const i=Y,d=q,U=I,w=F,A=D,$=z,g=T,X=L;return u(),h("div",H,[e("div",J,[e("div",{class:V(["step-item",{active:l.value===1,completed:l.value>1}])},[e("div",K,[l.value>1?(u(),h("i",O)):(u(),h("span",Q,"1"))]),o[2]||(o[2]=e("div",{class:"step-content"},[e("h3",null,"Configure BillBuddy Features"),e("p",null,"Set up invoice templates and payment options")],-1))],2),o[4]||(o[4]=e("div",{class:"step-divider"},null,-1)),e("div",{class:V(["step-item",{active:l.value===2}])},o[3]||(o[3]=[e("div",{class:"step-circle"},[e("span",null,"2")],-1),e("div",{class:"step-content"},[e("h3",null,"Bank Account & Branding"),e("p",null,"Configure reconciliation and upload your logo")],-1)]),2)]),e("div",R,[l.value===1?(u(),_(w,{key:0,class:"step-card"},{header:s(()=>o[5]||(o[5]=[e("div",{class:"card-header"},[e("div",{class:"header-icon"},[e("i",{class:"pi pi-file-edit"})]),e("div",null,[e("h2",null,"Apply BillBuddy to your invoice templates"),e("p",null," Select the invoice templates you would like BillBuddy to accept payments from. BillBuddy can be used for one-off invoice payments as well as variable recurring payments. ")])],-1)])),content:s(()=>[t(U,{value:b.value,class:"templates-table",paginator:!1,"striped-rows":""},{default:s(()=>[t(i,{field:"name",header:"Invoice Template",class:"template-name-col"},{body:s(({data:a})=>[e("div",W,j(a.name),1)]),_:1}),t(i,{header:"Accept payments from BillBuddy?",class:"checkbox-col"},{body:s(({data:a})=>[e("div",Z,[t(d,{modelValue:a.accept_pinch_pay,"onUpdate:modelValue":c=>a.accept_pinch_pay=c,"true-value":1,"false-value":0,binary:""},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),t(i,{header:"Allow customer opt-in for auto debit?",class:"checkbox-col"},{body:s(({data:a})=>[e("div",ee,[t(d,{modelValue:a.allow_auto_debit,"onUpdate:modelValue":c=>a.allow_auto_debit=c,"true-value":1,"false-value":0,binary:""},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),t(i,{header:"Monitor for auto debit?",class:"checkbox-col"},{body:s(({data:a})=>[e("div",oe,[t(d,{modelValue:a.monitor_debit,"onUpdate:modelValue":c=>a.monitor_debit=c,"true-value":1,"false-value":0,binary:""},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),t(i,{header:"Send invoice notifications?",class:"checkbox-col"},{body:s(({data:a})=>[e("div",ae,[t(d,{modelValue:a.send_invoice_notify,"onUpdate:modelValue":c=>a.send_invoice_notify=c,"true-value":1,"false-value":0,binary:""},null,8,["modelValue","onUpdate:modelValue"])])]),_:1})]),_:1},8,["value"])]),_:1})):f("",!0),l.value===2?(u(),_(w,{key:1,class:"step-card"},{header:s(()=>o[6]||(o[6]=[e("div",{class:"card-header"},[e("div",{class:"header-icon"},[e("i",{class:"pi pi-building"})]),e("div",null,[e("h2",null,"Xero Bank Account and Logo"),e("p",null,"Configure which bank account to reconcile with and upload your company logo for payment pages.")])],-1)])),content:s(()=>[e("div",le,[e("div",te,[o[9]||(o[9]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-university mr-2"}),B(" Bank Account Selection ")],-1)),e("div",se,[o[7]||(o[7]=e("label",{class:"field-label"},"Xero Bank Account",-1)),t(A,{modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=a=>m.value=a),options:k.value,"option-label":"name","option-value":"id",placeholder:"Select a bank account",class:"w-full"},null,8,["modelValue","options"]),o[8]||(o[8]=e("small",{class:"field-hint"},"Now we just need to know which bank account in Xero we should update when you receive payments.",-1))])]),e("div",ne,[o[10]||(o[10]=e("h3",{class:"section-title"},[e("i",{class:"pi pi-image mr-2"}),B(" Company Logo ")],-1)),e("div",ie,[e("div",ce,[e("div",ue,[t($,{modelValue:v.value,"onUpdate:modelValue":o[1]||(o[1]=a=>v.value=a),multiple:!1,"auto-upload":!0,accept:"image/jpeg,image/png,.jpg,.jpeg,.png","max-size":1024*1024*10,"max-files":1,class:"avatar-uploader"},null,8,["modelValue"])])])])])])]),_:1})):f("",!0)]),e("div",de,[e("div",re,[l.value>1?(u(),_(g,{key:0,label:"Previous",icon:"pi pi-arrow-left",class:"p-button-outlined p-button-secondary",onClick:C})):f("",!0),l.value<2?(u(),_(g,{key:1,label:"Next",icon:"pi pi-arrow-right","icon-pos":"right",class:"p-button-primary",onClick:x})):f("",!0),t(g,{label:"Save Configuration",icon:"pi pi-check",class:"p-button-success",loading:p.value,onClick:S},null,8,["loading"])])]),t(X)])}}}),Te=G(pe,[["__scopeId","data-v-8b5a8c75"]]);export{Te as default};
