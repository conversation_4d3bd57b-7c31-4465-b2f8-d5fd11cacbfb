import{d as $,r as j,a as l,o as a,A as y,e as o,b as e,u as n,y as _,aJ as k,w as d,j as r,t as g,s as I,z as T,X as M}from"./index-BPwFJVSY.js";import{_ as N}from"./BaseTag-dd68UHCy.js";import{s as F}from"./index-CBr4Z_s8.js";import{s as v}from"./index-CQ6UwYfx.js";import{S as u}from"./invoice-B6mg1HKE.js";import{f as P}from"./date-D3KeWVvs.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DDQWKtix.js";import"./dayjs.min-Bar1rHDB.js";const C="/svg/connect-white-inY4r0oW.svg",W="/svg/disconnect-white-Duypgkad.svg",A={key:0,class:"flex items-center"},Y={class:"flex flex-col space-y-2 w-2/3"},q={class:"flex-1 flex items-center w-2/3"},E={key:0,class:"flex items-center"},J={class:"flex-1 flex items-center w-2/3"},Q={key:1,class:"connected__wrapper"},U={class:"connected-header"},G={class:"connected-title__wrapper"},H={class:"connected-sub-title"},K={class:"connected-content mt-4"},O={class:"flex items-center gap-4 mb-4"},Z={class:"flex items-center gap-4 mb-4"},ee={class:"flex border-b pb-4 border-gray-200 justify-between items-center gap-4 mb-4"},se={class:"flex flex-col w-full gap-4 mb-4 mt-4"},te={class:"flex flex-col gap-4 mb-4 bg-(--color-white-100) p-6 rounded-lg",style:{"font-size":"15px"}},oe={class:"flex items-center gap-4 mb-4 justify-center"},ne={class:"flex flex-col p-6 more-content"},ie={class:"bg-white rounded-lg py-6"},le={class:"flex justify-between items-center mb-4"},ae={class:"flex items-center"},re={class:"text-right mt-4"},ce=$({__name:"xreo",props:{loading:{type:Boolean,default:!1},syncLoading:{type:Boolean,default:!1},refreshLoading:{type:Boolean,default:!1},info:{default:()=>({email:"",file_name:"",id:0,merchant_id:"",status:0,sync_records:[],business_id:""})}},emits:["disconnect","sync","refresh","connect"],setup(S,{emit:X}){const i=S,c=X,w=j(!1),B=()=>{window.$confirm.require({header:"Disconnect",message:"Are you sure you want to disconnect?",accept:()=>{c("disconnect")}})},h=()=>{window.open("https://apps.xero.com/au/function/payments","_blank")},D=()=>{c("sync")},L=()=>{c("refresh")},z=()=>{c("connect")};return(p,s)=>{var x;const f=F,b=N,m=I,R=M;return a(),l("div",{class:y({"bg-white not-connected__wrapper":i.info.status===0})},[i.loading?(a(),l("div",A,[e("div",Y,[o(n(v),{width:"80%",height:"24px",class:"mb-2"}),o(n(v),{width:"60%",height:"16px",class:"mb-2"})]),e("div",q,[o(n(v),{width:"100%",height:"43px"})])])):(a(),l(_,{key:1},[i.info.status===0?(a(),l("div",E,[e("div",{class:"flex flex-col space-y-2 w-2/3"},[s[2]||(s[2]=e("div",{class:"title"}," Xero ",-1)),e("div",{class:"sub-title",onClick:h}," More information ")]),e("div",J,[o(f,{class:"w-full cursor-pointer",src:n(C),alt:"Xero Logo",onClick:k(z,["stop"])},null,8,["src"])])])):(a(),l("div",Q,[e("div",U,[e("div",G,[s[4]||(s[4]=e("div",{class:"connected-title"}," Synchronizing with Xero ",-1)),e("div",H,[o(b,{type:"paid",class:"!font-bold w-30"},{default:d(()=>s[3]||(s[3]=[r(" Queued ")])),_:1,__:[3]})])]),s[5]||(s[5]=e("p",{class:"connected-description"}," We're getting BillBuddy up to date with your Xero account. This process will run in the background and may take some time depending on the size of your account. ",-1)),s[6]||(s[6]=e("p",null," You will be notified when we've completed your integration. ",-1))]),e("div",K,[s[11]||(s[11]=e("h2",{class:"mb-4 text-2xl",style:{"font-weight":"800"}}," Integration Status ",-1)),s[12]||(s[12]=e("div",{class:"flex items-center gap-4 mb-4"},[e("div",{style:{"font-weight":"600"}}," Connected to: "),e("div",null," Xero ")],-1)),e("div",O,[s[7]||(s[7]=e("div",{style:{"font-weight":"600"}}," Status: ",-1)),e("div",null,[o(b,{type:"paid",class:"!font-bold w-30",severity:((x=i.info)==null?void 0:x.status)===1?"paid":"upcoming"},{default:d(()=>{var t;return[r(g(((t=i.info)==null?void 0:t.status)===1?"Connected":"Disconnected"),1)]}),_:1},8,["severity"])])]),e("div",Z,[s[8]||(s[8]=e("div",{style:{"font-weight":"600"}}," Connected by: ",-1)),e("div",null,g(i.info.email),1)]),e("div",ee,[o(m,{severity:"warn",class:"refresh-btn",label:"Refresh Status",loading:p.refreshLoading,onClick:L},null,8,["loading"]),o(f,{class:"cursor-pointer",src:n(W),alt:"Xero Logo",style:{width:"180px"},onClick:k(B,["stop"])},null,8,["src"])]),s[13]||(s[13]=e("div",{class:"text-xl",style:{"font-weight":"800"}}," Recent Daily Syncs ",-1)),e("div",se,[(a(!0),l(_,null,T(i.info.sync_records,t=>(a(),l("div",{key:t.id,class:"flex items-center"},[e("div",{class:y(["flex mr-4 items-center justify-center w-6 h-6 rounded-full bg-gray-200",t.status===n(u).Fail?"bg-red-500":t.status===n(u).Pending?"bg-yellow-500":"bg-green-500"])},[e("i",{class:y(["pi",t.status===n(u).Fail?"pi-times":t.status===n(u).Pending?"pi-clock":"pi-check"])},null,2)],2),e("div",null,g(n(P)(t.created_at)),1)]))),128))]),e("div",te,[e("strong",null,[s[9]||(s[9]=r("Do not press this button if you are experiencing Payment or Settlement sync issues to your Xero file. ")),e("span",{class:"contact-support cursor-pointer",onClick:s[0]||(s[0]=t=>p.$router.push("/support"))},"Contact Support"),s[10]||(s[10]=r(" instead. "))]),e("div",oe,[o(m,{class:"sync-now-btn",label:"Sync Now",loading:p.syncLoading,onClick:D},null,8,["loading"])])])])]))],64)),o(R,{visible:w.value,"onUpdate:visible":s[1]||(s[1]=t=>w.value=t),modal:!0,closable:!1,draggable:!1,resizable:!1,"dismissable-mask":!0,"close-on-escape":!0,"close-on-backdrop":!0,"show-header":!1,"show-footer":!1,style:{width:"578px"}},{default:d(()=>[e("div",ne,[e("div",ie,[e("div",le,[s[14]||(s[14]=e("div",{class:"flex items-center"},[e("div",{class:"font-bold text-xl"}," Connect to Xero ")],-1)),e("div",ae,[o(f,{class:"cursor-pointer",src:n(C),alt:"Xero Logo",style:{width:"180px",height:"43px"}},null,8,["src"])])]),s[15]||(s[15]=e("hr",{class:"my-4"},null,-1)),s[16]||(s[16]=e("div",{class:"text-base mb-4",style:{"font-size":"15px"}}," When you connect to Xero, the data flows between the tool – so changes you make in the app are reflected in Xero and vice-versa. This connection means you only need to complete tasks once, in one place, so it's easier to see the exact state of your finances. ",-1)),s[17]||(s[17]=e("ul",{class:"list-disc pl-6 space-y-2"},[e("li",null,"Speed up processes with apps that automate and simplify everything – from cash flow to CRM"),e("li",null,"Integrate with your Xero account to get a more complete view of your business"),e("li",null,"Reduce manual data entry: your numbers are accurate and you save time")],-1))]),e("div",re,[o(m,{class:"close-btn underline",text:"",onClick:h},{default:d(()=>s[18]||(s[18]=[r(" Close ")])),_:1,__:[18]})])])]),_:1},8,["visible"])],2)}}}),he=V(ce,[["__scopeId","data-v-9823885a"]]);export{he as default};
