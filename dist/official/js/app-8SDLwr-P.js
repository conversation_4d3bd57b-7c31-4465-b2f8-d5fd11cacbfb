import{ag as I,f as o,aD as c,z as u}from"./index-BNQN8zN8.js";const A=I("app",()=>{const a=o(!1),t=o(localStorage.getItem("locale")||"en"),r=o([]),s=o(localStorage.getItem("theme")||"Lara"),i=o(!0),l=o(!1),n=o("merchant"),v=e=>{a.value=e},g=e=>{t.value=e,localStorage.setItem("locale",e),c.global.locale.value=e},m=e=>{r.value=e},p=e=>{s.value=e,localStorage.setItem("theme",e)},f=()=>{l.value=!l.value},S=()=>{l.value=!1},b=e=>{i.value=e},h=e=>{n.value!==e&&(n.value=e)};c.global.locale.value!==t.value&&(c.global.locale.value=t.value);const C=u(()=>t.value),M=u(()=>a.value),d=u(()=>s.value);return{loading:a,locale:t,theme:s,isMobileMenuVisible:l,isShowCookiesPop:i,currentClient:n,setLoading:v,setLocale:g,setTheme:p,toggleMobileMenu:f,closeMobileMenu:S,setIsShowCookiesPop:b,setCurrentClient:h,currentLocale:C,isLoading:M,currentTheme:d,keepAliveComponents:r,setKeepAlive:m}},{persist:!0});export{A as u};
