import{e as c,aR as u,aS as f,b as t,o as n,x as o,g as e,h as m,T as h,w as v,t as _,aA as g,r as p}from"./index-BNQN8zN8.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const O={class:"panel-header-left"},k={key:"minus",class:"pi pi-minus panel-icon"},B={key:"plus",class:"pi pi-plus panel-icon"},C={class:"panel-title"},x={class:"panel-content-inner"},M=c({__name:"collapsePanel",props:u({title:{type:String,required:!0},isLastItem:{type:Boolean,default:!1}},{isOpen:{type:Boolean,required:!1,default:!1,type:Boolean},isOpenModifiers:{}}),emits:["update:isOpen"],setup(a){const d=a,s=f(a,"isOpen"),r=()=>{s.value=!s.value};return(i,l)=>(n(),t("div",{class:o(["panel",{"last-item":a.isLastItem}])},[e("div",{class:"panel-header",onClick:r},[e("div",O,[m(h,{name:"icon-fade",mode:"out-in"},{default:v(()=>[s.value?(n(),t("i",k)):(n(),t("i",B))]),_:1}),e("span",C,_(d.title),1)]),e("div",{class:"panel-header-right",onClick:l[0]||(l[0]=g(()=>{},["stop"]))},[p(i.$slots,"header-right",{isOpen:s.value},void 0,!0)])]),e("div",{class:o(["panel-content",{"panel-content-expanded":s.value}])},[e("div",x,[p(i.$slots,"default",{},void 0,!0)])],2)],2))}}),I=y(M,[["__scopeId","data-v-f6c0eb8f"]]);export{I as default};
