import{c as L,B,s as T,a as O,b as g,o as b,d as x,m as I,r as R,e as w,f as S,g as t,h as l,i as N,w as p,j as d,k as z,l as A,n as D,p as P,t as U,u as k,q as Y,v as M,x as v,T as j,y as V}from"./index-BNQN8zN8.js";import{_ as K,a as q,b as F,c as W,d as G}from"./LinkedIn Footer Icon-CGo-XYzb.js";import{s as H}from"./index-D1zV2t7Y.js";import Q from"./collapsePanel-BDV6PFWW.js";import{_ as h}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{u as J}from"./app-8SDLwr-P.js";var X=L`
    .p-divider-horizontal {
        display: flex;
        width: 100%;
        position: relative;
        align-items: center;
        margin: dt('divider.horizontal.margin');
        padding: dt('divider.horizontal.padding');
    }

    .p-divider-horizontal:before {
        position: absolute;
        display: block;
        inset-block-start: 50%;
        inset-inline-start: 0;
        width: 100%;
        content: '';
        border-block-start: 1px solid dt('divider.border.color');
    }

    .p-divider-horizontal .p-divider-content {
        padding: dt('divider.horizontal.content.padding');
    }

    .p-divider-vertical {
        min-height: 100%;
        display: flex;
        position: relative;
        justify-content: center;
        margin: dt('divider.vertical.margin');
        padding: dt('divider.vertical.padding');
    }

    .p-divider-vertical:before {
        position: absolute;
        display: block;
        inset-block-start: 0;
        inset-inline-start: 50%;
        height: 100%;
        content: '';
        border-inline-start: 1px solid dt('divider.border.color');
    }

    .p-divider.p-divider-vertical .p-divider-content {
        padding: dt('divider.vertical.content.padding');
    }

    .p-divider-content {
        z-index: 1;
        background: dt('divider.content.background');
        color: dt('divider.content.color');
    }

    .p-divider-solid.p-divider-horizontal:before {
        border-block-start-style: solid;
    }

    .p-divider-solid.p-divider-vertical:before {
        border-inline-start-style: solid;
    }

    .p-divider-dashed.p-divider-horizontal:before {
        border-block-start-style: dashed;
    }

    .p-divider-dashed.p-divider-vertical:before {
        border-inline-start-style: dashed;
    }

    .p-divider-dotted.p-divider-horizontal:before {
        border-block-start-style: dotted;
    }

    .p-divider-dotted.p-divider-vertical:before {
        border-inline-start-style: dotted;
    }

    .p-divider-left:dir(rtl),
    .p-divider-right:dir(rtl) {
        flex-direction: row-reverse;
    }
`,Z={root:function(n){var e=n.props;return{justifyContent:e.layout==="horizontal"?e.align==="center"||e.align===null?"center":e.align==="left"?"flex-start":e.align==="right"?"flex-end":null:null,alignItems:e.layout==="vertical"?e.align==="center"||e.align===null?"center":e.align==="top"?"flex-start":e.align==="bottom"?"flex-end":null:null}}},tt={root:function(n){var e=n.props;return["p-divider p-component","p-divider-"+e.layout,"p-divider-"+e.type,{"p-divider-left":e.layout==="horizontal"&&(!e.align||e.align==="left")},{"p-divider-center":e.layout==="horizontal"&&e.align==="center"},{"p-divider-right":e.layout==="horizontal"&&e.align==="right"},{"p-divider-top":e.layout==="vertical"&&e.align==="top"},{"p-divider-center":e.layout==="vertical"&&(!e.align||e.align==="center")},{"p-divider-bottom":e.layout==="vertical"&&e.align==="bottom"}]},content:"p-divider-content"},ot=B.extend({name:"divider",style:X,classes:tt,inlineStyles:Z}),et={name:"BaseDivider",extends:T,props:{align:{type:String,default:null},layout:{type:String,default:"horizontal"},type:{type:String,default:"solid"}},style:ot,provide:function(){return{$pcDivider:this,$parentInstance:this}}};function _(s){"@babel/helpers - typeof";return _=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},_(s)}function $(s,n,e){return(n=it(n))in s?Object.defineProperty(s,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):s[n]=e,s}function it(s){var n=st(s,"string");return _(n)=="symbol"?n:n+""}function st(s,n){if(_(s)!="object"||!s)return s;var e=s[Symbol.toPrimitive];if(e!==void 0){var a=e.call(s,n);if(_(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(s)}var E={name:"Divider",extends:et,inheritAttrs:!1,computed:{dataP:function(){return O($($($({},this.align,this.align),this.layout,this.layout),this.type,this.type))}}},nt=["aria-orientation","data-p"],rt=["data-p"];function lt(s,n,e,a,i,u){return b(),g("div",I({class:s.cx("root"),style:s.sx("root"),role:"separator","aria-orientation":s.layout,"data-p":u.dataP},s.ptmi("root")),[s.$slots.default?(b(),g("div",I({key:0,class:s.cx("content"),"data-p":u.dataP},s.ptm("content")),[R(s.$slots,"default")],16,rt)):x("",!0)],16,nt)}E.render=lt;const at={class:"cookies-pop w-xl"},dt={class:"flex justify-between items-center mt-4"},pt={class:"settings"},ut={class:"w-[700px] p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl"},ct={class:"header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4"},vt=["onClick"],ft={class:"choice"},mt={class:"bg-[#f5f5ff] border border-[#fe4c1c]"},bt={class:"flex justify-between items-center mt-8"},yt={class:""},gt=w({__name:"index",emits:["accept"],setup(s,{emit:n}){const e=n,a=S(!1),i=()=>{e("accept")};return(u,r)=>{const y=H,c=N;return b(),g("div",null,[t("div",at,[r[4]||(r[4]=t("p",null," By clicking “Accept All Cookies”, you agree to the storing of cookies on your device to enhance site navigation, analyze site usage, and assist in our marketing efforts. ",-1)),t("div",dt,[t("div",null,[t("button",{class:"btn cursor-pointer btn-setting",onClick:r[0]||(r[0]=o=>a.value=!0)}," COOKIE SETTINGS ")]),t("div",null,[t("button",{class:"btn btn-operation cursor-pointer",onClick:i}," REJECT ALL "),t("button",{class:"btn btn-operation cursor-pointer",onClick:i}," ACCEPT ALL COOKIES ")])])]),t("div",pt,[l(c,{visible:a.value,"onUpdate:visible":r[3]||(r[3]=o=>a.value=o),modal:!0,style:{border:"none"},"pt:mask:class":"backdrop-blur-sm"},{container:p(({closeCallback:o})=>[t("div",ut,[t("div",ct,[r[5]||(r[5]=t("div",{class:"font-semibold text-[2rem]"}," Privacy Preference Center ",-1)),t("i",{class:"pi pi-times",style:{color:"#fe4c1c","font-weight":"700","font-size":"1.5rem"},onClick:o},null,8,vt)]),l(y,{style:{width:"100%",height:"600px"}},{default:p(()=>[r[9]||(r[9]=t("div",{class:"description mt-4 mb-8"},[t("p",{class:"leading-8"}," Websites store information on your browser, mainly through cookies, to enhance your experience. While this data doesn’t usually identify you, it personalizes your web experience. You can manage your cookie preferences, but disabling some may affect site functionality. "),t("div",{class:"mt-8 italic underline cursor-pointer"}," More information ")],-1)),t("div",ft,[r[7]||(r[7]=t("div",{class:"choice-title mb-6 font-bold text-[28px]"}," Customize Your Choice ",-1)),t("div",mt,[l(Q,{title:"Strictly Necessary Cookies"},{"header-right":p(()=>r[6]||(r[6]=[t("span",{class:"text-[#fe4c1c] font-semibold"},"Always Active",-1)])),_:1})])]),t("div",bt,[r[8]||(r[8]=t("div",{class:"flex flex-col leading-6"},[t("div",{class:"italic underline"},[d(" Cookies Notice"),t("i",{class:"pi pi-arrow-up-right"})]),t("div",{class:"italic underline"},[d(" Cookies List"),t("i",{class:"pi pi-arrow-up-right"})])],-1)),t("div",yt,[t("button",{class:"btn btn-secondary mt-2 cursor-pointer",onClick:r[1]||(r[1]=()=>{a.value=!1,i()})}," REJECT ALL "),t("button",{class:"btn btn-secondary mt-2 cursor-pointer",onClick:r[2]||(r[2]=()=>{a.value=!1,i()})}," ACCEPT ALL COOKIES ")])])]),_:1,__:[9]})])]),_:1},8,["visible"])])])}}}),kt=h(gt,[["__scopeId","data-v-00842d70"]]),_t={class:"footer"},wt={class:"footer-content"},ht={class:"footer-links-container"},Ct={class:"footer-section"},$t={class:"footer-section"},St={class:"footer-bottom"},Pt={class:"footer-bottom-container"},It={class:"footer-links"},Dt=w({__name:"appFooter",setup(s){const n=J(),{isShowCookiesPop:e}=z(n);return(a,i)=>{const u=P("router-link"),r=E;return b(),g("footer",_t,[t("div",wt,[i[12]||(i[12]=D('<div class="footer-section logo-section" data-v-957f08a1><div class="footer-logo" data-v-957f08a1><img src="'+K+'" alt="Bill Buddy" style="width:160px;" data-v-957f08a1></div><div class="social-links" data-v-957f08a1><a href="https://www.facebook.com/BillBuddy" aria-label="Facebook" data-v-957f08a1><img src="'+q+'" alt="Facebook" style="width:26px;" data-v-957f08a1></a><a href="https://au.linkedin.com/company/billbuddy-au" aria-label="LinkedIn" data-v-957f08a1><img src="'+F+'" alt="LinkedIn" style="width:30px;" data-v-957f08a1></a><a href="https://www.instagram.com/bill_buddy_australia" aria-label="Instagram" data-v-957f08a1><img src="'+W+'" alt="Instagram" style="width:26px;" data-v-957f08a1></a></div></div>',1)),t("div",ht,[t("div",Ct,[i[6]||(i[6]=t("h3",{class:"footer-section-title"},[d(" Products "),t("span",{class:"dot"})],-1)),l(u,{class:"footer-link",to:"/DirectDebit"},{default:p(()=>i[2]||(i[2]=[d(" Direct Debit ")])),_:1,__:[2]}),l(u,{class:"footer-link",to:"/BPay"},{default:p(()=>i[3]||(i[3]=[d(" BPAY ")])),_:1,__:[3]}),l(u,{class:"footer-link",to:"/PayMyInvoice"},{default:p(()=>i[4]||(i[4]=[d(" Pay My Invoice ")])),_:1,__:[4]}),l(u,{class:"footer-link",to:"/WebPay"},{default:p(()=>i[5]||(i[5]=[d(" WebPay ")])),_:1,__:[5]})]),t("div",$t,[i[10]||(i[10]=t("h3",{class:"footer-section-title"},[d(" Support "),t("span",{class:"dot"})],-1)),l(u,{class:"footer-link",to:{name:"officialSupport",hash:"#supportDivide"}},{default:p(()=>i[7]||(i[7]=[d(" Frequently Asked Questions ")])),_:1,__:[7]}),l(u,{class:"footer-link",to:{name:"officialSupport",hash:"#billerDivide"}},{default:p(()=>i[8]||(i[8]=[d(" Biller Support ")])),_:1,__:[8]}),l(u,{class:"footer-link",to:{name:"officialSupport",hash:"#payerDivide"}},{default:p(()=>i[9]||(i[9]=[d(" Payer Support ")])),_:1,__:[9]})]),i[11]||(i[11]=D('<div class="footer-section" data-v-957f08a1><h3 class="footer-section-title" data-v-957f08a1> Quick Links <span class="dot" data-v-957f08a1></span></h3><a class="footer-link" href="https://o2.billbuddy.com/index/login" data-v-957f08a1> Biller Login </a><a class="footer-link" href="https://site.billbuddy.com/index/online-ddr-authority" target="_blank" data-v-957f08a1> Online DDR Authority </a><a class="footer-link" href="https://p.billbuddy.com/" data-v-957f08a1> Bulk Process </a><a href="https://www.paymyinvoice.com.au/" class="footer-link" data-v-957f08a1> PayMyInvoice </a></div>',1))])]),t("div",St,[t("div",Pt,[t("p",null,"© "+U(new Date().getFullYear())+" BILL BUDDY PTY LTD. ALL RIGHTS RESERVED",1),t("div",It,[l(u,{class:"footer-link",to:"/terms"},{default:p(()=>i[13]||(i[13]=[d(" Terms ")])),_:1,__:[13]}),l(r,{layout:"vertical"}),l(u,{class:"footer-link",to:"/privacy"},{default:p(()=>i[14]||(i[14]=[d(" Privacy ")])),_:1,__:[14]}),l(r,{layout:"vertical"}),t("div",{class:"footer-link cursor-pointer",onClick:i[0]||(i[0]=y=>k(n).setIsShowCookiesPop(!k(e)))}," Cookies "),l(r,{layout:"vertical"}),l(u,{class:"footer-link",to:"/Contact-Us"},{default:p(()=>i[15]||(i[15]=[d(" Contact Us ")])),_:1,__:[15]})])])]),k(e)?(b(),A(kt,{key:0,onAccept:i[1]||(i[1]=y=>k(n).setIsShowCookiesPop(!1))})):x("",!0)])}}}),xt=h(Dt,[["__scopeId","data-v-957f08a1"]]),At={class:"app-office-header"},Et={class:"header-container"},Lt={class:"logo"},Bt={class:"dropdown dropdown-products"},Tt={class:"dropdown"},Ot={class:"dropdown"},Rt={class:"action-buttons"},Nt=["href"],zt=w({__name:"appHeader",setup(s){const n="https://test-bbmechant.embracy.com/",e=S(!1),a=S({support:!1,links:!1,products:!1}),i=()=>{e.value=!e.value},u=c=>{for(const o in a.value)o!==c&&(a.value[o]=!1);a.value[c]=!a.value[c]},r=c=>{a.value[c]=!1,e.value&&i()},y=c=>{const o=document.querySelectorAll(".dropdown-toggle"),f=document.querySelectorAll(".dropdown-menu");let m=!1;o.forEach(C=>{C.contains(c.target)&&(m=!0)}),f.forEach(C=>{C.contains(c.target)&&(m=!0)}),m||(a.value.support=!1,a.value.links=!1,a.value.products=!1)};return Y(()=>{document.addEventListener("click",y)}),M(()=>{document.removeEventListener("click",y)}),(c,o)=>{const f=P("router-link");return b(),g("header",At,[t("div",Et,[t("div",Lt,[l(f,{to:"/"},{default:p(()=>o[12]||(o[12]=[t("div",{class:"logo-image"},[t("img",{src:G,alt:"BillBuddy Logo",class:"logo-image"})],-1)])),_:1,__:[12]})]),t("div",{class:"mobile-menu-toggle",onClick:i},[t("i",{class:v(["pi",e.value?"pi-times":"pi-bars"])},null,2)]),t("nav",{class:v(["nav-links",{"mobile-open":e.value}])},[t("div",Bt,[t("button",{class:v(["nav-link dropdown-toggle",{"nav-link-active":c.$route.path.includes("/products")}]),onClick:o[0]||(o[0]=m=>u("products"))},[o[13]||(o[13]=d(" PRODUCTS ")),t("i",{class:v(["pi",a.value.products?"pi-chevron-up":"pi-chevron-down"])},null,2)],2),t("div",{class:v(["dropdown-menu",{"dropdown-open":a.value.products}])},[l(f,{to:{path:"/DirectDebit"},class:"dropdown-item","active-class":"dropdown-item-active",onClick:o[1]||(o[1]=m=>r("products"))},{default:p(()=>o[14]||(o[14]=[d(" DIRECT DEBIT ")])),_:1,__:[14]}),l(f,{to:{path:"/WebPay"},class:"dropdown-item","active-class":"dropdown-item-active",onClick:o[2]||(o[2]=m=>r("products"))},{default:p(()=>o[15]||(o[15]=[d(" WEBPAY ")])),_:1,__:[15]}),l(f,{to:{path:"/BPay"},class:"dropdown-item","active-class":"dropdown-item-active",onClick:o[3]||(o[3]=m=>r("products"))},{default:p(()=>o[16]||(o[16]=[d(" BPAY ")])),_:1,__:[16]}),o[17]||(o[17]=t("a",{href:"/PayMyInvoice",class:"dropdown-item"}," PAYMYINVOICE ",-1))],2)]),l(f,{to:"/biller-registration",class:"nav-link","active-class":"nav-link-active",onClick:o[4]||(o[4]=()=>{e.value&&i()})},{default:p(()=>o[18]||(o[18]=[d(" BILLER REGISTRATION ")])),_:1,__:[18]}),l(f,{to:"/About-Us",class:"nav-link","active-class":"nav-link-active",onClick:o[5]||(o[5]=()=>{e.value&&i()})},{default:p(()=>o[19]||(o[19]=[d(" ABOUT ")])),_:1,__:[19]}),t("div",Tt,[t("button",{class:v(["nav-link dropdown-toggle",{"nav-link-active":c.$route.path.includes("/support")}]),onClick:o[6]||(o[6]=m=>u("support"))},[o[20]||(o[20]=d(" SUPPORT ")),t("i",{class:v(["pi",a.value.support?"pi-chevron-up":"pi-chevron-down"])},null,2)],2),t("div",{class:v(["dropdown-menu",{"dropdown-open":a.value.support}])},[l(f,{to:{name:"officialSupport",hash:"#startDivide"},class:v(["dropdown-item",{"dropdown-item-active":c.$route.hash==="#startDivide"}]),onClick:o[7]||(o[7]=m=>r("support"))},{default:p(()=>o[21]||(o[21]=[d(" GENERAL INQUIRIES ")])),_:1,__:[21]},8,["class"]),l(f,{to:{name:"officialSupport",hash:"#billerDivide"},class:v(["dropdown-item",{"dropdown-item-active":c.$route.hash==="#billerDivide"}]),onClick:o[8]||(o[8]=m=>r("support"))},{default:p(()=>o[22]||(o[22]=[d(" BILLER SUPPORT ")])),_:1,__:[22]},8,["class"]),l(f,{to:{name:"officialSupport",hash:"#payerDivide"},class:v(["dropdown-item",{"dropdown-item-active":c.$route.hash==="#payerDivide"}]),onClick:o[9]||(o[9]=m=>r("support"))},{default:p(()=>o[23]||(o[23]=[d(" PAYER SUPPORT ")])),_:1,__:[23]},8,["class"]),l(f,{to:{name:"officialSupport",hash:"#enquiryDivide"},class:v(["dropdown-item",{"dropdown-item-active":c.$route.hash==="#enquiryDivide"}]),onClick:o[10]||(o[10]=m=>r("support"))},{default:p(()=>o[24]||(o[24]=[d(" TRANSACTION ENQUIRY ")])),_:1,__:[24]},8,["class"])],2)]),t("div",Ot,[t("button",{class:"nav-link dropdown-toggle",onClick:o[11]||(o[11]=m=>u("links"))},[o[25]||(o[25]=d(" LINKS ")),t("i",{class:v(["pi",a.value.links?"pi-chevron-up":"pi-chevron-down"])},null,2)]),t("div",{class:v(["dropdown-menu",{"dropdown-open":a.value.links}])},o[26]||(o[26]=[t("a",{href:"https://o2.billbuddy.com/index/login",class:"dropdown-item"}," Biller Login ",-1),t("a",{href:"https://site.billbuddy.com/index/online-ddr-authority",class:"dropdown-item"}," ONLINE DDR AUTHORITY ",-1),t("a",{href:"https://p.billbuddy.com/",class:"dropdown-item"}," BULK PROCESS ",-1),t("a",{href:"https://www.paymyinvoice.com.au/",class:"dropdown-item"}," PAYMYINVOICE ",-1)]),2)]),t("div",Rt,[l(f,{to:"/Contact-Us",class:"btn btn-book"},{default:p(()=>o[27]||(o[27]=[d(" BOOK A CALL ")])),_:1,__:[27]}),t("a",{href:k(n),class:"btn btn-login"}," MERCHANT LOGIN ",8,Nt)])],2)])])}}}),Ut=h(zt,[["__scopeId","data-v-7cb6a33e"]]),Yt={class:"official-layout"},Mt={class:"main-content"},jt=w({__name:"index",setup(s){return(n,e)=>{const a=P("router-view");return b(),g("div",Yt,[l(Ut),t("main",Mt,[l(a,null,{default:p(({Component:i})=>[l(j,{name:"fade",mode:"out-in"},{default:p(()=>[(b(),A(V(i)))]),_:2},1024)]),_:1})]),l(xt)])}}}),Ht=h(jt,[["__scopeId","data-v-10aacbbb"]]);export{Ht as default};
