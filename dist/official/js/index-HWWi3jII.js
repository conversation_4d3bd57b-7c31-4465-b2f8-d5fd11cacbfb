import{e as xe,f as B,ah as V,C as le,z as U,b as k,g as t,h as i,a3 as Ve,d as h,aE as Ie,F as L,D as N,j as g,t as p,w as a,p as Ee,i as Se,o as d,l as f,x as z,u as y,ai as re,aj as _,E as Ce,aF as Te}from"./index-BNQN8zN8.js";import{s as qe}from"./index-CT2F00Zp.js";import{s as Oe}from"./index-D1zV2t7Y.js";import{s as Ae}from"./index-DTPqL2Ai.js";import{r as pe,s as De}from"./httpOfficial-Cr_5bmkW.js";import{s as Pe}from"./index-Bhs1UJko.js";import{t as ue,o as de,s as w,G as ce,m as I}from"./vee-validate-zod-Day-AUxf.js";import{d as K}from"./index-CIlWFH9f.js";import F from"./collapsePanel-BDV6PFWW.js";import{_ as Re}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ue=W=>pe.post("/payerSupport",W),Le=W=>pe.post("/billerSupport",W),Ne={class:"support"},We={class:"search"},He={class:"search-container"},Ye={class:"search-input-wrapper"},ze={key:0,class:"search-dropdown"},Fe=["onClick"],$e={class:"search-item-title"},Qe=["innerHTML"],Me={id:"supportDivide",class:"information"},je={class:"panel-container"},Ge={class:"border border-[#fe4c1c]"},Ke=["innerHTML"],Ze={class:"view-more"},Je={id:"billerDivide",class:"biller"},Xe={class:"support-title"},et={class:"support-help-container"},tt={class:"panel-container"},ot={class:"border border-[#fe4c1c]"},st={class:"leading-8 whitespace-pre-line"},it={class:"view-more"},at={id:"payerDivide",class:"payer"},nt={class:"support-title"},lt={class:"support-help-container"},rt={class:"panel-container"},ut={class:"border border-[#fe4c1c]"},dt=["innerHTML"],ct={class:"view-more"},pt={id:"enquiryDivide",class:"enquiry"},mt={class:"panel-container"},ft={class:"border border-[#fe4c1c]"},yt=["innerHTML"],ht={class:"cta"},bt={class:"cta-content"},vt={class:"cta-buttons"},gt={class:"dialog"},_t={class:"w-[750px] p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl font-medium font-medium"},wt={class:"header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4"},Bt=["onClick"],kt={class:"form grid"},xt={class:"flex justify-between w-full"},Vt={class:"flex flex-col w-2/3"},It={class:"flex items-center"},Et={class:"text-[#ff3131]"},St={class:"flex items-center"},Ct={class:"text-[#ff3131]"},Tt={class:"flex items-center"},qt={class:"text-[#ff3131]"},Ot={class:"flex items-center"},At={class:"text-[#ff3131]"},Dt={class:"flex items-center"},Pt={class:"text-[#ff3131]"},Rt={class:"custom-textarea"},Ut={class:"flex items-center"},Lt={class:"text-[#ff3131]"},Nt={class:"my-4"},Wt={class:"field mb-4"},Ht={class:"flex justify-between items-center"},Yt=["disabled"],zt={class:"w-[650px] p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl font-medium"},Ft={class:"header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4"},$t=["onClick"],Qt={class:"w-[750px] p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl font-medium"},Mt={class:"header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4"},jt=["onClick"],Gt={class:"form grid"},Kt={class:"flex items-center"},Zt={class:"text-[#ff3131]"},Jt={class:"flex items-center"},Xt={class:"text-[#ff3131]"},eo={class:"flex items-center"},to={class:"text-[#ff3131]"},oo={class:"flex items-center"},so={class:"text-[#ff3131]"},io={class:"flex items-center"},ao={class:"text-[#ff3131]"},no={class:"custom-textarea"},lo={class:"flex items-center"},ro={class:"text-[#ff3131]"},uo={class:"read flex items-start mt-6"},co={class:"my-8"},po={class:"field mb-4"},mo={class:"flex justify-between items-center"},fo=xe({__name:"index",setup(W){const H=B(""),Z=Te(),C=V([{title:"How do I get support from Bill Buddy?",content:`Getting support is easy! Our team is here to help with any questions or issues you may have. You can reach us via:<br />
    <span class="font-semibold">Email: </span> <a style="color: #fe4c1c;" target="_blank" href="mailto:<EMAIL>"><EMAIL></a><br />
    <span class="font-semibold">Phone: </span>1300 434 398 (Mon–Fri, 9am–5pm AEDT)<br />
    <span class="font-semibold">Online: </span>Use the contact form on our website or log in to your portal to raise a support ticket.
    <p>We aim to respond promptly and provide personal, efficient service every step of the way.</p> `,isOpen:!1},{title:"Is Bill Buddy Australian Owned?",content:"Yes, Bill Buddy is 100% Australian owned and operated. Our banking facilities are also provided by Australian Banks.",isOpen:!1},{title:"How long has Bill Buddy been operating?",content:"Bill Buddy was established in 2003 and was largely responsible for bringing competitive pricing to the market. Since our establishment our product range has grown to include Direct Debit, WebPay, Pay-by-Phone, and BPAY.",isOpen:!1},{title:"What industries does Bill Buddy provide facilities to?",content:"Our client base covers a diverse range of industries and business sizes. No business is too big or too small for us to service.",isOpen:!1},{title:"Why should I trust Bill Buddy?",content:"Bill Buddy has been operating for over seven years and in that time has built a reputation based on trust, honesty, and reliable service. For the last few years we have been sponsored by Westpac who conducted exhaustive checks on the Company and its Directors prior to providing transactional facilities. Under our arrangements with Westpac we are also required to fully comply with the Bulk Electronic Clearing System (BECS) as governed by the Australian Payments Clearing Association (APCA). All online transactions are 1,024 bit encrypted and our systems and PCI DSS compliant. Bill Buddy also employs continually learning in-house anti-fraud systems to assist in identifying suspicious transactions or activity. The large number of Billers who have been using our services for several years without being locked into a contract is testament to our ability to provide quality services.",isOpen:!1},{title:"Does Bill Buddy report suspicious transactions to the authorities?",content:"Bill Buddy has both a policy and legal obligation of reporting suspicious transactions to the authorities. Bill Buddy is also a reporting entity under the Anti-Money Laundering and Counter-Terrorism Financing Act (Cth) 2006.",isOpen:!1},{title:"How can I be assured that my information is kept safe?",content:"Bill Buddy fully complies with the requirements of the Privacy Act and the National Privacy Principles. We make no differentiation between our personal and business customers, either. We treat all the information you give us as confidential. We will only make use of it to carry out our processes and procedures on your behalf, as required under the Privacy Act or in compliance with the requirements of the various government agencies that we work with. To avoid confusion, Bill Buddy must (from time to time) provide information to various banking bodies to ensure correct transactions are made and resolve any errors. These are standard procedures for payment transaction businesses and we may do this without specific reference to any other party.",isOpen:!1}]),T=V([{title:"How soon after submitting my application can I be using Bill Buddys facilities?",content:"If your application is completed fully it will typically be processed within one business day with successful applicants being able to use our facilities immediately after approval. If your application is urgent please bring this to our attention when you submit it. Applicants who have been approved for the use of BPAY will need to wait up to 10 business days to be established as a BPAY Sub-Biller in the banking systems.",isOpen:!1},{title:"How long after a transaction occurs will I receive the funds?",content:"Bill Buddy has daily remittances that typically occur on the second business day after the transaction. Be aware that some of our competitors hold the funds for longer periods before remitting them to you and only remit once a week.",isOpen:!1},{title:"Will I be locked into a contract?",content:"No, Bill Buddy Billers are not locked into any fixed term contracts.",isOpen:!1},{title:"Can I change the amounts and dates for my customer payments?",content:"Yes. Billers have access to our secure Biller Portal where they can manage their customer payments including changing the amounts and dates of payments. Ad-hoc or one-off payments can also be created using this portal. Billers can of course also call our office to make changes to customer payments.",isOpen:!1},{title:"What reporting is provided to me?",content:`Bill Buddy offers comprehensive, real-time reporting via the Biller Portal, as well as email-based updates. You'll have access to customer payment statuses, payment and remittance histories, detailed breakdowns of each remittance, and downloadable CSV files.
              At no extra cost, you can also opt in to receive SMS notifications whenever a remittance is made to your account.`,isOpen:!1},{title:"Can I use authority forms branded with my logo?",content:"Yes, if you provide us with your logo Bill Buddy can provide a branded version of our Direct Debit Authority Form.",isOpen:!1},{title:"Can the transaction related fees be passed on to my customer?",content:"Yes, Bill Buddy's system is capable of adding the transaction related fees to the transaction amount. This of course should only be done with your customers consent.",isOpen:!1}]),q=V([{title:"How can I change the date or amount of my next payment?",content:"To change the date or amount of your next payment you need to contact your Biller. Your Biller is the organisation that is providing you with the product or service that the transaction relates to. If the Biller approves your request to alter the payment amount or date they will advise Bill Buddy or make the update online. Please note that changes to the amount or date cannot be made on the day the payment is due.",isOpen:!1},{title:"How can I cancel the direct debit authority I have provided?",content:"To cancel the direct debit authority you must notify us in writing at least 1 week prior to the next payment due date. Please note that cancelling your direct debit authority does not necessarily cancel any contractual arrangements you have with your Biller and you may still have monies owing to them.",isOpen:!1},{title:"Who do I contact if I have a query about the product/service that was paid for using Bill Buddy?",content:"Queries relating to the product or service supplied should be directed to your Biller. Your Biller is the organisation that is providing you with the product or service that the transaction relates to. If you believe your Biller has acted fraudulently or been misleading please notify us.",isOpen:!1},{title:"Who do I contact if I have a query about the amount I was charged via Bill Buddy?",content:"Transactions conducted on behalf of Billers by Bill Buddy are done under the instruction of the Billers; therefore you should contact your Biller if you have a question regarding the amount of a transaction. Please be aware that in some instances your Biller may have instructed Bill Buddy to add its transaction related fees to the amount of the transaction. Your Biller is the organisation that is providing you with the product or service that the transaction relates to.",isOpen:!1},{title:"How do I update my bank account/credit details previously provided to Bill Buddy?",content:"To change the account details for a direct debit you will need to complete a new Direct Debit Authority. To arrange for this simply contact Bill Buddy or your Biller.",isOpen:!1},{title:"When will my account be debited for a schedule payment?",content:"Your accoun will be debited on the scheduled date if that date is a business day. If the scheduled date falls on a weekend or national public holiday your account will be debited the next business day after the scheduled date. The transaction on your account may occur at any time of the day or night on the effective scheduled date and it is your responsibility to ensure you have sufficient funds in your account to honour the debit.",isOpen:!1},{title:"How do I dispute a transaction that Bill Buddy is associated with?",content:"If you want to dispute a transaction you should firstly contact your Biller to discuss your concerns with them. If you are unable to contact your Biller or do not know what the transaction is for please contact Bill Buddy. If you are not satisfied with Bill Buddy's decision regarding your dispute you should contact your financial institution and follow their transaction disputes process. Please note that a dispute should only be lodged with your financial institution if you have been unable to resolve the issue directly with your Biller or Bill Buddy.",isOpen:!1},{title:"How do I find out what the charge from Bill Buddy on my credit card statement is for?",content:`Bill Buddy conducts transactions for many Australian businesses across many industries. If you see "Bill Buddy" on your statement and aren't sure what the transaction is for please visit the Transaction Enquiry page in the Support section at www.billbuddy.com.au.`,isOpen:!1},{title:"Where I can find the direct debit service agreement?",content:`<p>By signing our Direct Debit Request you acknowledge and agree to the following terms and conditions:
</p><br>
                            <ol style="list-style-type: decimal; list-style-position: inside;">
								<li>You authorise Bill Buddy to debit your nominated account in the manner specified by your Biller. Your Biller is the
organisation providing you with the product or service for which we are debiting your account.</li>
<li>We will provide you with at least 14 days prior notice in writing if we propose to vary any of the terms of the debit
arrangements in place between us.</li>
<li>You should contact your Biller if you wish to defer or alter any of the debit arrangements.</li>
<li>You will need to advise us in writing if you wish to cancel a Direct Debit Request. Such notice should be delivered
to us at least one working day before the due date for payment or as otherwise stipulated in our Terms and
Conditions.</li>
<li>If you wish to dispute any Debit Item you should refer to us in the first instance and we will seek to resolve the
matter with you. If we cannot resolve the dispute you can contact your financial institution at which your nominated
account is held. Your financial institution will then commence a formal claims procedure on your behalf.</li>
<li>Some financial institution accounts do not facilitate direct debits. If you are uncertain, you should check with your
financial institution before signing a Direct Debit Request, to ensure that your nominated account is able to receive
direct debits through the Bulk Electronic Clearing System.</li>
<li>Before completing the Direct Debit Request, you should check the details of your nominated account against a
recent statement from your financial institution, to ensure that your account details are correct.</li>
<li>You agree that it is your responsibility to have sufficient cleared funds in your nominated account by the due date
to enable payment of Debit Items in accordance with the directions of your Biller.</li>
<li>We will initiate the Debit Item on the due date as advised by your Biller. If the due date for payment falls on a day
which is not a business day in Queensland, then the Debit Item will be processed on the next business day. You
should enquire directly with your financial institution if you are uncertain as to when the Debit Item will be processed
to your account.</li>
<li>If a Debit Item is returned unpaid by your financial institution, you authorise us to present a further debit for
payment. Furthermore you authorise Bill Buddy to debit your account for our Dishonour Charge.</li>
<li>We will ensure the details of your personal records and account details held by us remain confidential. However,
if you lodge a claim in relation to an alleged incorrect or wrongful debit, it may be necessary for us to release such
information to your financial institution or its representative, or to our financial institution or its representative to
enable your claim to be assessed. Further, we will share certain personal information with our contracted agents for
statistical purposes only. This does NOT include your bank account or credit card details.</li>
							</ol>
                        </div>`,isOpen:!1}]),J=V([{title:"Why is Bill Buddy showing on my credit card statement?",content:`<p class="mb-4">
              Bill Buddy is a payment facilitator for thousands of Australian businesses.
            </p>
            <p class="font-semibold mb-4">
              If you see something similar to "Bill Buddy" on your credit card statement, chances are this
              transaction is for a product
              or service you have purchased from one of our Billers.
            </p>
            <p class="mb-4">
              <span class="font-semibold">
                To find out who the Biller associated with the transaction is please go to our
                <span class="underline">Check
                  Payment page</span>.
              </span>
              Note that to enable
              us to locate your transaction you will be asked to enter the first four and last three
              digits of your credit card along with
              the exact amount of the transaction in Australian dollars.
            </p>
            <p class="mb-4">
              If you wish to discuss the transaction further you should contact the Biller for
              whom we facilitated the transaction.
            </p>`,isOpen:!1},{title:"Why is Bill Buddy debiting my bank account?",content:`<p class="mb-4">
              Bill Buddy is a payment facilitator for thousands of Australian businesses.
            </p>
            <p class="font-semibold mb-4">
              If you see something similar to "Bill Buddy" on your credit card statement, chances are this
              transaction is for a product
              or service you have purchased from one of our Billers.
            </p>
            <p class="mb-4">
              <span class="font-semibold">
                To find out who the Biller associated with the transaction is please go to our
                <span class="underline">Check
                  Payment page</span>.
              </span>
              Note that to enable
              us to locate your transaction you will be asked to enter the first four and last three
              digits of your credit card along with
              the exact amount of the transaction in Australian dollars.
            </p>
            <p class="mb-4">
              If you wish to discuss the transaction further you should contact the Biller for
              whom we facilitated the transaction.
            </p>
          </collapsePanel>
          <collapsePanel title="Why is Bill Buddy debiting my bank account?">
            <p class="font-semibold mb-4">
              Bill Buddy will only debit your bank account if a Direct Debit
              Authority has been provided.
            </p>
            <p class="mb-4">
              Usually the transaction will appear on your statement along with the name of the
              organisation that has provided you
              with the products/services. This is the organisation we are debiting on behalf of. You
              should contact them with any
              questions regarding the amount or timing of your debit.
            </p>
            <p class="mb-4">
              The other reason a direct debit may take place on your account is if a previous debit
              dishonoured. In this case we may
              charge a failed payment fee as per our DDR Service Agreement and this is usually charged in
              the form of debiting that
              fee from your account.
            </p>`,isOpen:!1}]),O=B(!1),Y=B(!1),A=B(!1),$=B(!1),r=V({google_token:"",ddr_id_or_crn:"",customer_name:"",contact_name:"",phone:"",contact_email:"",support_request:"",recaptcha:!1}),u=V({google_token:"",biller_id:"",organisation_name:"",name:"",phone:"",email:"",support_request:"",recaptcha:!1}),me=ue(de({ddr_id_or_crn:w().min(1,{message:"DDR ID/CRN is required."}),customer_name:w().min(1,{message:"Customer Name is required."}),contact_name:w().min(1,{message:"Contact Name is required."}),contact_email:w().min(1,{message:"Contact Email is required."}).email({message:"PLEASE ENTER A VALID EMAIL ADDRESS"}),phone:w().min(1,{message:"Phone is required."}).regex(/^\d+(-\d+)*$/,{message:"PLEASE ENTER VALID PHONE NUMBER"}),support_request:w().min(1,{message:"Support Request is required."}),google_token:w({required_error:"Please complete the reCAPTCHA verification"})})),fe=ue(de({biller_id:w().min(1,{message:"Biller ID is required."}),organisation_name:w().min(1,{message:"Organisation Name is required."}),name:w().min(1,{message:"Name is required."}),email:w().min(1,{message:"Email is required."}).email({message:"PLEASE ENTER A VALID EMAIL ADDRESS"}),phone:w().min(1,{message:"Phone is required."}).regex(/^\d+(-\d+)*$/,{message:"PLEASE ENTER VALID PHONE NUMBER"}),support_request:w().min(1,{message:"Support Request is required."}),google_token:w({required_error:"Please complete the reCAPTCHA verification"})})),X=B(null),x=B(!1),ee=s=>{s&&(O.value?u.google_token=s:A.value&&(r.google_token=s),x.value=!0)},te=()=>{x.value=!1},oe=()=>{x.value=!1},ye=B(),he=B(),E=B(!1),be=async s=>{if(x.value||(u.recaptcha=!1),!s.google_token){I({message:"Please complete the CAPTCHA verification.",type:"error",duration:3e3,closable:!1});return}E.value=!0;try{const e={google_token:s.google_token,biller_id:s.biller_id,name:s.name,organisation_name:s.organisation_name,phone:s.phone,email:s.email,support_request:s.support_request};(await Le(e)).code===0&&(I({message:'Thank you for your submission! We"ll get in touch with you SHORTLY.',type:"success",duration:3e3,closable:!1}),O.value=!1,u.google_token="",x.value=!1)}catch{I({message:"form submission failed. please review information & try again.",type:"error",duration:3e3,closable:!1})}finally{E.value=!1}},ve=async s=>{if(!$.value){I({message:"Please read the clause.",type:"error",duration:3e3,closable:!1});return}if(x.value||(r.recaptcha=!1),!s.google_token){I({message:"Please complete the CAPTCHA verification.",type:"error",duration:3e3,closable:!1});return}E.value=!0;try{const e={google_token:s.google_token,ddr_id_or_crn:s.ddr_id_or_crn,contact_name:s.contact_name,customer_name:s.customer_name,phone:s.phone,contact_email:s.contact_email,support_request:s.support_request};(await Ue(e)).code===0&&(I({message:'Thank you for your submission! We"ll get in touch with you SHORTLY.',type:"success",duration:3e3,closable:!1}),A.value=!1,r.google_token="",x.value=!1)}catch{I({message:"form submission failed. please review information & try again.",type:"error",duration:3e3,closable:!1})}finally{E.value=!1}},se=async s=>{s&&setTimeout(()=>{var e;(e=document.getElementById(s))==null||e.scrollIntoView({behavior:"smooth",block:"center"})},0)};le(()=>Z.hash,()=>{const s=Z.hash.slice(1);s&&se(s)},{immediate:!0});const m=V({information:!0,billerSupport:!0,payerSupport:!0}),Q=s=>{switch(s){case"information":m.information=!m.information;break;case"biller-support":m.billerSupport=!m.billerSupport;break;case"payer-support":m.payerSupport=!m.payerSupport;break}},D=U(()=>m.information?C.slice(0,4):C),P=U(()=>m.billerSupport?T.slice(0,4):T),R=U(()=>m.payerSupport?q.slice(0,4):q),ge=U(()=>[...C,...T,...q,...J]),M=B("");let j=null;le(H,s=>{j&&clearTimeout(j),j=setTimeout(()=>{M.value=s},300)});const ie=U(()=>{if(!M.value.trim())return[];const s=M.value.toLowerCase().trim();return ge.value.filter(e=>e.title.toLowerCase().includes(s)||e.content&&e.content.toLowerCase().includes(s)).slice(0,8)}),_e=s=>{if(!s)return"";const e=s.replace(/<[^>]*>/g," "),b=300;return e.length>b?`${e.slice(0,b)}...`:e},S=B(""),we=s=>{H.value="";let e="",b=!1;C.includes(s)?(e="supportDivide",b=m.information&&!D.value.includes(s),b&&(m.information=!1),C.forEach(c=>{c.isOpen=c===s})):T.includes(s)?(e="billerDivide",b=m.billerSupport&&!P.value.includes(s),b&&(m.billerSupport=!1),T.forEach(c=>{c.isOpen=c===s})):q.includes(s)?(e="payerDivide",b=m.payerSupport&&!R.value.includes(s),b&&(m.payerSupport=!1),q.forEach(c=>{c.isOpen=c===s})):e="enquiryDivide",S.value=s.title,se(e),setTimeout(()=>{S.value=""},1e3*3)};return(s,e)=>{const b=Ee("router-link"),c=Pe,v=De,ae=Ae,ne=Oe,G=Se,Be=qe,ke=Ce;return d(),k("div",Ne,[e[73]||(e[73]=t("div",{id:"startDivide",class:"title-img"},null,-1)),t("div",We,[t("div",He,[t("div",Ye,[Ve(t("input",{"onUpdate:modelValue":e[0]||(e[0]=n=>H.value=n),type:"text",placeholder:"What are you looking for?",class:"search-input"},null,512),[[Ie,H.value]]),ie.value.length>0?(d(),k("div",ze,[(d(!0),k(L,null,N(ie.value,(n,o)=>(d(),k("div",{key:o,class:"search-dropdown-item",onClick:l=>we(n)},[t("div",$e,p(n.title),1),t("div",{class:"search-item-content",innerHTML:_e(n.content)},null,8,Qe)],8,Fe))),128))])):h("",!0)])])]),t("section",Me,[e[37]||(e[37]=t("div",{class:"support-title"},[t("h1",null,[g("General Inquiries and Company Information"),t("span",{class:"accent-dot"},".")])],-1)),t("div",je,[t("div",Ge,[(d(!0),k(L,null,N(D.value,(n,o)=>(d(),f(F,{key:o,"is-open":D.value[o].isOpen,"onUpdate:isOpen":l=>D.value[o].isOpen=l,title:n.title,"is-last-item":o===D.value.length-1,class:z({"highlight-panel":S.value===n.title})},{default:a(()=>[t("p",{class:"leading-8",innerHTML:n.content},null,8,Ke)]),_:2},1032,["is-open","onUpdate:isOpen","title","is-last-item","class"]))),128))])]),t("div",Ze,[t("button",{class:"support-btn",onClick:e[1]||(e[1]=n=>Q("information"))},p(m.information?"VIEW MORE":"VIEW LESS"),1)])]),i(K,{class:"support-divide"}),t("section",Je,[t("div",Xe,[e[39]||(e[39]=t("div",null,[t("h1",null,[g("Biller Support"),t("span",{class:"accent-dot"},".")])],-1)),t("div",et,[e[38]||(e[38]=t("span",{class:"italic text-xl"},"Need more help?",-1)),t("button",{class:"support-btn request",onClick:e[2]||(e[2]=n=>O.value=!0)}," REQUEST SUPPORT ")])]),t("div",tt,[t("div",ot,[(d(!0),k(L,null,N(P.value,(n,o)=>(d(),f(F,{key:o,"is-open":P.value[o].isOpen,"onUpdate:isOpen":l=>P.value[o].isOpen=l,title:n.title,"is-last-item":o===P.value.length-1,class:z({"highlight-panel":S.value===n.title})},{default:a(()=>[t("p",st,p(n.content),1)]),_:2},1032,["is-open","onUpdate:isOpen","title","is-last-item","class"]))),128))])]),t("div",it,[t("button",{class:"support-btn",onClick:e[3]||(e[3]=n=>Q("biller-support"))},p(m.billerSupport?"VIEW MORE":"VIEW LESS"),1)])]),i(K,{class:"support-divide"}),t("section",at,[t("div",nt,[e[41]||(e[41]=t("div",null,[t("h1",null,[g("Payer Support"),t("span",{class:"accent-dot"},".")])],-1)),t("div",lt,[e[40]||(e[40]=t("span",{class:"italic text-xl"},"Need more help?",-1)),t("button",{class:"support-btn request",onClick:e[4]||(e[4]=n=>Y.value=!0)}," REQUEST SUPPORT ")])]),t("div",rt,[t("div",ut,[(d(!0),k(L,null,N(R.value,(n,o)=>(d(),f(F,{key:o,"is-open":R.value[o].isOpen,"onUpdate:isOpen":l=>R.value[o].isOpen=l,title:n.title,"is-last-item":o===R.value.length-1,class:z({"highlight-panel":S.value===n.title})},{default:a(()=>[t("p",{class:"leading-8",innerHTML:n.content},null,8,dt)]),_:2},1032,["is-open","onUpdate:isOpen","title","is-last-item","class"]))),128))])]),t("div",ct,[t("button",{class:"support-btn",onClick:e[5]||(e[5]=n=>Q("payer-support"))},p(m.payerSupport?"VIEW MORE":"VIEW LESS"),1)])]),i(K,{class:"support-divide"}),t("section",pt,[e[42]||(e[42]=t("div",{class:"support-title"},[t("div",null,[t("h1",null,[g("Transaction Enquiry"),t("span",{class:"accent-dot"},".")])])],-1)),t("div",mt,[t("div",ft,[(d(!0),k(L,null,N(J,(n,o)=>(d(),f(F,{key:o,"is-open":n.isOpen,"onUpdate:isOpen":l=>n.isOpen=l,title:n.title,class:z({"highlight-panel":S.value===n.title})},{default:a(()=>[t("div",{innerHTML:n.content},null,8,yt)]),_:2},1032,["is-open","onUpdate:isOpen","title","class"]))),128))])])]),t("section",ht,[t("div",bt,[e[45]||(e[45]=t("div",{class:"cta-text"},[t("h2",null,[g("TALK TO A BIZ DEV MANAGER"),t("span",{class:"accent-dot"},".")]),t("p",null," Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help your business. ")],-1)),t("div",vt,[i(b,{to:"/biller-registration",class:"talk-to-us-button register"},{default:a(()=>e[43]||(e[43]=[g(" BILLER REGISTRATION ")])),_:1,__:[43]}),i(b,{to:"/Contact-Us",class:"talk-to-us-button request-call"},{default:a(()=>e[44]||(e[44]=[g(" REQUEST A CALL ")])),_:1,__:[44]})])])]),t("div",gt,[i(G,{visible:O.value,"onUpdate:visible":e[19]||(e[19]=n=>O.value=n),modal:!0,style:{border:"none"},"pt:mask:class":"backdrop-blur-sm"},{container:a(({closeCallback:n})=>[t("div",_t,[t("div",wt,[e[46]||(e[46]=t("div",{class:"font-semibold text-[2rem]"}," Request Biller Support ",-1)),t("i",{class:"pi pi-times",style:{color:"#fe4c1c","font-weight":"700","font-size":"1.5rem"},onClick:n},null,8,Bt)]),i(ne,{style:{width:"100%",height:"600px"}},{default:a(()=>[e[57]||(e[57]=t("div",{class:"description mt-8 mb-16"},[t("p",{class:"mb-6"}," If you are a registered Bill Buddy Biller requiring support please complete the following form: "),t("p",{class:"italic"}," If you are a customer (payer) of a business who uses Bill Buddy please go to the Payer Support page. ")],-1)),i(y(re),{ref_key:"billerFormRef",ref:he,"validation-schema":y(fe),onSubmit:be},{default:a(()=>[t("div",kt,[i(y(_),{modelValue:u.biller_id,"onUpdate:modelValue":e[7]||(e[7]=o=>u.biller_id=o),name:"biller_id"},{default:a(({errorMessage:o})=>[t("div",xt,[t("div",Vt,[i(c,{modelValue:u.biller_id,"onUpdate:modelValue":e[6]||(e[6]=l=>u.biller_id=l),class:"custom-input col-span-3",name:"biller_id",type:"text",placeholder:"*Biller ID"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",It,[e[47]||(e[47]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Et,p(o),1)])]),_:2},1024)):h("",!0)]),e[48]||(e[48]=t("div",{class:"pt-2 w-46"}," Please input either ID or organisation name. ",-1))])]),_:1},8,["modelValue"]),i(y(_),{modelValue:u.organisation_name,"onUpdate:modelValue":e[9]||(e[9]=o=>u.organisation_name=o),name:"organisation_name"},{default:a(({errorMessage:o})=>[i(c,{modelValue:u.organisation_name,"onUpdate:modelValue":e[8]||(e[8]=l=>u.organisation_name=l),class:"custom-input",name:"organisation_name",type:"text",placeholder:"*Organisation Name"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",St,[e[49]||(e[49]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Ct,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:u.name,"onUpdate:modelValue":e[11]||(e[11]=o=>u.name=o),name:"name"},{default:a(({errorMessage:o})=>[i(c,{modelValue:u.name,"onUpdate:modelValue":e[10]||(e[10]=l=>u.name=l),class:"custom-input",name:"name",type:"text",placeholder:"*Name"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",Tt,[e[50]||(e[50]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",qt,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:u.phone,"onUpdate:modelValue":e[13]||(e[13]=o=>u.phone=o),name:"phone"},{default:a(({errorMessage:o})=>[i(c,{modelValue:u.phone,"onUpdate:modelValue":e[12]||(e[12]=l=>u.phone=l),class:"custom-input",name:"phone",type:"text",placeholder:"*Phone"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",Ot,[e[51]||(e[51]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",At,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:u.email,"onUpdate:modelValue":e[15]||(e[15]=o=>u.email=o),name:"email"},{default:a(({errorMessage:o})=>[i(c,{modelValue:u.email,"onUpdate:modelValue":e[14]||(e[14]=l=>u.email=l),class:"custom-input",name:"email",type:"text",placeholder:"*Email"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",Dt,[e[52]||(e[52]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Pt,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"])]),t("div",Rt,[e[54]||(e[54]=t("div",{class:"font-semibold mb-2"}," *Support Request ",-1)),i(y(_),{modelValue:u.support_request,"onUpdate:modelValue":e[17]||(e[17]=o=>u.support_request=o),name:"support_request"},{default:a(({errorMessage:o})=>[i(ae,{modelValue:u.support_request,"onUpdate:modelValue":e[16]||(e[16]=l=>u.support_request=l),rows:"12",cols:"76"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",Ut,[e[53]||(e[53]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Lt,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"])]),t("div",Nt,[e[55]||(e[55]=t("div",{class:"font-semibold mb-2"}," *CAPTCHA ",-1)),i(y(_),{modelValue:u.google_token,"onUpdate:modelValue":e[18]||(e[18]=o=>u.google_token=o),name:"google_token"},{default:a(({errorMessage:o})=>[t("div",Wt,[i(ce,{ref_key:"recaptchaRef",ref:X,class:"mb-2",onVerify:ee,onExpired:te,onError:oe},null,512),o?(d(),f(v,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:a(()=>[g(p(o),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"])]),t("div",Ht,[t("button",{class:"btn btn-secondary mt-2 cursor-pointer",severity:"warn",disabled:E.value,type:"submit"}," SUBMIT ",8,Yt),e[56]||(e[56]=t("div",{class:"w-2/3 italic leading-6"},[g(" Thank you for your submission. "),t("br"),g(" We’ll get in touch with you within 2 business days. ")],-1))])]),_:1},8,["validation-schema"])]),_:1,__:[57]})])]),_:1},8,["visible"]),i(G,{visible:Y.value,"onUpdate:visible":e[21]||(e[21]=n=>Y.value=n),modal:!0,style:{border:"none"},"pt:mask:class":"backdrop-blur-sm"},{container:a(({closeCallback:n})=>[t("div",zt,[t("div",Ft,[e[58]||(e[58]=t("div",{class:"font-semibold text-[2rem]"}," NOTICE ",-1)),t("i",{class:"pi pi-times",style:{color:"#fe4c1c","font-weight":"700","font-size":"1.5rem"},onClick:n},null,8,$t)]),e[59]||(e[59]=t("div",{class:"notice pr-4"},[t("p",{class:"mb-6 mt-10"}," Looking for answers? Check our FAQ first! "),t("p",{class:"mb-6"}," Before submitting a support request, please review the Customer (Payer) Related Information section in our FAQ. "),t("p",{class:"mb-6"}," If you still need assistance, click PROCEED to fill out the Payer Support form. ")],-1)),t("div",null,[t("button",{class:"btn btn-secondary mt-2",onClick:e[20]||(e[20]=()=>{A.value=!0,Y.value=!1})}," PROCEED ")])])]),_:1},8,["visible"]),i(G,{visible:A.value,"onUpdate:visible":e[36]||(e[36]=n=>A.value=n),modal:!0,style:{border:"none"},"pt:mask:class":"backdrop-blur-sm"},{container:a(({closeCallback:n})=>[t("div",Qt,[t("div",Mt,[e[60]||(e[60]=t("div",{class:"font-semibold text-[2rem]"}," Request Payer Support ",-1)),t("i",{class:"pi pi-times",style:{color:"#fe4c1c","font-weight":"700","font-size":"1.5rem"},onClick:n},null,8,jt)]),i(ne,{style:{width:"100%",height:"600px"}},{default:a(()=>[e[72]||(e[72]=t("div",{class:"description mt-8 mb-8"},[t("p",{class:"mb-6"}," Please fill out the form below: ")],-1)),i(y(re),{ref_key:"payerFormRef",ref:ye,"validation-schema":y(me),onSubmit:ve},{default:a(()=>[t("div",Gt,[i(y(_),{modelValue:r.ddr_id_or_crn,"onUpdate:modelValue":e[23]||(e[23]=o=>r.ddr_id_or_crn=o),name:"ddr_id_or_crn"},{default:a(({errorMessage:o})=>[i(c,{modelValue:r.ddr_id_or_crn,"onUpdate:modelValue":e[22]||(e[22]=l=>r.ddr_id_or_crn=l),class:"custom-input",name:"ddr_id_or_crn",type:"text",placeholder:"*DDR ID/CRN (if known)"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",Kt,[e[61]||(e[61]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Zt,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:r.customer_name,"onUpdate:modelValue":e[25]||(e[25]=o=>r.customer_name=o),name:"customer_name"},{default:a(({errorMessage:o})=>[i(c,{modelValue:r.customer_name,"onUpdate:modelValue":e[24]||(e[24]=l=>r.customer_name=l),class:"custom-input",name:"customer_name",type:"text",placeholder:"*Customer Name"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",Jt,[e[62]||(e[62]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Xt,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:r.contact_name,"onUpdate:modelValue":e[27]||(e[27]=o=>r.contact_name=o),name:"contact_name"},{default:a(({errorMessage:o})=>[i(c,{modelValue:r.contact_name,"onUpdate:modelValue":e[26]||(e[26]=l=>r.contact_name=l),class:"custom-input",name:"contact_name",type:"text",placeholder:"*Contact Name"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",eo,[e[63]||(e[63]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",to,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:r.phone,"onUpdate:modelValue":e[29]||(e[29]=o=>r.phone=o),name:"phone"},{default:a(({errorMessage:o})=>[i(c,{modelValue:r.phone,"onUpdate:modelValue":e[28]||(e[28]=l=>r.phone=l),class:"custom-input",name:"phone",type:"text",placeholder:"*Phone"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",oo,[e[64]||(e[64]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",so,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"]),i(y(_),{modelValue:r.contact_email,"onUpdate:modelValue":e[31]||(e[31]=o=>r.contact_email=o),name:"contact_email"},{default:a(({errorMessage:o})=>[i(c,{modelValue:r.contact_email,"onUpdate:modelValue":e[30]||(e[30]=l=>r.contact_email=l),class:"custom-input",name:"contact_email",type:"text",placeholder:"*Contact Email"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 -mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",io,[e[65]||(e[65]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",ao,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"])]),t("div",no,[e[67]||(e[67]=t("div",{class:"font-semibold mb-2"}," *Support Request ",-1)),i(y(_),{modelValue:r.support_request,"onUpdate:modelValue":e[33]||(e[33]=o=>r.support_request=o),name:"support_request"},{default:a(({errorMessage:o})=>[i(ae,{modelValue:r.support_request,"onUpdate:modelValue":e[32]||(e[32]=l=>r.support_request=l),rows:"12",cols:"76"},null,8,["modelValue"]),o?(d(),f(v,{key:0,class:"ml-4 mt-2 mb-4",severity:"error",size:"small",variant:"simple"},{default:a(()=>[t("div",lo,[e[66]||(e[66]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",ro,p(o),1)])]),_:2},1024)):h("",!0)]),_:1},8,["modelValue"])]),t("div",uo,[i(Be,{modelValue:$.value,"onUpdate:modelValue":e[34]||(e[34]=o=>$.value=o),class:"custom-checkbox",binary:""},null,8,["modelValue"]),e[68]||(e[68]=t("label",{class:"ml-4 leading-6"},[t("p",null," I have read the Customer FAQ and acknowledge that customer support is limited to existing customers of Bill Buddy. Any queries relating to the product or service paid for via Bill Buddy should be directed to the organization who was responsible for the supply of that product/service. If you believe that organization is acting dishonestly or fraudulently please let us know.* ")],-1))]),t("div",co,[e[69]||(e[69]=t("div",{class:"font-semibold mb-2"}," *CAPTCHA ",-1)),i(y(_),{modelValue:r.google_token,"onUpdate:modelValue":e[35]||(e[35]=o=>r.google_token=o),name:"google_token"},{default:a(({errorMessage:o})=>[t("div",po,[i(ce,{ref_key:"recaptchaRef",ref:X,class:"mb-2",onVerify:ee,onExpired:te,onError:oe},null,512),o?(d(),f(v,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:a(()=>[g(p(o),1)]),_:2},1024)):h("",!0)])]),_:1},8,["modelValue"])]),t("div",mo,[i(ke,{class:"btn btn-secondary mt-2",disabled:E.value,type:"submit"},{default:a(()=>e[70]||(e[70]=[g(" SUBMIT ")])),_:1,__:[70]},8,["disabled"]),e[71]||(e[71]=t("div",{class:"w-2/3 italic leading-6"},[g(" Thank you for your submission. "),t("br"),g(" We’ll get in touch with you within 2 business days. ")],-1))])]),_:1},8,["validation-schema"])]),_:1,__:[72]})])]),_:1},8,["visible"])])])}}}),Vo=Re(fo,[["__scopeId","data-v-ba5744b2"]]);export{Vo as default};
