import{e as D,f as y,ah as O,b as L,g as t,j as g,h as a,w as o,u as r,ai as z,p as H,ae as $,o as d,aj as u,l as p,d as c,t as _,E as j}from"./index-BNQN8zN8.js";import{s as G}from"./index-CT2F00Zp.js";import{r as Q,s as W}from"./httpOfficial-Cr_5bmkW.js";import{s as F}from"./index-Bhs1UJko.js";import{d as A}from"./index-CIlWFH9f.js";import{t as K,o as Y,s as f,G as Z,m as V}from"./vee-validate-zod-Day-AUxf.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";const X=E=>Q.post("/merchantQuestionnaire",E),M={class:"biller-registration"},ee={class:"questionnaire-form"},te={class:"my-10"},se={class:"flex gap-1 mb-6"},le={class:"w-full"},ae={class:"flex items-center"},oe={class:"text-[#ff3131]"},ne={class:"flex justify-between items-center gap-1 mb-10"},ie={class:"w-full"},re={class:"flex items-center"},me={class:"text-[#ff3131]"},de={class:"mb-10"},ue={class:"sub-form mb-4"},pe={class:"flex items-center"},ce={class:"text-[#ff3131]"},_e={class:"flex items-center"},fe={class:"text-[#ff3131]"},ve={class:"flex items-center"},ye={class:"text-[#ff3131]"},ge={class:"flex items-center"},be={class:"text-[#ff3131]"},xe={class:"service mt-4 mb-10"},Ve={class:"check-box flex"},he={class:"flex items-center gap-2 mr-16"},ke={class:"flex items-center gap-2 mr-16"},we={class:"flex items-center gap-2 mr-16"},Ee={class:"flex items-center"},Pe={class:"text-[#ff3131]"},Ae={class:"mb-10"},Ue={class:"contact"},Te={class:"mb-4"},Re={class:"flex items-center"},qe={class:"text-[#ff3131]"},Se={class:"mb-4"},Ce={class:"flex items-center"},Ie={class:"text-[#ff3131]"},Be={class:"mb-4"},Ne={class:"flex items-center"},De={class:"text-[#ff3131]"},Oe={class:"mb-4"},Le={class:"flex items-center"},ze={class:"text-[#ff3131]"},He={class:"captcha mt-4"},$e={class:"field mb-4"},je={class:"cta"},Ge={class:"cta-content"},Qe={class:"cta-buttons flex"},We=D({__name:"index",setup(E){const h=y(null),v=y({directDebit:!1,bPay:!1,webPay:!1}),U=async()=>{h.value&&(await $(),h.value.scrollIntoView({behavior:"smooth"}))},l=O({abn:"",business_category:"",average_sales_amount_per_order:"",percentage_of_orders_prepaid:"",estimate_monthly_turnover:"",average_of_days_prepays:"",proposed_services:[],website:"",email:"",phone:"",contact_name:"",google_token:"",recaptcha:!1}),T=y(null),x=y(!1),R=b=>{b&&(l.google_token=b,x.value=!0)},q=()=>{x.value=!1},S=()=>{x.value=!1},k=y(!1),C=K(Y({abn:f().min(1,{message:"ABN is required."}),business_category:f().min(1,{message:"Business Industry is required."}),average_sales_amount_per_order:f().min(1,{message:"Average Sales Amount Per Order is required."}),percentage_of_orders_prepaid:f().min(1,{message:"Percentage Of Orders Prepaid is required."}),estimate_monthly_turnover:f().min(1,{message:"Estimate Monthly Turnover is required."}),average_of_days_prepays:f().min(1,{message:"Average of Days Prepays is required."}),website:f().min(1,{message:"Website is required."}),email:f().min(1,{message:"Email is required."}).email({message:"PLEASE ENTER A VALID EMAIL ADDRESS"}),phone:f().min(1,{message:"Phone is required."}).regex(/^\d+(-\d+)*$/,{message:"PLEASE ENTER VALID PHONE NUMBER"}),contact_name:f().min(1,{message:"Contact Name is required."}),google_token:f({required_error:"Please complete the reCAPTCHA verification"})})),I=async b=>{if(x.value||(l.recaptcha=!1),!b.google_token){V({message:"Please complete the CAPTCHA verification.",type:"error",duration:3e3,closable:!1});return}const e=[];for(const i in v.value)if(Object.prototype.hasOwnProperty.call(v.value,i)&&(console.log(i),v.value[i])){switch(i){case"directDebit":e.push("0");break;case"bPay":e.push("1");break;case"webPay":e.push("2");break}break}if(e.length===0){V({message:"Please select at least one Proposed Service.",type:"error",duration:3e3,closable:!1});return}k.value=!0;try{const i={abn:l.abn,business_category:l.business_category,average_sales_amount_per_order:l.average_sales_amount_per_order,percentage_of_orders_prepaid:l.percentage_of_orders_prepaid,estimate_monthly_turnover:l.estimate_monthly_turnover,average_of_days_prepays:l.average_of_days_prepays,website:l.website,email:l.email,phone:l.phone,contact_name:l.contact_name,google_token:l.google_token,proposed_services:e};(await X(i)).code===0&&V({message:'Thank you for your submission! We"ll get in touch with you SHORTLY.',type:"success",duration:3e3,closable:!1})}catch{V({message:"form submission failed. please review information & try again.",type:"error",duration:3e3,closable:!1})}finally{k.value=!1}},B=y();return(b,e)=>{const i=F,m=W,w=G,N=j,P=H("router-link");return d(),L("div",M,[t("section",{class:"start"},[t("div",{class:"start-wrap"},[e[24]||(e[24]=t("div",{class:"title"},[t("h2",null,"Receive a full"),t("h2",null,[g("Biller Application Kit"),t("span",{class:"accent-dot"},".")])],-1)),e[25]||(e[25]=t("div",{class:"description"},[t("p",null," Complete our quick pre-application questionnaire to help us understand your business and the solutions you're interested in. One of our Business Development Managers will be in touch shortly to guide you through the next steps. ")],-1)),t("div",null,[t("button",{class:"btn text-white",onClick:U}," PROCEED WITH QUESTIONNAIRE ")])])]),t("section",{ref_key:"questionnaireSection",ref:h,class:"questionnaire"},[e[44]||(e[44]=t("div",{class:"questionnaire-title"},[t("h2",{class:"text-[36px] py-[6rem] text-center text-white"}," MERCHANT QUESTIONNAIRE ")],-1)),t("div",ee,[t("div",te,[a(r(z),{ref_key:"veeformRef",ref:B,"validation-schema":r(C),onSubmit:I},{default:o(()=>[a(r(u),{modelValue:l.abn,"onUpdate:modelValue":e[1]||(e[1]=s=>l.abn=s),name:"abn"},{default:o(({errorMessage:s})=>[t("div",se,[t("div",le,[a(i,{modelValue:l.abn,"onUpdate:modelValue":e[0]||(e[0]=n=>l.abn=n),class:"custom-input w-[80%]",name:"abn",type:"text",placeholder:"*ABN"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",ae,[e[26]||(e[26]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",oe,_(s),1)])]),_:2},1024)):c("",!0)])])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.business_category,"onUpdate:modelValue":e[3]||(e[3]=s=>l.business_category=s),name:"business_category"},{default:o(({errorMessage:s})=>[t("div",ne,[t("div",ie,[a(i,{modelValue:l.business_category,"onUpdate:modelValue":e[2]||(e[2]=n=>l.business_category=n),class:"custom-input w-[80%]",name:"business_category",type:"text",placeholder:"*Business Industry"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",re,[e[27]||(e[27]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",me,_(s),1)])]),_:2},1024)):c("",!0)])])]),_:1},8,["modelValue"]),t("div",de,[a(A)]),t("div",ue,[a(r(u),{modelValue:l.average_sales_amount_per_order,"onUpdate:modelValue":e[5]||(e[5]=s=>l.average_sales_amount_per_order=s),name:"average_sales_amount_per_order"},{default:o(({errorMessage:s})=>[t("div",null,[a(i,{modelValue:l.average_sales_amount_per_order,"onUpdate:modelValue":e[4]||(e[4]=n=>l.average_sales_amount_per_order=n),class:"custom-input w-full",name:"average_sales_amount_per_order",type:"text",placeholder:"*Average Sales Amount Per Order ($)"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",pe,[e[28]||(e[28]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",ce,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.percentage_of_orders_prepaid,"onUpdate:modelValue":e[7]||(e[7]=s=>l.percentage_of_orders_prepaid=s),name:"percentage_of_orders_prepaid"},{default:o(({errorMessage:s})=>[t("div",null,[a(i,{modelValue:l.percentage_of_orders_prepaid,"onUpdate:modelValue":e[6]||(e[6]=n=>l.percentage_of_orders_prepaid=n),class:"custom-input w-full",name:"percentage_of_orders_prepaid",type:"text",placeholder:"*Percentage of Orders Prepaid (%)"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",_e,[e[29]||(e[29]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",fe,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.estimate_monthly_turnover,"onUpdate:modelValue":e[9]||(e[9]=s=>l.estimate_monthly_turnover=s),name:"estimate_monthly_turnover"},{default:o(({errorMessage:s})=>[t("div",null,[a(i,{modelValue:l.estimate_monthly_turnover,"onUpdate:modelValue":e[8]||(e[8]=n=>l.estimate_monthly_turnover=n),class:"custom-input w-full",name:"estimate_monthly_turnover",type:"text",placeholder:"*Estimate Monthly Turnover ($)"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",ve,[e[30]||(e[30]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",ye,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.average_of_days_prepays,"onUpdate:modelValue":e[11]||(e[11]=s=>l.average_of_days_prepays=s),name:"average_of_days_prepays"},{default:o(({errorMessage:s})=>[t("div",null,[a(i,{modelValue:l.average_of_days_prepays,"onUpdate:modelValue":e[10]||(e[10]=n=>l.average_of_days_prepays=n),class:"custom-input w-full",name:"average_of_days_prepays",type:"text",placeholder:"*Estimate Monthly Turnover ($)"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",ge,[e[31]||(e[31]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",be,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),t("div",xe,[e[36]||(e[36]=t("div",{class:"service-title"}," *Proposed Service ",-1)),a(r(u),{name:"proposed_services"},{default:o(({errorMessage:s})=>[t("div",Ve,[t("div",he,[a(w,{modelValue:v.value.directDebit,"onUpdate:modelValue":e[12]||(e[12]=n=>v.value.directDebit=n),class:"custom-checkbox",binary:""},null,8,["modelValue"]),e[32]||(e[32]=t("label",null," Direct Debit ",-1))]),t("div",ke,[a(w,{modelValue:v.value.bPay,"onUpdate:modelValue":e[13]||(e[13]=n=>v.value.bPay=n),class:"custom-checkbox",binary:""},null,8,["modelValue"]),e[33]||(e[33]=t("label",null," BPay ",-1))]),t("div",we,[a(w,{modelValue:v.value.webPay,"onUpdate:modelValue":e[14]||(e[14]=n=>v.value.webPay=n),class:"custom-checkbox",binary:""},null,8,["modelValue"]),e[34]||(e[34]=t("label",null," WebPay ",-1))])]),s?(d(),p(m,{key:0,class:"ml-4 mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",Ee,[e[35]||(e[35]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Pe,_(s),1)])]),_:2},1024)):c("",!0)]),_:1})]),t("div",Ae,[a(A,{style:{transform:"scale(-1)"}})]),t("div",Ue,[e[41]||(e[41]=t("div",{class:"contact-title mt-4 mb-6"}," Contact ",-1)),a(r(u),{modelValue:l.contact_name,"onUpdate:modelValue":e[16]||(e[16]=s=>l.contact_name=s),name:"contact_name"},{default:o(({errorMessage:s})=>[t("div",Te,[a(i,{modelValue:l.contact_name,"onUpdate:modelValue":e[15]||(e[15]=n=>l.contact_name=n),class:"custom-input w-full",name:"contact_name",type:"text",placeholder:"*Contact Name"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 -mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",Re,[e[37]||(e[37]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",qe,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.phone,"onUpdate:modelValue":e[18]||(e[18]=s=>l.phone=s),name:"phone"},{default:o(({errorMessage:s})=>[t("div",Se,[a(i,{modelValue:l.phone,"onUpdate:modelValue":e[17]||(e[17]=n=>l.phone=n),class:"custom-input w-full",name:"phone",type:"text",placeholder:"*Phone"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 -mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",Ce,[e[38]||(e[38]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",Ie,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.email,"onUpdate:modelValue":e[20]||(e[20]=s=>l.email=s),name:"email"},{default:o(({errorMessage:s})=>[t("div",Be,[a(i,{modelValue:l.email,"onUpdate:modelValue":e[19]||(e[19]=n=>l.email=n),class:"custom-input w-full",name:"email",type:"text",placeholder:"*Email"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 -mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",Ne,[e[39]||(e[39]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",De,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"]),a(r(u),{modelValue:l.website,"onUpdate:modelValue":e[22]||(e[22]=s=>l.website=s),name:"website"},{default:o(({errorMessage:s})=>[t("div",Oe,[a(i,{modelValue:l.website,"onUpdate:modelValue":e[21]||(e[21]=n=>l.website=n),class:"custom-input w-full",name:"website",type:"text",placeholder:"*Website"},null,8,["modelValue"]),s?(d(),p(m,{key:0,class:"ml-4 -mt-2",severity:"error",size:"small",variant:"simple"},{default:o(()=>[t("div",Le,[e[40]||(e[40]=t("i",{class:"pi pi-exclamation-triangle",style:{color:"#ff3131","margin-right":"10px"}},null,-1)),t("span",ze,_(s),1)])]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),t("div",He,[e[42]||(e[42]=t("div",{class:"contact-title mb-4"}," *CAPTCHA ",-1)),a(r(u),{modelValue:l.google_token,"onUpdate:modelValue":e[23]||(e[23]=s=>l.google_token=s),name:"google_token"},{default:o(({errorMessage:s})=>[t("div",$e,[a(Z,{ref_key:"recaptchaRef",ref:T,class:"mb-2",onVerify:R,onExpired:q,onError:S},null,512),s?(d(),p(m,{key:0,class:"mt-2",severity:"error",variant:"simple"},{default:o(()=>[g(_(s),1)]),_:2},1024)):c("",!0)])]),_:1},8,["modelValue"])]),a(N,{class:"btn mt-2",severity:"warn",disabled:k.value,type:"submit",label:"SUBMIT"},{default:o(()=>e[43]||(e[43]=[g(" SUBMIT ")])),_:1,__:[43]},8,["disabled"])]),_:1},8,["validation-schema"])])])],512),t("section",je,[t("div",Ge,[e[47]||(e[47]=t("div",null,[t("h2",null,[g("TALK TO A BIZ DEV MANAGER"),t("span",{class:"accent-dot"},".")]),t("p",null," Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help your business. ")],-1)),t("div",Qe,[a(P,{to:"/biller-registration",class:"talk-to-us-button register"},{default:o(()=>e[45]||(e[45]=[g(" BILLER REGISTRATION ")])),_:1,__:[45]}),a(P,{to:"/Contact-Us",class:"talk-to-us-button request-call"},{default:o(()=>e[46]||(e[46]=[g(" REQUEST A CALL ")])),_:1,__:[46]})])])])])}}}),et=J(We,[["__scopeId","data-v-6c68bfab"]]);export{et as default};
