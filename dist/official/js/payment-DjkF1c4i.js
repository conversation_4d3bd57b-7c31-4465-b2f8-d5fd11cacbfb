import{s as o}from"./index-Bhs1UJko.js";import{_ as n}from"./cut-payment-w6_1nyLB.js";import{_ as i}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{b as c,o as r,n as e,g as a,j as l,h as s}from"./index-BNQN8zN8.js";const p={},m={class:"payment"},v={class:"payment-content"},f={class:"payment-form"},u={class:"form-detail"};function y(h,t){const d=o;return r(),c("div",m,[t[4]||(t[4]=e('<div class="payment-header" data-v-d607180d><div class="left" data-v-d607180d></div><div class="right" data-v-d607180d></div><div class="content" data-v-d607180d><h1 data-v-d607180d>Check Payment Made<span class="accent-dot" data-v-d607180d>.</span></h1></div></div>',1)),a("div",v,[t[3]||(t[3]=e('<div class="tips" data-v-d607180d><div class="first" data-v-d607180d><p data-v-d607180d> You may find on your credit card statement the entries of payment a payment to: </p><p class="font-semibold" data-v-d607180d> &quot;BBY*BusinessName&quot; </p><p data-v-d607180d> To find the details of who this payment is for, you need to enter the following details. </p></div><div class="second" data-v-d607180d><img src="'+n+'" alt="" data-v-d607180d></div></div>',1)),a("div",f,[t[2]||(t[2]=a("div",null,[a("h2",null,[l("Fill out the form"),a("span",{class:"accent-dot"},".")])],-1)),a("div",u,[s(d,{class:"custom-input",name:"first",type:"text",placeholder:"*The first 6 digits of your credit card number"}),s(d,{class:"custom-input",name:"last",type:"text",placeholder:"*The last 3 digits of your credit card number"}),s(d,{class:"custom-input",name:"amount",type:"text",placeholder:"*The exact amount of the transaction in AUD"}),t[0]||(t[0]=a("div",{class:"captcha"},[a("div",{class:"captcha-title"}," *CAPTCHA ")],-1)),t[1]||(t[1]=a("div",{class:"payment-btn"},[a("button",{to:"/biller-registration",class:"btn btn-secondary"}," SEARCH ")],-1))])])])])}const T=i(p,[["render",y],["__scopeId","data-v-d607180d"]]);export{T as default};
