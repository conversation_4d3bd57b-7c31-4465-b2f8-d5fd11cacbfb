import{e as Ie,aJ as He,f as H,q as Ge,v as Je,C as Ye,b as V,o as L,d as Ee,g as C,x as G,ae as he,aK as Qe,h as Xe,w as Ke,t as et,aL as $e,aM as tt,aN as rt,aO as st,aP as at,aQ as nt}from"./index-BNQN8zN8.js";import{_ as it}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ot={class:"recaptcha-wrap"},ct={key:0,class:"recaptcha-loading"},dt={key:1,class:"recaptcha-timeout"},ut={key:2,class:"recaptcha-error"},lt=Ie({__name:"index",props:{siteKey:{default:"6Lc7EhQrAAAAAIsN6MfROAMUqehxNI27h2gPHcG8"},theme:{default:"light"},size:{default:"normal"},tabindex:{default:0}},emits:["verify","expired","error"],setup(r,{expose:e,emit:t}){const s=r,a=t,{locale:n}=He(),i=H(null),o=H(null),l=H(!1),u=H(!0),_=H(!1);let w=null;const W=()=>new Promise((k,N)=>{if(window.grecaptcha){k();return}const P=document.createElement("script");P.src=`https://www.google.com/recaptcha/api.js?hl=${n.value}&render=explicit`,P.async=!0,P.defer=!0,P.onload=()=>k(),P.onerror=We=>N(We),document.head.appendChild(P)}),ae=async()=>{if(await he(),!(!window.grecaptcha||!i.value)){if(o.value!==null){window.grecaptcha.reset(o.value);return}o.value=window.grecaptcha.render(i.value,{sitekey:s.siteKey,theme:s.theme,size:s.size,tabindex:s.tabindex,callback:k=>a("verify",k),"expired-callback":()=>a("expired"),"error-callback":()=>a("error")})}},Fe=()=>{window.grecaptcha&&o.value!==null&&window.grecaptcha.reset(o.value)},qe=()=>window.grecaptcha&&o.value!==null?window.grecaptcha.getResponse(o.value):"",ne=()=>{w!==null&&window.clearTimeout(w),w=window.setTimeout(()=>{u.value&&(u.value=!1,_.value=!0)},3e4)},E=()=>{w!==null&&(window.clearTimeout(w),w=null)},fe=async()=>{try{u.value=!0,_.value=!1,l.value=!1,ne(),await W();const k=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(k),ae(),u.value=!1,E())},100)}catch(k){console.error("Failed to load reCAPTCHA on retry:",k),u.value=!1,l.value=!0,E(),a("error")}};return Ge(async()=>{try{u.value=!0,ne(),await W();const k=setInterval(()=>{window.grecaptcha&&typeof window.grecaptcha.render=="function"&&(clearInterval(k),ae(),u.value=!1,E())},100)}catch(k){console.error("Failed to load reCAPTCHA:",k),u.value=!1,l.value=!0,E(),a("error")}}),Je(()=>{if(window.grecaptcha&&o.value!==null){const k=i.value;k&&(k.innerHTML=""),o.value=null}E()}),Ye(n,()=>{o.value!==null&&(o.value=null,i.value&&(i.value.innerHTML=""),l.value=!1,_.value=!1,u.value=!0,ne(),W().then(async()=>{await he(),ae(),u.value=!1,E()}).catch(k=>{console.error("Failed to load reCAPTCHA after locale change:",k),u.value=!1,l.value=!0,E(),a("error")}))}),e({reset:Fe,execute:qe}),(k,N)=>(L(),V("div",ot,[u.value?(L(),V("div",ct,N[0]||(N[0]=[C("div",{class:"loading-spinner"},null,-1),C("p",null,"Loading Google reCAPTCHA...",-1)]))):_.value?(L(),V("div",dt,[N[1]||(N[1]=C("p",null,"Loading Google reCAPTCHA timed out after 30 seconds.",-1)),C("button",{class:"retry-button",onClick:fe}," Retry ")])):l.value?(L(),V("div",ut,[N[2]||(N[2]=C("p",null,"Failed to load Google reCAPTCHA. Please check your internet connection and try again.",-1)),C("button",{class:"retry-button",onClick:fe}," Retry ")])):Ee("",!0),C("div",{ref_key:"recaptchaContainer",ref:i,class:G(k.$attrs.class)},null,2)]))}}),Yt=it(lt,[["__scopeId","data-v-7523f593"]]),ft={class:"card flex justify-center"},ht={class:"flex items-center gap-3"},mt=["onClick"],pt=Ie({__name:"index",setup(r,{expose:e}){const t=Qe();return e({showMessage:a=>{t.add({group:"headless",detail:a.type,summary:a.message,life:a.duration||0,closable:a.closable??!0})}}),(a,n)=>{const i=$e;return L(),V("div",ft,[Xe(i,{unstyled:!0,position:"top-center",style:{top:"120px",left:"50%",transform:"translateX(-50%)"},group:"headless"},{container:Ke(({message:o,closeCallback:l})=>[C("section",{class:G(["z-1000 flex items-center justify-between py-6 px-4 w-188",[{"bg-[#e1ffa9]":o.detail==="success","bg-[#ff3131]":o.detail==="error"}]])},[C("div",ht,[C("i",{class:G(["text-white dark:text-black text-xl",[{"":o.detail==="success","pi pi-exclamation-triangle":o.detail==="error"}]])},null,2),C("span",{class:G(["font-bold",[{"text-[#181349]":o.detail==="success","text-[#fff]":o.detail==="error"}]])},et(o.summary),3)]),o.closable?(L(),V("button",{key:0,class:"text-white dark:text-black hover:opacity-70",onClick:l},[C("i",{class:G(["pi pi-times text-lg",[{"text-[#181349]":o.detail==="success","text-[#fff]":o.detail==="error"}]])},null,2)],8,mt)):Ee("",!0)],2)]),_:1})])}}});let z=null;const yt=()=>{const r=document.createElement("div");document.body.appendChild(r);const e=tt(pt);return e.use(rt),e.use(st),e.component("Toast",$e),z=e.mount(r),z},Qt=r=>{z||(z=yt()),typeof r=="string"?z.showMessage({message:r,type:"info",duration:0,closable:!0}):z.showMessage({...r,duration:r.duration||0,closable:r.closable??!0})};var v;(function(r){r.assertEqual=a=>{};function e(a){}r.assertIs=e;function t(a){throw new Error}r.assertNever=t,r.arrayToEnum=a=>{const n={};for(const i of a)n[i]=i;return n},r.getValidEnumValues=a=>{const n=r.objectKeys(a).filter(o=>typeof a[a[o]]!="number"),i={};for(const o of n)i[o]=a[o];return r.objectValues(i)},r.objectValues=a=>r.objectKeys(a).map(function(n){return a[n]}),r.objectKeys=typeof Object.keys=="function"?a=>Object.keys(a):a=>{const n=[];for(const i in a)Object.prototype.hasOwnProperty.call(a,i)&&n.push(i);return n},r.find=(a,n)=>{for(const i of a)if(n(i))return i},r.isInteger=typeof Number.isInteger=="function"?a=>Number.isInteger(a):a=>typeof a=="number"&&Number.isFinite(a)&&Math.floor(a)===a;function s(a,n=" | "){return a.map(i=>typeof i=="string"?`'${i}'`:i).join(n)}r.joinValues=s,r.jsonStringifyReplacer=(a,n)=>typeof n=="bigint"?n.toString():n})(v||(v={}));var me;(function(r){r.mergeShapes=(e,t)=>({...e,...t})})(me||(me={}));const f=v.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),S=r=>{switch(typeof r){case"undefined":return f.undefined;case"string":return f.string;case"number":return Number.isNaN(r)?f.nan:f.number;case"boolean":return f.boolean;case"function":return f.function;case"bigint":return f.bigint;case"symbol":return f.symbol;case"object":return Array.isArray(r)?f.array:r===null?f.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?f.promise:typeof Map<"u"&&r instanceof Map?f.map:typeof Set<"u"&&r instanceof Set?f.set:typeof Date<"u"&&r instanceof Date?f.date:f.object;default:return f.unknown}},c=v.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class A extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(n){return n.message},s={_errors:[]},a=n=>{for(const i of n.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,l=0;for(;l<i.path.length;){const u=i.path[l];l===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],l++}}};return a(this),s}static assert(e){if(!(e instanceof A))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,v.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}A.create=r=>new A(r);const oe=(r,e)=>{let t;switch(r.code){case c.invalid_type:r.received===f.undefined?t="Required":t=`Expected ${r.expected}, received ${r.received}`;break;case c.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,v.jsonStringifyReplacer)}`;break;case c.unrecognized_keys:t=`Unrecognized key(s) in object: ${v.joinValues(r.keys,", ")}`;break;case c.invalid_union:t="Invalid input";break;case c.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${v.joinValues(r.options)}`;break;case c.invalid_enum_value:t=`Invalid enum value. Expected ${v.joinValues(r.options)}, received '${r.received}'`;break;case c.invalid_arguments:t="Invalid function arguments";break;case c.invalid_return_type:t="Invalid function return type";break;case c.invalid_date:t="Invalid date";break;case c.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:v.assertNever(r.validation):r.validation!=="regex"?t=`Invalid ${r.validation}`:t="Invalid";break;case c.too_small:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:t="Invalid input";break;case c.too_big:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?t=`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:t="Invalid input";break;case c.custom:t="Invalid input";break;case c.invalid_intersection_types:t="Intersection results could not be merged";break;case c.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case c.not_finite:t="Number must be finite";break;default:t=e.defaultError,v.assertNever(r)}return{message:t}};let _t=oe;function gt(){return _t}const vt=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,n=[...t,...a.path||[]],i={...a,path:n};if(a.message!==void 0)return{...a,path:n,message:a.message};let o="";const l=s.filter(u=>!!u).slice().reverse();for(const u of l)o=u(i,{data:e,defaultError:o}).message;return{...a,path:n,message:o}};function d(r,e){const t=gt(),s=vt({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===oe?void 0:oe].filter(a=>!!a)});r.common.issues.push(s)}class b{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return p;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const n=await a.key,i=await a.value;s.push({key:n,value:i})}return b.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:n,value:i}=a;if(n.status==="aborted"||i.status==="aborted")return p;n.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),n.value!=="__proto__"&&(typeof i.value<"u"||a.alwaysSet)&&(s[n.value]=i.value)}return{status:e.value,value:s}}}const p=Object.freeze({status:"aborted"}),J=r=>({status:"dirty",value:r}),T=r=>({status:"valid",value:r}),pe=r=>r.status==="aborted",ye=r=>r.status==="dirty",D=r=>r.status==="valid",X=r=>typeof Promise<"u"&&r instanceof Promise;var h;(function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(h||(h={}));class j{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const _e=(r,e)=>{if(D(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new A(r.common.issues);return this._error=t,this._error}}};function y(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(i,o)=>{const{message:l}=r;return i.code==="invalid_enum_value"?{message:l??o.defaultError}:typeof o.data>"u"?{message:l??s??o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:l??t??o.defaultError}},description:a}}class g{get description(){return this._def.description}_getType(e){return S(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:S(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new b,ctx:{common:e.parent.common,data:e.data,parsedType:S(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(X(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){const s={common:{issues:[],async:(t==null?void 0:t.async)??!1,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:S(e)},a=this._parseSync({data:e,path:s.path,parent:s});return _e(s,a)}"~validate"(e){var s,a;const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:S(e)};if(!this["~standard"].async)try{const n=this._parseSync({data:e,path:[],parent:t});return D(n)?{value:n.value}:{issues:t.common.issues}}catch(n){(a=(s=n==null?void 0:n.message)==null?void 0:s.toLowerCase())!=null&&a.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(n=>D(n)?{value:n.value}:{issues:t.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:S(e)},a=this._parse({data:e,path:s.path,parent:s}),n=await(X(a)?a:Promise.resolve(a));return _e(s,n)}refine(e,t){const s=a=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,n)=>{const i=e(a),o=()=>n.addIssue({code:c.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((s,a)=>e(s)?!0:(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new F({schema:this,typeName:m.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return Z.create(this,this._def)}nullable(){return q.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return O.create(this)}promise(){return re.create(this,this._def)}or(e){return ee.create([this,e],this._def)}and(e){return te.create(this,e,this._def)}transform(e){return new F({...y(this._def),schema:this,typeName:m.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new se({...y(this._def),innerType:this,defaultValue:t,typeName:m.ZodDefault})}brand(){return new Ut({typeName:m.ZodBranded,type:this,...y(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new de({...y(this._def),innerType:this,catchValue:t,typeName:m.ZodCatch})}describe(e){const t=this.constructor;return new t({...this._def,description:e})}pipe(e){return le.create(this,e)}readonly(){return ue.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const xt=/^c[^\s-]{8,}$/i,kt=/^[0-9a-z]+$/,bt=/^[0-9A-HJKMNP-TV-Z]{26}$/i,wt=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Ct=/^[a-z0-9_-]{21}$/i,Tt=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ot=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,At=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Nt="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let ie;const St=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Rt=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Zt=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,jt=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,It=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Et=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Pe="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",$t=new RegExp(`^${Pe}$`);function Me(r){let e="[0-5]\\d";r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`);const t=r.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${t}`}function Pt(r){return new RegExp(`^${Me(r)}$`)}function Mt(r){let e=`${Pe}T${Me(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Vt(r,e){return!!((e==="v4"||!e)&&St.test(r)||(e==="v6"||!e)&&Zt.test(r))}function Lt(r,e){if(!Tt.test(r))return!1;try{const[t]=r.split("."),s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return!(typeof a!="object"||a===null||"typ"in a&&(a==null?void 0:a.typ)!=="JWT"||!a.alg||e&&a.alg!==e)}catch{return!1}}function zt(r,e){return!!((e==="v4"||!e)&&Rt.test(r)||(e==="v6"||!e)&&jt.test(r))}class R extends g{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==f.string){const n=this._getOrReturnCtx(e);return d(n,{code:c.invalid_type,expected:f.string,received:n.parsedType}),p}const s=new b;let a;for(const n of this._def.checks)if(n.kind==="min")e.data.length<n.value&&(a=this._getOrReturnCtx(e,a),d(a,{code:c.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),s.dirty());else if(n.kind==="max")e.data.length>n.value&&(a=this._getOrReturnCtx(e,a),d(a,{code:c.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),s.dirty());else if(n.kind==="length"){const i=e.data.length>n.value,o=e.data.length<n.value;(i||o)&&(a=this._getOrReturnCtx(e,a),i?d(a,{code:c.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}):o&&d(a,{code:c.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}),s.dirty())}else if(n.kind==="email")At.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"email",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="emoji")ie||(ie=new RegExp(Nt,"u")),ie.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"emoji",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="uuid")wt.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"uuid",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="nanoid")Ct.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"nanoid",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="cuid")xt.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"cuid",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="cuid2")kt.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"cuid2",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="ulid")bt.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"ulid",code:c.invalid_string,message:n.message}),s.dirty());else if(n.kind==="url")try{new URL(e.data)}catch{a=this._getOrReturnCtx(e,a),d(a,{validation:"url",code:c.invalid_string,message:n.message}),s.dirty()}else n.kind==="regex"?(n.regex.lastIndex=0,n.regex.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"regex",code:c.invalid_string,message:n.message}),s.dirty())):n.kind==="trim"?e.data=e.data.trim():n.kind==="includes"?e.data.includes(n.value,n.position)||(a=this._getOrReturnCtx(e,a),d(a,{code:c.invalid_string,validation:{includes:n.value,position:n.position},message:n.message}),s.dirty()):n.kind==="toLowerCase"?e.data=e.data.toLowerCase():n.kind==="toUpperCase"?e.data=e.data.toUpperCase():n.kind==="startsWith"?e.data.startsWith(n.value)||(a=this._getOrReturnCtx(e,a),d(a,{code:c.invalid_string,validation:{startsWith:n.value},message:n.message}),s.dirty()):n.kind==="endsWith"?e.data.endsWith(n.value)||(a=this._getOrReturnCtx(e,a),d(a,{code:c.invalid_string,validation:{endsWith:n.value},message:n.message}),s.dirty()):n.kind==="datetime"?Mt(n).test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{code:c.invalid_string,validation:"datetime",message:n.message}),s.dirty()):n.kind==="date"?$t.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{code:c.invalid_string,validation:"date",message:n.message}),s.dirty()):n.kind==="time"?Pt(n).test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{code:c.invalid_string,validation:"time",message:n.message}),s.dirty()):n.kind==="duration"?Ot.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"duration",code:c.invalid_string,message:n.message}),s.dirty()):n.kind==="ip"?Vt(e.data,n.version)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"ip",code:c.invalid_string,message:n.message}),s.dirty()):n.kind==="jwt"?Lt(e.data,n.alg)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"jwt",code:c.invalid_string,message:n.message}),s.dirty()):n.kind==="cidr"?zt(e.data,n.version)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"cidr",code:c.invalid_string,message:n.message}),s.dirty()):n.kind==="base64"?It.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"base64",code:c.invalid_string,message:n.message}),s.dirty()):n.kind==="base64url"?Et.test(e.data)||(a=this._getOrReturnCtx(e,a),d(a,{validation:"base64url",code:c.invalid_string,message:n.message}),s.dirty()):v.assertNever(n);return{status:s.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:c.invalid_string,...h.errToObj(s)})}_addCheck(e){return new R({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...h.errToObj(e)})}url(e){return this._addCheck({kind:"url",...h.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...h.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...h.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...h.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...h.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...h.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...h.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...h.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...h.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...h.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...h.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...h.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(e==null?void 0:e.offset)??!1,local:(e==null?void 0:e.local)??!1,...h.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...h.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...h.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...h.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...h.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...h.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...h.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...h.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...h.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...h.errToObj(t)})}nonempty(e){return this.min(1,h.errToObj(e))}trim(){return new R({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new R({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new R({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}R.create=r=>new R({checks:[],typeName:m.ZodString,coerce:(r==null?void 0:r.coerce)??!1,...y(r)});function Dt(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s,n=Number.parseInt(r.toFixed(a).replace(".","")),i=Number.parseInt(e.toFixed(a).replace(".",""));return n%i/10**a}class U extends g{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==f.number){const n=this._getOrReturnCtx(e);return d(n,{code:c.invalid_type,expected:f.number,received:n.parsedType}),p}let s;const a=new b;for(const n of this._def.checks)n.kind==="int"?v.isInteger(e.data)||(s=this._getOrReturnCtx(e,s),d(s,{code:c.invalid_type,expected:"integer",received:"float",message:n.message}),a.dirty()):n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(s=this._getOrReturnCtx(e,s),d(s,{code:c.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),a.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(s=this._getOrReturnCtx(e,s),d(s,{code:c.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),a.dirty()):n.kind==="multipleOf"?Dt(e.data,n.value)!==0&&(s=this._getOrReturnCtx(e,s),d(s,{code:c.not_multiple_of,multipleOf:n.value,message:n.message}),a.dirty()):n.kind==="finite"?Number.isFinite(e.data)||(s=this._getOrReturnCtx(e,s),d(s,{code:c.not_finite,message:n.message}),a.dirty()):v.assertNever(n);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,s,a){return new U({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:h.toString(a)}]})}_addCheck(e){return new U({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:h.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:h.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:h.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:h.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&v.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}U.create=r=>new U({checks:[],typeName:m.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...y(r)});class Y extends g{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==f.bigint)return this._getInvalidInput(e);let s;const a=new b;for(const n of this._def.checks)n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(s=this._getOrReturnCtx(e,s),d(s,{code:c.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),a.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(s=this._getOrReturnCtx(e,s),d(s,{code:c.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),a.dirty()):n.kind==="multipleOf"?e.data%n.value!==BigInt(0)&&(s=this._getOrReturnCtx(e,s),d(s,{code:c.not_multiple_of,multipleOf:n.value,message:n.message}),a.dirty()):v.assertNever(n);return{status:a.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return d(t,{code:c.invalid_type,expected:f.bigint,received:t.parsedType}),p}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,s,a){return new Y({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:h.toString(a)}]})}_addCheck(e){return new Y({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Y.create=r=>new Y({checks:[],typeName:m.ZodBigInt,coerce:(r==null?void 0:r.coerce)??!1,...y(r)});class ge extends g{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==f.boolean){const s=this._getOrReturnCtx(e);return d(s,{code:c.invalid_type,expected:f.boolean,received:s.parsedType}),p}return T(e.data)}}ge.create=r=>new ge({typeName:m.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...y(r)});class K extends g{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==f.date){const n=this._getOrReturnCtx(e);return d(n,{code:c.invalid_type,expected:f.date,received:n.parsedType}),p}if(Number.isNaN(e.data.getTime())){const n=this._getOrReturnCtx(e);return d(n,{code:c.invalid_date}),p}const s=new b;let a;for(const n of this._def.checks)n.kind==="min"?e.data.getTime()<n.value&&(a=this._getOrReturnCtx(e,a),d(a,{code:c.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),s.dirty()):n.kind==="max"?e.data.getTime()>n.value&&(a=this._getOrReturnCtx(e,a),d(a,{code:c.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),s.dirty()):v.assertNever(n);return{status:s.value,value:new Date(e.data.getTime())}}_addCheck(e){return new K({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:h.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:h.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}K.create=r=>new K({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:m.ZodDate,...y(r)});class ve extends g{_parse(e){if(this._getType(e)!==f.symbol){const s=this._getOrReturnCtx(e);return d(s,{code:c.invalid_type,expected:f.symbol,received:s.parsedType}),p}return T(e.data)}}ve.create=r=>new ve({typeName:m.ZodSymbol,...y(r)});class xe extends g{_parse(e){if(this._getType(e)!==f.undefined){const s=this._getOrReturnCtx(e);return d(s,{code:c.invalid_type,expected:f.undefined,received:s.parsedType}),p}return T(e.data)}}xe.create=r=>new xe({typeName:m.ZodUndefined,...y(r)});class ke extends g{_parse(e){if(this._getType(e)!==f.null){const s=this._getOrReturnCtx(e);return d(s,{code:c.invalid_type,expected:f.null,received:s.parsedType}),p}return T(e.data)}}ke.create=r=>new ke({typeName:m.ZodNull,...y(r)});class be extends g{constructor(){super(...arguments),this._any=!0}_parse(e){return T(e.data)}}be.create=r=>new be({typeName:m.ZodAny,...y(r)});class we extends g{constructor(){super(...arguments),this._unknown=!0}_parse(e){return T(e.data)}}we.create=r=>new we({typeName:m.ZodUnknown,...y(r)});class I extends g{_parse(e){const t=this._getOrReturnCtx(e);return d(t,{code:c.invalid_type,expected:f.never,received:t.parsedType}),p}}I.create=r=>new I({typeName:m.ZodNever,...y(r)});class Ce extends g{_parse(e){if(this._getType(e)!==f.undefined){const s=this._getOrReturnCtx(e);return d(s,{code:c.invalid_type,expected:f.void,received:s.parsedType}),p}return T(e.data)}}Ce.create=r=>new Ce({typeName:m.ZodVoid,...y(r)});class O extends g{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==f.array)return d(t,{code:c.invalid_type,expected:f.array,received:t.parsedType}),p;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,o=t.data.length<a.exactLength.value;(i||o)&&(d(t,{code:i?c.too_big:c.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(d(t,{code:c.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(d(t,{code:c.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>a.type._parseAsync(new j(t,i,t.path,o)))).then(i=>b.mergeArray(s,i));const n=[...t.data].map((i,o)=>a.type._parseSync(new j(t,i,t.path,o)));return b.mergeArray(s,n)}get element(){return this._def.type}min(e,t){return new O({...this._def,minLength:{value:e,message:h.toString(t)}})}max(e,t){return new O({...this._def,maxLength:{value:e,message:h.toString(t)}})}length(e,t){return new O({...this._def,exactLength:{value:e,message:h.toString(t)}})}nonempty(e){return this.min(1,e)}}O.create=(r,e)=>new O({type:r,minLength:null,maxLength:null,exactLength:null,typeName:m.ZodArray,...y(e)});function M(r){if(r instanceof x){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=Z.create(M(s))}return new x({...r._def,shape:()=>e})}else return r instanceof O?new O({...r._def,type:M(r.element)}):r instanceof Z?Z.create(M(r.unwrap())):r instanceof q?q.create(M(r.unwrap())):r instanceof $?$.create(r.items.map(e=>M(e))):r}class x extends g{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=v.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==f.object){const u=this._getOrReturnCtx(e);return d(u,{code:c.invalid_type,expected:f.object,received:u.parsedType}),p}const{status:s,ctx:a}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof I&&this._def.unknownKeys==="strip"))for(const u in a.data)i.includes(u)||o.push(u);const l=[];for(const u of i){const _=n[u],w=a.data[u];l.push({key:{status:"valid",value:u},value:_._parse(new j(a,w,a.path,u)),alwaysSet:u in a.data})}if(this._def.catchall instanceof I){const u=this._def.unknownKeys;if(u==="passthrough")for(const _ of o)l.push({key:{status:"valid",value:_},value:{status:"valid",value:a.data[_]}});else if(u==="strict")o.length>0&&(d(a,{code:c.unrecognized_keys,keys:o}),s.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const _ of o){const w=a.data[_];l.push({key:{status:"valid",value:_},value:u._parse(new j(a,w,a.path,_)),alwaysSet:_ in a.data})}}return a.common.async?Promise.resolve().then(async()=>{const u=[];for(const _ of l){const w=await _.key,W=await _.value;u.push({key:w,value:W,alwaysSet:_.alwaysSet})}return u}).then(u=>b.mergeObjectSync(s,u)):b.mergeObjectSync(s,l)}get shape(){return this._def.shape()}strict(e){return h.errToObj,new x({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var n,i;const a=((i=(n=this._def).errorMap)==null?void 0:i.call(n,t,s).message)??s.defaultError;return t.code==="unrecognized_keys"?{message:h.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new x({...this._def,unknownKeys:"strip"})}passthrough(){return new x({...this._def,unknownKeys:"passthrough"})}extend(e){return new x({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new x({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:m.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new x({...this._def,catchall:e})}pick(e){const t={};for(const s of v.objectKeys(e))e[s]&&this.shape[s]&&(t[s]=this.shape[s]);return new x({...this._def,shape:()=>t})}omit(e){const t={};for(const s of v.objectKeys(this.shape))e[s]||(t[s]=this.shape[s]);return new x({...this._def,shape:()=>t})}deepPartial(){return M(this)}partial(e){const t={};for(const s of v.objectKeys(this.shape)){const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}return new x({...this._def,shape:()=>t})}required(e){const t={};for(const s of v.objectKeys(this.shape))if(e&&!e[s])t[s]=this.shape[s];else{let n=this.shape[s];for(;n instanceof Z;)n=n._def.innerType;t[s]=n}return new x({...this._def,shape:()=>t})}keyof(){return Ve(v.objectKeys(this.shape))}}x.create=(r,e)=>new x({shape:()=>r,unknownKeys:"strip",catchall:I.create(),typeName:m.ZodObject,...y(e)});x.strictCreate=(r,e)=>new x({shape:()=>r,unknownKeys:"strict",catchall:I.create(),typeName:m.ZodObject,...y(e)});x.lazycreate=(r,e)=>new x({shape:r,unknownKeys:"strip",catchall:I.create(),typeName:m.ZodObject,...y(e)});class ee extends g{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;function a(n){for(const o of n)if(o.result.status==="valid")return o.result;for(const o of n)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=n.map(o=>new A(o.ctx.common.issues));return d(t,{code:c.invalid_union,unionErrors:i}),p}if(t.common.async)return Promise.all(s.map(async n=>{const i={...t,common:{...t.common,issues:[]},parent:null};return{result:await n._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}})).then(a);{let n;const i=[];for(const l of s){const u={...t,common:{...t.common,issues:[]},parent:null},_=l._parseSync({data:t.data,path:t.path,parent:u});if(_.status==="valid")return _;_.status==="dirty"&&!n&&(n={result:_,ctx:u}),u.common.issues.length&&i.push(u.common.issues)}if(n)return t.common.issues.push(...n.ctx.common.issues),n.result;const o=i.map(l=>new A(l));return d(t,{code:c.invalid_union,unionErrors:o}),p}}get options(){return this._def.options}}ee.create=(r,e)=>new ee({options:r,typeName:m.ZodUnion,...y(e)});function ce(r,e){const t=S(r),s=S(e);if(r===e)return{valid:!0,data:r};if(t===f.object&&s===f.object){const a=v.objectKeys(e),n=v.objectKeys(r).filter(o=>a.indexOf(o)!==-1),i={...r,...e};for(const o of n){const l=ce(r[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(t===f.array&&s===f.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let n=0;n<r.length;n++){const i=r[n],o=e[n],l=ce(i,o);if(!l.valid)return{valid:!1};a.push(l.data)}return{valid:!0,data:a}}else return t===f.date&&s===f.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class te extends g{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(n,i)=>{if(pe(n)||pe(i))return p;const o=ce(n.value,i.value);return o.valid?((ye(n)||ye(i))&&t.dirty(),{status:t.value,value:o.data}):(d(s,{code:c.invalid_intersection_types}),p)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([n,i])=>a(n,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}te.create=(r,e,t)=>new te({left:r,right:e,typeName:m.ZodIntersection,...y(t)});class $ extends g{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.array)return d(s,{code:c.invalid_type,expected:f.array,received:s.parsedType}),p;if(s.data.length<this._def.items.length)return d(s,{code:c.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),p;!this._def.rest&&s.data.length>this._def.items.length&&(d(s,{code:c.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const n=[...s.data].map((i,o)=>{const l=this._def.items[o]||this._def.rest;return l?l._parse(new j(s,i,s.path,o)):null}).filter(i=>!!i);return s.common.async?Promise.all(n).then(i=>b.mergeArray(t,i)):b.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new $({...this._def,rest:e})}}$.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new $({items:r,typeName:m.ZodTuple,rest:null,...y(e)})};class Te extends g{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.map)return d(s,{code:c.invalid_type,expected:f.map,received:s.parsedType}),p;const a=this._def.keyType,n=this._def.valueType,i=[...s.data.entries()].map(([o,l],u)=>({key:a._parse(new j(s,o,s.path,[u,"key"])),value:n._parse(new j(s,l,s.path,[u,"value"]))}));if(s.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const l of i){const u=await l.key,_=await l.value;if(u.status==="aborted"||_.status==="aborted")return p;(u.status==="dirty"||_.status==="dirty")&&t.dirty(),o.set(u.value,_.value)}return{status:t.value,value:o}})}else{const o=new Map;for(const l of i){const u=l.key,_=l.value;if(u.status==="aborted"||_.status==="aborted")return p;(u.status==="dirty"||_.status==="dirty")&&t.dirty(),o.set(u.value,_.value)}return{status:t.value,value:o}}}}Te.create=(r,e,t)=>new Te({valueType:e,keyType:r,typeName:m.ZodMap,...y(t)});class Q extends g{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.set)return d(s,{code:c.invalid_type,expected:f.set,received:s.parsedType}),p;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(d(s,{code:c.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(d(s,{code:c.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const n=this._def.valueType;function i(l){const u=new Set;for(const _ of l){if(_.status==="aborted")return p;_.status==="dirty"&&t.dirty(),u.add(_.value)}return{status:t.value,value:u}}const o=[...s.data.values()].map((l,u)=>n._parse(new j(s,l,s.path,u)));return s.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,t){return new Q({...this._def,minSize:{value:e,message:h.toString(t)}})}max(e,t){return new Q({...this._def,maxSize:{value:e,message:h.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Q.create=(r,e)=>new Q({valueType:r,minSize:null,maxSize:null,typeName:m.ZodSet,...y(e)});class Oe extends g{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Oe.create=(r,e)=>new Oe({getter:r,typeName:m.ZodLazy,...y(e)});class Ae extends g{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return d(t,{received:t.data,code:c.invalid_literal,expected:this._def.value}),p}return{status:"valid",value:e.data}}get value(){return this._def.value}}Ae.create=(r,e)=>new Ae({value:r,typeName:m.ZodLiteral,...y(e)});function Ve(r,e){return new B({values:r,typeName:m.ZodEnum,...y(e)})}class B extends g{_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return d(t,{expected:v.joinValues(s),received:t.parsedType,code:c.invalid_type}),p}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return d(t,{received:t.data,code:c.invalid_enum_value,options:s}),p}return T(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return B.create(e,{...this._def,...t})}exclude(e,t=this._def){return B.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}B.create=Ve;class Ne extends g{_parse(e){const t=v.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==f.string&&s.parsedType!==f.number){const a=v.objectValues(t);return d(s,{expected:v.joinValues(a),received:s.parsedType,code:c.invalid_type}),p}if(this._cache||(this._cache=new Set(v.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const a=v.objectValues(t);return d(s,{received:s.data,code:c.invalid_enum_value,options:a}),p}return T(e.data)}get enum(){return this._def.values}}Ne.create=(r,e)=>new Ne({values:r,typeName:m.ZodNativeEnum,...y(e)});class re extends g{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.promise&&t.common.async===!1)return d(t,{code:c.invalid_type,expected:f.promise,received:t.parsedType}),p;const s=t.parsedType===f.promise?t.data:Promise.resolve(t.data);return T(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}re.create=(r,e)=>new re({type:r,typeName:m.ZodPromise,...y(e)});class F extends g{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===m.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,n={addIssue:i=>{d(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(n.addIssue=n.addIssue.bind(n),a.type==="preprocess"){const i=a.transform(s.data,n);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return p;const l=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return l.status==="aborted"?p:l.status==="dirty"||t.value==="dirty"?J(l.value):l});{if(t.value==="aborted")return p;const o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?p:o.status==="dirty"||t.value==="dirty"?J(o.value):o}}if(a.type==="refinement"){const i=o=>{const l=a.refinement(o,n);if(s.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){const o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?p:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?p:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(a.type==="transform")if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!D(i))return p;const o=a.transform(i.value,n);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>D(i)?Promise.resolve(a.transform(i.value,n)).then(o=>({status:t.value,value:o})):p);v.assertNever(a)}}F.create=(r,e,t)=>new F({schema:r,typeName:m.ZodEffects,effect:e,...y(t)});F.createWithPreprocess=(r,e,t)=>new F({schema:e,effect:{type:"preprocess",transform:r},typeName:m.ZodEffects,...y(t)});class Z extends g{_parse(e){return this._getType(e)===f.undefined?T(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Z.create=(r,e)=>new Z({innerType:r,typeName:m.ZodOptional,...y(e)});class q extends g{_parse(e){return this._getType(e)===f.null?T(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}q.create=(r,e)=>new q({innerType:r,typeName:m.ZodNullable,...y(e)});class se extends g{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===f.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}se.create=(r,e)=>new se({innerType:r,typeName:m.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...y(e)});class de extends g{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return X(a)?a.then(n=>({status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new A(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new A(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}de.create=(r,e)=>new de({innerType:r,typeName:m.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...y(e)});class Se extends g{_parse(e){if(this._getType(e)!==f.nan){const s=this._getOrReturnCtx(e);return d(s,{code:c.invalid_type,expected:f.nan,received:s.parsedType}),p}return{status:"valid",value:e.data}}}Se.create=r=>new Se({typeName:m.ZodNaN,...y(r)});class Ut extends g{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class le extends g{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const n=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return n.status==="aborted"?p:n.status==="dirty"?(t.dirty(),J(n.value)):this._def.out._parseAsync({data:n.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?p:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new le({in:e,out:t,typeName:m.ZodPipeline})}}class ue extends g{_parse(e){const t=this._def.innerType._parse(e),s=a=>(D(a)&&(a.value=Object.freeze(a.value)),a);return X(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}ue.create=(r,e)=>new ue({innerType:r,typeName:m.ZodReadonly,...y(e)});var m;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(m||(m={}));const Xt=R.create,Kt=U.create;I.create;O.create;const er=x.create,tr=ee.create;te.create;$.create;B.create;re.create;Z.create;q.create;/**
  * vee-validate v4.15.0
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */const Re=r=>r!==null&&!!r&&typeof r=="object"&&!Array.isArray(r);function Le(r){return Number(r)>=0}function Bt(r){return typeof r=="object"&&r!==null}function Ft(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}function Ze(r){if(!Bt(r)||Ft(r)!=="[object Object]")return!1;if(Object.getPrototypeOf(r)===null)return!0;let e=r;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(r)===e}function ze(r,e){return Object.keys(e).forEach(t=>{if(Ze(e[t])&&Ze(r[t])){r[t]||(r[t]={}),ze(r[t],e[t]);return}r[t]=e[t]}),r}function qt(r){const e=r.split(".");if(!e.length)return"";let t=String(e[0]);for(let s=1;s<e.length;s++){if(Le(e[s])){t+=`[${e[s]}]`;continue}t+=`.${e[s]}`}return t}function rr(r,e){return{__type:"VVTypedSchema",async parse(s){const a=await r.safeParseAsync(s,e);if(a.success)return{value:a.data,errors:[]};const n={};return De(a.error.issues,n),{errors:Object.values(n)}},cast(s){try{return r.parse(s)}catch{const n=Ue(r);return Re(n)&&Re(s)?ze(n,s):s}},describe(s){try{if(!s)return{required:!r.isOptional(),exists:!0};const a=Wt(s,r);return a?{required:!a.isOptional(),exists:!0}:{required:!1,exists:!1}}catch{return{required:!1,exists:!1}}}}}function De(r,e){r.forEach(t=>{const s=qt(t.path.join("."));t.code==="invalid_union"&&(De(t.unionErrors.flatMap(a=>a.issues),e),!s)||(e[s]||(e[s]={errors:[],path:s}),e[s].errors.push(t.message))})}function Ue(r){if(r instanceof x)return Object.fromEntries(Object.entries(r.shape).map(([e,t])=>t instanceof se?[e,t._def.defaultValue()]:t instanceof x?[e,Ue(t)]:[e,void 0]))}function Wt(r,e){if(!je(e))return null;if(at(r))return e.shape[nt(r)];const t=(r||"").split(/\.|\[(\d+)\]/).filter(Boolean);let s=e;for(let a=0;a<=t.length;a++){const n=t[a];if(!n||!s)return s;if(je(s)){s=s.shape[n]||null;continue}Le(n)&&Ht(s)&&(s=s._def.type)}return null}function Be(r){return r._def.typeName}function Ht(r){return Be(r)===m.ZodArray}function je(r){return Be(r)===m.ZodObject}export{Yt as G,Qt as m,Kt as n,er as o,Xt as s,rr as t,tr as u};
